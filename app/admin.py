# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.contrib import admin
from .models import (
    Product, ProductScene, Scene, SceneTitle, SceneTitleBookmark, 
    BookmarkListBookMarks, Variation, SceneShareLink, SceneTaken, 
    SceneTakenScenes, DownloadedScene, SceneComment, SceneCommentFolder, 
    SceneCommentFile, DownloadedSceneComment, SceneCommentReceiver, 
    OfferCreator, OfferMessage, OfferMessageReceiver, ChatRoom, 
    ProductOrder, ProductOrderUpload, PreviewCommentOrder, ProductComment, 
    ProductCommentFolder, ProductCommentFile, DownloadedProductComment, 
    PreviewProductComment, ProductCommentDownloaded, OfferProduct, 
    SelectionOffer, ProductMessage, ProductMessageFolder, ProductMessageFile, 
    FormContractAndPlan, DownloadedProductMessageFile, MessageReceiver, 
    PlanOffer, VariationOffer, Post, PostItem, BlockListArtist, 
    OfferProject, OfferUser, ListWork, SaleContentListWork, TopicGallery,
    TopicTag, Category, TopicCategory, SelectionGallery, SaleContentSelection, 
    SectionCredit, ItemSectionCredit, OrderData, DraftMessage, 
    UserOnlineStatus, UserProductCharge, CreatorOfferFile, 
    ColorProjectSetting, FontProjectSetting, ReviewOffer
)

admin.site.register([
    ProductScene, Scene, SceneTitle, SceneTitleBookmark, 
    BookmarkListBookMarks, Variation, SceneShareLink, SceneTaken, 
    SceneTakenScenes, DownloadedScene, SceneComment, SceneCommentFolder, 
    SceneCommentFile, DownloadedSceneComment, SceneCommentReceiver, 
    OfferCreator, OfferMessage, OfferMessageReceiver, ChatRoom, 
    ProductOrder, ProductOrderUpload, PreviewCommentOrder, ProductComment, 
    ProductCommentFolder, ProductCommentFile, DownloadedProductComment, 
    PreviewProductComment, ProductCommentDownloaded, OfferProduct, 
    SelectionOffer, ProductMessage, ProductMessageFolder, ProductMessageFile, 
    FormContractAndPlan, DownloadedProductMessageFile, MessageReceiver, 
    PlanOffer, VariationOffer, Post, PostItem, BlockListArtist, 
    OfferProject, OfferUser, ListWork, SaleContentListWork, 
    TopicTag, Category, TopicCategory, SelectionGallery, SaleContentSelection, 
    SectionCredit, ItemSectionCredit, OrderData, DraftMessage, 
    UserOnlineStatus, UserProductCharge, CreatorOfferFile, 
    ColorProjectSetting, FontProjectSetting, ReviewOffer
])

@admin.register(TopicGallery)
class TopicGalleryAdmin(admin.ModelAdmin):
    list_display = ('title', 'overview', 'description', 'order')
    search_fields = ('title', 'overview', 'description')

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'last_update', 'max_scene', 'current_scene', 'progress')
    search_fields = ('name', 'code_name', 'client_name')
    list_filter = ('genres', 'acr_status')
