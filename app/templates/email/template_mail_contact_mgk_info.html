{% load util %}
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Email Template Contact info master admin</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0">
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <style type="text/css">
    @font-face {
      font-family: "A+mfCv-AXISラウンド 50 M StdN";
      src: url('../fonts/AxisRound50StdN-M.otf');
    }

    @font-face {
      font-family: "A+mfCv-AXISラウンド 50 L StdN";
      src: url('../fonts/AxisRound50StdN-L.otf');
    }

    @media only screen and (max-width: 620px) {
      body {
        font-size: 13px !important;
      }

      .email-sub-title {
        font-size: 16px !important;
      }

      .email {
        background-size: 23% auto !important;
      }

      .email-text {
        font-size: 13px;
      }

      .email-info {
        font-size: 13px;
      }

      .email-message {
        font-size: 13px;
      }

      .email-link {
        font-size: 13px;
      }

      .file-link {
        color: #a7a8a9;
        text-decoration: none;
        text-align: center;
      }

      .email-info-detail {
        font-size: 13px;
      }

      .email-footer {
        font-size: 13px;
      }

      img.fullwidth,
      img.fullwidthOnMobile {
        max-width: 100% !important;
      }
    }

    .account__file {
      position: relative;
      max-width: 170px;
      
      display: flex;
      align-items: center;
      padding: 8px 25px 8px 16px;
      background-color: #f0f0f0;
      border-radius: 6px;
      margin: 8px 0;
      color: #A7A8A9;
      text-decoration:none;
      text-align:center;
    }

    .account__file-name {
      font-size: 11px;
      line-height: 17px;
      display: block;
      margin: 0 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
      max-height: 20px
    }

    .email-message {
      word-break: break-word;
      white-space: pre-line;
    }
  </style>
</head>

<body style="margin: 0; font-family: 'A+mfCv-AXISラウンド 50 L StdN',
'M PLUS 1p'; font-size: 13px; line-height: 1.5; -webkit-font-smoothing: antialiased; color: #000000;">
<main class="email" style="font-size: 13px;">
  <div class="email-container" style="max-width: 800px; margin: 0 auto; padding: 0 15px 0 15px;">
    <table class="email-top" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
           table-layout="fixed" border-spacing="0" style="margin-bottom: 30px">
      <tbody>
      <tr>
        <td class="email-logo"><img src="{{ host }}/static/images/soremo-favi2_01.png" alt="Soremo"
                                    height="45px" width="45px" style="border-radius: 50%"></td>
        {% if recipient %}
          <td style="text-align: right">
            <table class="user-info" style="width:100%; table-layout: fixed">
              <tbody>
              <tr>
                <td class="user-name">
                  {{ recipient.get_display_name }} 様
                </td>
                <td style="width: 50px; padding-left: 10px; background-color: white; border-radius: 50%;">
                  <img src="{{ recipient_avt }}" alt="User Avatar"
                       border="0"
                       style="width: 40px; max-width: 40px; border: 1px solid #eee; border-radius: 50%">
                </td>
              </tr>
              </tbody>
            </table>
          </td>
        {% endif %}
      </tr>
      </tbody>
    </table>

    {% if not recipient %}
      <p class="email-text" style="margin: 0 0 16px">
        {{ contact_info.fullname }} 様</p>
      <p class="email-text" style="margin: 0 0 8px">このたびは、SOREMOにお問い合わせいただき、ありがとうございます。</p>
      <p class="email-text" style="margin: 0 0 24px">下記内容を承りました。</p>
    {% endif %}

    {# content #}
    <hr>
    <h2>お問い合わせ内容</h2>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">サービスプラン:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.get_plan_display }}</span>
    </p>
  {% if contact_info.option %}
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">オプション:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.option }}</span>
    </p>
  {%  endif %}
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">お名前:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.fullname }}</span>
    </p>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">メールアドレス:</span><span
            class="email-text" style="margin-left: 4px"><a href="mailto:{{ contact_info.email }}"
                                  target="_blank">{{ contact_info.email }}</a></span>
    </p>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">ポジション:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.job_type }}</span>
    </p>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">会社名:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.enterprise }}</span>
    </p>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">電話番号（日中連絡がとれる番号）:</span><span
            class="email-text" style="margin-left: 4px"><a href="tel:{{ contact_info.phone }}"
                                  target="_blank">{{ contact_info.phone }}</a></span>
    </p>
    <p class="email-text" style="margin: 0 0 8px">
      <span class="email-text strong-text" style="color:#a7a8a9;">ご希望の連絡方法:</span><span
            class="email-text" style="margin-left: 4px">{{ contact_info.get_contact_channel_display }}</span>
    </p>
    <div class="email-text" style="margin: 0 0 8px;display: flex;">
      <p class="email-text strong-text" style="color:#a7a8a9; min-width: fit-content">メッセージ: </p>
      <div class="email-text email-message" style="word-break: break-word; white-space: pre-line; margin-left: 4px"><div style="word-break: break-word; white-space: pre-line;">{{ contact_info.message | linebreaks }}</div></div>
    </div>
    <hr>
    {# text2 #}
    <p class="email-text" style="margin: 30px 0 8px">{{ text2 }}</p>
    {% if not recipient %}
      <p class="email-text" style="margin: 0 0 8px">今しばらくお待ちいただきますようお願いいたします。</p>
      <p class="email-text" style="margin: 0 0 8px">何かございましたら、下記よりお気軽にお問い合わせください。</p>
    {% endif %}

    {# footer #}
    {% include 'emails/_footer_email.html' %}
  </div>
</main>
</body>
</html>
