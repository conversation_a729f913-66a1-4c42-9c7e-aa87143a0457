{% load bootstrap3 %}
{% load util %}
{% load static compress %}
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/index.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/spnavi.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/1904nr.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/spnavi.css' %}"/>
{% endcompress %}
<!-- [spnavi] -->
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"></script>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.3/jquery.easing.min.js"></script>
<!-- /[spnavi] -->

<style>
  #container {
    margin-top: 10vh;
  }
</style>
<!-- [search] -->

<script>
    (function () {
        var cx = '009877191623187414177:iykqoiopghu';
        var gcse = document.createElement('script');
        gcse.type = 'text/javascript';
        gcse.async = true;
        gcse.src = 'https://cse.google.com/cse.js?cx=' + cx;
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(gcse, s);
    })();
</script>

<script>
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-93363802-1', 'auto');
    ga('send', 'pageview');

</script>
<!-- /[search] -->
