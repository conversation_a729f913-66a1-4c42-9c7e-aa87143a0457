{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static compress %}
{% block title %}<title>Update Info ｜ SOREMO</title>{% endblock title %}

{% block content %}
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/index.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/topic.css' %}"/>
    {% endcompress %}
  <section class="banner">
    <h2>Update Info</h2>
  </section>

  <article class="wrapper blog">

      <h3>Version History</h3>
      <h5 class="lightgray">21/3/8(mon) - 21/3/12(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>soremo.jpをdevelop.soremo.jpと統合。AWSサーバーに一元化しました。</li>
        <li>アーティストのプロフィールアドレスをご自身で設定できるようにしました。</li>
      </ul>

      <h5 class="lightgray">21/3/1(mon) - 21/3/5(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>再生ボタンと停止ボタン押下した際に、行間スペースが変化してしまう点を修正しました。</li>
        <li>管理者のアーティストアカウント管理画面の仕様を調整しました。</li>
        <li>動画内表示されていたチャプター>シーン >バリエーション、及びバージョンNoを、動画外に移動させました。</li>
        <li>「やりとり中」。画面左下にもハートアイコン表示を戻し、ホバーしなくてもON・OFFできるようにしました。</li>
        <li>アーティストとキュレーターの相互承認、チェッククリック後のOKを不要にしました。</li>
        <li>検収データが存在しないオファーも、ディレクターが検収できるように改善しました。</li>
        <li>プロジェクトオーナーがメンバー招待できないことがある不具合を修正しました。</li>
        <li>バージョン数の位置を調整しました。</li>
        <li>シーンのないプロジェクト（スタジオでの収録案件など）は、シーン0でアーカイブに残るようにしました。</li>
        <li>キュレーターとアーティストの相互承認システム。NEWを「更新待ち」タグを追加しました。</li>
        <li>ディレクター、複数シーンをアップロードした際に一部がバージョンに移動してしまう不具合を修正しました。</li>
      </ul>

      <h3>Version History</h3>
      <h5 class="lightgray">21/2/22(mon) - 21/2/26(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>「全てのシーン」やりとり中のシーンも表示させる仕様にしました。</li>
        <li>アーティストのメッセンジャー、コメントが重なって表示される不具合を修正しました。</li>
        <li>アーティスト契約書、職種項目表示の不具合を修正しました。</li>
        <li>アーティストのメッセンジャー、導線を自動化しました。（やりとり中 > すべてのスレッド）</li>
        <li>アーティストアカウントもプロジェクトバナーをドラッグ&ドロップで並び替えできるようにしました。</li>
        <li>アーティストのプロフィール申請、アップロード中にプログレスバーを追加しました。</li>
        <li>トースト、メッセンジャのテキスト表示を一部改善しました。</li>
        <li>管理者予算管理メーター、検収済み金額→オファー済み金額に変更しました。</li>
        <li>管理者予算管理の表示設定を修正しました。</li>
      </ul>


      <h5 class="lightgray">21/2/15(mon) - 21/2/19(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>ディレクターがアーティストにオファーする際、納品物項目詳細を明記できるフォームを追加しました。</li>
        <li>ディレクターがアーティストにオファーする際、契約書詳細バリエーションを追加しました。</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>アーティスト契約書表示の不具合を修正しました。</li>
        <li>コメント編集中、アイコンが表示されたままになっていた点を直しました。</li>
      </ul>

      </ul>


      <h5 class="lightgray">21/2/8(mon) - 21/2/12(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>Gallery。アーティストを横断して、アルバム再生できるようにしました。</li>
        <li>アルバムの投稿。パラメーター属性にグラフィックを追加しました。</li>
        <li>Galleryの同時再生数上限を設定しました。MUSICは1つまで、SEは2つまで、VOICEは3つまでです。</li>
        <li>アルバムをクリックした際、キャプションとアーティスト名をトーストを表示するようにしました。</li>
      </ul>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>コメントUIのカラーおよびパディングを調整しました。</li>
        <li>バリエーション再生。再生を切り替えた際に前の再生途中位置をではなく、常に0:00から再生開始するように改善しました。</li>
        <li>シーンのバリエーションが１つのみの場合は、バリエーション名を非表示にしました。</li>
      </ul>

      <h5 class="lightgray">21/2/1(mon) - 21/2/5(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>アーティストプロフィール。アルバムをドラッグ&ドロップで並び替えできるようにしました</li>
        <li>グローバルメニュー。現在選択中メニューを青にしました。</li>
        <li>アーティストプロフィールの画像アップロード。jpgにも対応しました。</li>
        <li>管理者アカウント。ディレクター招待と予算権限設定を追加しました。</li>
        <li>管理者アカウント。プロジェクト毎の開始日・終了日・内容のフォームを追加しました。</li>
        <li>管理者アカウント。プロジェクト毎の取引情報CSV書き出し、内容を調整しました。</li>
        <li>ディレクターアカウント。予算上限設定を超えている場合にモーダルを表示させるようにしました。</li>
      </ul>

      <h5 class="lightgray">21/1/25(mon) - 21/1/29(fri)</h5>
      <ul>
        <p><span class="tag-gray">Update</span></p>
        <li>アーティスト納品の際、アップロード中にプログレスメーターを追加しました。</li>
        <li>アーティストメッセンジャー。未読バッヂが更新されない不具合を改善しました。</li>
        <li>アーティストとキュレーターのトースト通知、テキスト表現を直しました。</li>
        <li>オファー通知メール、添付ファイルがある場合にレイアウトが崩れてしまうのを直しました。</li>
        <li>グローバルメニューを更新。SP経由でのアクセスを改善しました。</li>
        <li>ディレクター、PROJECTとMESSANGERの切り替え導線を改善しました。</li>
        <li>納品ファイルをクリックした際のアクションを調整しました。</li>
        <li>検収されたオファーは、「検収が完了しました。」が表示されるようにしました。</li>
        <li>キュレーターの提案ボタン。テキストを修正しました。</li>
      </ul>

      <h5 class="lightgray">21/1/18(mon) - 21/1/22(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>オーディオサンプルをアップロードする際、各種設定を入力できるようになりました。種類（MUSIC、SOUND EFFECTS、VOICE）、サムネイルカラーの選択、属性、販売の有無（Contact for
          Price、またはSold）、オークション出品の有無（期間及び開始価格と終了価格）
        </li>
        <li>
          プロフィール提案機能を追加しました。アーティストに対して、実績追加など、キュレーターからのプロフィール更新提案が届きます。アーティストは更新箇所のNEW部分をクリックして、「更新」「承認」を選ぶだけでプロフィールをアップデートできます。プロフィールは、相互承認された内容のみオーナー様へ開示されます。
        </li>
        <li>ナビメニューにテキストを追加し、色を整理しました。</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>プロフィールのPhilosophy部分、テキスト記載なしでもOKにしました。</li>
        <li>プロフィールのサウンドサンプル、削除アイコンを追加しました。</li>
        <li>グローバルメニューに表示されるNEWタグの位置を調整しました。</li>
        <li>ディレクターがアーティストにオファーする際の表示不具合を修正しました。</li>
        <li>ディレクターとアーティストのメッセンジャー、既読アイコンがつかないのを修正しました。</li>
        <li>ディレクター。オーナーやりとりとアーティストやりとりの切り替え導線を改善しました。</li>
        <li>ディレクターとアーティストのメッセンジャー、取引成立状況のテキストを修正しました。</li>

      </ul>


      <h5 class="lightgray">21/1/11(mon) - 21/1/15(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>グローバルメニューを整理し、プロジェクトとメッセンジャーのやりとり導線を整理しました。</li>
        <li>ディレクターとアーティスト間のメール通知の不具合を修正しました。</li>
        <li>新規登録されたアーティスト情報がディレクターに届かない不具合を修正しました。</li>
        <li>アーティストのナビメニューにテキストを追加しました。</li>
      </ul>


      <h5 class="lightgray">21/1/4(mon) - 21/1/8(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>プロフィール画面、アーティストとキュレーターが相互更新・承認で創り上げていくスタイルに刷新しました。</li>
        <li>アーティスト、キュレーター。いずれかが内容を更新すると、相手側にNEWタグが表示されます。</li>
        <li>相手側は、NEWタグをクリックし、チェックマークで承認。全ての内容が相互同意されたもののみが掲載される仕様にしました。</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>アーティストメッセンジャーの送信日付。1週間以上経過したものは、曜日ではなく日付表示になるよう修正しました。</li>
        <li>アーティストの職種属性をなくしました。肩書きのみに一元化。</li>
        <li>アーティストメッセンジャーのPlaceholderを修正しました。</li>
        <li>アーティストプロフィールページ。デザインを修正しました。</li>
      </ul>


      <h5 class="lightgray">20/12/21(mon) - 20/12/24(fri)</h5>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>All View。ハートONOFF、ダウンロードアイコンを直接クリックできるようにしました。</li>
        <li>All View。チェックバックメニューを削除しました。サムネイルをクリックでチェックバック画面へ遷移します。</li>
        <li>All View。ホバー再生時のアニメーションを調整しました。</li>
        <li>SPサイトでログインした際にメッセージが見えなくなるのを修正しました。</li>

        <li>アーティストアカウント情報。アカウント情報の支払い情報。支店番号。口座種目の選択を追加しました。</li>
        <li>アーティスト利用設定。ボタン名を修正しました。</li>

        <li>アーティストへのオファーメール。“円”（単位）を追加しました。</li>
        <li>アーティストメール通知を芸名に修正しました。</li>
        <li>アーティストメッセンジャー。ダウンロードボタン位置ずれを修正しました。</li>
        <li>アーティストメッセンジャー。サイドバーのスレッド表示。アーティストの職種ではなく肩書きに修正しました。</li>
        <li>アーティストメッセンジャー。納品ファイルをアップロードする際の自動コメント（納品ファイルがアップロードしました）を削除しました。</li>
        <li>アーティストプロフィールページ。テキスト文字上限をなくしました。</li>
        <li>アーティストプロフィール。フッダーを修正しました。</li>
        <li>アーティストプロフィール。レイアウトを更新しました。</li>
      </ul>

      <h5 class="lightgray">20/12/14(mon) - 20/12/18(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>All Viewにフィルターを追加しました。デフォ（全表示）→ハートON（検収済みのみ）→ハートOFF（未検収のもののみ）を切り替えできます。</li>
        <li>All Viewのサムネイル下に、シーン名、最終更新日、ステータスを常時表示としました。シンプルを極めようとしていたのですが、まさかの展開です。</li>
        <li>アーティストアカウント。メール通知が届くようになりました。オファー、納品、検収、メッセージ。アーティスト、ディレクターの各人に必要な通知が届きます。</li>
        <li>アーティストアカウント。取引状況に応じてボックスの表示内容を最適化しました。取引成立前、データ納品待ち、検収待ち、評価がひとめでわかります！</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>見積相談。既読がつかない不具合を修正しました。</li>
        <li>Update View。バージョン履歴をa,b,c・・・ではなく、1,2,3に変更しました。（バリエーションはa,c,b。バージョンを1,2,3という表記とすることで命名規則一貫性を持たせるため。）</li>
        <li>アーティストアカウント。アカウント情報が保存されない場合がある不具合を修正しました。</li>
        <li>アーティストアカウント。郵便番号検索が機能しない不具合を修正しました。</li>
        <li>アーティストアカウント。取引承認後のテキストを修正しました。</li>

        <li>アーティストアカウント。ログインしていない状態でメール通知リンクをクリックするとエラーになってしまう不具合を直しました。</li>
        <li>アーティストアカウント。トレードオフメーターの利用設定テキストを修正しました。</li>
        <li>アーティストアカウント。アーティストからディレクターの評価ができるよう修正しました。</li>
        <li>アーティストアカウント。メール通知の一部を修正しました。</li>
        <li>ナビメニューの>を消しました。</li>
      </ul>


      <h5 class="lightgray">20/12/7(mon) - 20/12/12(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>見積相談。契約折衝のあとに、既に進行しているプロジェクトと紐付けできるようにしました。</li>
        <li>見積相談。管理者アカウントで、スレッドの整理をできるようにしました。</li>
        <li>アーティストアカウント。ログインしたら、最初に担当プロジェクト一覧が表示されるようにしました。</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>共有リンク。バリエーションをクリックできないことがある不具合を修正しました。</li>
        <li>ディレクターアカウントの動画メニュー不具合を修正しました。</li>
        <li>プロジェクトバナー。SPサイトでも縦固定とする仕様に変更しました。</li>
        <li>コメントリストのエクスポート。ユーザー名を姓名表示に変更しました。</li>
        <li>動画リンク、バリエーションをexpandしたものをクリックできない。</li>
      </ul>


      <h5 class="lightgray">20/11/30(mon) - 20/12/4(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>動画リンク。シーン単位でカルーセル毎共有できるようにしました。</li>
        <li>動画リンク。メッセージの再生ピンにも対応しました。</li>
        <li>見積相談。右下にPDFダウンロードアイコンをつけました。</li>
        <li>見積相談。左下にステータスアイコンをつけました。見積もりはチェック。契約書はハートでお手続きを進めていけます。</li>
      </ul>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>見積相談。グループチャットのときのコメント位置をロール毎に揃えました。オーナーアカウントの場合、管理者側（左）、オーナー側（右）となります。</li>
        <li>ディレクター編集画面。チャプターを非表示としました。</li>
      </ul>


      <h5 class="lightgray">20/11/23(mon) - 20/11/27(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>見積相談。見積書・契約書・請求書を右上のタグで更新履歴を紐づけてみれるようにしました。</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>見積相談。リストメニューの並びをシンプルに更新日順としました。</li>
        <li>見積相談。契約締結後は、プロジェクトオーナーを複数でグループチャットできるようにしました。</li>
        <li>ディレクターアカウントのシーン評価（好き好きじゃない）を削除。オーナーアカウントのみが評価できる仕様としました。</li>
        <li>動画リンク。メッセージも一緒に表示できるようにしました。</li>
        <li>エクスポート。楽曲認識レポートの不具合を修正しました。</li>
      </ul>


      <h5 class="lightgray">20/11/16(mon) - 20/11/20(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>見積相談のUIを刷新しました。PCでは、左に契約書プレビュー、右にタイムラインです。</li>
        <img src="/static/images/<EMAIL>">

        <li>見積相談メッセンジャー。プロジェクト毎の折衝状況に応じて、リストメニューを最適化しました。（１）見積もり待ち、は（白）。（２）折衝中は、見積担当者を表示）。（３）契約締結後は、プロジェクトバナーです。</li>
      </ul>
      <img src="/static/images/<EMAIL>">

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>動画内アイコンを消し、ホバーでメニュー表示されるよう、UIを改善しました。</li>
        <img src="/static/images/<EMAIL>">
        <li>動画を削除しても、ピンづけされたコメントは削除されないよう、修正しました。</li>
        <li>アーカイブのないユーザーは、「アーカイブを観る」ボタンが表示されないようにしました。</li>
        <img src="/static/images/<EMAIL>">
        <li>コメントリストのエクスポート機能の不具合を修正しました。</li>
      </ul>


      <h5 class="lightgray">20/11/9(mon) - 20/11/13(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li><span class="tag-director">admin</span>スマートコントラスト導線を改善しました。見積もり（とりあえずOK）→契約書（OK）→全てにハートがついたら請求書（OK）で自動支払いとなります。
        </li>
        <li><span class="tag-owner">owner</span>スタッフロール不具合を改善しました。プロジェクトバナー右下のinfoアイコンより御確認いただけます。</li>
      </ul>


      <h5 class="lightgray">20/11/2(mon) - 20/11/6(fri)</h5>

      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li><span class="tag-owner">owner</span><b>アップロード中にプログレスバー表示</b>しました。</li>
        <img src="/static/images/<EMAIL>">
        <li><span class="tag-owner">owner</span>ナビメニューの<b>アイコン色を整理</b>しました。「クリックすべき」をblue、「クリックできる」はdeepgray。オプショナル要素（exportなど）はgray。
        </li>
        <li><span class="tag-owner">owner</span><b>コメント内再生ボタンの色</b>を最適化しました。（過去バージョンは、deepgray。最新のものは、blue）</li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li><span class="tag-owner">owner</span>チャプター並び替えが保存されないバグを修正しました。</li>
        <li><span class="tag-owner">owner</span>全表示でのソートイニシャル設定を<b>「シーン名▲」</b>にしました。</li>
        <li><span class="tag-owner">owner</span>「メール通知」、CIを更新しました。</li>
        <li><span class="tag-director">director</span>動画が削除できない不具合を解消しました。</li>
        <li><span class="tag-director">director</span>動画編集画面、<b>サムネイル更新</b>が正常に機能するように直しました。</li>
        <li><span class="tag-director">director</span>動画編集画面、シーン名更新がすぐ反映されるように直しました。</li>
        <li><span class="tag-director">director</span>動画編集画面のセーブ後、リロードしなくても画面更新されるように直しました。</li>
      </ul>

      <h5 class="lightgray">20/10/26(mon) - 20/10/30(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li><span class="tag-owner">owner</span>見積書、契約書、請求書の<b>PDFプレビューも動画と同じ</b>やりとりにしました。見積もり、契約書、請求書を左上タグで切り替えることができます。
        </li>
        <li><span class="tag-owner">owner</span>契約書OKのあとは、<b>プロジェクト単位でやりとり</b>できるようにしました。もちろん、やりとりできるのはプロジェクトオーナーのみです。プロジェクトオーナーが複数ユーザーいる場合に、グループで確認できるようになります。
        </li>
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li><span class="tag-owner">owner</span><b>受送信メッセージを左寄せ</b>に直しました。</li>
        <li><span class="tag-owner">owner</span>before Afterの表示。シンプルにa,b,c・・・としました。</li>
        <li><span class="tag-owner">owner</span>URLリンク共有、タイトル表示が病んでいたのを直しました。</li>
        <li><span class="tag-owner">owner</span>「進行中に戻しました」のコメントをなくしました。</li>
        <li><span class="tag-owner">owner</span>SPサイトのUI不具合を修正しました。</li>
        <li><span class="tag-director">director</span>編集画面で<b>バリエーションの並び替え</b>ができるようになりました。</li>
        <li><span class="tag-director">director</span>編集画面で<b>シーン、バリエーション、バージョンの編集</b>ができるようになりました。</li>
        <li><span class="tag-director">admin</span>オーナー選択ボックスを更新日順の表示にしました。</li>

      </ul>


      <h5 class="lightgray">20/10/18(mon) - 20/10/22(fri)</h5>
      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>「チャプターごとに分ける」トグルOFFが、正常に機能しないことがあるバグを修正しました。</li>
        <li>「楽曲照合レポート」ACR Cloudの不具合を修正しました。</li>
        <li>「削除されたチャプター」シーンがない場合は非表示に修正しました。</li>
      </ul>


      <h5 class="lightgray">20/10/12(mon) - 20/10/16(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li><b>リンク共有</b>ができるようになりました😆（もちろんプロジェクト毎のオーナー御意向に応じてON・OFFできる仕様となっております。）動画ホバー中に右下アイコンをクリックすると、URLリンクが表示されます。開始位置も指定できます。
        </li>
        <img src="/static/images/<EMAIL>">
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>「オーナー」NEWタグのシーンは、動画中央のアイコンを「<b>とりあえずOK</b>」としました。「とりあえずOK」をクリックすると、NEWタグが消えて、すべてのシーン内に移動します。全体比較しながら、急がずにじっくりチェックバック頂けます。
        </li>
        <img src="/static/images/<EMAIL>">
        <li>チャプターを削除しても、<b>シーンは削除されない</b>安心仕様にしました😊</li>
        <li>「ディレクター」オーナー様の<b>チェックバック対応を最優先</b>できるよう、NEWタグを消しました。</li>
        <li>「ディレクター」プロジェクトナビメニューの<b>＋アイコンで直接アップロード</b>画面へ遷移できるようにしました。</li>
        <li>「ディレクター」NEWとAFTERがダブってしまうバグを直しました。</li>
        <li>「アーティスト一覧」UIを修正。<b>表示順もアカウントに応じて自動</b>になりました。</li>
        <li>「動画」チャプター名表示が進行中の内容と違うことがあるバグを直しました。</li>
        <li>「アーティスト」プライバシーポリシー設定で一覧表示のONOFFを切り替えできるようになりました。</li>
        <li>「アーティスト」SNSリンク。twitterとInstargamは、ユーザー名のみ登録でリンクできるようにしました。</li>
        <li>URLリンク、全てのシーンからアクセスした場合に「URLをコピー」ボタンが機能しないバグを直しました。</li>
        <li>「削除したチャプター」シーンがないときはタイトルを非表示にしました。
      </ul>


      <h5 class="lightgray">20/10/5(mon) - 20/10/9(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>プロジェクトのナビメニューを全てアイコンにしました！😊 左から「ファイル受送信」「アップデート」「すべてのシーン」。右のアイコンは「エクスポート」です。</li>
        <img src="/static/images/<EMAIL>">
        <li>「ファイル受送信」にもバッヂをつけました。</li>
        <li>新規と更新を統合。アップデート内でまとめてチェックできるようにしました。新規シーン（NEWタグがついているもの）は、更新よりも上位に表示されます。</li>
        <img src="/static/images/<EMAIL>">

        <li>「すべてのシーン」では、完了動画も一緒に表示する仕様としました！😊　サムネイルがグレーアウトは検収済み。ダウンロードアイコンで納品データを落とせます。</li>
        <img src="/static/images/update_201007_hearton.png">
      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>プロジェクトバナー、ホバーで拡大をやめて、固定としました😫</li>
        <li>メール通知、複数バリエーションをアップロードした際もサムネイルがひとつにまとまるように改善しました。</li>
        <li>プロジェクトバナークリック後の導線を改善しました。</li>
        <li>検収済み動画は「ハートを戻す」アイコンが中央に表示されるようにしました。</li>
        <li>アーカイブプロジェクトの不具合を修正しました。</li>
        <li>「管理者」プロジェクトを削除した際の不具合を修正しました。</li>
        <li>「管理者」新規プロジェクト作成の遷移を修正しました。</li>
        <li>「アーティスト」一覧の導線を改善しました。</li>
        <li>「アーティスト」アカウント情報更新の不具合を修正しました。</li>
        <li>「アーティスト」プロフィール画像アップロードの不具合を修正しました。</li>
        <li>「アーティスト」プロフィール内URLリンク導線を改善しました。</li>
      </ul>


      <h5 class="lightgray">20/9/28(mon) - 20/10/2(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>納品データなしのプロジェクトも設定できるようになりました。（プレビューやりとりのみ）…なので、旧バージョン時代のプロジェクトも、チャプター毎に整理されて見れるように直りました😊</li>

        <p><span class="tag-gray">Update</span></p>
        <ul>
          <li>「完了動画」にはチェックバックできない仕様としました…🙏 検収を差し戻ししてから、コメント可能です！</li>
          <li>管理者アカウント管理画面。プロジェクトオーナーを変更できるようになりました。</li>
          <li>管理者アカウント管理画面。よりシンプルに新規プロジェクト作成できるようになりました。</li>
          <li>「ファイルを送る」アップロードを完了しました。モーダルがつきました。</li>
          <li>「ファイルを送る」更新タブから入るとアップロードできないバグを修正しました。</li>
          <li>見積相談画面。PDFファイルは、クリックで開く仕様にしました。</li>
          <li>管理者アカウント管理画面。アカウントロール毎に一覧表示できるようになりました。</li>
        </ul>

        <h5 class="lightgray">20/9/21(mon) - 20/9/25(fri)</h5>
        <p><span class="tag-blue">NEW</span></p>
        <ul>
          <li>ピン再生中に停止ができるようになりました。</li>
          <img src="/static/images/<EMAIL>">

          <li>ピン付きコメントにオーディオ添付すると、動画と合わせて再生できるようになりました。動画を書き出すことなく、音をあててみてのやりとりができます。</li>
          <img src="/static/images/<EMAIL>">

          <li>ディレクターアカウント。編集でシーン名も更新できるようになりました。</li>
          <li>プログレスバーの総シーン数。直接クリックして更新できるようになりました。（ディレクターおよびプロジェクトオーナーのみ）。進捗可視化できるよう、開発状況に即したこまめな更新がオススメです！</li>
          <li>見積相談画面を更新しました、添付ファイルなしでも御相談いただけるようになりました。またオプショナルでバジェットを入力できるようになりました。</li>
          <img src="/static/images/<EMAIL>">
          <li>ディレクター側もオーナーアカウント一覧を確認できる仕様としました。</li>

        </ul>

        <p><span class="tag-gray">Update</span></p>
        <ul>
          <li>エクスポート。進行中と完了をそれぞれ分けて書き出しできるようにしました。</li>
          <li>コメントを編集して保存すると、改行が消えてしまうことがあるバグを直しました。</li>
          <li>プロジェクトバーのUIを調整しました。「スタッフを見る」をinfoアイコンに。総シーン数以外の数字はクリックでONOFF。</li>
          <li>「ファイルを送る」でオーディオを添付したときも波形表示と再生ボタンを追加しました。</li>
          <li>「ファイルを送る」の既読アイコンがつかないことがあるバグを直しました。</li>
          <li>「メンバー招待」メンバーが一覧に表示されないことがあるバグを直しました。</li>
          <li>「ファイルを送る」表示中は、周りを暗くするようにしました。</li>
          <li>ディレクターアカウントでは、アラームONOFFアイコンを削除としました。</li>
          <li>「支払い情報」追加カード入力の画面ズレを修正しました。</li>
          <li>「支払い情報」カード情報の削除もできるようになりました。</li>
          <li>メール通知のバグを直しました。</li>

        </ul>


        <h5 class="lightgray">20/9/14(mon) - 20/9/18(fri)</h5>
        <p><span class="tag-blue">NEW</span></p>
        <ul>
          <li>グローバルメニューからいつでも支払い情報を変更できるようになりました。</li>
          <img src="/static/images/<EMAIL>">

          <li>クレジットカードの追加登録もできるようになりました。<a href="https://stripe.com/jp" target="_blank">stripe</a>サービスと連携しています。</li>
          <img src="/static/images/<EMAIL>">

          <li>コメントをCSVで出力できるようになりました</li>
          <li>楽曲認識照合レポートを出力できるようになりました。<a href="https://www.acrcloud.com/" target="_blank">ACR Cloud</a>と連携しています。</li>
          <img src="/static/images/<EMAIL>">

          <li>プロジェクト毎に通知がONOFFできるようになりました</li>
          <img src="/static/images/<EMAIL>">

          <li>サインイン、<a href="{% url 'accounts:accounts_signup' %}">ログイン画面</a>のデザインが刷新されました。</li>
          <li>「ファイルを送る」データアップロード中にトーストをつけました。</li>
          <li>「ファイルを送る」添付ファイルのみを表示、トグルがつきました。</li>
        </ul>

      </ul>

      <p><span class="tag-gray">Update</span></p>
      <ul>
        <li>同じチャプター名が別々になってしまうバグを修正しました。</li>
        <li>カルーセルの移動ボタンを真ん中に移動しました。</li>
        <li>デフォルトアイコンを調整しました。</li>
        <li>ロール間（ディレクター→ディレクター）のメール通知をなくしました。</li>
        <li>「ファイルを送る」のダウンロード既読がつかないことがあるバグを修正しました。</li>
        <li>御相談の確認メール、Outlook環境で改行が効かないバグを修正しました。</li>
        <li>見積相談で添付ファイルをアップロードする際、「アップロード中」トースト表示を追加しました。</li>


      </ul>


      <h5 class="lightgray">20/9/7(mon) - 20/9/11(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>

      <ul>
        <li>新規・更新中・進行中・完了を横断して、シーンを検索をできるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>プロジェクト一覧、ドラッグアンドドロップで並び替えできるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>見積相談内容の確認。見積書の御案内。契約書の御案内。それぞれメール通知を追加しました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <div><img src="/static/images/<EMAIL>"></div>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>オーナー側が「ファイルを送る」からファイルを送付したら、ディレクター側に即時にメール通知されるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>ディレクター側もプロジェクトログイン後、「ファイルを送る」→更新→進行中→完了の順で遷移を自動化しました。</li>

      </ul>

      <p><span class="tag-gray">UPDATE</span></p>
      <ul>
        <li>チャプター > シーン >バリエーション名の表示。テキスト部分のみが暗くなるように改善しました。</li>
        <div><img src="/static/images/update_200907_telop.png"></div>

        <li>プロジェクトバナーをクリックした後の遷移を自動化しました。</li>
        <div><img src="/static/images/update_200907_autoselect.png"</div>

        <li>制作中のサムネイルから、バリエーション名表示を消しました。</li>
        <li>制作中タグ内、制作中のものがない場合は、グレーのブロックを非表示にしてスッキリさせました。</li>
        <li>検収済み動画をディレクター側が更新した場合は、コメントをつけない仕様としました。</li>
        <li>管理者権限アカウント、見積もり送付プロセスを改善しました。</li>
        <li>進行中並び替えのUI統一、ボタンテキストなどの調整をしました。</li>

      </ul>


      <h5 class="lightgray">20/8/31(mon) - 20/9/4(fri)</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>「見積相談」モーダルを改善。電話で相談もできるようになりました笑。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>
          「メンバー管理」オーナー側で、プロジェクト内のメンバー管理ができるようになりました。バナー右のアイコンから、参加メンバーを確認できます。プロジェクトオーナーは、メンバー削除、グローバルIP設定、観覧権限設定もできます。
        </li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>「メンバー招待」オーナー側で、プロジェクト内にメンバー招待ができるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>進行中シーン一覧に、「チャプターごとに分ける」トグルが追加されました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>
          シーンを並び替えできるようになりました。更新日、シーン名、優先順を選択できます。優先順はオーナー様の評価（「いいね」やシーンの視聴状況）を元に、コンテンツ品質向上に向けて優先してアップデートすべきシーンを提案します。アルゴリズムは随時更新していきます。
        </li>
        <div><img src="/static/images/<EMAIL>"></div>
      </ul>

      <p><span class="tag-gray">UPDATE</span></p>
      <ul>
        <li>「チャプターごとに分ける」ONの際もシーン並び替えが機能するよう改善しました。</li>
        <li>オーナー側もチャプター編集ができるように改善しました。</li>
        <li>スマートフォンでアクセスした際、メニューが隠れてしまう問題を改善しました。</li>
        <li>サインアップ導線を改善しました。</li>
        <li>「この内容で見積もり依頼」押下したとき、送付確認トーストを追加しました。</li>
        <li>見積相談メニュー、折衝中スレッドがない場合は、直接相談モーダルに遷移するように改善しました。</li>
      </ul>


      <h5 class="lightgray">20/8/24 - 20/8/28</h5>

      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>プロジェクト一覧。進行中のもののみを表示されるよう整理されました。完了したものは、「アーカイブを観る」でご覧いただけます。</li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>グローバルメニューから「見積相談」ができるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
      </ul>

      <li>サインアップ導線。メールとパスワードを一緒に入力し、メール認証へ遷移するように改善しました。</li>
      <div><img src="/static/images/<EMAIL>"></div>
      </ul>

      <p><span class="tag-gray">UPDATE</span></p>
      <ul>
        <li>通常ログインしたら、プロジェクト選択画面に遷移するように改善しました。</li>
        <li>完了動画のチャプター編集もできるようになりました。（近日中に、オーナー側も編集できるようになります。）</li>
        <li>プロジェクトバナーのレギュレーションを更新。プログレスバーは下に移動しました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>総シーン数設定を改善しました。開発スタート時点の想定シーン数を超えても、自動更新されるようになりました。</li>
        <li>同じチャプター名が、別々に表示されてしまう問題を改善しました。</li>
        <li>「ハート」の反応が重い問題を改善しました。同時にスマホ経由でチェックした際、再生ボタンと被らないよう改善しました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li>進行中一覧でホバー再生されるのは、カルーセルの一番左（一番長いバリエーション）の最新としました。</li>
        <li>メール通知のサムネイル、BEFORE AFTERの際も正常に更新されるようになりました。</li>
        <li>オーナーアカウントの「新規」「更新」「進行中」のアルゴリズムを再修正しました。</li>
        <div><img src="/static/images/update_200825_owners-scene-sort.png"></div>
        <li>
          チェックバックのない「進行中」シーンに対して、ディレクター側がバリエーションなどの追加提案をした場合、オーナー側へは通知させない仕様としました。動画更新に伴うタイミング調整などの軽微なアップデート、創り込みを繰り返した場合でも、チェックいただくシーンが突然膨大になりすぎることを防ぎたいためです。ディレクター側は、重要なアップデートのみ該当箇所にコメントを付加することで、チェックいただきたい箇所を絞ってお伝えできます。
        </li>
        <li>オーナーアカウント「新規」の「とりあえずOK」が機能しないことがある不具合を修正しました。</li>
        <li>シーン移動によって使われなくなったチャプターは、自動で削除されるように修正しました。</li>
        <li>ACR CloudのAPI連携機能の不具合を修正しました。</li>
        <li>プロジェクトバナーのレイアウトを改善しました。</li>
        <li>「新規」「更新」「進行中」のアルゴリズムを改善しました。オーナーアカウントの「更新」は、<b>AFTER</b>がある、もしくは、<b>ディレクターからの未解決コメントがある</b>もののみがまとまっています。
        </li>
        <li>検収済みのものをディレクター側で更新した場合の不具合を修正しました。</li>
        <li>メール通知が届かない場合があるバグを修正しました。</li>
      </ul>


      <h5 class="lightgray">20/8/17 - 20/8/21</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>
          プログレスバーの下に<b>進捗シーン数を表示</b>しました。右から、完了シーン。進行中シーン、￼総シーン数です。完了シーンは、ハート済みのもの。￼進行中シーンは、￼やりとり中のもの（新規、更新、進行中すべての合計数）。総シーン数は、まだ動画待ちのもの。プロジェクト仕様を元に、全体進捗率の目安としておおよその目安数が設定されています。
        </li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>オーナーアカウント側でも、進行中のシーン、チャプターをドラッグアンドドロップで整理きるようになりました。￼￼￼</li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li>
          <b>プログレスバー</b>が復活しました。総シーン数に対し、青は検収済み、グレーは未検収（提出済み）です。
        </li>
        <div><img src="/static/images/<EMAIL>"></div>

        <li><b>「スタッフを見る」</b>でプロジェクトに紐づくスタッフクレジットを確認できるようになりました。検収状況に応じて随時更新されます。</li>
      </ul>
      <div><img src="/static/images/update_200818_staffcredits.png"></div>


      <p><span class="tag-gray">UPDATE</span></p>
      <ul>
        <li><b>メール通知のサムネイル</b>、最新に更新されるように修正しました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        <li><b>更新中のソート</b>：全コメント解決済みのシーンが混ざってしまう問題を修正しました。</li>
        <li>サムネイルが更新されないバグを修正しました。￼</li>
        <li>「完了動画」一覧でもシーン移動ができるようになりました。</li>
        <li>カルーセルの1番右を再生するときだけ、動画内メニューが表示されたままになるバグを修正しました。</li>
        <li>動画が再生されると、「チャプター > シーン > バリエーション名」がフェードアウトするように改善しました。</li>
      </ul>

      <hr>
      <h5 class="lightgray">20/8/10 - 20/8/14</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li>コメントフォームが入力テキストに応じて拡大されるようになりました。</li>
        <li>プロジェクト全体に対するファイルやりとりができるようになりました。オーナー様側は、<a href="{% url 'app:ownershelp_sendfile' %}"><b>「ファイルを送る」</b></a>。
          <div><img src="/static/images/<EMAIL>"></div>

          ディレクター側は、「オーダーを見る」。プロジェクト毎のメニューからアクセスできます。既にSOREMOインハウスでの業務遂行経験のあるディレクターの方々には、「from
          Client」フォルダがWEBサービス内に統合された！という表現が分かりやすいかも。
          <div><img src="/static/images/update_200814_fromOwner.png"></div>
        </li>
        <li>「進行中」シーンを<b>ドラッグドロップでチャプター移動</b>できるようになりました。</li>
      </ul>

      <p><span class="tag-gray">UPDATE</span></p>
      <ul>
        <li>コメントの「解決」「送信」アクション。レスポンスを改善しました。</li>
        <li>ディレクター側の編集機能。更新するたびに、「進行中」へ遷移してしまうバグを修正しました。</li>
        <li>チャプター名を編集・削除できるようになりました。
          <div><img src="/static/images/<EMAIL>"></div>
        </li>
      </ul>

      <hr>
      <h5 class="lightgray">20/8/3 - 20/8/7</h5>
      <p><span class="tag-blue">NEW</span></p>
      <ul>
        <li><b>BEFORE AFTER</b>:右上タグのクリックだけで更新履歴を簡単に比較できるようになりました。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        </li>

        <li>コメントを<b>チェックリスト化</b>しました。リスト内のコメントをクリックするだけで直接対象シーンを再生できます。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        </li>

        <li>シーンごとのバリエーションは、<b>カルーセル</b>でまとめてチェックできるようになりました。ハート検収も、ひとつでOKです。</li>
        <div><img src="/static/images/<EMAIL>"></div>
        </li>
      </ul>
    </section>
  </article>

{% endblock content %}
