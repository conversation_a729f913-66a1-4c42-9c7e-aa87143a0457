{% load static %}
{% load util %}

<div class="updateinfo__tree-container" data-post-id="{{ post.pk }}">
  <label class="updateinfo__tree-collapse" for="collapse_{{ index }}">
    <div class="form-check-collapse">
      <input type="checkbox" class="form-check-input collapse-checkbox" id="collapse_{{ index }}"
             name="collapse_{{ index }}">
      <span class="updateinfo__tree-toggle"></span>
      <div class="updateinfo__tree-line"></div>
    </div>
  </label>
  {% with update_items=post|get_items_update  new_items=post|get_items_new %}
    <div class="updateinfo__tree-wrap">
      <div class="updateinfo__tree-header">
        <span class="updateinfo__tree-time">{{ post.title }}</span>
        {% if editable %}
          <span class="updateinfo__tree-action">
           <button class="btn--edit" data-target="#editInfo" data-toggle="modal"><i
                   class="icon icon--sicon-pencil"></i></button>
           <button class="btn--delete" data-target="#deleteInfo" data-toggle="modal"><i
                   class="icon icon--sicon-trash"></i></button>
        </span>
        {% endif %}
      </div>

      <div class="updateinfo__tree-body">
        {% if new_items %}
          <div class="updateinfo__tree-content">
            <p><span class="updateinfo__tree-status tag-blue">NEW</span></p>
            <ul class="updateinfo__tree-list">
              {% for item in new_items %}
                {% include 'root/posts/_item_post.html' with item=item type='new' %}
              {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if update_items %}
          <div class="updateinfo__tree-content">
            <p><span class="updateinfo__tree-status tag-gray">UPDATE</span></p>
            <ul class="updateinfo__tree-list">
              {% for item in update_items %}
                {% include 'root/posts/_item_post.html' with item=item type='update' %}
              {% endfor %}
            </ul>
          </div>
        {% endif %}
      </div>
    </div>

    <div class="updateinfo__tree-wrap-hide">
      <span class="updateinfo__tree-time">{{ post.text_range }}</span>
      {% if new_items %}
        <span class="updateinfo__tree-status tag-blue">NEW</span>
      {% endif %}
      {% if update_items %}
        <span class="updateinfo__tree-status tag-gray">UPDATE</span>
      {% endif %}
    </div>
  {% endwith %}

</div>
