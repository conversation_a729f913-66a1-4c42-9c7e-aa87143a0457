{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static compress %}
{% block title %}<title>Owner Help Center | SOREMO</title>{% endblock title %}

{% block content %}
    {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/index.css' %}"/>
    {% endcompress %}
  <main>
    <div class="banner">
      <h2>Owner Help Center</h2>
    </div>

    <nav class="wrapper">
      <ul class="main-nav">
        <li><a href="{% url 'app:ownershelp' %}#new">新規</a></li>
        <li><a href="{% url 'app:ownershelp' %}#update">更新</a></li>
        <li><a href="{% url 'app:ownershelp' %}#progress">進行中</a></li>
        <li><a href="#">ファイルを送る</a></li>
      </ul>
    </nav>


    <article class="wrapper blog">
      <section>
        <h3>ファイルを送る</h3>
        <p>資料（動画仕様書等々）は、ナビメニュー右にある「ファイルを送る」が便利です。</p>
        <img src="/static/images/<EMAIL>">

        <p>コメントと添付ファイルで、プロジェクトのディレクターにすぐ送れます。</p>
        <img src="/static/images/<EMAIL>">

        <p>アップロードすると即時にディレクター側に通知が届くしくみなので、別途メール報告しなくて大丈夫。</p>
        <img src="/static/images/<EMAIL>">

        <p>ディレクターがデータを受け取ったかどうかは、既読アイコンで確認できます。</p>
        <img src="/static/images/<EMAIL>">

      </section>
    </article>
  </main>

  <aside class="wrapper blog">
    <ul>
      <li>マニュアル作成：善里 信哉</a></li>
      <li>20/8/11</li>
    </ul>
  </aside>

{% endblock content %}

