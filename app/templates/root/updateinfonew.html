{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n compress %}
{% block title %}
  <title>UPDATE INFO | SOREMO</title>
  <meta name="format-detection" content="telephone=no, date=no">
{% endblock %}

{% block content %}
  <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/index.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/update_info_new.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/main_new.css' %}" />
  <style>
    footer {
      background: #ffffff;
      position: relative;
      bottom: -400px;
    }

    @media (max-width: 992px) {
      footer {
        background: #ffffff;
        position: relative;
        bottom: -250px;
      }
    }
  </style>
    {% endcompress %}
  <!-- Page update info -->
  <div class="container updateinfo">
    <div class="updateinfo__header">
      <h2 class="updateinfo__heading">Update Info</h2>
    </div>

    <div class="updateinfo__container{% if editable %} can-edit{% endif %}" data-update-info-id="">
      {% if editable %}
        <button class="btn btn--add" data-target="#addInfo" data-toggle="modal">
          <i class="icon icon--sicon-add-circle-o"></i>{% trans "Add updates" %}
        </button>
      {% endif %}
      <div class="updateinfo__tree">
        {% for post in posts %}
          {% include 'root/posts/_post.html' with post=post index=posts.count|minus:forloop.counter0 editable=editable %}
        {% endfor %}
      </div>
    </div>
  </div>
  <!-- End page updateinfo -->
  {% if editable %}
    <!-- Modal add -->
    <div class=" modal popup updateinfo__popup-container" id="addInfo" tabindex="-1" role="dialog"
         aria-labelledby="addInfoTitle" aria-hidden="true">
      <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
          <div class="popup-header">
            <h5 class="popup-title" id="addInfoTitle">更新内容を追加</h5>
            <button type="button" class="popup-close" data-dismiss="modal" aria-label="Close">
              <span class="icon icon--sicon-close"></span>
            </button>
          </div>
          <div class="popup-body">
            <form method="post" action="" id="create_info" enctype="multipart/form-data">
              <div class="row col-md-12 popup-rangetime">
                <div class="form-group col-md-6">
                  <label class="control-label" for="start_time_id">開発期間</label>
                  <div class="form-group__input-group">
                    <input class="form-control control-datepicker js-daterangepicker"
                           id="deadline-date" type="text" placeholder="Deadline date" autocomplete="off"/>
                    <label class="form-group__append" for="deadline-date">
                      <i class="icon icon--undefined"></i>
                      <i class="icon icon--sicon-calendar"></i>
                    </label>
                  </div>
                </div>
              </div>

              <div class="row col-md-12 popup-container popup-container-add">
                <div class="popup-wrap" id="add_new">
                  <div class="popup-wheader">
                    <span class="updateinfo__tree-status tag-blue">NEW</span>
{#                    <label class="form-check-label">#}
{#                      <div class="form-check-group">#}
{#                        <input type="checkbox" class="form-check-input switch-checkbox" name="switch_add_new"#}
{#                               id="switch_add_new" checked="checked">#}
{#                        <span class="switch-slider"></span>#}
{#                      </div>#}
{#                    </label>#}
                  </div>
                  <div class="popup-wbody">
                    <div class="popup-wbody-list">
                      <div class="popup-wbody-item blank_item"  data-order="1">
                        <div class="popup-wbody-img">
                          <label for="imgInpNew" class="popup-wbody-img-insert">
                            <img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt=""/>
                          </label>
                          <input type="file" class="popup-wbody-file" accept="image/*" name="image" id="imgInpNew">
                        </div>

                        <div class="popup-wbody-content">
                          <textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2"
                                    cols="10"></textarea>
                        </div>

                        <div class="popup-wbody-action">
                          <span class="delete-row icon icon--sicon-trash"></span>
                          <span class="drag-row icon icon--sicon-drag-indicator"></span>
                        </div>
                      </div>
                    </div>

                    <div class="popup-wbody-add">
                      <a class="popup-wbody-add__icon" href="#">
                        <i class="icon icon--sicon-plus"></i>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="popup-wrap" id="add_update">
                  <div class="popup-wheader">
                    <span class="updateinfo__tree-status tag-gray">UPDATE</span>
{#                    <label class="form-check-label">#}
{#                      <div class="form-check-group">#}
{#                        <input type="checkbox" class="form-check-input switch-checkbox" name="switch_add_update"#}
{#                               id="switch_add_update" checked="checked">#}
{#                        <span class="switch-slider"></span>#}
{#                      </div>#}
{#                    </label>#}
                  </div>
                  <div class="popup-wbody">
                    <div class="popup-wbody-list">
                      <div class="popup-wbody-item blank_item"  data-order="1">
                        <div class="popup-wbody-img">
                          <label for="imgInpUpdate" class="popup-wbody-img-insert">
                            <img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt=""/>
                          </label>
                          <input type="file" class="popup-wbody-file" accept="image/*" name="image" id="imgInpUpdate">
                        </div>

                        <div class="popup-wbody-content">
                          <textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2"
                                    cols="10"></textarea>
                        </div>
                        <div class="popup-wbody-action">
                          <span class="delete-row icon icon--sicon-trash"></span>
                          <span class="drag-row icon icon--sicon-drag-indicator"></span>
                        </div>
                      </div>
                    </div>

                    <div class="popup-wbody-add">
                      <a class="popup-wbody-add__icon" href="#">
                        <i class="icon icon--sicon-plus"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="popup-footer">
            <button type="button" class="btn btn-popup-save">OK</button>
          </div>
        </div>
      </div>
    </div>

    <!-- End modal add -->

    <!-- Modal edit -->
    <div class="modal popup updateinfo__popup-container" id="editInfo" tabindex="-1" role="dialog"
         aria-labelledby="editInfoTitle" aria-hidden="true">
      <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
          <div class="popup-header">
            <h5 class="popup-title" id="editInfoTitle"></h5>
            <button type="button" class="popup-close" data-dismiss="modal" aria-label="Close">
              <span class="icon icon--sicon-close"></span>
            </button>
          </div>
          <div class="popup-body">
            <form method="post" action="" id="edit_info" enctype="multipart/form-data">

            </form>
          </div>
          <div class="popup-footer">
            <button type="button" class="btn btn-popup-save">OK</button>
          </div>
        </div>
      </div>
    </div>
    <!-- End modal edit -->

    <!-- Modal delete -->
    <div class="modal popup updateinfo__popup-container" id="deleteInfo" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
          <div class="popup-body">
            <p class="popup-text">{% trans "Once implemented, it will not be possible to restore. Do you really want to delete it?" %}</p>
          </div>
          <div class="popup-footer">
            <button type="button" class="btn btn-popup-close btn--primary" data-dismiss="modal">{% trans "no" %}</button>
            <button type="button" class="btn btn-popup-delete btn--tertiary">{% trans "yes" %}</button>
          </div>
        </div>
      </div>
    </div>
    <!-- End modal delete -->
  {% endif %}
{% endblock %}

{% block extra_script %}
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <script src="{% url 'javascript-catalog' %}"></script>
      {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/update_info_new.js' %}"></script>
    {% endcompress %}
{% endblock %}

