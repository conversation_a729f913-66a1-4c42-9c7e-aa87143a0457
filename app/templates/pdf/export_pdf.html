{% load util %}

<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>御見積書_{{ instance.contract_code }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Export pdf */
        @font-face {
            font-family: 'Axis L';
            src: url('{{ axis_l_path }}') format('opentype');
            font-weight: 100;
            font-style: normal;
        }

        .container {
            max-width: 970px;
            padding: 100px;
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        table {
            width: 100%;
        }

        .table.table-header {
            margin: 30px 0 60px;
        }

        .table-header tr td, .table-quotation tr td {
            vertical-align: top;
            word-break: break-word;
        }

        .table-quotation tr:first-child td {
            padding: 20px 0;
        }

        .table-quotation tr td {
            padding: 0;
        }

        .table-quotation tr:nth-child(2) td {
            padding-bottom: 6px;
        }

        .table-header tr td:nth-child(2), .table-quotation tr td:nth-child(2) {
            text-align: right;
        }

        .table-quotation tr div {
            height: 17px;
        }

        .table-quotation tr:first-child td {
            padding-bottom: 30px;
        }

        .export-wrap__header {
            margin-bottom: 50px;
        }

        .export-wrap__header-logo img {
            width: 80px;
            height: auto;
        }

        .export-wrap__content .heading--18 {
            margin-bottom: 8px;
        }

        .export-wrap__content .bodytext--13 {
            margin-bottom: 16px;
        }

        .export-wrap__table {
            border-collapse: collapse;
            width: 100%;
        }

        .export-wrap__table thead,
        .export-wrap__table tfoot {
            border-top: 0.5mm solid #53565A !important;
            border-bottom: 0.5mm solid #53565A !important;
        }

        .export-wrap__table th,
        .export-wrap__table td {
            color: #000000;
            line-height: 17px;
            padding: 8px;
            word-break: break-word;
        }

        .export-wrap__table thead tr th {
            border: none;
            min-width: 100px;
            text-align: left;
        }

        .export-wrap__table tr th, .export-wrap__table tr td {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .export-wrap__table tbody tr:not(:last-child) td,
        .export-wrap__table tfoot tr:not(:last-child) td {
            border-bottom: 0.5mm solid #D3D3D3;
        }

        .export-wrap__table tbody tr td:last-child {
            word-break: break-word;
            white-space: pre-line;
        }

        .export-wrap__table td:nth-child(2),
        .export-wrap__table td:nth-child(3),
        .export-wrap__table td:nth-child(5) {
            text-align: right;
        }

        .export-wrap__table td:nth-child(3) {
            padding-right: 2px;
        }

        .export-wrap__table td:nth-child(4) {
            text-align: left;
            padding-left: 2px;
        }

        .export-wrap__table td:first-child, .export-wrap__table th:first-child {
            padding-left: 0px;
        }

        .letter-spacing-300 {
            letter-spacing: 4.8px;
        }

        .letter-spacing-230 {
            letter-spacing: 3.68px;
        }

        .bodytext--13 {
            line-height: 20px;
            color: #000000 !important;
            font-weight: 300;
        }

        .heading--18 {
            line-height: 27px;
            color: #000000;
            font-weight: 400;
        }

        .heading--20 {
            line-height: 30px;
            color: #000000;
            font-weight: 400;
            word-break: break-word;
            white-space: pre-line;
        }

        .export-wrap__content-note {
            margin-top: 100px;
            margin-bottom: 40px;
            word-break: break-word;
            white-space: pre-line;
        }

        .export-wrap__footer .bodytext--13 {
            padding-top: 8px;
            text-align: right;
        }

        .text-13q-r {
            font-size: 4.25mm;
            font-family: 'Axis L';
            font-weight: 600;
        }

        .text-16q-r {
            font-size: 5mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-9q-l {
            font-size: 3.25mm;
            font-family: 'Axis L';
        }

        .text-9q-r {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-r {
            font-size: 2.75mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-l {
            font-size: 2.75mm;
            font-family: 'Axis L';
        }

        /* End Export pdf */
    </style>
</head>
<body>
    <div>
      <main>
        <div class="container">
            <div class="export-wrap">
                <div class="export-wrap__header">
                    <table class="table table-header">
                        <tbody>
                            <tr>
                                <td width="80%">
                                    {% if instance.owner_infor %}
                                    <div class="heading--18 text-13q-r letter-spacing-230">
                                        {{ instance.owner_infor }}御中
                                    </div>
                                    {% endif %}
                                </td>
                                <td width="20%">
                                    <div class="export-wrap__header-logo">
                                        <img src="data:;base64,{{ image_base64 }}" alt="SOREMO" width="24mm" height="13.75mm">
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table class="table table-quotation" style="font-family: 'Axis L';">
                        <tbody>
                            <tr>
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <div class="heading--18 letter-spacing-300 text-16q-r">御見積書</div>
                                    </div>
                                </td>
                                <td width="50%" class="text-7q-l">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 ">発行 No： </span>
                                            <span class="bodytext--13 ">{{ instance.contract_code }}</span>
                                        </div>
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 ">発行日： </span>
                                            <span class="bodytext--13 ">{{ release_time }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13 ">下記の通り、御見積申し上げます。</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 "></span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13">受領期日：</span>
                                        <span class="bodytext--13">{{ pre_deadline }}</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 ">{{ producer_info.company_name }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13">引渡場所： </span>
                                        <span class="bodytext--13">{{ instance.get_delivery_place_display }}</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 ">{{ producer_info.address }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13">取引方法： </span>
                                        <span class="bodytext--13">{{ instance.get_pick_up_method_display }}</span>
                                    </div>
                                </td>
                                <td width="50%">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13">{{ producer_info.job_title }} {{ producer_info.fullname }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="text-9q-l">
                                <td width="50%">
                                    <div class="export-wrap__header-left">
                                        <span class="bodytext--13 ">有効期限： </span>
                                        <span class="bodytext--13 ">{{ valid_date }}</span>
                                    </div>
                                </td>
                                <td width="50%" class="text-7q-l">
                                    <div class="export-wrap__header-right">
                                        <div class="export-wrap__header-detail">
                                            <span class="bodytext--13 "></span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="export-wrap__content">
                    <div class="heading--20 text-13q-r">{{ subject }}</div>
                    <table class="table export-wrap__table">
                        <thead>
                            <tr class="text-9q-r">
                                <th width="30%">項目</th>
                                <th width="15%" style="text-align: center;">単価</th>
                                <th colspan="2" width="10%" style="text-align: center;">数量<span class="text-7q-r">（／単位）</span></th>
                                <th width="15%" style="text-align: center;">金額</th>
                                <th width="30%">備考</th>
                            </tr>
                        </thead>
                        <tbody class="text-9q-l">
                            {% for item in work_content %}
                            <tr>
                                <td>{{ item.work_type_dsp }}</td>
                                <td>¥{{ item.price|display_currency }}</td>
                                <td width="5%"><span>{{ item.quantity }}</span></td>
                                <td width="5%"><span class="text-7q-l">{{ item.unit_dsp }}</span></td>
                                <td>¥{{ item.amount_money|display_currency }}</td>
                                <td class="text-7q-l" style="padding-left: 5px;">{{ item.note }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="text-9q-l">
                            <tr>
                                <td>課税対象額</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>¥{{ total_without_tax|display_currency }}</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>消費税（10%）</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>¥{{ tax|display_currency }}</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>合計</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>¥{{ total_money|display_currency }}</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="export-wrap__content text-9q-l">
                {% if note %}
                <div class="export-wrap__content-note">{{ note }}</div>
                {% endif %}
            </div>
        </div>
      </main>
    </div>
</body>
</html>
