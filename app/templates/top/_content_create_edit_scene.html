{% load static %}
{% load util %}
{% load i18n %}

<div class="create-scene__content" data-scene-title-id="{{original_scene.title.pk}}">
    <div class="modal-offer-container">
      <div class="create-scene__form" style="margin-top: 0px;">
        <div class="form-row">
          <div class="col-sm-12 form-group" style="margin-bottom: -1px; padding: 0; width: 100%;">
            <label for="id_scene_name">
              <div class="contract__form-label heading--16">{% trans "scene name" %} <span class="blue-label--8"> {% trans "required" %}</span></div>
            </label>
            {% include 'input_box/component_input.html' with attribute='id="id_scene_name" name="sceneName"' value=original_scene.title.title placeholder='シーン名をここに入力' %}
          </div>
        </div>
        
        <div class="form-row">
          <div class="col-sm-12 form-group" style="margin-bottom: -1px; padding: 0;">
            <label for="id_scene_schedule">
              <div class="contract__form-label heading--16">次の配信日時 </div>
            </label>
            <div class="schedule-date-container">
              <div class="form-row row form-row__mobile">
                <div class="col-xs-6 form-row__mobile-date" style="padding-right: 8px; padding-left: 0;">
                  {% with original_scene.schedule_date|get_date_and_time:'date' as date %}
                    {% include 'input_box/component_input.html' with className='mcalendar mcalendar--small' value=date type='datetime' attribute='id="id_scene_schedule" name="scene_schedule"' placeholder="yyyy/mm/dd" %}
                  {% endwith %}
                </div>
                <div class="col-xs-6" style="padding: 0;">
                  {% with original_scene.schedule_date|get_date_and_time:'time' as date %}
                    {% include 'input_box/component_input.html' with value=date type='time' placeholder='10:00' attribute='name="schedule_hour" id="id_scene_schedule_time"' %}
                  {% endwith %}
                </div>
              </div>
              <span class="text-count-day"><span class="pre-count">残り </span><span class="count-days">0日</span></span>
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="col-sm-12 form-group" style="margin-bottom: -1px; padding: 0; width: 100%;">
            <label for="create-scene-upload-dropzone">
              <div class="contract__form-label heading--16">ファーストテイクを追加 <span class="grey-label--8">{% trans "any" %}</span></div>
            </label>
            <div class="account_upload-file mattach mattach-form mattach-form-upload-scene" style="margin-bottom: 0px;">
              <div class="mcomment-attached" style="display: none;">
                <div class="mattach-preview-container mattach-preview-container-form">
                  <div class="mattach-previews mattach-previews-form collection">
                    <div class="mattach-template mattach-template-form collection-item item-template">
                      <div class="mattach-info" data-dz-thumbnail="">
                        <div class="mcommment-file">
                          <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                           <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                          <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                  class="icon icon--sicon-close"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="fallback dropzone" id="create-scene-upload-dropzone">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="scene-list-take">
    {% if scenes %}
      {% for scene in scenes %}
        {% include 'top/_take_item.html' with scene=scene index=forloop.counter %}
      {% endfor %}
    {% endif %}
  </div>

  <div class="button-scroll-top" >
    <div class="button-scroll-top-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path d="M0 8L1.41 9.41L7 3.83V16H9V3.83L14.58 9.42L16 8L8 0L0 8Z" fill="#A7A8A9"/>
      </svg>
    </div>
  </div>
