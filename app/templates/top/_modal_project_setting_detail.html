{% load util %}
{% load static %}
{% load i18n %}
{% load bootstrap3 %}

<form id="modal-form-project-settings" action="{% url 'app:get_form_project_setting' project.pk %}" method="post" data-project-id={{ project.pk }}>
  <div class="col-sm-12 popup-body__part-content">
    <h3>{% trans "Banner" %}</h3>
    <span>{% trans "Upload your image with a 16: 9 aspect ratio." %}</span>
  </div>
  {% csrf_token %}
  <div class="form-group">
    <label class="label-form" for="id_text_note">
      <span class="account__field-label">{% trans "text" %}<span class="account__jp-astarisk-op">[任意]</span></span>
      {{ project_form.text_note }}
    </label>
  </div>
  <!-- Upload logo -->
  <div class="form-group project__upload-logo hide">
    <label class="label-form" for="">
      <span class="account__field-label">{% trans "logo" %}<span class="account__jp-astarisk-op">[任意]</span></span>

      {% if project.logo %}
        <div class="account__file hide">
          <i class="icon icon--sicon-clip"></i>
          <a href="{{ project.logo.url }}" target="_blank">
            <div class="account__file-name">{{ project.logo_name }}</div>
          </a>
          <i class="icon icon--sicon-close icon--sicon-close-logo"></i>
        </div>
      {% endif %}

      <div class="account_upload-file mattach mattach-form hide">
        <div class="mcomment-attached">
          <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
            <div class="mattach-previews mattach-previews-form collection">
              <div class="mattach-template mattach-template-form collection-item item-template">
                <div class="mattach-info" data-dz-thumbnail="">
                  <div class="mcommment-file">
                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                            class="icon icon--sicon-close"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="dropzoneUpLoadLogo" class="fallback dropzone">
        </div>
      </div>

    </label>
  </div>

  <div class="form-group project__upload_banner">
    <label class="label-form" for="">
      <span class="account__field-label">{% trans "thumbnail" %}<span class="account__jp-astarisk-op">[任意]</span></span>

      {% if project.image %}
        <div class="account__file">
          <i class="icon icon--sicon-clip"></i>
          <a href="{{ project.image.url }}" target="_blank">
            <div class="account__file-name">{% if project.image_name %}{{ project.image_name }}{% else %}{{ project.image.name }}{% endif %}</div>
          </a>
          <i class="icon icon--sicon-close icon--sicon-close-image"></i>
        </div>
      {% endif %}

      <div class="account_upload-file mattach mattach-form">
        <div class="mcomment-attached">
          <div class="mattach-preview-container mattach-preview-container-form-upload-thumbnail">
            <div class="mattach-previews mattach-previews-form collection">
              <div class="mattach-template mattach-template-form collection-item item-template">
                <div class="mattach-info" data-dz-thumbnail="">
                  <div class="mcommment-file">
                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                            class="icon icon--sicon-close"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="dropzoneUpLoadThumbnail" class="fallback dropzone">
        </div>
      </div>

    </label>
  </div>

  <div class="col-sm-12 popup-body__part-content">
    <h3>{% trans "ACR Cloud" %}</h3>
    <span>{% trans "Let's register the necessary information and output the music recognition report." %}</span>
  </div>

  <div class="form-group">
    <label class="label-form" for="id_host">
      <span class="account__field-label">{% trans "host" %}<span class="account__jp-astarisk-op">[任意]</span></span>
      {{ project_form.acr_host }}
    </label>
  </div>

  <div class="form-group">
    <label class="label-form" for="id_access_key">
      <span class="account__field-label">{% trans "access key" %}<span
              class="account__jp-astarisk-op">[任意]</span></span>
      {{ project_form.acr_access_key }}
    </label>
  </div>

  <div class="form-group">
    <label class="label-form" for="id_access_secret">
      <span class="account__field-label">{% trans "access secret" %}<span
              class="account__jp-astarisk-op">[任意]</span></span>
      {{ project_form.acr_access_secret }}
    </label>
  </div>

  {% bootstrap_form project_form exclude='text_note,acr_host,acr_access_key,acr_access_secret,logo,image' %}

  <div class="col-sm-12 popup-body__part-content">
    <h3>{% trans "Block list" %}</h3>
    <span>{% trans "Register artists who will not participate in this project." %}</span>
  </div>

  <div class="project-setting__block-list-artist">
    {% for artist in project.artists_block.all %}
      {% include 'product/_item_artist_block.html' with project=project artist=artist.user_creator.first %}
    {% endfor %}
  </div>

  <div class="project-setting__block-list__butoon-add">
    <i class="icon icon--sicon-add-cirlce"></i>
    <p>{% trans "Add to block list" %}</p>
  </div>

  <div class="popup-footer">
    <button type="button" class="btn btn--tertiary" data-dismiss="modal">キャンセル</button>
    <button type="submit" id="submit-form-update" class="btn btn--primary" data-toggle="modal">OK</button>
  </div>
</form>
