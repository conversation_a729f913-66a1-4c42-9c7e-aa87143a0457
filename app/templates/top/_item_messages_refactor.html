{% load static %}
{% load util %}
{% load i18n %}
<!-- message for scene -->
{% if type == 'product' or type == 'scene_comment' %}
  {% for message in comments %}
    {% if message.user.role == user.role %}
      {% include 'top/_message_send.html' with message=message user=user type=type %}
    {% else %}
      {% include 'top/_message_received.html' with message=message user=user type=type %}
    {% endif %}
  {% endfor %}
<!-- message for offer creator -->
{% elif type_comment != 'messenger_owner' %}
  {% for message in messages %}
    {% if message.type_message != '1' %}
      {% include 'messenger/_item_system_message.html' with message=message user=user type_message='messenger_artist' %}
    {% else %}
      {% if message|get_offer_user_by_message == offer_user.position %}
        {% if forloop.counter0 == 0 %}
          {% include 'top/_message_send.html' with message=message user=user type='messenger_artist' first_message=True %}
        {% else %}
          {% include 'top/_message_send.html' with message=message user=user type='messenger_artist' first_message=False %}
        {% endif %}
      {% else %}
        {% include 'top/_message_received.html' with message=message user=user type='messenger_artist' %}
      {% endif %}
    {% endif %}
  {% endfor %}
<!-- message for offer product -->
{% else %}
  {% for message in messages %}
    {% if message.type_message != '1' %}
      {% include 'messenger/_item_system_message.html'  with message=message user=user type_message='messenger_owner' %}
    {% else %}
      {% with message|check_plan_contract_message as message_file_type %}
        {% include "top/_item_messages_for_plan_contract.html" %}
      {% endwith %}
    {% endif %}
  {% endfor %}
{% endif %}
