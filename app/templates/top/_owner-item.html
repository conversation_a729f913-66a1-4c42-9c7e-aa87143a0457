{% load util %}
{% load static %}
{% load i18n %}
<div class="member-item-container">
  <div class="member-item project-member-invited-{{ owner.signature }} sub-member-item" data-user="{{ owner.pk }}"
       data-pf="{{ product.pk }}" data-jwt="{{ owner.jwt_token }}">
    <div class="member-item-left">
      <div class="member-item__avatar">
        <div class="avatar avatar--image avatar--48 avatar--round">
          <div class="avatar-image"
               style="background-image: url(
                       {% if owner.role == 'master_client' and not owner.avatar %}/static/images/default-avatar-owner.png{% else %}{{ owner|get_avatar:'medium' }}{% endif %})"></div>
        </div>

      </div>
      <div class="member-item__info">
        <div class="member-item__name sheading sheading--13">{{ owner.get_display_name }}</div>
        <div class="member-item__role scaption">{{ owner.position|hide_if_empty }}</div>
        <div class="member-item__company scaption">{{ owner.enterprise|hide_if_empty }}</div>
      </div>
    </div>
  </div>

  <div class="member-item-action">
    {% if editable %}
      <div class="member-item-action__button-container">
          <span class="member-item__btn-move" data-toggle="modal" data-target="">
            <i class="icon icon--sicon-equal"></i>
          </span>
      </div>
    {% endif %}
  </div>
</div>
