{% load i18n %}
<div class="smodal modal fade" id="modal-member-manage" role="dialog">
  <div class="modal-dialog" role="document">
    <!-- <div class="modal-header">
      <a class="smodal-close" href="#" data-dismiss="modal" aria-label="Close">
        <i class="icon icon--sicon-close"></i>
      </a>
    </div> -->
    <div class="modal-content">
      <div class="modal-body mscrollbar" style="padding:32px 16px 32px;">
        <div class="member-manage">
          <div class="member-manage__content">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="smodal modal fade modal-second" id="modal-member-ip" role="dialog">
  <div class="modal-dialog" role="document">

  </div>
</div>
<div class="smodal smodal--large modal fade modal-second" id="modal-invite-member" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-header">
      <div class="heading-spacing-1824">クライアントを招待</div>
      {% comment %} <a class="smodal-close smodal-close--prev" href="#" data-dismiss="modal" aria-label="Close">
        <i class="icon icon--sicon-prev"></i>
      </a> {% endcomment %}
    </div>
    <div class="modal-content">
      <div class="modal-body mscrollbar">
        <div class="invite-member">
          <div class="invite-member__content">
            <div class="u-mb24">クライアントを招待して成果物を確認してもらうことができます。招待はメールアドレスで行います
            </div>
            <form method="post" action="{% url 'app:invite_user' %}" id="invite_member_form">
              {% csrf_token %}
              <div class="invite-member__form">
                <input class="form-control form-control" type="hidden" name="member-product-id"
                       id="product-member-inviting"
                       value="" required>
                <div class="sform-group u-mb16">
                  <label class="sform-group__label u-row u-gap8 u-mb0" for="email">
                    <span class="heading">メールアドレス</span><span class="label8 u-text-blue">[必須]</span>
                  </label>
                  <input class="sform-control sform-control--input sform-control--full modal-sform-control--full u-mb16" id="email-member-inviting"
                         type="email" name="member-email"
                         placeholder="<EMAIL>" onfocusout="checkEmailExistInviteUser()" required/>
                </div>
                <div class="sform-group u-mb16">
                  <label class="sform-group__label u-row u-gap8 u-mb0" for="ip">
                    <span class="heading">IP制限</span><span class="label8 u-text-gray">[任意]</span>
                  </label>
                  <input class="sform-control sform-control--input sform-control--full  modal-sform-control--full" id="ip-member-inviting"
                         type="text" name="member-ip"
                         placeholder="***********"/>
                </div>
                <div class="sform-group u-mb16">
                  <div class="toggle-button-status">
                    <div class="form-check custom-switch">
                      <label class="form-check-label">
                        <div class="form-check-group">
                          <input class="form-check-input switch-checkbox" type="checkbox" name="permit-view-only"><span class="switch-slider"></span>
                        </div>
                        <span class="switch-label">閲覧のみ</span>
                      </label>
                      <div class="explaination-text">
                          <span class="item-explain-text">※オンにすると、検収とコメントの投稿が制限されます</span>
                      </div>
                    </div>
                  </div>
                </div>
                <hr>
                <div class="u-mt16 u-row-end">
                  <a class="btn btn--blue btn--lg send-invite-email" href="javascript:void(0)">
                    <span class="bodytext-13">{% trans "Send invitation email" %}</span>
                  </a>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- End modal drag credit -->

<!-- Modal code name edit -->
<div class="modal popup-container fade" id="modal-code-name-edit" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <form id='code-name-edit' action='' class="form-group">
          <input class="form-control" placeholder="CODE NAME" type='text' name='code_name' value='' maxlength="255"/>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-update-project-setting" id="submit--form">{% trans "keep" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal code name edit -->

<!-- Modal owner company name edit -->
<div class="modal popup-container fade" id="modal-owner-company-name-edit" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <form id='owner-company-name-edit' action='' class="form-group">
          <label class="label-form" for="owner-company-name">
            <input class="form-control owner-company-name-input" placeholder="オーナー会社名" type='text' name='owner-company-name' value='' maxlength="255">
          </label>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-update-project-setting" id="submit--form">{% trans "keep" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal code name edit -->

<!-- Modal schedule edit -->
<div class="modal popup-container fade" id="modal-schedule-edit" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <div class="sform-group__input-group">
          <input class="sform-control sform-control--input sform-control--full js-daterangepicker"
            id="schedule_date" type="text" placeholder="yyyy/mm/dd - yyyy/mm/dd" autocomplete="off">
          <label class="sform-group__append" for="schedule_date">
            <i class="icon icon--undefined"></i>
            <i class="icon icon--sicon-calendar"></i>
          </label>
        </div>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-update-project-setting" id="submit--form">{% trans "keep" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal code name edit -->

<!-- Modal description edit -->
<div class="modal popup-container fade" id="modal-description-edit" role="dialog" style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content">
      <div class="popup-body">
        <form id='description-edit' action='' class="bootbox-owner-name form-group">
          <!-- TO DO: Value textarea here -->
          <textarea style="resize: none;" class="description-input" rows="10" maxlength="1000"
                    placeholder="{% trans 'Please enter the detailed information of the project.' %}" type='text'
                    name='description-project'></textarea>
        </form>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <button type="button" class="btn btn--tertiary btn-popup-close"
                data-dismiss="modal">{% trans "cancel" %}</button>
        <button type="button" class="btn btn--primary btn-popup-send btn-update-project-setting" id="submit--form">{% trans "keep" %}</button>
      </div>
    </div>
  </div>
</div>
<!-- End modal description edit -->

<!-- Modal add director -->
<div class="modal popup-container search-block-list-modal fade" id="modal-add-director" role="dialog"
       style="z-index: 9998">
    <div class="modal-dialog">
      <div class="modal-content popup-content row" style="margin: 0px">
        <div class="modal-header">
          <a class="smodal-close smodal-close--prev" href="#" data-dismiss="modal" aria-label="Close">
            <i class="icon icon--sicon-prev"></i>
          </a>
        </div>
        <div class="popup-body">
          <div class="col-sm-12 popup-body__part-content header-modal-add-director">
            <h3>{% trans "Invite the director" %}</h3>
            <span>{% trans "You can invite artists who have an offer." %}</span>
          </div>

          <div class="col-sm-12 popup-body__part-content header-modal-add-producer">
            <h3>プロデューサーとして招聘</h3>
            <span>{% trans "You can invite artists that not in blocklist" %}</span>
          </div>


          <div class="col-sm-12 popup-body__part-content" style="margin-bottom: 0px;">
            <div class="block-list__results">
              <div class="block-list__results-search mscrollbar">
                    <!-- --------------------- -->

                    <!-- --------------------- -->
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
<!-- End modal add director -->

<!-- Modal add admin to project -->
<div class="modal popup-container search-block-list-modal fade" id="modal-add-admin" role="dialog"
       style="z-index: 9998">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content row" style="margin: 0px">
        <div class="modal-header">
          <a class="smodal-close smodal-close--prev" href="#" data-dismiss="modal" aria-label="Close">
            <i class="icon icon--sicon-prev"></i>
          </a>
        </div>
        <div class="popup-body">
          <div class="col-sm-12 popup-body__part-content header-modal-add-admin">
            <h3>{% trans "Add admin" %}</h3>
{#            <span>{% trans "You can invite artists who have an offer." %}</span>#}
          </div>

          <div class="col-sm-12 popup-body__part-content" style="margin-bottom: 0px;">
            <div class="block-list__results">
              <div class="block-list__results-search mscrollbar">
                    <!-- --------------------- -->

                    <!-- --------------------- -->
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
<!-- End modal add admin to project -->

<!-- Modal drag credit -->
{% if user.role in 'master_admin, admin' %}
  <div class="modal popup-container" id="modal-drag-file-credit" role="dialog">
    <div class="modal-dialog popup-dialog" role="document">
      <div class="modal-content popup-content">
        <div class="popup-body">
          <div class="mattach-info mattach-info-file mattach-info-file-information hide" data-dz-thumbnail="">
            <div class="mcommment-file">
              <div class="mcommment-file__name mcommment-file__name-form" data-dz-name="">
                <i class="icon icon--sicon-clip"></i>
                <p class="file-name"></p>
              </div>
              <div class="mcommment-file__delete" href="#!" data-dz-remove="">
                <i class="icon icon--sicon-close"></i>
              </div>
            </div>
          </div>

          <div class="account_upload-file mattach mattach-form">
            <div class="mcomment-attached">
              <div class="mattach-preview-container mattach-preview-container-form">
                <div class="mattach-previews mattach-previews-form collection">
                  <div class="mattach-template mattach-template-form collection-item item-template">
                    <div class="mattach-info" data-dz-thumbnail="">
                      <div class="mcommment-file">
                        <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                        <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                class="icon icon--sicon-close"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="fallback dropzone" id="uploadFileInformation">
            </div>
          </div>

        </div>
        <div class="popup-footer" style="text-align: right !important;">
          <button type="button" class="btn btn--tertiary btn-popup-close"
                  data-dismiss="modal">{% trans "cancel" %}</button>
          <button type="button"
                  class="btn btn--primary btn-popup-ok btn-upload-file-messenger-owner btn-update-project-setting">
            OK
          </button>
        </div>
      </div>
    </div>
  </div>
  {% include 'top/_project_setting_modal.html' with project_list=project_list%}
{% endif %}


<!-- Modal confirm add producer -->

<div class="modal popup-container fade" id="modal-confirm-add-producer" role="dialog"
     style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content row" style="margin: 0px">
      <div class="popup-body">
        <div class="col-sm-12 popup-body__part-content" style="margin-top: 0px; padding-bottom: 24px !important; border-bottom: 1px solid #F0F0F0;;">
          <span>{% trans "The producer's consideration is calculated from the owner's contract." %}</span><br>
          <span>{% trans "The price of the offer undertaken in this project will be 0 yen and will be integrated. Is that okay?" %}</span>
        </div>
      </div>
      <div class="popup-footer" style="text-align: right; ">
        <hr style="color: #F0F0F0;">

        <button type="button" class="btn  btn--primary"
                data-dismiss="modal">{% trans "no" %}</button>
        <button type="button" class="btn btn--tertiary button-accept-add-producer"
                id="">{% trans "yes" %}</button>
      </div>
    </div>
  </div>
</div>

<!-- End modal confirm add producer -->


<!-- Modal over budget producer -->

<div class="modal popup-container fade" id="modal-add-producer-error" role="dialog"
     style="z-index: 9998">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content row" style="margin: 0px">
      <div class="popup-body">
        <div class="col-sm-12 popup-body__part-content">
          <span>プロデューサーの対価は、オーナーの契約書から算出されますが、利用料をマイナスした後、<span class="col-content-budget current-budget">600,000</span>円となります</span><br>
          <span>しかし、現在他のアーティストにオファーした金額は<span class="col-content-budget master-admin-offered">600,000</span>円となりますが、よろしいですか?</span>
        </div>
      </div>
      <div class="popup-footer" style="text-align: right;">
        <hr style="color: #F0F0F0;">

        <button type="button" class="btn  btn--primary"
                data-dismiss="modal">{% trans "no" %}</button>
{#        <button type="button" class="btn btn--tertiary button-accept-add-producer"#}
{#                id="">{% trans "yes" %}</button>#}
      </div>
    </div>
  </div>
</div>

<!-- End over budget producer -->
