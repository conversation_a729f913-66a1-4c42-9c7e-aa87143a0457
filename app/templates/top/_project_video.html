{% load static %}
{% load util %}
{% if scene_titles.exists %}
    {% for scene_title in scene_titles %}
      {% if scene_title.scene_title.first %}
        <div class="project-video-item" data-scene-title-id="{{ scene_title.pk }}">
            <div class="video-item-wrap">
            {% with scene_title.scene_title.count as scene_count %}
                {% for scene in scene_title.scene_title.all %}
                    <div class="video-item-list {% if forloop.counter0 == 0 %}active{% else %}hide{% endif %}"
                         data-variation-id="{{ scene.pk }}"
                         data-index="{{ forloop.counter0 }}" id="{{ scene.pk }}">
                        {% include 'top/_video_item_component.html' with scene_count=scene_count scene=scene preload="none" role='client' is_update=False can_share_link=can_share_link %}
                    </div>
                {% endfor %}
                {% if scene_count >= 1 %}
                    <div class="video-item-control">
                        <div class="video-item-bullets-wrap">
                            <div class="video-item-bullets">
                            {% if scene_count > 1 %}
                                <div class="video-item-bullet-prev disable"
                                     data-current_index="0"></div>
                                <div class="video-item-bullet-list">
                                    {% for scene in scene_title.scene_title.all %}
                                        <div class="video-item-bullet {% if forloop.counter0 == 0 %}active{% endif %}"
                                             data-index="{{ forloop.counter0 }}"></div>
                                    {% endfor %}
                                </div>
                                <div class="video-item-bullet-next"
                                     data-current_index="0"></div>
                            {% endif %}
                            </div>
                        </div>
                        <div class="video-item-thumbnails">
                            <div class="video-item-thumbnail-list">
                                {% for scene in scene_title.scene_title.all %}
                                    {% if scene.other_versions.count > 0 %}
                                        <div class="video-item-thumbnail {% if forloop.counter0 == 0 %}active{% endif %}"
                                             data-index="{{ forloop.counter0 }}"
                                             data-id="{{ scene.other_versions.first.pk }}">
                                            <video class="active" width="100%" height="auto"
                                                   poster="{{ scene.other_versions.first|get_thumbnail }}" preload="none">
                                            </video>
                                            <div class="variation-name">{{ scene.other_versions.first.get_file_name }}</div>
                                        </div>
                                    {% else %}
                                        <div class="video-item-thumbnail {% if forloop.counter0 == 0 %}active{% endif %}"
                                             data-index="{{ forloop.counter0 }}" data-id="{{ scene.pk }}">
                                            <video class="active" width="100%" height="auto"
                                                   poster="{{ scene|get_thumbnail }}" preload="none">
                                            </video>
                                            <div class="variation-name">{{ scene.get_file_name }}</div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% if scene_count > 1 %}
                        <div class="video-item-collapse">
                            <div class="video-item-collapse-button"></div>
                        </div>
                    {% endif %}
                    </div>
                {% else %}
                    <br>
                {% endif %}
              {% endwith %}
            </div>
            <div class="video-item-comment">
                {% include 'top/_comment_placeholder.html' %}
            </div>
        </div>
      {% endif %}
    {% endfor %}
{% elif not load_more %}
    <div class="empty-message">新規の演出がございません。</div>
{% endif %}
