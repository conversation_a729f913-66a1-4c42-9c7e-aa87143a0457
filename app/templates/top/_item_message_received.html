{% load static %}
{% load util %}
<div class="mmessage-main">
  {% if not message.is_near %}
    <div class="c-avatar32"  title="{{ message.user.get_display_name }}"
      style="background-image: url({{ message.user|get_avatar:'medium' }})">
    </div>
  {% endif %}
  <div class="mmessage-content">
    {% if message.parent %}
      {% if message.comment and message.comment != '' %}
        <div class="s-filedisable-wrap">
          <a class="s-filedisable s-filedisable--filedisable s-filedisable--gray"
             href="javascript:void(0)">{{ message.parent|get_content_comment_parent }}</a>
          {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}
            <div class="s-audio s-audio--audio s-audio--gray" data-scene-id="{{ message.scene_id }}">
              <div class="s-audio-icon">
                <div class="s-audio-control video-pin-time">
                  <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                    play_circle
                  </span>
                  <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                </div>
                <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
              </div>
              <div class="s-filetext">{{ message.comment }}</div>
            </div>
          {% else %}
            <div class="s-filetext">{{ message.comment }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% for file in message|get_sorted_message_files %}
        {% if not file.folder %}
          {% with file.is_audio_file as type_file %}
            {% if forloop.counter0 == 0 and not message.comment and message.comment == '' %}
              <div class="s-filedisable-wrap">
                <a class="s-filedisable s-filedisable--filedisable s-filedisable--gray"
                   href="javascript:void(0)">{{ message.parent|get_content_comment_parent }}</a>
                {% include 'top/_item_received.html' with type_file=type_file user=user message=message file=file type=type file_name=file.real_name type_comment=type %}
              </div>
            {% else %}
              {% include 'top/_item_received.html' with type_file=type_file user=user message=message file=file type=type file_name=file.real_name type_comment=type %}
            {% endif %}
          {% endwith %}
        {% endif %}
      {% endfor %}

      {% for folder in message.folders.all %}
        {% if not folder.parent %}
            {% include 'top/_item_received.html' with type_file='folder' user=user message=message file=folder type=type file_name=folder.name type_comment=type %}
        {% endif %}
      {% endfor %}
    {% else %}
      {% if message.comment and message.comment != '' %}
        <div class="mmessenger mmessenger--text mmessenger--gray">
          <div class="messenger-content">
            {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}
              <div class="s-audio s-audio--audio s-audio--gray" data-scene-id="{{ message.scene_id }}">
                <div class="s-audio-icon">
                  <div class="s-audio-control video-pin-time">
                    <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                      play_circle
                    </span>
                    <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                  </div>
                  <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
                </div>
                <div class="s-audio-text">{{ message.comment }}</div>
              </div>
            {% else %}
              <div class="s-text s-text--gray {% if message|get_message_files_count == 0 %}single-text{% endif %} ">{{ message.comment }}</div>
            {% endif %}
          </div>
        </div>
      {% endif %}

      {% for file in message|get_sorted_message_files %}
        {% if not file.folder %}
          {% with file.is_audio_file as type_file %}
            {% include 'top/_item_received.html' with type_file=type_file user=user message=message file=file type=type file_name=file.real_name type_comment=type %}
          {% endwith %}
        {% endif %}
      {% endfor %}

      {% for folder in message.folders.all %}
        {% if not folder.parent %}
          {% include 'top/_item_received.html' with file=folder message=message user=user type_file='folder' type=type file_name=folder.name type_comment=type %}
        {% endif %}
      {% endfor %}
    {% endif %}
  </div>
</div>
<div class="mmessage-info {% if message|check_has_audio_file %}message-info-audio{% endif %}">
  {% include 'top/_message_infor.html' with message=message user=user type_infor='received' type=type %}
</div>
