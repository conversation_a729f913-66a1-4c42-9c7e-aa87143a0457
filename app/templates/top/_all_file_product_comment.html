{% load static %}
{% load util %}
<div class="pd-file-content mscrollbar">
  <div class="tfile">
    <div class="tfile-list">
      <div class="mlast__file"></div>
      {% for key in comment_files %}
        {% with key|get_object_product_comment as object %}
          {% if object %}
            {% if object.file_id %}
              <div class="tfile-item">
                <div class="tfile-item-time">{{ object.modified|get_weekday_new }}</div>
                <div class="tfile-item-content">
                  {% include 'top/_file_infor.html' with file=object message=object.message type_comment=type_comment name=object.real_name %}
                </div>
              </div>
            {% elif object.folder_id %}
              <div class="tfile-item-time">{{ object.modified|get_weekday_new }}</div>
              <div class="tfile-item-content">
                <div class="tfile-file">
                  {% include 'top/_sfolder.html' with folder=object message=object.message type_comment=type_comment %}
                </div>
              </div>
            {% endif %}
          {% endif %}
        {% endwith %}
      {% endfor %}
    </div>
  </div>
</div>
