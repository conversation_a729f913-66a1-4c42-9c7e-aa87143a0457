{% load static %}
{% load util %}

{% if count_processing > 0 %}
  <div class="pd-section pd-section--delivery-video d-none-chapter chapter-block refactor">
    <div class="pd-section__title sheading sheading--18">鋭意制作中
      <span class="pd-section__title-sub">
          次のテイクにご期待ください
        </span>
    </div>

    <div class="pd-section__content">
      <div class="project-chapter-item sortable-element hide">
        <div class="project-chapter-title ui-sortable-handle"></div>
        <div class="project-chapter-videos ui-sortable" id="id-sort-videos">
        </div>
      </div>
      <div class="pd-section__video mscrollbar">
        {% for st in scene_titles %}
          {% if st.get_last_version %}
            {% with st.get_last_version as scene %}
              {% include 'top/_cscene_delivery.html' with scene=scene st=st role=role is_done=is_done view_only=view_only height="80px" show_button="hide" style="style='margin-left: 12px;'" %}
            {% endwith %}
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
{% endif %}

<div class="pd-section pd-section--all-video">
  {% include 'top/_menu_sort.html' %}

  <div class="pd-section__main">
    <div class="pd-section__content">
      <div class="pd-chapter-list">
        {% include 'top/_project_product_scenes_refactor.html' with product_scenes=product_scenes role=role is_done=is_done view_only=view_only %}
      </div>
    </div>
  </div>
</div>
