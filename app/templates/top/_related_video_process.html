{% load static %}
{% load util %}
<style>
    .video-item-component video {
        margin: 50px auto 0;
    }

</style>

<div class="pd-section pd-section--detail pd-scene-title-detail show-comment-unresolved hide"
     data-scene-title-id="" data-total="{{ total_page }}">
    <div class="pd-section__content">
        <div class="header-page">
            <div class="block-back-scene-detail">
                <span class="material-symbols-rounded u-fontsize-24 img-back-previous">
                    chevron_left
                </span>
            </div>
            {% if not share_scene %}
                {% if scene.title.status in '5, 6' %}
                    <a class="scene-detail-heart project-chapter-video-undone {% if view_only %}cannot-check{% endif %} {% if role == 'master_client' %} is-owner{% endif %}"
                        href="javascript:void(0)">
                        <span class="material-symbols-rounded u-text-blue">
                            check_circle
                        </span>
                        <p class="txt-bellow-icon txt-bellow-heart text-icon-new">OK</p>
                    </a>
                {% else %}
                    <a class="scene-detail-heart project-chapter-video-done u-line-height-100 {% if role == 'admin' or view_only %} cannot-check{% else %} is-owner {% endif %}"
                        href="javascript:void(0)">
                        <span class="material-symbols-rounded u-text-gray">
                            check_circle
                        </span>
                        <p class="txt-bellow-icon text-icon-new">OK</p>
                    </a>
                {% endif %}
                <div class="block-rating">
                    <div class="cvideo__rating block-starts">
                        {% with scene.title|get_rating:role as rating %}
                            <div class="stars {% if role == 'master_client' %} can-rate {% endif %} {% if role == 'admin' %}average-star cannot-check admin-rate {% if rating == 0.00 %}not-yet-rate{% endif %}{% endif %}" data-rating="{{ rating }}">
                                <span class="material-symbols-rounded star-1" data-value="1">star</span>
                                <span class="material-symbols-rounded star-2" data-value="2">star</span>
                                <span class="material-symbols-rounded star-3" data-value="3">star</span>
                                <span class="material-symbols-rounded star-4" data-value="4">star</span>
                                <span class="material-symbols-rounded star-5" data-value="5">star</span>
                            </div>
                        {% endwith %}
                    </div>
                    {% if role == 'admin' %}
                    <p class="txt-bellow-icon txt-rating">
                        	{{ avg_rating }}({{ total_turn_rate }})
                        </p>
                        {% endif %}
                </div>
            {% endif %}
            <div class="block-txt-name">
                <p class="txt-des-above"
                   data-product-scene-id="{{ scene.title.product_scene.pk }}">{{ scene.title.product_scene.name }}</p>
                <p class="txt-des">{{ scene.title.title }}</p>
            </div>
            <div class="block-right">
                {% if user.role == 'admin' and not is_deleted %}
                    <a href="#video-modal" class="icon-edit-new edit-scene-item open-video-modal">
                        <span class="material-symbols-rounded u-text-light-gray">
                            edit
                        </span>
                        <p class="txt-bellow-icon padding-txt-icon">シーンを編集</p>
                    </a>
                {% endif %}
                {% if not share_scene %}
                    {% if not is_deleted %}
                        {% if can_share_link %}
                            <a href="javascript:void(0)"
                               class="cscene__share share-scene share-scene-pc video-item-slug btn-share-scene {% if scene_share %} cscene__can-share can-share-new can-share-new-pc {% endif %}"
                               data-toggle="modal"
                               data-target="#shareModal"
                               share-url="{{ scene.get_slug }}">
                                {#                            <i class="icon icon-share icon--sicon-share"></i>#}
                                {% if scene_share %}
                                    <span class="material-symbols-rounded u-text-blue">
                                        ios_share
                                    </span>

                                {% else %}
                                    <span class="material-symbols-rounded">
                                        ios_share
                                    </span>
                                {% endif %}
                                <p class="txt-bellow-icon padding-txt-icon"></p>
                            </a>
                        {% endif %}
                        <a href="#" class="cscene__bookmark bookmark-button save-bookmark-pc">
                            <span class="material-symbols-rounded {% if is_bookmark %} bookmark u-text-blue {% else %} unbookmark {% endif %}">
                                bookmark
                            </span>
                            <p class="txt-bellow-icon padding-txt-icon padding-txt-icon-bookmark {% if is_bookmark %} u-text-blue {% endif %}">{% if is_bookmark %} 保存済み {% else %} コレクション {% endif %}</p>
                        </a>
                    {% endif %}
                {% endif %}

            </div>
            <div class="more-action-responsive">
                <div class="block-right-responsive ms-auto dropdown me-1">
                    <a href="javascript:void(0);" class="dropdown-toggle" id="dropdownMenuButton" data-toggle="dropdown"
                       aria-haspopup="true" aria-expanded="false">
                        <img src="{% static 'images/icon-more-horiz.svg' %}" alt="more horiz">
                    </a>
                    <div class="dropdown-menu dropdown-menu-responsive" aria-labelledby="dropdownMenuButton">
                        {% if user.role == 'admin' and not is_deleted %}
                            <a class="edit-scene-item open-video-modal" href="#video-modal">
                                <span>このシーンを編集</span>
                                <img src="{% static 'images/scene-detail/icon-edit.svg' %}" alt="">
                            </a>
                        {% endif %}
                        {% if not share_scene %}
                            {% if not is_deleted %}
                                {% if can_share_link %}
                                    <a class="dropdown-item cscene__share video-item-slug share-scene btn-share-scene {% if scene_share %} cscene__can-share can-share-new {% endif %}"
                                       href="javascript:void(0);"
                                       data-toggle="modal"
                                       data-target="#shareModal"
                                       share-url="{{ scene.get_slug }}">
                                        {% if scene_share %}
                                            <span class="txt-share-sp">共有中</span>
                                            <img src="{% static 'images/scene-detail/icon-share-active.svg' %}"
                                                 alt="share scene">
                                        {% else %}
                                            <span class="txt-share-sp">このシーンを共有</span>
                                            <img src="{% static 'images/scene-detail/icon-share.svg' %}"
                                                 alt="share scene">
                                        {% endif %}
                                        {#                                    <i class="icon icon-share icon--sicon-share"></i> #}

                                    </a>
                                {% endif %}
                                <a class="dropdown-item cscene__bookmark bookmark-button" href="javascript:void(0);">
                                    {% if  is_bookmark %}
                                        <span class="txt-bookmark">保存済み</span>
                                        <i class="icon icon--sicon-bookmark"></i>
                                    {% else %}
                                        <span class="txt-bookmark-o">コレクションに保存</span>
                                        <i class="icon icon--sicon-bookmark-o"></i>
                                    {% endif %}
                                </a>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    <!-- <div class="line-header"></div> -->
        <div class="block-content-scene refactor">
            <div class="pd-scene block-scene-video">
                {#                      {% include 'top/_carousel_all_scene.html' %}#}
            </div>
            <div class="pd-comment" style="min-width: auto">
                <div class="name-responsive">
                <p class="txt-des-above"
                   data-product-scene-id="{{ scene.title.product_scene.pk }}">{{ scene.title.product_scene.name }}</p>
                <div class="d-flex">
                    <span class="txt-des m-0">{{ scene.title.title }}</span>
                    <div class="dropdown block-dropdown-more">
                        <a href="javascript:void(0);" class="dropdown-toggle dropdown" id="dropdownMenuButton"
                           data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            <img src="{% static 'images/icon-more-horiz.svg' %}" alt="more horiz">
                        </a>
                        <div class="dropdown-menu dropdown-more dropdown-more-sp" aria-labelledby="dropdownMenuButton">
                            {% if user.role == 'admin' and not is_deleted %}
                                <a class="dropdown-item edit-scene-item open-video-modal" href="#video-modal">
                                    <span>このシーンを編集</span>
                                    <img src="{% static 'images/scene-detail/icon-edit.svg' %}" alt="">
                                </a>
                            {% endif %}
                            {% if not share_scene %}
                                {% if not is_deleted %}
                                    {% if can_share_link %}
                                        <a class="dropdown-item cscene__share video-item-slug share-scene btn-share-scene video-item-slug {% if scene_share %} cscene__can-share can-share-new {% endif %}"
                                           href="javascript:void(0);"
                                           data-toggle="modal"
                                           data-target="#shareModal"
                                           share-url="{{ scene.get_slug }}">
                                            {% if scene_share %}
                                                <span class="txt-share-sp">共有中</span>
                                                <img src="{% static 'images/scene-detail/icon-share-active.svg' %}"
                                                     alt="share scene">

                                            {% else %}
                                                <span class="txt-share-sp">このシーンを共有</span>
                                                <img src="{% static 'images/scene-detail/icon-share.svg' %}"
                                                     alt="share scene">
                                            {% endif %}
                                        </a>
                                    {% endif %}
                                    <a class="dropdown-item bookmark-item cscene__bookmark bookmark-button"
                                       href="javascript:void(0);">
                                        {% if  is_bookmark %}
                                            <span class="txt-bookmark">保存済み</span>
                                            <i class="icon icon--sicon-bookmark"></i>
                                        {% else %}
                                            <span class="txt-bookmark-o">コレクションに保存</span>
                                            <i class="icon icon--sicon-bookmark-o"></i>
                                        {% endif %}
                                    </a>
                                {% endif %}

                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="line-block-name"></div>
            </div>
                {% include 'top/_comment_scene_detail.html' %}
            </div>
        </div>
    </div>

</div>