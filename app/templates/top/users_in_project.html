{% load util %}
{% load static %}
{% load i18n %}

<div class="block-users-in-project member-manage-sortable manage-owner-member-invited">

  <div class="c-group u-bg-blue u-col u-gap8 u-w100">
    <div class="heading u-text-white">クライアント</div>
    <div class="u-col u-gap8 u-w100 member-manage-sortable member-manage__group {% if can_drag %}can-drag{% endif %}">
      {% comment %} マスタークライアントリスト {% endcomment %}
      {% for owner in owners %}
      {% with product=product member=owner editable=editable %}
          <div class="p-lists-user u-row-between p-lists-user u-row-between member-manage {% if editable_all %}can-edit-all{% endif %} member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %} member-manage__list member-manage__list-director member-item-container {% if owner == user %}can-self-edit-delete{% endif %}" data-role="{{ owner.role }}">
            <div class="u-row u-gap16">
              <div class="u-relative">
                <div class="c-avatar48" style="background-image: url({{ owner|get_avatar:'medium' }})"></div>
                {% if owner in online_users %}<div class="c-avatar-status"></div>{% endif %}
              </div>

              <div class="u-col u-gap4">
                <div class="heading-13-spacing  {% if editable %}user-can--editable{% endif %}">{{ owner.get_display_name }}</div>
                <div class="bodytext-11 u-line-height-100">{{ owner.position|hide_if_empty }}<span class="u-text-light-gray">（責任者）</span></div>
                <div class="bodytext-11 u-line-height-100 u-text-light-gray">{{ owner.enterprise|hide_if_empty }}</div>
              </div>
            </div>

            {% with owner|get_product_user:product as pu %}
            {% if login_producer or login_masteradmin or login_masterclient %}
              <div class="u-relative member-item" data-user="{{ member.pk }}" data-pf="{{ product.pk }}" data-jwt="{{ member.jwt_token }}">
                <span class="material-symbols-rounded c-icon-more-horiz">more_horiz</span>
                <ul class="c-dropdown">
                  {% if user.id == owner.pk %}
            {% else %}
                  <input id="view-only-toggle" class="form-check-input switch-checkbox" {% if pu.view_only %} checked {% endif %} type="checkbox" name="view-only" onchange="changeViewOnlyStatus(this)" style="display:none;">                      
                  <li id="view-only-off" class="u-row-between u-w100">閲覧のみに設定<span class="material-symbols-rounded u-text-light-gray">edit</span></li>
                  <li id="view-only-on" class="u-row-between u-w100 u-text-blue" style="display: none;">閲覧のみ（コメント不可）<span class="material-symbols-rounded u-text-blue">edit_off</span></li>
                  <div class="detail-list-ip" style="display:none;">{{ pu|get_list_ip }}</div>
                  <li class="u-row-between u-w100 member-item__ip-edit ip-setting" data-toggle="modal" data-target="#modal-member-ip">IP制限を設定<span class="material-symbols-rounded u-text-light-gray">lock_open</span></li>
                  <li class="u-row-between u-w100 member-item__ip-edit u-text-blue ip-setting-progress" data-toggle="modal" data-target="#modal-member-ip" style="display: none;">IP制限を設定中<span class="material-symbols-rounded u-text-blue">lock</span></li>
                  <li class="u-row-between u-w100 member-item__btn-change" id="roleChangeContainer">
                    <div onclick="ajaxChangeUserPosition">責任者の権限を解除</div>
                    <span class="material-symbols-rounded">rule_settings</span>
                  </li>
                  {% endif %}
                  {% if forloop.first %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="down">
                    <div>下に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_down</span>
                  </li>
                {% elif forloop.last %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% else %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% endif %}
                {% if user.id == owner.pk %}
                {% else %}
                <li class="u-row-between u-w100 member-item__btn-delete" data-toggle="modal" data-target="">プロジェクトから退出<span class="material-symbols-rounded u-text-light-gray">exit_to_app</span></li>{% endif %}
                </ul>
              </div>
              {% endif %}
            {% endwith %}
          </div>
          {% endwith %}
      {% endfor %}


      
      {% comment %} クライアントリスト {% endcomment %}
      {% for member in members_invited %}
      {% with member=member product=product editable=editable %}
          <div class="p-lists-user u-row-between p-lists-user u-row-between member-manage member-manage__group {% if editable_all %}can-edit-all{% endif %} member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %} member-manage__list member-manage__list-director member-item-container {% if owner == user %}can-self-edit-delete{% endif %}" data-role="{{ owner.role }}">
            <div class="u-row u-gap16">
              <div class="u-relative u-ml16">
                <div class="c-avatar32" style="background-image: url({{ member|get_avatar:'medium' }})"></div>
                {% if owner in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
              <div class="u-col u-gap4">
                <div class="heading-13-spacing  {% if editable %}user-can--editable{% endif %}">{{ member.get_display_name }}</div>
                <div class="bodytext-11 u-line-height-100">{{ member.position|hide_if_empty }}</div>
              </div>
            </div>

            {% with member|get_product_user:product as pu %}
            {% if login_producer or login_masteradmin or login_masterclient %}
              <div class="u-relative member-item" data-user="{{ member.pk }}" data-pf="{{ product.pk }}" data-jwt="{{ member.jwt_token }}">
                <span class="material-symbols-rounded c-icon-more-horiz">more_horiz</span>
                <ul class="c-dropdown">
                  <input id="view-only-toggle" class="form-check-input switch-checkbox" {% if pu.view_only %} checked {% endif %} type="checkbox" name="view-only" onchange="changeViewOnlyStatus(this)" style="display:none;">                      
                  <li id="view-only-off" class="u-row-between u-w100">閲覧のみに設定<span class="material-symbols-rounded u-text-light-gray">edit</span></li>
                  <li id="view-only-on" class="u-row-between u-w100 u-text-blue" style="display: none;">閲覧のみ（コメント不可）<span class="material-symbols-rounded u-text-blue">edit_off</span></li>
                  <div class="detail-list-ip" style="display:none;">{{ pu|get_list_ip }}</div>
                  <li class="u-row-between u-w100 member-item__ip-edit ip-setting" data-toggle="modal" data-target="#modal-member-ip">IP制限を設定<span class="material-symbols-rounded u-text-light-gray">lock_open</span></li>
                  <li class="u-row-between u-w100 member-item__ip-edit u-text-blue ip-setting-progress" data-toggle="modal" data-target="#modal-member-ip" style="display: none;">IP制限を設定中<span class="material-symbols-rounded u-text-blue">lock</span></li>
                  <li class="u-row-between u-w100 member-item__btn-change" id="roleChangeContainer">
                    <div onclick="ajaxChangeUserPosition">責任者の権限を付与</div>
                    <span class="material-symbols-rounded">rule_settings</span>
                  </li>
                  {% if forloop.first %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="down">
                    <div>下に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_down</span>
                  </li>
                {% elif forloop.last %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% else %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% endif %}                            
                  <li class="u-row-between u-w100 member-item__btn-delete" data-toggle="modal" data-target="">プロジェクトから退出<span class="material-symbols-rounded u-text-light-gray">exit_to_app</span></li>
                </ul>
              </div>
              {% endif %}
            {% endwith %}
          </div>
          {% endwith %}
      {% endfor %}
    </div>

    {% comment %} 招待中のクライアントリスト {% endcomment %}
    {% for member in members_inviting %}
      <div class="member-item-layout member-item project-member-inviting-{{ member.signature }}" data-user="{{ member.pk }}"
           data-pf="{{ product.pk }}" data-jwt="{{ member.jwt_token }}">
        <div class="u-column u-w100">
          <div class="u-row u-gap8">
            <div class="member-item__avatar">
              <div class="c-avatar32" style="background-image: url({{ member|get_avatar:'medium' }})"></div>
            </div>
            <div class="bodytext-13 u-line-height-100 u-text-light-gray u-mb0">{{ member.email }}</div>
          </div>
            <div class="member-item__action u-row-end">
              <div class="btn-cancel_invitation">
                <button type="button" onclick="deleteUserInviting('{{ member.jwt_token }}', {{ member.pk }})" class="btn btn-link">招待を取り消す
                </button>
              </div>
              <div class="btn-resend-invitation">
                <button type="button" onclick="sendInviteEmailAgain('{{ member.jwt_token }}', '{{ member.pk }}')"
                        class="btn btn-primary">招待を再送信
                </button>
              </div>
            </div>
        </div>
      </div>
    {% endfor %}

    {% if login_masteradmin or login_producer or login_masterclient or login_client %}
      <div class="c-btn-text-white u-row u-justify-end u-w100 u-gap8" data-toggle="modal" data-target="#modal-invite-member" data-dismiss="modal" aria-label="Close">
        クライアントを招待<span class="material-symbols-rounded">navigate_next</span></div>
    {% endif %}
  </div>


  <div class="c-group u-bg-light-gray u-col u-gap8 u-w100">
    <div class="heading u-text-white">プロダクションパートナー</div>
    {% comment %} プロデューサーリスト {% endcomment %}


    {% for producer in masters %}
    {% with owner=producer product=product editable=editable user=user %}
          <div class="p-lists-user u-row-between p-lists-user u-row-between member-manage {% if editable_all %}can-edit-all{% endif %} member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %} member-manage__list member-manage__list-director member-item-container {% if owner == user %}can-self-edit-delete{% endif %}" data-role="{{ owner.role }}">
            <div class="u-row u-gap16">
              {% if producer.role == 'admin' %}
                <div class="u-relative">
                  <a href="{% if producer.user_creator.first.slug %}{% url 'app:creator_info' producer.user_creator.first.slug %}{% else %}{% url 'accounts:accounts_creator' producer.user_creator.first.user.pk %}{% endif %}"
                     target="_blank"><div class="c-avatar48" style="background-image: url({{ producer|get_avatar:'medium' }})"></div></a>
                  {% if producer in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
              {% else %}
                <div class="u-relative">
                  <div class="c-avatar48" style="background-image: url({{ producer|get_avatar:'medium' }})"></div>
                  {% if producer in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
              {% endif %}
  
              <div class="u-col u-gap4">
                <div class="heading-13-spacing">{{ producer.get_display_name }}</div>
                <div class="bodytext-11 u-line-height-100">{{ producer.position|hide_if_empty }}<span class="u-text-light-gray">（管理者）</span></div>
                <div class="bodytext-11 u-line-height-100 u-text-light-gray">{{ producer.enterprise|hide_if_empty }}</div>
              </div>
            </div>

            {% with user|get_product_user:product as pu %}
            {% if login_producer or login_masteradmin %}
            <div class="u-relative member-item project-member-invited-{{ owner.signature }} sub-member-item" data-user="{{ owner.pk }}" data-pf="{{ product.pk }}" data-jwt="{{ owner.jwt_token }}">
              <div class="material-symbols-rounded c-icon-more-horiz">more_horiz</div>
              <ul class="c-dropdown">
                  {% if forloop.first %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="down">
                    <div>下に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_down</span>
                  </li>
                {% elif forloop.last %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% else %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% endif %}
                {% if user.id == owner.pk %}
                {% else %}
              <li class="u-row-between u-w100 member-item__btn-delete" data-toggle="modal" data-target="">プロジェクトから退出<span class="material-symbols-rounded u-text-light-gray">exit_to_app</span></li>{% endif %}
              </ul>
            </div>
            {% endif %}
            {% endwith %}
          </div>
        {% endwith %}
    {% endfor %}



    {% for producer in producers %}
    {% with owner=producer product=product editable=editable user=user %}
        <div class="p-lists-user u-row-between p-lists-user u-row-between member-manage {% if editable_all %}can-edit-all{% endif %} member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %} member-manage__list member-manage__list-director member-item-container {% if owner == user %}can-self-edit-delete{% endif %}" data-role="{{ owner.role }}">
          <div class="u-row u-gap16">
            {% if producer.role == 'admin' %}
              <div class="u-relative">
                <a href="{% if producer.user_creator.first.slug %}{% url 'app:creator_info' producer.user_creator.first.slug %}{% else %}{% url 'accounts:accounts_creator' producer.user_creator.first.user.pk %}{% endif %}"
                   target="_blank"><div class="c-avatar48" style="background-image: url({{ producer|get_avatar:'medium' }})"></div></a>
                {% if producer in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
            {% else %}
              <div class="u-relative">
                <div class="c-avatar48" style="background-image: url({{ producer|get_avatar:'medium' }})"></div>
                {% if producer in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
            {% endif %}

            <div class="u-col u-gap4">
              <div class="heading-13-spacing">{{ producer.get_display_name }}</div>
              <div class="bodytext-11 u-line-height-100">{{ producer.position|hide_if_empty }}<span class="u-text-light-gray">（責任者）</span></div>
              <div class="bodytext-11 u-line-height-100 u-text-light-gray">{{ producer.enterprise|hide_if_empty }}</div>
            </div>
          </div>



          {% with user|get_product_user:product as pu %}
          {% if login_producer or login_masteradmin %}
          <div class="u-relative member-item project-member-invited-{{ owner.signature }} sub-member-item" data-user="{{ owner.pk }}" data-pf="{{ product.pk }}" data-jwt="{{ owner.jwt_token }}">
            <div class="material-symbols-rounded c-icon-more-horiz">more_horiz</div>
            <ul class="c-dropdown">
              {% if user.id == owner.pk %}
          {% else %}
              <li class="u-row-between u-w100 member-item__btn-change" id="roleChangeContainer">
                <div onclick="ajaxChangeUserPosition">プロデューサー権限を解除</div>
                <span class="material-symbols-rounded">rule_settings</span>
              </li>
              {% endif %}
                  {% if forloop.first %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="down">
                    <div>下に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_down</span>
                  </li>
                {% elif forloop.last %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% else %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% endif %}
                {% if user.id == owner.pk %}
                {% else %}     
            <li class="u-row-between u-w100 member-item__btn-delete" data-toggle="modal" data-target="">プロジェクトから退出<span class="material-symbols-rounded u-text-light-gray">exit_to_app</span></li>{% endif %}
            </ul>
          </div>
          {% endif %}
          {% endwith %}
        </div>
      {% endwith %}
    {% endfor %}


    {% if login_masteradmin or login_producer %}
        <div class="c-btn-text-white u-row u-justify-end u-w100 u-gap8 invite-the-btn invite-the-producer-btn">
          プロデューサーを招聘<span class="material-symbols-rounded">navigate_next</span></div>
    {% endif %}

    {% comment %} ディレクターリスト {% endcomment %}
    {% for admin in admins %}
    {% with owner=admin product=product editable=editable user=user %}
        <div class="p-lists-user u-row-between member-manage {% if editable_all %}can-edit-all{% endif %} member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %} member-manage__list member-manage__list-director member-item-container {% if owner == user %}can-self-edit-delete{% endif %}" data-role="{{ owner.role }}">
          <div class="u-row u-gap16">
            {% if admin.role == 'admin' %}
              <div class="u-relative u-ml16">
                <a href="{% if admin.user_creator.first.slug %}{% url 'app:creator_info' admin.user_creator.first.slug %}{% else %}{% url 'accounts:accounts_creator' admin.user_creator.first.user.pk %}{% endif %}"
                   target="_blank"><div class="c-avatar32" style="background-image: url({{ admin|get_avatar:'medium' }})"></div></a>
                {% if admin in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
            {% else %}
              <div class="u-relative u-ml16">
                <div class="c-avatar32" style="background-image: url({{ admin|get_avatar:'medium' }})"></div>
                {% if admin in online_users %}<div class="c-avatar-status"></div>{% endif %}</div>
            {% endif %}

            <div class="u-col u-gap4">
              <div class="heading-13-spacing">{{ admin.get_display_name }}</div>
              <div class="bodytext-11 u-line-height-100">{{ admin.position|hide_if_empty }}</div>
            </div>
          </div>

          {% with user|get_product_user:product as pu %}
          {% if login_producer or login_masteradmin %}
          <div class="u-relative member-item project-member-invited-{{ owner.signature }} sub-member-item" data-user="{{ owner.pk }}" data-pf="{{ product.pk }}" data-jwt="{{ owner.jwt_token }}">
            <span class="material-symbols-rounded c-icon-more-horiz">more_horiz</span>
            <ul class="c-dropdown">
              <li class="u-row-between u-w100 member-item__btn-change" id="roleChangeContainer">                
                <div onclick="ajaxChangeUserPosition">プロデューサー権限を付与</div>
                <span class="material-symbols-rounded">rule_settings</span>
              </li>
                  {% if forloop.first %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="down">
                    <div>下に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_down</span>
                  </li>
                {% elif forloop.last %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% else %}
                  <li class="u-row-between u-w100 member-item__btn-swap-order" data-direction="up">
                    <div>上に移動</div>
                    <span class="material-symbols-rounded">keyboard_arrow_up</span>
                  </li>
                {% endif %}      
              <li class="u-row-between u-w100 member-item__btn-delete" data-toggle="modal" data-target="">プロジェクトから退出<span class="material-symbols-rounded u-text-light-gray">exit_to_app</span></li>
            </ul>
          </div>
          {% endif %}
          {% endwith %}
        </div>
        {% endwith %}
    {% endfor %}
    {% if login_masteradmin or login_producer %}
      <div class="c-btn-text-white u-row u-justify-end u-w100 u-gap8 invite-the-btn invite-the-director-btn" >
        ディレクターを招聘<span class="material-symbols-rounded">navigate_next</span></div>
  </div>
  {% endif %}


<script>

    $(document).ready(function() {
        // "View Only" 表示を更新する関数
        function updateViewOnlyDisplay(checkbox) {
            var $container = checkbox.closest('.member-item');
            var viewOnlyOn = $container.find('#view-only-on');
            var viewOnlyOff = $container.find('#view-only-off');
  
            if (checkbox.prop('checked')) {
                viewOnlyOn.show();
                viewOnlyOff.hide();
            } else {
                viewOnlyOn.hide();
                viewOnlyOff.show();
            }
        }
  
        // 初期表示の設定
        $('.switch-checkbox').each(function() {
            updateViewOnlyDisplay($(this));
        });
  
        // イベントリスナーが重複しないように一度すべて解除
        $(document).off('change', '.switch-checkbox');
        $(document).off('click', '.c-icon-more-horiz');
        $(document).off('click', '#view-only-off');
        $(document).off('click', '#view-only-on');
  
        // "View Only" 切り替え用の変更イベントを設定
        $(document).on('change', '.switch-checkbox', function(event) {
            updateViewOnlyDisplay($(this));
        });
  
        // ドロップダウンメニューの表示/非表示を切り替える
        $(document).on('click', '.c-icon-more-horiz', function(event) {
            event.stopPropagation();
            var dropdown = $(this).siblings('.c-dropdown');
            $('.c-dropdown').not(dropdown).hide(); // 他のドロップダウンを閉じる
            dropdown.toggle();
        });
  
        // ドキュメント全体のクリックイベントでドロップダウンを閉じる
        $(document).on('click', function(event) {
            if (!$(event.target).closest('.c-icon-more-horiz, .c-dropdown').length) {
                $('.c-dropdown').hide();
            }
        });
  
        // "閲覧のみに設定" と "閲覧のみ（コメント不可）" をクリックで切り替える
        $(document).on('click', '#view-only-off', function(event) {
            var $container = $(this).closest('.member-item');
            var checkbox = $container.find('.switch-checkbox');
            checkbox.prop('checked', true).trigger('change');
        });
  
        $(document).on('click', '#view-only-on', function(event) {
            var $container = $(this).closest('.member-item');
            var checkbox = $container.find('.switch-checkbox');
            checkbox.prop('checked', false).trigger('change');
        });
  
       
  });

// IP設定の表示を更新する関数
function updateDisplay(container) {
    var detailText = container.closest('.member-item').find('.detail-list-ip').text().trim();

    if (detailText.includes('IP制限なし')) {
        container.find('.ip-setting').show();
        container.find('.ip-setting-progress').hide();
    } else {
        container.find('.ip-setting').hide();
        container.find('.ip-setting-progress').show();
    }
}

// 初期表示の設定
$('.member-item').each(function() {
    updateDisplay($(this));
});


function ajaxChangeUserPosition(user_id, project_id) {
    if (user_id && project_id) {
        $.ajax({
            type: "POST",
            url: "/top/project/top/change_userposition/",
            dataType: "json",
            data: {
                'user_id': user_id,
                'project_id': project_id
            },
            success: function(response) {
                // 取得したデータで `.block-user-in-project` の内容を更新
                $('.block-users-in-project').html(response);
            }
        });
    }
}

$(document).ready(function() {
    // "Change Position" ボタンにイベントをバインド
    $('.member-item__btn-change').on('click', function() {
        var userId = $(this).closest('.member-item').data('user');
        var projectId = $(this).closest('.member-item').data('pf');
        ajaxChangeUserPosition(userId, projectId);
    });
});

// 任意の非同期処理後に更新する（例: Ajax完了後）
$(document).on('ajaxComplete', function() {
    $('.member-item').each(function() {
        updateDisplay($(this));
    });
});



document.addEventListener('DOMContentLoaded', function() {
    // すべての.c-icon-more-horiz要素を取得
    const moreIcons = document.querySelectorAll('.c-icon-more-horiz');
    
    moreIcons.forEach(function(icon) {
        // 各アイコンのデータ属性からユーザーIDを取得
        const userId = icon.getAttribute('data-user-id');
        const dataUser = icon.getAttribute('data-user');

        // data-user-id と data-user の値が一致する場合に.hiddenクラスを追加する
        if (userId === dataUser) {
            icon.classList.add('hidden');
        }
    });
});


// CSRFトークンを取得する関数
function getCookie(name) {
    var cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        var cookies = document.cookie.split(';');
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim(); // 先頭と末尾の空白を除去
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 全てのAJAXリクエストにCSRFトークンを自動的に追加
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
            // クロスドメインでない場合のみ、ヘッダーにCSRFトークンを設定
            xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
        }
    }
});

// 順番変更ボタンのクリックイベントハンドラ
$(document).on('click', '.member-item__btn-swap-order', function() {
    var direction = $(this).data('direction');
    ajaxSwapOrderUser(this, direction);
});

// ajaxSwapOrderUser関数の修正
function ajaxSwapOrderUser(element, direction) {
    var $memberItem = $(element).closest('.member-item');
    var userId = $memberItem.data('user');
    var projectId = $memberItem.data('pf');

    $.ajax({
        type: "POST",
        url: '/top/project/top/swap_order_user/',
        dataType: "json",
        data: {
            'direction': direction,
            'user_id': userId,
            'project_id': projectId
        },
        success: function(response) {
            if (response.status === 'success') {
                // 更新されたHTMLでユーザーリストを置き換える
                $('.block-users-in-project').html(response.html);
                // 必要に応じてイベントハンドラを再設定
            } else {
                console.error('エラーが発生しました: ' + response.message);
            }
        }
    });
}


</script>
