{% load static %}
{% load util %}

{% if role == 'admin' or role == 'master_client' %}
  <div class="pd-chapter active d-none-chapter chapter-block" data-index="{{ ps.order }}" data-product-scene-id="{{ ps.pk }}">
    <div class="pd-chapter__title active">
      <span class="pd-chapter__name ">{{ ps.name }}</span>
      <div class="cvideo__rating {{ show_button }}" style="margin: 0 4px 0 24px;">
          {% with ps.rating|round_rating as rating_point %}
          <div class="average-star stars cannot-check" data-rating="{{ rating_point }}">
            <span class="material-symbols-rounded star-1" data-value="1">star</span>
            <span class="material-symbols-rounded star-2" data-value="2">star</span>
            <span class="material-symbols-rounded star-3" data-value="3">star</span>
            <span class="material-symbols-rounded star-4" data-value="4">star</span>
            <span class="material-symbols-rounded star-5" data-value="5">star</span>
        </div>
          {% endwith %}
      </div>
      <span class="pd-chapter__rating">{{ps.rating}} ({{ps.number_rating}})</span>
      {% if not view_only %}
        <div class="pd-chapter__action">
          {% if role == 'admin' %}
            <a class="pd-chapter-button pd-chapter__delete" href="#">
              <i class="icon icon--sicon-trash"></i>
            </a>
          {% endif %}
          <a class="pd-chapter-button pd-chapter__edit" href="#edit-chapter">
            <i class="icon icon--sicon-pencil"></i>
          </a>
        </div>
      {% endif %}
      <div class="pd-chapter__line"></div>
    </div>
    <div class="pd-chapter__content" style="display: block;">
      <div class="pd-chapter__list mscrollbar">
        {% if role == 'admin' %}
          <a class="pd-chapter__add pd-chapter-new refactor">
            <div class="c-fab heading-13 u-gap8 u-mb8">
              <span class="material-symbols-rounded">add_circle</span>
              納品
            </div>
          </a>
        {% endif %}

        {% for st in ps.title_product_scene.all %}
          {% with st.get_last_version as scene %}
          {% if scene %}
            {% include 'top/_cscene.html' with st=st view_only=view_only scene=scene role=role is_done=is_done %}
          {% endif %}
          {% endwith %}
        {% endfor %}
      </div>
    </div>
  </div>
{% endif %}
