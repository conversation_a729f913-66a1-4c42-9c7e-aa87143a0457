{% load static %}
{% load util %}

<div class="project-member-setting active" style="width: 570px;" data-project="{{ product_id }}">
    <div class="area-show-user-invite">
        <div class="project-member-title">プロジェクトオーナー</div>
        <div class="project-member-owner">
        <div class="project-member-list">
        {% for owner in owners %}
          <div class="project-setting-member">
            <div class="project-setting-member__avatar">
              <img class="project-setting-member__avatar-img"
                   {% if not owner.avatar %}src="/static/images/default-avatar-owner.png"
                   {% else %}src="{{ owner|get_avatar:'medium' }}"{% endif %} alt="">
            </div>
            <div class="project-setting-member__info">
              <div class="project-setting-member__name">{{ owner.get_full_name }}</div>
              <div class="project-setting-member__mess">{{ owner.position|hide_if_empty }}</div>
              <div class="project-setting-member__work">{{ owner.enterprise|hide_if_empty }}</div>
            </div>
          </div>
        {% endfor %}
        </div>
        </div>
        <div class="project-member-title">メンバー</div>
        <div class="project-member-order">権限別 ▼</div>

        <!--show member invited-->
        <div class="project-member-list">
            {% for member in members_invited %}
                {% include 'top/_member_invited.html' with member=member product=product editable=editable %}
            {% endfor %}
        </div>
        <div class="project-member-title">招待中</div>

        <!--show member inviting-->
        <div class="project-member-invite-list">
            {% if user in owners or user.role in 'master_admin,admin' %}
                {% for member in members_inviting %}
                    <div class="project-member-invite-item project-member-inviting-{{ member.signature }}">
                        <div class="project-member-invite-item__email">{{ member.email }}</div>
                        <div class="project-member-invite-item__resend">
                            <a class="button button--text button--text-gray" href="#" role="button"
                               onclick="sendInviteEmailAgain('{{ member.jwt_token }}', '{{ member.pk }}')">招待を再送信</a>
                        </div>
                        <div class="project-member-invite-item__revoke">
                            <a class="button button--text button--text-gray" href="#" role="button"
                               onclick="deleteUserInviting('{{ member.jwt_token }}')">招待を取り消す</a>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    {% if user in owners or user.role in 'master_admin,admin' %}
        <div class="project-setting__action">
            <div class="project-setting__form-group">
                <a class="button button--gradient button--gradient-primary button--round" href="#"
                   role="button" name="display-area-create-user-invite">メンバーを招待 </a>
            </div>
        </div>
    {% endif %}
    </div>

    <div class="area-create-user-invite" style="display: none">
        <div class="project-member-title">クライアントを招待</div>
        <div class="project-member-guide">
            招待したいメンバーの情報を入力してください。進捗共有のみしたい場合は、「閲覧のみ」をチェックしてください。検収・コメント記載は制限されます。
        </div>

        <!--show member inviting-->
        <div class="project-member-invite-edit">
        </div>
        {% if user in owners or user.role in 'master_admin,admin' %}

            <form method="post" action="{% url 'app:invite_user' %}" id="invite_user_form">
                {% csrf_token %}
                <div class="project-setting-member-form">
                    <div class="form-group">
                        <label for="member-email">メールアドレス *</label>
                        <input class="form-control form-control" type="hidden" name="member-product-id"
                               value="{{ product_id }}">
                        <input class="form-control form-control member-email" type="email" name="member-email" required
                               onfocusout="checkEmailExistInviteUser()">
                    </div>
                    <div class="account__column-2">
                        <div class="form-group">
                            <label for="member-last-name">姓 *</label>
                            <input class="form-control form-control member-last-name" type="text"
                                   name="member-last-name" required>
                        </div>
                        <div class="form-group">
                            <label for="member-first-name">名 *</label>
                            <input class="form-control form-control member-first-name" type="text"
                                   name="member-first-name" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="member-position">役職</label>
                        <input class="form-control form-control" type="text" name="member-position">
                    </div>
                    <div class="form-group">
                        <label for="member-enterprise">会社名</label>
                        <input class="form-control form-control" type="text" name="member-enterprise">
                    </div>
                    <div class="form-group">
                        <label for="member-ip">IP制限</label>
                        <input class="form-control form-control" type="text" name="member-ip">
                    </div>
                    <div class="form-group">
                        <div class="checkbox input-checkbox">
                            <input type="checkbox" name="member-permit-view-only">
                            <label for="member-checkbox"><a></a>閲覧のみ
                            </label>
                        </div>
                        <div class="project-setting-member_field-desc">※検収・コメント記載するには、チェックを外してください。</div>
                    </div>
                </div>
                <div class="project-setting__action">
                    <div class="project-setting__form-group">
                        <a class="button button--gradient button--gradient-primary button--round create-user-invite-js"
                           href="javascript:;"
                           role="button" style="cursor: pointer">
                            招待メールを送信
                        </a>
                        <a class="button button--text button--text-gray" href="#" role="button"
                           name="display-area-show-user-invite">キャンセル</a>
                    </div>
                </div>
            </form>
        {% endif %}

    </div>
</div>
