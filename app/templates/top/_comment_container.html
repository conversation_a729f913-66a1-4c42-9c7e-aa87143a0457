{% load static %}
{% load util %}
{% load i18n %}

<div class="pd-section pd-section--detail pd-product-comment show-comment-unresolved"
     data-product-id="{{ obj.pk }}">
  <div class="pd-section__content active main-talk-room">
    <div class="pd-comment">
      <div class="pd-comment__heading">メッセージ</div>
      <div class="pd-comment__main">
        <div class="pd-comment__top">
          <div class="pd-comment__attach">
            <div class="form-check custom-switch">
              <label class="form-check-label">
                <div class="form-check-group">
                  <input class="form-check-input switch-checkbox" type="checkbox" name="switch-checkbox-comment"
                         id="order-monthy"/><span class="switch-slider"></span>
                </div>
                <span class="switch-label">{% trans "See also resolved" %}</span>
              </label>
            </div>
          </div>

          <div class="pd-file active">
            <div class="pd-file-hide-heading">
              <i class="icon icon--sicon-storage"></i>
            </div>
          </div>
        </div>
        <div class="pd-comment__content">
          <div class="mmessage-component" style="height: 80vh !important;">
            <div class="mattach" style="color: black;" id="comment-input-99-mcomment-attach" data-maxfile="2" data-maxsize="32">
              <div class="mattach-overlay"></div>
              <form class="mattach-file" action="upload.php" id="comment-input-99-mcomment-attach-form">
                <div class="mattach-drop">
                  <div class="mattach-text"></div>
                  <div class="border"></div>
                </div>
              </form>
            </div>
            <div class="mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom{% if view_only %} view_only{% endif %}{% if is_seen %} not-seen{% endif %}">
              {% include 'top/_item_messages.html' with comments=comments user=user type=type %}
              <div class="mlast__content"></div>

            </div>
            {% if not view_only %}
              <div class="maction">
                <div class="mcommment" id="comment-input-99">
                  <div class="mcomment-message">
                    <div class="mcomment-top">
                      <div class="mcomment-attached">
                        <div class="mattach-preview-container">
                          <div class="mattach-previews collection">
                            <div class="mattach-template collection-item item-template">
                              <div class="mattach-info" data-dz-thumbnail="">
                                <div class="mcommment-file">
                                  <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                  <div class="mcommment-file__name" data-dz-name=""></div>
                                  <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                          class="icon icon--sicon-close"></i>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="mcomment-input">
                        <div class="mcomment-input-title"></div>
                        <textarea class="mcomment-input-text mcomment-autoExpand" name="mcomment-input" rows="1"
                                  placeholder="Aa…"></textarea>
                        <div class="mcomment-input-close"><i class="icon icon--sicon-close"></i>
                        </div>
                      </div>
                        <div class="block-remove-msg-editing d-none">
                            <div class="mcomment-input-close btn-remove-msg">
                                <i class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                    </div>
                    <div class="mcomment-bottom">
                      {#                    <div class="mcomment-pin"><i class="icon icon--sicon-pin"></i>#}
                      {#                    </div>#}
                      <div class="mcomment-action">
                        <div class="mattach-label"><i class="icon icon--sicon-clip"></i>
                        </div>
                      </div>
                      <div class="mcomment-input-placeholder">Aa…</div>
                      <div class="mcomment-icon"><a class="mcomment-icon-btn" href="#"><i
                              class="icon icon--sicon-stick"></i></a>
                        <div class="mboard-triangle"></div>
                        <div class="mcomment-icon-board mscrollbar">
                          <div class="micon-board-section micon-board-recently">
                            <div class="micon-board-heading">最近使った</div>
                            <div class="micon-board-list">
                            </div>
                          </div>
                          <div class="micon-board-section micon-board-all">
                            <div class="micon-board-heading">全て</div>
                            <div class="micon-board-list">
                            </div>
                          </div>
                        </div>
                      </div>
                      <a class="mcomment-send disabled" href="#">
                        <span class="material-symbols-rounded">send</span></a>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="pd-section-file file-hide">
    <div class="pd-file-heading "><i class="icon icon--sicon-storage"></i>
      <div class="pd-file-toggle "><i class="icon icon--sicon-next"></i>
      </div>
    </div>
  </div>
</div>