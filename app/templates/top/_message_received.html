{% load static %}
{% load util %}
{% if message|check_exist_message %}
<div class="mmessage-container refactor">
  <div class="mmessage mmessage--received clicked  {% if message.is_near %}mmessage-near{% endif %} {% if message.resolved %}resolved{% endif %}"
      data-message-id="{{ message.pk }}"
      {% if message.parent %}data-parent-id="{{ message.parent.pk }}"{% endif %}>
    {% include 'top/_item_message_received.html' with message=message sender=message.user user=user type=type %}
  </div>  
</div>
{% endif %}
