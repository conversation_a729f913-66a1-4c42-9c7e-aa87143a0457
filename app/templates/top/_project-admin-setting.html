{% load static %}
{% load util %}
{% load i18n %}

<div class="member-manage__group__director {% if editable_all %}can-edit-all{% endif %}">
  <div class="member-manage__list manage-producer-director {% if product.get_status_offer_product %}project-done{% endif %}">
    <div class="member-manage__head heading-18-spacing u-mb16">プロデューサー</div>
    <div class="member-manage__head-hint bodytext-13">クライアント側との契約交渉を担当します。
    </div>
    <div class="member-manage__list member-manage__list-producer">
      {% comment %} プロデューサーリスト {% endcomment %}
      {% for producer in producers %}
        {% include 'top/_list_project_admin.html' with owner=producer product=product editable=editable user=user %}
      {% endfor %}
    </div>
    {% comment %} 管理者か編集権限があるあれば「 {% endcomment %}
    {% if editable or user.role == 'master_admin' %}
      <div class="producer-manage-action">
        {% include 'product/_item_button_invite_admin.html' with user=user editable=editable %}
      </div>
    {% endif %}

    <div class="member-manage__head heading-18-spacing u-mb16">ディレクター</div>
    <div class="member-manage__head-hint bodytext-13">クライアント側への納品を担当し、成果物に対する直接的なフィードバックを受け取れます。</div>
    <div class="member-manage__list member-manage__list-director">
      {% comment %} ディレクターリスト {% endcomment %}
      {% for admin in admins %}
        {% include 'top/_list_project_admin.html' with owner=admin product=product editable=editable user=user %}
      {% endfor %}
    </div>
  </div>

  {% if editable %}
    <hr>

    <div class="member-manage-action">
      {% include 'product/_item_button_invite_artist.html' with product=product user=user %}
    </div>
  {% endif %}
</div>
