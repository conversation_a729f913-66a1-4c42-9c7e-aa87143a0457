{% load static %}
{% load util %}
{% load i18n %}
<div id="footerCommentBlockOffer" class="footer-comment-block refactor">
    <div class="action-panel-head">
        {% if offer.status == '2' and user != offer.creator or offer.status == "3" and user != offer.creator %}
            <div class="c-fab heading-13 u-gap8 u-mb8 button-confirm-offer-quick" onclick="acceptOfferArtist('{{real_offer.pk}}')">
                <span class="material-symbols-rounded">check_circle</span>
                検収
            </div>
        {% elif offer.status == '4' and user.pk not in user_ids %}
            <div class="c-fab heading-13 u-gap8 u-mb8 rate-review" id="fab-review" onclick="openModalReview('{{user.get_display_name}}', '{{offer.creator_id|get_display_fullname}}', '{{offer.admin_id|get_display_fullname}}', '{{user.position}}')">
                <span class="material-symbols-rounded">
                    rate_review
                </span>レビューを書く
            </div>
        {% endif %}
        <div class="offer-block-right maction maction-new" data-offer="{{ real_offer.pk }}">
            {% if type_comment != 'messenger_owner' %}
                {% if offer.status == '1' and user == offer.creator %}
                    {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom position="pinned" %}
                        {% include 'top/_item_message_for_artist_contract.html'%}
                    {% endwith %}
                {% endif %}
                <!-- message for offer product -->
            {% else %}
                <!-- normal messages -->
                {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill plan_contract_message=messages|get_plan_contract_bill_message:offer%}
                    {% if plan_contract_message %} 
                        {% include 'top/_item_button_for_plan_contract.html' with message_file_type=plan_contract_message|check_plan_contract_message message=plan_contract_message%} 
                    {% endif %}
                {% endwith %}
            {% endif %}
            <div class="mcommment mcomment-new" id="comment-input-1">
                <div class="floating-button-container"
                     default-value-time-form="{{ real_offer|get_default_deadline_from_offer }}">
                    {% include 'direct/_floating_icon_DM.html' with offer=offer %}
                </div>
                <div class="mcomment-message">
                    <div class="mcomment-top comment-top-area">
                        <div class="mcomment-attached">
                            <div class="mattach-preview-container">
                                <div class="mattach-previews collection">
                                    <div class="mattach-template collection-item item-template">
                                        <div class="mattach-info" data-dz-thumbnail="">
                                            <div class="mcommment-file">
                                                <div class="progress">
                                                    <div class="determinate"
                                                         style="width:0"
                                                         data-dz-uploadprogress=""></div>
                                                </div>
                                                <div class="mcommment-file__name"
                                                     data-dz-name=""></div>
                                                <div class="mcommment-file__delete"
                                                     href="#!" data-dz-remove="">
                                                    <span class="progress-text"></span>
                                                    <i class="icon icon--sicon-close"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mcomment-input mcomment-input-new">
                            <div class="mcomment-input-title"></div>
                            <div class="mcomment-input-close"><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                        <div class="block-remove-msg-editing d-none">
                            <div class="mcomment-input-close btn-remove-msg">
                                <i class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                        <div class="mcomment-bottom mcomment-bottom-new">
                            <div class="mcomment-action mcomment-action-new">
                                <div class="mattach-label mattach-label-new">
                                    <span class="material-symbols-rounded c-icon-attach">add_circle</span>
                                </div>
                            </div>
                            <textarea class="mcomment-input-text mcomment-autoExpand"
                                      name="mcomment-input" id="text-input-comment" rows="1"
                                      placeholder="メッセージを送信"></textarea>
                            <a href="#" class="mcomment-send disabled">
                                <span class="material-symbols-rounded c-icon-send is-icon-inactive">send</span>
                            </a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
