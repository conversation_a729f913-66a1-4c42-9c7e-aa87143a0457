{% load static %}
{% load util %}
<div class="sfolder  {% if type_comment in 'messenger,messenger_owner' %}{% if folder.message|get_offer_user_by_message != folder.message.offer|get_offer_user:user %}sfolder-gray{% endif %}{% else %}{% if folder.message.user.role != user.role %}sfolder-gray{% endif %}{% endif %}" data-message-id="{{ folder.message.pk }}" data-folder-id="{{ folder.pk }}">
  <ul class="mfolder">
      <div class="parent-folder">
          <i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="{{folder.pk}}">{{ folder.name }}</span>
          {% if folder.pk|check_child_folder:type_comment %}
            <i class="icon icon--sicon-download icon--sicon-folder-download pointer" ></i>
          {% endif %}
      </div>
    <ul>
      {% for child in folder.child_folders.all %}
        <li class="mfolder__sub"><i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="{{child.pk}}">{{ child.name }}</span>
          {% if child.pk|check_child_folder:type_comment %}
            <i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>
          {% endif %}
        </li>
      {% endfor %}
      {% for file in folder.children.all %}
        {% include 'top/_item_mfolder_sub.html' with file=file message=folder.message type_comment=type_comment %}
      {% endfor %}
    </ul>
  </ul>
</div>
