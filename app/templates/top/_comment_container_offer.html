{% load static %}
{% load util %}
{% load i18n %}
<div id="footerCommentBlockOffer" class="footer-comment-block">
    <div class="action-panel-head">
{#        <div class="offer-block-left  show-block">#}
{#            {% if user.role == 'admin' or user.role == 'master_admin' %}#}
{#                  <div class="messenger-add">#}
{#                    <div class="messenger-add-row">#}
{#                      <div class="messenger-add-icon">#}
{#                        <i class="icon icon--sicon-add-cirlce"></i>#}
{#                        <p>{% trans "Deliver an offer" %}</p>#}
{#                      </div>#}
{#                    </div>#}
{#                  </div>#}
{#                {% endif %}#}
{#           {% if user.role == 'admin' or user.role == 'master_admin' %}#}
{#            <button class="offer-btn messenger-add" type="button">#}
{#                <span class="material-symbols-rounded mail-offer-btn">mail</span>#}
{#                <span class="txt-offer-btn">オファーを送る</span>#}
{#            </button>#}
{#            {% endif %}#}
{#        </div>#}
        <div class="offer-block-right maction maction-new" data-offer="{{ real_offer.pk }}">
            {% if type_comment != 'messenger_owner' %}
                {% if offer.status == '1' and user == offer.creator %}
                    {% with file_name=offer.get_contract_file_name file_attribute=offer|get_artist_contract_file_attribute text_bottom=offer|get_artist_contract_file_text_bottom position="pinned" %}
                        {% include 'top/_item_message_for_artist_contract.html'%}
                    {% endwith %}
                {% endif %}
                <!-- message for offer product -->
            {% else %}
                <!-- normal messages -->
                {% with offer_last_plan=offer.get_offer_last_plan offer_last_contract=offer.get_offer_last_contract offer_last_bill=offer.get_offer_last_bill plan_contract_message=messages|get_plan_contract_bill_message:offer%}
{#                    {% include 'top/_item_messages.html' with messages=messages user=user type=type type_message='messenger_owner' %}#}
                    {% comment %} {% include 'direct/_message_check_file.html' with offer=offer user=user type='messenger_owner' %} {% endcomment %}
                    {% if plan_contract_message %} 
                        {% include 'top/_item_button_for_plan_contract.html' with message_file_type=plan_contract_message|check_plan_contract_message message=plan_contract_message%} 
                    {% endif %}
                {% endwith %}
            {% endif %}
            <div class="mcommment mcomment-new" id="comment-input-1">
                <div class="floating-button-container"
                     default-value-time-form="{{ real_offer|get_default_deadline_from_offer }}">
                    {% include 'direct/_floating_icon_DM.html' with offer=offer %}
                </div>
                <div class="mcomment-message">
                    <div class="mcomment-top comment-top-area">
                        <div class="mcomment-attached">
                            <div class="mattach-preview-container">
                                <div class="mattach-previews collection">
                                    <div class="mattach-template collection-item item-template">
                                        <div class="mattach-info" data-dz-thumbnail="">
                                            <div class="mcommment-file">
                                                <div class="progress">
                                                    <div class="determinate"
                                                         style="width:0"
                                                         data-dz-uploadprogress=""></div>
                                                </div>
                                                <div class="mcommment-file__name"
                                                     data-dz-name=""></div>
                                                <div class="mcommment-file__delete"
                                                     href="#!" data-dz-remove="">
                                                    <span class="progress-text"></span>
                                                    <i class="icon icon--sicon-close"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mcomment-input mcomment-input-new">
                            <div class="mcomment-input-title"></div>
                            <div class="mcomment-input-close"><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                        <div class="block-remove-msg-editing d-none">
                            <div class="mcomment-input-close btn-remove-msg">
                                <i class="icon icon--sicon-close"></i>
                            </div>
                        </div>
                        <div class="mcomment-bottom mcomment-bottom-new">
                            <div class="mcomment-action mcomment-action-new">
                                <div class="mattach-label mattach-label-new">
                                    <span class="material-symbols-rounded c-icon-attach">add_circle</span>
                                </div>
                            </div>
                            {#                                                             <div class="mcomment-input-placeholder">Aa…</div>#}
                            <textarea class="mcomment-input-text mcomment-autoExpand"
                                      name="mcomment-input" id="text-input-comment" rows="1"
                                      placeholder="メッセージを送信"></textarea>
{#                            <a class="mcomment-icon" href="javascript:void(0)">#}
{#                                <i class="icon icon--sicon-stick"></i>#}
{#                            </a>#}
                            <a href="#" class="mcomment-send disabled">
                                <span class="material-symbols-rounded c-icon-send is-icon-inactive">send</span>
                            </a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
