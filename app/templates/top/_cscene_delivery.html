{% load static %}
{% load util %}

<div class="cvideo cvideo__thumb-list-delivery refactor scene-home">
  <div class="cvideo__thumb-item-delivery"
       data-scene-title-id="{{ st.pk }}"
       data-status="{{ st.status }}" data-rating="{{ st|get_rating:role }}"
       data-title="{{ st.title }}" data-scene-id="{{ scene.pk }}"
        data-product-scene="{{ st.product_scene.pk }}">
    <div class="cvideo__thumb" data-file-type="{% if scene.movie.name %}{{scene.is_audio_file}}{% endif %}" out-of-date-schedule="{% if scene|get_schedule_out_of_date %}{{scene|get_schedule_out_of_date}}{% endif %}">
      {% if not scene|get_schedule_out_of_date %}
        <video width="100%" height="256px" poster="{{ scene|get_thumbnail }}"
              preload="none" loading="lazy" style="vertical-align: middle; border-radius:6px;">
          <source src="{% if scene.movie %}{{ scene.movie.url }}{% endif %}" type="video/mp4">
        </video>
      {% else %}
        <div class="thumb-schedule-video view_only refactor" style="{% if scene.movie.name %}background-image: url({{ scene|get_thumbnail }});mix-blend-mode: luminosity;opacity: 0.5;{% endif %}">
          <!-- <span>
            {{scene|get_schedule_out_of_date}}
          </span> -->
        </div>
      {% endif %}
    </div>

    <div class="cvideo__heading" style="line-height: 200%; margin: 0; padding: 0;">
      {% if st.status not in '5,6' %}
        <div title="" class="{{ show_button }} icon--sicon-heart-o project-chapter-video-done {% if role == 'admin' or view_only %}cannot-check{% endif %}"></div>
      {% else %}
        <div title="" class="{{ show_button }} icon--sicon-heart project-chapter-video-undone {% if view_only %}cannot-check{% endif %}"></div>
      {% endif %}
      <div class="cvideo__title" style="padding: 0;">{{ st.title }}</div>
    </div>

    <div class="cvideo__meta">
      <div class="cvideo__rating {{ show_button }}">
        {% with st|get_rating:role as rating %}
        <div class="stars {% if role == 'admin' %}cannot-check average-star{% endif %}" data-rating="{{ rating }}"><span><a class="star-1" href="javascript:void(0)" data-value="1">1</a><a class="star-2" href="javascript:void(0)" data-value="2">2</a><a
                class="star-3" href="javascript:void(0)" data-value="3">3</a><a class="star-4" href="javascript:void(0)" data-value="4">4</a><a class="star-5"
                                                                                       href="javascript:void(0)" data-value="5">5</a></span></div>
        {% endwith %}
      </div>

      <div class="cvideo__date-time" style="margin: 0; padding: 0;">
        {% if scene.movie.name %}
          {% if scene|get_schedule_out_of_date %}
            <div class="cvideo__date">配信予定: {{scene|get_schedule_out_of_date}}</div>
          {% else %}
            <div class="cvideo__date">{{ st|get_schedule_out_of_date:'last-update' }}</div>
          {% endif %}
        {% else %}
          {% if not scene|get_schedule_out_of_date or scene|get_schedule_out_of_date == 'まもなくリリース' %}
            <div class="release-soon" style="font-size: 8px;">まもなくリリース</div>
          {% else %}
            <div class="cvideo__date">配信予定: {{scene|get_schedule_out_of_date}}</div>
          {% endif %}
        {% endif %}
      </div>

      <!-- <div class="cvideo__date-time" {{ style }}>
        <div class="cvideo__date">{{ st.updated_at|get_updated_time }}</div>
        <div class="cvideo__time">{{ st.updated_at|date:"H:i" }}</div>
      </div> -->
    </div>
  </div>
</div>
