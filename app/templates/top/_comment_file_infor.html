{% load static %}
{% load util %}

{% if message.files.exists %}
  {% for file in message|get_sorted_message_files %}
    {% if not file.folder %}
      <div class="tfile-item">
        <div class="tfile-item-time">{{ file.modified|get_weekday_new }}</div>
        <div class="tfile-item-content">
          {% include 'top/_file_infor.html' with file=file message=file.message type_comment=type_comment user=user%}
        </div>
      </div>
    {% endif %}
  {% endfor %}

  {% for folder in message.folders.all %}
    {% if not folder.parent %}
      <div class="tfile-item-time">{{ folder.modified|get_weekday_new }}</div>
      <div class="tfile-item-content">
        <div class="tfile-file">
          {% include 'top/_sfolder.html' with folder=folder message=folder.message type_comment=type_comment user=user %}
        </div>
      </div>
    {% endif %}
  {% endfor %}
{% endif %}
