{% load static %}
{% load util %}

{% with file|get_list_user_download_refactor as user_dowloadeds %}
  <li class="mfolder__sub" data-file-id="{{ file.pk }}"><i class="icon icon--sicon-clip"></i>
    <span>{{ file.real_name }}</span>
    {% if file.is_audio_file in 'video, audio' %}
      {% if file.acr_status in '1,2,3' %}
        {% if file.acr_status in '3' %}
          <img class="acr-result-icon active" src="/static/images/icon_acr_active.svg" />
        {% else %}
          <img class="acr-result-icon deactive" src="/static/images/icon_acr_deactive.svg" />
        {% endif %}
      {% endif %}
    {% endif %}
    <i class="icon icon--sicon-download pointer"></i>
  {% include 'top/_sview_user_refactor.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
  </li>
{% endwith %}
