{% load util %}
{% load static %}
{% with user|check_notification_product:project as notification %}
    <div class="project-item__general">
        <div class="project-item__info-top">
            <div class="project-item__member">
                <div class="project-item__member-list">
                  {% with project|get_owners_project as owners %}
                    {% if owners %}
                      {% for owner in owners %}
                      <div class="project-item__member-item background-avt" title="{{ owner|get_full_name }}">
                          <img class="{% if not owner.avatar %}owner-project{% endif %}" src="{{ owner|get_avatar:'small' }}" alt="">
                      </div>
                        {% endfor %}
                      {% endif %}
                    {% with project|get_member as members %}
                      {% for user in members %}
                          <div class="project-item__member-item background-avt" title="{{ user|get_full_name }}">
                              <img src="{{ user|get_avatar:'small' }}" alt="">
                          </div>
                      {% endfor %}
                    {% endwith %}
                  {% endwith %}
                </div>
              {% if user in project|get_owners_project or user.role in 'master_admin,admin' %}
                  <div class="project-item__member-add">
                      <div class="button button--icon button--icon-add button--round button-setting"
                           role="button" data-toggle="modal" data-target="#project-member-setting"></div>
                  </div>
              {% else %}
                  <div class="notification-setting action-notification">
                    {% if not notification %}
                        <img src="{% static 'images/icon_noti_on.svg' %}" alt="" class="notify on hide"/>
                        <img src="{% static 'images/icon_noti_off.svg' %}" alt="" class="notify off"/>
                    {% else %}
                        <img src="{% static 'images/icon_noti_on.svg' %}" alt="" class="notify on"/>
                        <img src="{% static 'images/icon_noti_off.svg' %}" alt="" class="notify off hide"/>
                    {% endif %}
                  </div>
              {% endif %}
            </div>
        </div>
    {% with done_count=project.current_heart scene_count=project.current_scene %}

    <div class="project-item__info-bottom">
        <div class="project-item__more">
          {% if user.role == 'master_admin' %}
              <div class="project-item_admin">
                  <div class="project-item__member">
                      <div class="project-item__member-list">
                        {% with project|get_admin_project as admins %}
                          {% if admins %}
                            {% for admin in admins %}
                                <div class="project-item__member-item background-avt" title="{{ admin|get_full_name }}">
                                    <img src="{{ admin|get_avatar:'small' }}" alt="">
                                </div>
                            {% endfor %}
                          {% endif %}
                        {% endwith %}
                      </div>
                  </div>
              </div>
          {% endif %}
            <div class="project-item__more-left">

                <div class="project-item__more-left-top">
                    <ul class="project-item__category">
                    </ul>
                    {% if project.information and not is_pc and show_staff %}
                    <div class="project-item__more-right">
                        <div class="button button--text button--text-primary button-staff"
                           role="button"></div>
                    </div>
                    {% endif %}
                  {% if user.role == 'master_admin' %}
                    {% with budget_offer=project|get_budget_offer budget_admin=project|get_budget_for_admin %}
                      {% with project.total_budget as budget %}
                        <div class="project-item__progress-percent">
                          <div class="project-item__progress-total" title="オファー済金額" style="margin-right: 5px">
                            ￥{{ budget_offer|get_reward }}- |
                          </div>
                          <div class="project-item__progress-total"
                               title="ディレクター予算上限" style="margin-right: 5px">￥{{ budget_admin|get_reward }}- |
                          </div>
                          <div class="project-item__progress-total" title="受託金額">￥{{ budget|get_reward }}-
                          </div>
                        </div>
                        <div class="project-item__bar">
                          <div class="progressbar-new">
                            <div class="progress">
                              {% with budget_offers=project|get_money_spent:budget_offer budget_admins=project|get_budget_admins:budget_admin %}
                                <div class="progress-bar bg-success"
                                     style="width: {{ budget_offers }}%"></div>
                                <div class="progress-bar bg-warning"
                                     style="width: {{ budget_admins|minus:budget_offers }}%"></div>
                              {% endwith %}
                            </div>
                          </div>
                        </div>
                      {% endwith %}
                    {% endwith %}
                  {% else %}
                    <div class="project-item__progress-percent">

                      <div class="project-item__progress-success" title="完了">{{ done_count|floatformat:"0" }}</div>
                      <div class="project-item__progress-warning"
                           title="完了また進行中">{{ scene_count|minus:done_count|floatformat:"0" }}</div>
                      <div class="project-item__progress-total" title="残る" data-max-scene="{{ project.max_scene }}">
                        <span>{{ project.max_scene }}</span>
                        {% if user.role in 'admin' or user.role == 'master_client' and user in project|get_owners_project %}
                          <div class="max_scene_edit_btn max_scene_edit"></div>
                        {% endif %}
                      </div>
                    </div>

                    <div class="project-item__bar">
                      <div class="progressbar-new">
                        <div class="progress">
                          <div class="progress-bar bg-success"
                               style="width: {{ project.get_current_heart_rate }}%"></div>
                          <div class="progress-bar bg-warning"
                               style="width: {{ project.get_current_scene_rate }}%"></div>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                </div>
            </div>
            {% if project.information and is_pc and show_staff %}
            <div class="project-item__more-right">
                <div class="button button--text button--text-primary button-staff"
                   role="button"></div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endwith %}
    <div class="project-item__info" style="background-image: url({{ project|get_image }});">
    </div>
</div>
{% endwith %}
