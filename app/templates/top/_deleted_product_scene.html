{% load static %}
{% load util %}

{% if deleted_product_scenes %}
  <div class="pd-section pd-section--deleted-video d-none-chapter">
    <div class="pd-section__title sheading sheading--18">削除したチャプター</div>
    <div class="pd-section__content">
      {% for ps in deleted_product_scenes %}
        {% if ps.title_product_scene|check_null %}
          <div class="pd-chapter active" data-product-scene-id="{{ ps.pk }}">
            <div class="pd-chapter__title active">
              <span class="pd-chapter__name ">{{ ps.name }}</span>
              {% if role == 'admin' %}
                <div class="pd-chapter__action">
                  <a class="pd-chapter-button pd-chapter__delete" href="#">
                    <i class="icon icon--sicon-trash"></i>
                  </a>
                </div>
              {% endif %}
              <div class="pd-chapter__line"></div>
            </div>
            <div class="pd-chapter__content" style="display: block;">
              <div class="pd-chapter__list mscrollbar">

                {% for st in ps.title_product_scene.all %}
                  {% if st.get_last_version %}
                    {% with st.get_last_version as scene %}
                      {% include 'top/_cscene.html' with st=st scene=scene role=role is_done=is_done view_only=True %}
                    {% endwith %}
                  {% endif %}
                {% endfor %}
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
{% endif %}
