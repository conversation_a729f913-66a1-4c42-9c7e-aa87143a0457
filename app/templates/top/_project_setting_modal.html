{% load util %}
{% load static %}
{% load i18n %}

{% if project_list == True %}
  <div class="modal popup-container project-setting-modal fade" id="modal-project-setting" role="dialog"
       style="z-index: 9998">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="popup-header">
          <h4 class="popup-title">{% trans "Project settings" %}</h4>
        </div>
        <div class="popup-body popup-body-project-setting">
        </div>
      </div>
    </div>
  </div>

  <div class="modal popup-container search-block-list-modal fade" id="modal-search-block-list" role="dialog"
       style="z-index: 9998">
    <div class="modal-dialog popup-dialog">
      <div class="modal-content popup-content">
        <div class="modal-header">
          <a class="smodal-close smodal-close--prev" href="#" data-dismiss="modal" aria-label="Close">
            <i class="icon icon--sicon-prev"></i>
          </a>
        </div>
        <div class="popup-body">
          <div class="col-sm-12 popup-body__part-content">
            <h3>{% trans "Add block list" %}</h3>
            <span>ブロックする会社名でアーティストを検索できます。</span>
          </div>

          <div class="form-group">
            <form action="">
              <label class="label-form" for="id_block-list__search">
                <input type="text" name="block-list__search" value="" placeholder="会社名で検索"
                       class="form-control account__input-text block-list__search" id="id_block-list__search">
                <span class="account__field-label__icon-search"><i class="icon icon--sicon-search"></i></span>
              </label>
            </form>
          </div>
          <div class="block-list__results">
            <div class="block-list__results-search mscrollbar">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="modalCropBanner" style="z-index: 9999">
    <div class="modal-dialog" style="transform: translate(0,10%);">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title">画像登録</h4>
        </div>
        <div class="modal-body">
          <img src="" id="image-crop" style="max-width: 100%;">
        </div>
        <div class="modal-footer">
          <div class="btn-group pull-left" role="group">
            <button type="button" class="btn btn-default js-zoom-in">
              <span class="glyphicon glyphicon-zoom-in"></span>
            </button>
            <button type="button" class="btn btn-default js-zoom-out">
              <span class="glyphicon glyphicon-zoom-out"></span>
            </button>
          </div>
          <button type="button" class="btn btn-primary js-crop-and-upload js-crop-and-upload-project">登録する</button>
        </div>
      </div>
    </div>
  </div>

{% endif %}
