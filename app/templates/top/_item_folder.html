{% load static %}
{% load util %}

<li class="list-group-item list-sub">
  <span class="hasSub" data-folder-id="{{ folder_parent.pk }}"><i class="glyphicon glyphicon-folder-open"></i>{{ folder_parent.name }}</span>
  {% if folder_parent.pk|check_child_folder:type_message %}
      <i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>
  {% endif %}
</li>
<ul class="list-group expanded">
    {% include 'top/_item_sub_folder.html' with folder_parent=folder_parent type_message=type_message %}
</ul>


