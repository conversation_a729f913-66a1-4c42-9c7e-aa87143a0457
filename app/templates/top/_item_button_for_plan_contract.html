{% load static %}
{% load util %}

{% if message_file_type in 'plan' %}
    {% with file_status=offer_last_plan|get_plan_file_status:message file=message.files.first file_attribute=message|get_file_attribute text_bottom=message|get_file_text_bottom position='pinned'%}
        {% include 'top/_item_message_for_plan.html' %}
    {% endwith %}
{% elif message_file_type in 'contract' %}
    {% with file_status=offer_last_contract|get_contract_file_status:message file=message.files.first file_attribute=message|get_file_attribute text_bottom=message|get_file_text_bottom position='pinned'%}
        {% include 'top/_item_message_for_contract.html' %}
    {% endwith %}
{% elif message_file_type == 'all' %}
    {% for file in message|get_sorted_message_files %}
        {% if file.type_file == '2' %}
            {% with file_status=offer_last_contract|get_contract_file_status:message file_attribute=message|get_file_attribute text_bottom=message|get_file_text_bottom position='pinned' %}
                {% include 'top/_item_message_for_contract.html' %}
            {% endwith %}
        {% elif file.type_file == '5' %}
            {% with file_status=offer_last_plan|get_plan_file_status:message file_attribute=message|get_file_attribute text_bottom=message|get_file_text_bottom position='pinned' %}
                {% include 'top/_item_message_for_plan.html' %}
            {% endwith %}
        {% endif %}
    {% endfor %}
{% elif message_file_type == 'bill' %}
    {% with file_status=offer_last_bill|get_bill_file_status:message file=message.files.first file_attribute=message|get_file_bill_attribute:offer.amount text_bottom=message|get_file_text_bottom position='pinned'%}
        {% if file_status == 'disabled' %}
            {% if user and not user.role == 'master_client' and offer_last_bill == file %}
                {% include 'messenger_file/messenger_file_conponent.html' with className='btn-plan-contract open-edit-bill-modal' file_icon_start='icons/_icon_edit_pencil.html' attribute=file_attribute text_top='書類を確認' text_bottom=text_bottom text_left=file.real_name %}
            {% else %}
                {% include 'messenger_file/messenger_file_conponent.html' with className='btn-plan-contract disable open-file-preview-only' file_icon_start='icons/_icon_document.html' attribute=file_attribute text_top='書類を確認' text_bottom=text_bottom text_left=file.real_name %}
            {% endif %}
        {% elif file_status == 'waiting' %}
            {% if user and not user.role == 'master_client' %}
                {% include 'messenger_file/messenger_file_conponent.html' with className='btn-plan-contract open-edit-bill-modal' file_icon_start='icons/_icon_edit_pencil.html' attribute=file_attribute text_top='請求書を編集' text_bottom=text_bottom text_left=file.real_name %}
            {% else %}
                {% include 'messenger_file/messenger_file_conponent.html' with className='btn-plan-contract open-preview-bill-approve-modal' file_icon_start='icons/_icon_document.html' attribute=file_attribute text_top='支払う' text_bottom=text_bottom text_left=file.real_name text_offer='請求書が届いています！'%}
            {% endif %}
        {% endif %}
    {% endwith %}

{% endif %}

{% block extra_script %}
    <script>
        gsap.from(".btn-plan-contract", {
            opacity: 1.0,
            boxShadow: "0px 0px 32px 0px #009ACE",
            repeat: -1, // 無限に繰り返す
            yoyo: true, // アニメーションを逆再生する
            ease: "power1.inOut", // イージング関数を指定
            duration: 1.0, // アニメーションの期間を指定
        });
    </script>
{% endblock %}