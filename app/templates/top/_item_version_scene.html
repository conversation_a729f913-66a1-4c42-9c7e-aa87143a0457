<div class="version-item" data-scene-id="{{original_scene.pk}}">
    <img src="{% if original_scene.thumbnail %}{{original_scene.thumbnail.url}}{% endif %}" alt="">
    <div class="content-container">
    <div class="version-real-name">{{original_scene.real_name}}</div>
    <div class="version-file-name">{% if original_scene.movie %}{{original_scene.get_file_movie_name}}{% endif %}</div>
    </div>
    <div class="version-action-container">
    <div class="version-action__button-container">
        <span class="member-item__btn-move">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <g clip-path="url(#clip0_20694_7610)">
            <path
                d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z"
                fill="#A7A8A9" />
            </g>
            <defs>
            <clipPath id="clip0_20694_7610">
                <rect width="24" height="24" fill="white" />
            </clipPath>
            </defs>
        </svg>
        </span>
        <span class="member-item__btn-delete">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <g clip-path="url(#clip0_20694_7613)">
            <path
                d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V9C18 7.9 17.1 7 16 7H8C6.9 7 6 7.9 6 9V19ZM18 4H15.5L14.79 3.29C14.61 3.11 14.35 3 14.09 3H9.91C9.65 3 9.39 3.11 9.21 3.29L8.5 4H6C5.45 4 5 4.45 5 5C5 5.55 5.45 6 6 6H18C18.55 6 19 5.55 19 5C19 4.45 18.55 4 18 4Z"
                fill="#A7A8A9" />
            </g>
            <defs>
            <clipPath id="clip0_20694_7613">
                <rect width="24" height="24" fill="white" />
            </clipPath>
            </defs>
        </svg>
        </span>
    </div>
    </div>
</div>
{% if scenes %}
    {% for scene in scenes %}
        <div class="version-item" data-scene-id="{{scene.pk}}">
            <img src="{% if scene.thumbnail %}{{scene.thumbnail.url}}{% endif %}" alt="">
            <div class="content-container">
            <div class="version-real-name">{{scene.real_name}}</div>
            <div class="version-file-name">{% if scene.movie %}{{scene.get_file_movie_name}}{% endif %}</div>
            </div>
            <div class="version-action-container">
            <div class="version-action__button-container">
                <span class="member-item__btn-move">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <g clip-path="url(#clip0_20694_7610)">
                    <path
                        d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z"
                        fill="#A7A8A9" />
                    </g>
                    <defs>
                    <clipPath id="clip0_20694_7610">
                        <rect width="24" height="24" fill="white" />
                    </clipPath>
                    </defs>
                </svg>
                </span>
                <span class="member-item__btn-delete">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <g clip-path="url(#clip0_20694_7613)">
                    <path
                        d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V9C18 7.9 17.1 7 16 7H8C6.9 7 6 7.9 6 9V19ZM18 4H15.5L14.79 3.29C14.61 3.11 14.35 3 14.09 3H9.91C9.65 3 9.39 3.11 9.21 3.29L8.5 4H6C5.45 4 5 4.45 5 5C5 5.55 5.45 6 6 6H18C18.55 6 19 5.55 19 5C19 4.45 18.55 4 18 4Z"
                        fill="#A7A8A9" />
                    </g>
                    <defs>
                    <clipPath id="clip0_20694_7613">
                        <rect width="24" height="24" fill="white" />
                    </clipPath>
                    </defs>
                </svg>
                </span>
            </div>
            </div>
        </div>
    {% endfor %}
{% endif %}
