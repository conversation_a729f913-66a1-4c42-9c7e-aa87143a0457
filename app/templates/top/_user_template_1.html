{% load static %}
{% load util %}
{% load i18n %}

<div class="user-project-template-1 owner-template-1">
    <div class="block-user-project-avatar">
        <img src="{{ owner|get_avatar:'medium' }}"
             alt="" class="user-project-avatar owner-avatar">
            {% if owner in online_users %}
                <span class="material-symbols-rounded user-project-status">circle</span>
            {% endif %}
    </div>
    <div class="item-project-content owner-content">
        <p class="heading-13-spacing u-text-black u-mb0">{{ owner.get_display_name }}</p>
        <p class="bodytext-11 u-line-height-100 u-mb0">{{ member.position|hide_if_empty }}<span class="u-text-light-gray">（責任者）</span></p>
        <p class="bodytext-11 u-line-height-100 u-text-light-gray u-mb0">{% if owner.enterprise %} {{ owner.enterprise }}{% endif %}</p>
    </div>
    <div class="block-user-nav">
        <span class="material-symbols-rounded">more_horiz</span>
    </div>
</div>