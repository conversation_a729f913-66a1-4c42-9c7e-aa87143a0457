{% load static %}
{% load util %}
{% if message|check_exist_message %}
<div class="mmessage-container refactor">
     <div class="mmessage mmessage--sent  {% if message.is_near %}mmessage-near{% endif %} {% if message.resolved %}resolved{% endif %} clicked"
          data-message-id="{{ message.pk }}"
          {% if message.parent %}data-parent-id="{{ message.parent.pk }}"{% endif %}>
          {% include 'top/_item_message_send.html' with message=message user=user type=type first_message=first_message %}
     </div>
</div>
{% endif %}
