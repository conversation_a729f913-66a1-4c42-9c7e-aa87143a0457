{% load util %}
{% load static %}
{% load i18n %}

<div class="member-item-container draggable-member">
    <div class="member-item project-member-invited- sub-member-item" data-user=""
         data-pf="" data-jwt="">
      <div class="member-item-left">
        <div class="member-item__avatar">
            <a href="#" target="_blank">
              <div class="avatar avatar--image avatar--48 avatar--round">
                <div class="avatar-image" style="background-image: url(https://picsum.photos/40)"></div>
              </div>
            </a>
        </div>
        <div class="member-item__info">
          <div class="member-item__name sheading sheading--13">アーティスト名</div>
          <div class="member-item__info-bottom">
              <div class="member-item__role scaption">タイトル</div>
              <div class="member-item__company scaption">組織名</div>
          </div>
        </div>
      </div>
      <div class="member-item-right">
        <span class="member-item-identification">{% trans "Identification" %}</span>
        <span class="member-item-identification">{% trans "NDA" %}</span>
      </div>
    </div>
    <div class="member-item-action">
        <div class="member-item-action__button-container">
            <span class="member-item__btn-move" data-toggle="modal" data-target="">
              <i class="icon icon--sicon-equal"></i>
            </span>
            <span class="member-item__btn-delete" data-toggle="modal" data-target="">
              <i class="icon icon--sicon-trash"></i>
            </span>
        </div>
    </div>
</div>