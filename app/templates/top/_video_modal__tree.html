{% load util %}
{% load static %}
<div class="video-modal__tree modal-custom-scrollbar">
    <ul class="modal-tree-info">
        <li class="root chapter-name hide" data-product-scene-id="{{ product_scene.pk }}">
        </li>
        <li class="root scene-name" data-scene-title-id="{{ original_scene.title.pk }}">
            <span>{{ original_scene.title.title }}</span>
            <div class="scene-edit fa fa-pen"></div>
            <p class="input-field-error hide">データーが正しくありません。</p>
            <div class="exists-title hide" id="exists-title-vals">
                {% for title in titles %}
                    <span class="title">{{ title }}</span>
                {% endfor %}
            </div>
        </li>
        <div class="variation-upload variation-upload--before" data-variation-id="-1">
            <input class="variation-upload__file-input" type="file" accept=".mp4, .x-m4v, .webm, video/*, .pdf, .mp3, .wav">
            <a class="button button--icon button--icon-plus button--round variation-upload__link" href="#"
               role="button"></a>
        </div>
    </ul>
    <ul class="modal-tree first-load" data-variation-order="{{ list_index_bullet }}">
        {% for scene in scenes %}
            <li class="video-variation {% if forloop.first %}variation-active{% endif %}"
                data-variation="{{ forloop.counter0 }}" data-variation-id="{{ scene.pk }}">
                <div class="variation-upload" data-variation="{{ forloop.counter0 }}"
                     data-variation-id="{{ scene.pk }}">
                    <input class="variation-upload__file-input" type="file" accept=".mp4, .x-m4v, .webm, video/*, .pdf, .mp3, .wav">
                    <a class="button button--icon button--icon-plus button--round variation-upload__link"
                       href="#" role="button"></a>
                </div>
                <ul>
                    {% with forloop.counter0 as parent_index %}
                        {% for other_version in scene.other_versions.all %}
                            <li class="video-version left {% if parent_index == 0 and forloop.first %}version-active{% endif %}"
                                data-variation="{{ parent_index }}" data-index="{{ forloop.revcounter }}"
                                data-max-version="{{ scene.other_versions.count|add:1 }}" data-scene-id="{{ other_version.pk }}">
                                <span>{{ other_version.get_file_name }}</span>
                                <div class="comment-edit-button-group">
                                    <button id="edit-comment">
                                        <img src="{% static 'images/edit.svg' %}" alt="">
                                    </button>
                                    <button id="delete-comment" data-scene-id="{{ other_version.pk }}">
                                        <img src="{% static 'images/delete.svg' %}" alt="">
                                    </button>
                                </div>
                                <p class="input-field-error hide">データーが正しくありません。</p>
                            </li>
                        {% endfor %}
                        <li class="video-version left {% if parent_index == 0 and scene.other_versions.count == 0 %}version-active{% endif %}"
                            data-variation="{{ parent_index }}"
                            data-index="0"
                            data-max-version="{{ scene.other_versions.count|add:1 }}" data-scene-id="{{ scene.pk }}">
                            <span>{{ scene.get_file_name }}</span>
                            <div class="comment-edit-button-group">
                                <button id="edit-comment">
                                    <img src="{% static 'images/edit.svg' %}" alt="">
                                </button>
                                <button id="delete-comment" data-scene-id="{{ scene.pk }}">
                                    <img src="{% static 'images/delete.svg' %}" alt="">
                                </button>
                            </div>
                            <p class="input-field-error hide">データーが正しくありません。</p>
                        </li>
                    {% endwith %}
                </ul>
                <div class="variation-upload variation-upload--after" data-variation="{{ forloop.counter0 }}"
                     data-variation-id="-1">
                    <input class="variation-upload__file-input" type="file" accept=".mp4, .x-m4v, .webm, video/*, .pdf, .mp3, .wav">
                    <a class="button button--icon button--icon-plus button--round variation-upload__link"
                       href="#" role="button"></a>
                </div>
            </li>
        {% endfor %}
    </ul>
</div>
