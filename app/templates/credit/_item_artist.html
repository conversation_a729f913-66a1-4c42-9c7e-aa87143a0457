<div class="modal-staff-credit-section-artist" data-item-artist-id="{{ item_artist.pk }}">

  {% if language == 'jp' %}
    <div class="modal-staff-credit-section-artist--title bodytext--13">{{ item_artist.position|default_if_none:"" }}</div>
    <div class="modal-staff-credit-section-artist--name bodytext--13 {% if item_artist.link_profile %}link-to-profile{% endif %}"
            {% if item_artist.link_profile and item_artist.project_artist %}
              {% with artist=item_artist.project_artist.user %} data-href="
                      {% if artist.user_creator.first.slug %}{% url 'app:creator_info' slug=artist.user_creator.first.slug %}{% else %}{% url 'accounts:accounts_creator' artist.pk %}{% endif %}"
              {% endwith %}{% endif %}>{{ item_artist.artist_name|default_if_none:"" }}</div>

  {% else %}
    <div class="modal-staff-credit-section-artist--title bodytext--13">{{ item_artist.title|default_if_none:"" }}</div>
    <div class="modal-staff-credit-section-artist--name bodytext--13 {% if item_artist.link_profile %}link-to-profile{% endif %}"
         {% if item_artist.link_profile  and item_artist.project_artist%}
         {% with artist=item_artist.project_artist.user %}data-href="
                 {% if artist.user_creator.first.slug %}{% url 'app:creator_info' slug=artist.user_creator.first.slug %}{% else %}{% url 'accounts:accounts_creator' artist.pk %}{% endif %}"{% endwith %}{% endif %}>{{ item_artist.artist_name_en|default_if_none:"" }}</div>

  {% endif %}
</div>
