{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}
{% load compress %}

{% block extrahead %}
  <script>
    $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# article: http://ogp.me/ns/article#')
  </script>
  <meta property="og:url" content="https://soremo.jp/" />
  <meta property="og:type" content="article" />
  <meta property="og:title" content="{{ topic.title }}" />
  <meta property="og:description" content="{{ topic.overview }}" />
  <meta property="og:site_name" content="{{ topic.title }}" />
  <meta property="og:image" content="{% if topic.thumbnail %}{{ topic.thumbnail.url }}{% else %}{{ request.scheme }}://{{ request.META.HTTP_HOST }}{% static 'images/OGP_logo.png' %}{% endif %}" />
  <meta property="og:image:width" content="400" />
  <meta property="og:image:height" content="400" />
{% endblock extrahead %}

{% block content %}
<link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css"
        integrity="sha512-aOG0c6nPNzGk+5zjwyJaoRUgCdOrfSDhmMID2u4+OIslr0GjpLKo7Xm0Ao3xmpM4T8AmIouRkqwj1nrdVsLKEQ=="
        crossorigin="anonymous"/>
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
  {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  {% endcompress %}
  {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/creator_style.css' %}"/>
  <link href="{% static 'css/order_step.css' %}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{% static 'css/topic_style.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/components/utils.css' %}"/>
  <link rel="stylesheet" href="{% static 'css/flatpickr_soremo.css' %}">
        <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}" />
  {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
  <script src="{% static 'js/flatpickr_soremo.js' %}"></script>
  {% with topic=topics.0 %}
  <main style="background: #ffffff;">
    <div class="container section-topic-detail" data-topic="{{ topic.pk }}">
      <div class="topic-container">
        <div class="topic-container-content topic-container-content__left mscrollbar">
          <div class="topic-content__media">
            {% if topic.video %}
              <video width="100%" height="100%"
                     poster="{% if topic.thumbnail %}{{ topic.thumbnail.url }}{% endif %}"
                     preload="auto">
                <source src="{{ topic.video.url }}"
                        type="video/mp4"/>
              </video>
            {% else %}
              <img src="{% if topic.thumbnail %}{{ topic.thumbnail.url }}{% endif %}" alt="">
            {% endif %}
          </div>
          <div class="topic-content__title">
            <span>{{ topic.title }}</span>
          </div>

          {#            Category #}
          <div class="topic-content__sub-title">
            {% for category in topic.categories.all %}
              <span>{{ category.category_name }}</span>
            {% endfor %}
          </div>

          {#      <div class="topic-content__description">#}
          {#        {{ topic.description }}#}
          {#      </div>#}
          <div class="topic-content__description">
            <div>{{ topic.overview }}</div>
            <div>{{ topic.description }}</div>
          </div>
          <div class="topic-content__action-dowload">
            {% if topic.file %}
              <div class="btn btn--secondary btn-download-topic-file">{% trans "Download materials" %}</div>
            {% endif %}
          </div>
          <div class="topic-content__hashtag">
            {% for tag in topic.tags.all %}
              <span>#{{ tag.tag_name }}</span>
            {% endfor %}
          </div>
        </div>
        <div class="topic-container-content topic-container-content__right mscrollbar">
          {#          <form>#}

          {% for selection in topic.selections.all %}
            <div class="section-container" data-selection="{{ selection.pk }}">
              <div class="section-content__title">
                {{ selection.title }}
              </div>
              <div class="section-content__sub-title">
                {{ selection.description }}
              </div>

              {#              Show sale content #}

              <div class="section-content__list-media mscrollbar">
                {% for sale_content_selection in selection.salecontentselection_set.all %}
                  {% with sale_content_selection.sale_content as sale_content %}
                    {% with sale_content|get_artist_of_sale_content as artist %}
                      {% for album_variation in sale_content|get_audios:user %}
                        {% with album_variation|get_file_type:user as file_type %}
                          <div class="list-circle__component {% if file_type != 'audio' %}btn-preview-album{% endif %}"
                               data-type="{{ sale_content.last_published_version.content_type }}"
                               data-artist="{{ artist.get_display_name }}"
                               process-data="0"
                               process-status="pause">
                            <div class="section-content_sub-meida gallery__item list-circle__sub-component"
                                 data-type="{{ sale_content.last_published_version.content_type }}"
                                 style="{{ sale_content_selection|get_thumbnail_sale_content_selection }}"
                                 data-artist="{{ artist.get_display_name }}" data-sale="{{ sale_content.pk }}">
                              <div class="list-search__item-playpause opacity-0"></div>
                              <audio preload="none"
                                     class="gallery__item-banner"
                                     src="{{ album_variation|get_audio:user }}"
                                     data-name="{{ sale_content|get_title_sale_content:user }}"
                                     data-album="{{ album_variation.pk }}"
                                     data-file-type="{{ file_type }}"></audio>
                            </div>

                            <div class="section-content__title-artist hide">
                              <div class="item-sale-content-name">{{ sale_content|get_title_sale_content:user }}</div>
                              <div class="item-artist-name">{{ artist.get_display_name }}</div>
                            </div>
                          </div>
                        {% endwith %}
                      {% endfor %}
                    {% endwith %}
                  {% endwith %}
                {% endfor %}
              </div>

              {#              Show selections#}

              <div class="section-content__radio {% if not create_offer %}disabledbutton{% endif %}">
                {% for option in selection.get_selection_content %}
                  <label class="input-radio">
                    <input type="radio" name="content-{{ selection.pk }}"
                           value=""
                           index="" required="true"
                           {% if option.status == 'on' %}checked{% endif %}/><span
                          class="detail-radio">{{ option.detail }}</span>
                    <div class="check-mark"></div>
                  </label>
                {% endfor %}
              </div>

              {#                Show toggle #}
              <div>
                {% for option in selection.get_toggle_content %}
                  <div class="section-content__toggle {% if not create_offer %}disabledbutton{% endif %}">
                    <div class="form-check custom-switch">
                      <label class="form-check-label">
                        <div class="form-check-group">
                                        <span>
                                            <input class="form-check-input switch-checkbox switch-toggle-account"
                                                   type="checkbox" name="switch-toggle-account"
                                                   {% if option.status == 'on' %}checked{% endif %}><span
                                                class="switch-slider"></span>
                                        </span>
                        </div>
                        <span style="margin-left: 10px;" class="toggle-text-topic">{{ option.detail }}</span>
                      </label>
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          {% endfor %}

          {#          </form>#}
        </div>
      </div>
      <div class="topic-action">
        <div type="button" class="btn btn--tertiary button-back-old-page" data-href="{% if is_create_project %}{% url 'app:direct_create_offer' %}{% elif topic.user %}{{ topic.user.get_link_profile }}{% else %}{% url 'app:creator_index' %}{% endif %}">{% trans "return" %}</div>
        {% if create_offer %}
          <div class="btn btn--primary button--next-action-create-project " id="btn__next_1">{% trans "to the next" %}</div>
        {% endif %}
      </div>

    </div>

    <div class="container-wrap section-create-project hide" style="background: #ffffff;">
      <div class="container">
        <div class="order-step__main">
          <div class="order-step__heading">
            {% comment %} <h3 class="heading-24">{% trans "Consult a new project" %}</h3>
            <div class="order-step__heading-description">
              <p class="caption--11">{% trans "heading create new project" %}</p>
              <p class="caption--11"><a href="tel:03-6457-1780"><span
                      class="text--blue">03-6457-1780 </span></a>{% trans "You can also contact us by phone." %}</p>
            </div> {% endcomment %}
          </div>

          <div class="order-step__content" style="margin-bottom: 180px;">
            <form class="order-step__form row" id="form-order-step" method="post" action=""
                  enctype="multipart/form-data" data-add-by-sale="False" data-hide-step-3="False">
              {% csrf_token %}
              <!-- Step 1 -->
              <!-- End step 1 -->

              <!-- Step 2 -->
              <div class="order-step__form-tab step-2">
                <div class="order-step__form-heading">
                  <h3 class="heading--18">{% trans "Contents" %}
                    {% comment %} <span class="caption--11">(2/3)</span> {% endcomment %}
                  </h3>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <label for="id_message">
                        <div class="order-step__form-label heading--13">{% trans "message" %}<span
                                class="blue-label--8">{% trans "required" %}</span></div>
                      </label>
                      <div class="form-textarea">
                        {{ form.description }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <label for="id_message">
                        <div class="order-step__form-label heading--13">{% trans "label document" %}<span
                                class="grey-label--8">{% trans "any" %}</span></div>
                      </label>
                      <div class="order-step_upload-file mattach mattach-form">
                        <div class="mcomment-attached">
                          <div class="mattach-preview-container mattach-preview-container-form">
                            <div class="mattach-previews mattach-previews-form collection">
                              <div class="mattach-template mattach-template-form collection-item item-template">
                                <div class="mattach-info" data-dz-thumbnail="">
                                  <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                            class="icon icon--sicon-close"></i>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div id="uploadFile" class="fallback dropzone">
                        </div>
                        <p class="order-step__field-text">{% trans "You can send the entire folder by dragging and dropping." %}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_budget">
                        <div class="order-step__form-label heading--13">{% trans "Hope budget" %}<span
                                class="grey-label--8">{% trans "any" %}</span>
                        </div>
                      </label>
                      <div class="order-step__form-budget">
                        <input type="text" name="budget" placeholder="1,000,000"
                               class="form-control order-step__input-text"
                               maxlength="19" id="id_budget" min="0">
                        <span class="bodytext--13">{% trans "yen without tax" %}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__submit order-step__action">
                  <div class="container">
                  {% buttons %}
                    <button class="btn btn--tertiary btn-previous" id="btn__previous_2">{% trans "return" %}</button>
                    <button class="btn btn--primary btn-next" id="btn__next_2">{% trans "to the next" %}</button>
                  {% endbuttons %}
                  </div>
                </div>
              </div>
              <!-- End step 2 -->

              <!-- Step 3 -->
              <div class="order-step__form-tab">
                <div class="order-step__form-heading">
                  <h3 class="heading--18">{% trans "Conditions" %}
                    {% comment %} <span class="caption--11">(3/3)</span> {% endcomment %}
                  </h3>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="deadline_date">
                        <div class="order-step__form-label heading--13">{% trans "Production period" %}<span
                                class="grey-label--8">{% trans "any" %}</span></div>
                      </label>
                      <div class="sform-group__input-group">
                        <input class="datePicker js-daterangepicker"
                               id="deadline_date" type="text" placeholder="yyyy/mm/dd ~ yyyy/mm/dd" autocomplete="off">
                        <label class="sform-group__append" for="deadline_date">
                          <i class="icon icon--undefined"></i>
                          <i class="icon icon--sicon-calendar"></i>
                        </label>
                      </div>
                    </div>
                    {{ form.start_time|append_attr:"style:display:none;" }}
                    {{ form.end_time|append_attr:"style:display:none;" }}
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-4 form-group">
                      <label for="id_dob">
                        <div class="order-step__form-label heading--13">{% trans "deadline" %}<span
                                class="grey-label--8">{% trans "any" %}</span></div>
                      </label>
                      <div class="form-row row form-row__mobile">
                        <div class="col-sm-8 form-row__mobile-date">
                          <div class="sform-group__input-group">
                            {{ form.deadline|append_attr:"style:display:none;" }}
                            <input type="text" name="deadline_1" placeholder="yyyy/mm/dd"
                                   class="mcalendar mcalendar--small form-control" id="id_deadline_1">
                            <label class="sform-group__append" for="id_deadline_1">
                              <i class="icon icon--undefined"></i>
                              <i class="icon icon--sicon-calendar"></i>
                            </label>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <select class="input-time" name="hours" id="id_time" style="background: #FFFFFF;">
                            <option value="00:00" selected>00:00</option>
                            <option value="01:00">01:00</option>
                            <option value="02:00">02:00</option>
                            <option value="03:00">03:00</option>
                            <option value="04:00">04:00</option>
                            <option value="05:00">05:00</option>
                            <option value="06:00">06:00</option>
                            <option value="07:00">07:00</option>
                            <option value="08:00">08:00</option>
                            <option value="09:00">09:00</option>
                            <option value="10:00">10:00</option>
                            <option value="11:00">11:00</option>
                            <option value="12:00">12:00</option>
                            <option value="13:00">13:00</option>
                            <option value="14:00">14:00</option>
                            <option value="15:00">15:00</option>
                            <option value="16:00">16:00</option>
                            <option value="17:00">17:00</option>
                            <option value="18:00">18:00</option>
                            <option value="19:00">19:00</option>
                            <option value="20:00">20:00</option>
                            <option value="21:00">21:00</option>
                            <option value="22:00">22:00</option>
                            <option value="23:00">23:00</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <div class="order-step__form-label heading--13">{% trans "Handling of rights" %}</div>
                      <div class="order-step__sub-group">
                        {% for choice in form.contract_type %}
                          <div class="order-step__form-multi">
                            <label class="input-radio">
                              <input type="radio" name="{{ choice.data.name }}"
                                     {% if choice.data.selected %}checked{% endif %}
                                     value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                     required="{{ choice.data.attrs.required }}"
                                     id="{{ choice.data.attrs.id }}"
                                     data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                              <div class="check-mark"></div>
                            </label>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <div class="order-step__form-label heading--13">{% trans "contract" %}</div>
                      <div class="order-step__sub-group">
                        {% for choice in form.ownership_type %}
                          <div class="order-step__form-multi">
                            <label class="input-radio">
                              <input type="radio" name="{{ choice.data.name }}"
                                     {% if choice.data.selected %}checked{% endif %}
                                     value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                     required="{{ choice.data.attrs.required }}"
                                     id="{{ choice.data.attrs.id }}"
                                     data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                              <div class="check-mark"></div>
                            </label>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__form-group">
                  <div class="form-row row">
                    <div class="col-sm-12 form-group">
                      <div class="order-step__form-label heading--13">{% trans "Achievements released" %}</div>
                      <p class="caption--11">{% trans "In the case of a work whose production results can be disclosed to a third party, the range of participating artists will expand." %}</p>
                      <div class="order-step__sub-group">
                        {% for choice in form.disclosure_rule %}
                          <div class="order-step__form-multi">
                            <label class="input-radio">
                              <input type="radio" name="{{ choice.data.name }}"
                                     {% if choice.data.selected %}checked{% endif %}
                                     value="{{ choice.data.value }}" index="{{ choice.data.index }}"
                                     required="{{ choice.data.attrs.required }}"
                                     id="{{ choice.data.attrs.id }}"
                                     data-value="{{ choice.data.label }}"/>{{ choice.data.label }}
                              <div class="check-mark"></div>
                            </label>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="order-step__submit order-step__action">
                  <div class="container">
                  {% buttons %}
                    <button class="btn btn--tertiary btn-previous" id="btn__previous_3">{% trans "return" %}</button>
                    {% if not user.is_authenticated %}
                      <button class="btn btn--primary btn-next" id="btn__next_3">{% trans "to the next" %}</button>
                    {% else %}
                      <button class="btn btn--primary btn-next"
                              id="submit-contact">{% trans "Next (confirmation of contents)" %}</button>
                    {% endif %}
                  {% endbuttons %}
                  </div>
                </div>
              </div>
              <!-- End step 3 -->
              {% if not user.is_authenticated %}
                <!-- Step 4 -->
                <div class="order-step__form-tab order-step__form-tab-4">
                  <div class="order-step__form-heading">
                    <h3 class="heading--18">{% trans "Contact information" %}</h3>
                  </div>
                  <div style="display: none">
                    {{ form_contract.purpose|append_attr:"style:display:none;" }}
                  </div>
                  <div class="order-step__form-group">
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_fullname">
                          <div class="order-step__form-label heading--13">{% trans "Full name" %}<span
                                  class="blue-label--8">{% trans "required" %}</span></div>
                        </label>
                        {{ form_contract.fullname }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_email">
                          <div class="order-step__form-label heading--13">{% trans "Email address" %}<span
                                  class="blue-label--8">{% trans "required" %}</span>
                          </div>
                        </label>
                        {{ form_contract.email }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_email_confirm">
                          <div class="order-step__form-label heading--13">{% trans "Email address (confirmation)" %}<span
                                  class="blue-label--8">{% trans "required" %}</span>
                          </div>
                        </label>
                        {{ form_contract.email_confirm }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_job_type">
                          <div class="order-step__form-label heading--13">{% trans "job type" %}<span
                                  class="grey-label--8">{% trans "any" %}</span>
                          </div>
                        </label>
                        {{ form_contract.job_type }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_enterprise">
                          <div class="order-step__form-label heading--13">{% trans "company name" %}<span
                                  class="grey-label--8">{% trans "any" %}</span></div>
                        </label>
                        {{ form_contract.enterprise }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_company_url">
                          <div class="order-step__form-label heading--13">{% trans "website" %}<span
                                  class="grey-label--8">{% trans "any" %}</span>
                          </div>
                        </label>
                        {{ form_contract.company_url }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-4 form-group">
                        <label for="id_phone">
                          <div class="order-step__form-label heading--13">{% trans "phone number" %}<span
                                  class="grey-label--8">{% trans "any" %}</span></div>
                        </label>
                        {{ form_contract.phone }}
                      </div>
                    </div>
                    <div class="form-row row">
                      <div class="col-sm-12 form-group">
                        <div class="order-step__form-label heading--13">{% trans "Your preferred contact method" %}
                        </div>
                        <div class="order-step__sub-group">
                          {% for choice in form_contract.contact_channel %}

                            <div class="order-step__form-multi">
                              <label class="input-radio">
                                <input type="radio" name="{{ choice.data.name }}"
                                       {% if choice.data.selected %}checked{% endif %}
                                       value={{ choice.data.value }} index={{ choice.data.index }}
                                       required={{ choice.data.attrs.required }} data-value="{{ choice.data.label }}"
                                       id="{{ choice.data.attrs.id }}"/>{{ choice.data.label }}
                                <div class="check-mark"></div>
                              </label>
                            </div>
                          {% endfor %}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="order-step__submit order-step__action">
                    <div class="container">
                    {% buttons %}
                      <button class="btn btn--tertiary btn-previous" id="btn__previous_4">{% trans "return" %}</button>
                      <button class="btn btn--primary btn-next"
                              id="submit-contact">{% trans "Next (confirmation of contents)" %}</button>
                    {% endbuttons %}
                    </div>
                  </div>
                </div>
                <!-- End step 4 -->
              {% endif %}
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="upload-final-product-file upload-button-wrapper">
      <p>{% trans "Uploading" %}</p>
      <div class="fill">
        <div class="process"></div>
      </div>
      <div class="fa fa-check"></div>
    </div>

    {% include 'messenger/_modal_confirm_step.html' %}
    {% include 'messenger/_modal_open_file.html' %}

  </main>
  {% endwith %}
{#  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>#}
{#  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>#}
{#  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>#}
{#  <script src="{% url 'javascript-catalog' %}"></script>#}
{#  <script src="{% static 'js/topic_action.js' %}"></script>#}
{% endblock %}


{% block extra_script %}
  <script>
      let csrf = '{% csrf_token %}';
  </script>
  <script type="text/javascript"> window.CSRF_TOKEN = "{{ csrf_token }}";</script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

  <script src="{% url 'javascript-catalog' %}"></script>
{% compress js inline %}
  <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/main.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/common_variable.js' %}"></script>
  <script src="{% static 'js/upload_file.js' %}"></script>
  <script src="{% static 'js/validate_contract.js' %}"></script>
  <script src="{% static 'js/topic_detail.js' %}"></script>
  <script src="{% static 'js/order_step.js' %}"></script>
  <script src="{% static 'js/album_preview.js' %}"></script>
  <script src="{% static 'js/utils.js' %}"></script>
    {% endcompress %}
   <script src="{% static 'js/topic_action.js' %}"></script>
{% endblock %}
