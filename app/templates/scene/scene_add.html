{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}


{% block extrahead %}
    <style>
    .create_scene{
        padding-top: 70px;
    }
    </style>
{% endblock %}

{% block content %}
    <main class="container" style="min-height: 55vh">
        <div class="m10 row create_scene">
            <div class="col-xs-6">
                <form method="post" action="{% url 'app:product_scene_create' %}" enctype="multipart/form-data">
                    {% csrf_token %}
                    {% bootstrap_form form %}
                    {% buttons %}
                        <input type="submit" value="登録" class="btn btn-primary add_scene" />
                    {% endbuttons %}
                </form>
            </div>
            {{ request.product_id }}
        </div>
    </main>
    <script>
        $(document).ready(function(){
            $('.add_scene').on('click', function(event) {
                if ($('#id_name').val().trim() === "") {
                    alert("シーン名のフォーマットが正しくありません。");
                    return false;
                }
                else {
                    $(this).prop('disabled', true);
                    $(this).parents('form').submit();
                }
            });
        });
    </script>
{% endblock content %}