{% extends 'base_nofooter.html' %}
{% load static %}
{% load util compress %}
{% load user_agents %}

{% block extrahead %}
{% compress css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/video_modal.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
{% endcompress %}
{% endblock %}

{% block content %}
    <style>
        .share-video-container {
            margin-top: 80px;
        }

        .video-item-comment {
            max-height: calc(100vh - 80px);
            overflow: hidden;
        }

        @media screen and (max-width: 767px) {
            .video-item-comment {
                max-height: none;

            }
        }

        .video-item-component video {
          margin: 50px auto 0;
        }
    </style>
    <div class="container share-video-container">
        <div class="project-video-item show-comment">
            <div class="video-item-wrap video-item-wrap-noshare-comment">
                {% for scene in scenes %}
                    <div class="video-item-list {% if forloop.counter0 == 0 %}active{% else %}hide{% endif %}"
                         data-variation-id="{{ scene.pk }}" data-index="{{ forloop.counter0 }}">
                            <div style="flex-grow: 1">
                              {% include 'top/_video_item_component.html' with scene=scene scene_count=scenes.count is_update=True preload="auto" role='admin' can_share_link=True is_deleted=True %}
                            </div>
                    </div>
                {% endfor %}
                {% if scenes.count >= 1 %}
                    <div class="video-item-control">
                        <div class="video-item-bullets-wrap">
                            {% if role == 'admin' %}
                                <a href="#video-modal" class="open-video-modal">
                                    <img src="{% static 'images/icon-settings.svg' %}" class="video-upload" alt=""
                                         width="10px">
                                </a>
                            {% endif %}
                            <div class="video-item-bullets">
                                {% if scenes.count > 1 %}
                                    <div class="video-item-bullet-prev disable"
                                         data-current_index="0"></div>
                                    <div class="video-item-bullet-list">
                                        {% for scene in scenes %}
                                            <div class="video-item-bullet {% if forloop.counter0 == 0 %}active{% endif %}"
                                                 data-index="{{ forloop.counter0 }}"></div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="video-item-thumbnails">
                            <div class="video-item-thumbnail-list">
                                {% for scene in scenes %}
                                    {% if scene.other_versions.count > 0 %}
                                        <div class="video-item-thumbnail {% if forloop.counter0 == 0 %}active{% endif %}"
                                             data-index="{{ forloop.counter0 }}"
                                             data-id="{{ scene.other_versions.first.pk }}">
                                            <video class="active" width="100%" height="auto"
                                                   poster="{{ scene.other_versions.first|get_thumbnail }}"
                                                   preload="none">
                                            </video>
                                            <div class="variation-name">{{ scene.other_versions.first.get_file_name }}</div>
                                        </div>
                                    {% else %}
                                        <div class="video-item-thumbnail {% if forloop.counter0 == 0 %}active{% endif %}"
                                             data-index="{{ forloop.counter0 }}"
                                             data-id="{{ scene.pk }}">
                                            <video class="active" width="100%" height="auto"
                                                   poster="{{ scene|get_thumbnail }}"
                                                   preload="none">
                                            </video>
                                            <div class="variation-name">{{ scene.get_file_name }}</div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% if scenes.count > 1 %}
                            <div class="video-item-collapse">
                                <div class="video-item-collapse-button"></div>
                            </div>
                        {% endif %}
                    </div>
                {% else %}
                    <br>
                {% endif %}
            </div>
        </div>
    </div>
    {% compress js inline %}
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/main.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/utils.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/top_page.js' %}"></script>
    <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    <script>
      $(document).ready(function () {
        resizeVideo();

        if ('{{ request|is_pc }}' === 'True') {
          $('.video-item-comment').mCustomScrollbar({
            axis: 'y',
            theme: 'minimal-dark',
            mouseWheelPixels: 50,
            scrollEasing: 'linear',
            scrollInertia: 0
          });
        }

        $('input#order-monthy').off().on('change', function () {
          let video_item_comment = $(this).parents('.video-item-comment');
          if ($(this).is(':checked')) {
            video_item_comment.find('.resolved').removeClass('hide').hide(0).show(300);
          } else {
            video_item_comment.find('.resolved').hide(300)
          }
        });
        $('video').attr('controls', true);

        let current_length = wavesurfers.length;
        $('.video-comment-audio-wave').each(function (i, item) {
          if (!$(item).find('wave').length && !wavesurfers[i + current_length]) {
            var audio_url = $(this).data('audio');

            var wavesurfer = WaveSurfer.create({
              container: item,
              waveColor: '#a7a8a9',
              progressColor: '#36aac4',
              cursorColor: 'rgba(0,157,196,0.29)',
              barWidth: 3,
              barRadius: 3,
              cursorWidth: 3,
              barGap: 3,
              mediaControls: false,
              height: 50,
              responsive: true,
              hideScrollbar: true
            });

            wavesurfers[current_length + i] = wavesurfer;
              let dom_container = $(item).parents(".video-comment-item-reply");
              if (dom_container.length < 1) {
                  dom_container = $(item).parents(".video-item-component-content-video");
              }
              dom_container.attr('data-wavesurfer', current_length + i);


            wavesurfer.backend.peaks = [0.0218, 0.1183, 0.2165, 0.1198, 0.2137, 0.2888, 0.2313, 0.15, 0.2542, 0.2538,
              0.2358, 0.3195, 0.1591, 0.4599, 0.2742, 0.5447, 0.5328, 0.6878, 0.1988, 0.2645, 0.1218, 0.4005, 0.2828,
              0.2051, 0.1664, 0.1181, 0.1621, 0.2966, 0.189, 0.246, 0.2445, 0.4621, 0.1618, 0.489, 0.2354, 0.5561,
              0.1638, 0.2799, 0.0923, 0.1659, 0.1675, 0.1268, 0.0984, 0.0997, 0.1248, 0.1495, 0.1431, 0.3236, 0.1755,
              0.1183, 0.1349, 0.3018, 0.1109, 0.7833, 0.8813, 0.5422, 0.3961, 0.1191, 0.0791, 0.0631, 0.0315, 0.0157,
              0.0166, 0.0108];
            wavesurfer.load(audio_url, wavesurfer.backend.peaks);

            $(this).siblings('.video-pin-time').on('click', function () {
              if ($(this).siblings(".video-comment-audio-wave").length !== 0) {
                let scene_id = $(this).parents('.video-comment-content').data('scene-id');
                let pin_time = $(this).parent().find('.video-pin-start');
                let target_pin = pin_time.parents('.video-pin-time');
                if (pin_time.length === 0) {
                  let is_play = false;
                  let play_pause = $(this).siblings(".video-comment-audio-wave").siblings('.video-pin-time');
                  $('video').each((i, e) => e.pause());
                  $('.video-pin-time').each(function () {
                    $(this).removeClass('playing')
                  });
                  for (i = 0; i < wavesurfers.length; i++) {
                    if (wavesurfers[i]) {
                      if (wavesurfers[i].isPlaying()) {
                        if (wavesurfers[i] === wavesurfer) {
                          is_play = true;
                          console.log(is_play)
                        }
                        wavesurfers[i].playPause();
                      }
                    }
                  }
                  if (!is_play) {
                    play_pause.addClass('playing');
                    wavesurfer.play();
                    wavesurfer.on('pause', function () {
                      if (wavesurfer.getDuration() === wavesurfer.getCurrentTime()) {
                        play_pause.removeClass('playing');
                      }
                    })
                  }
                } else {
                  let start_time = TimeToSeconds(pin_time.text());
                  if (isNaN(start_time)) {
                    start_time = 0;
                  }
                  let video_item_list = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-list');
                  let video_item_component = video_item_list.find('.video-item-component[data-scene-id=' + scene_id + ']').eq(0);
                  let video = video_item_component.find('.video-item-component-content video').get(0);
                    if (video) {
                        playPinWavesurfer(wavesurfer, video, start_time, target_pin);
                    } else {
                        let audio = video_item_component.find('.video-item-component-content wave').get(0);
                        let scene_pin = video_item_component.find('.video-item-component-content .video-item-component-content-video .pin-time-audio');
                        let scene_index = parseInt(video_item_component.find('.video-item-component-content .video-item-component-content-video').attr('data-wavesurfer'));
                        let scene_wavesurfer = wavesurfers[scene_index];
                        if (audio) {
                            playWavesurferWavesurfer(scene_wavesurfer, wavesurfer, start_time, target_pin, scene_pin);
                        }
                    }
                }
              }
            });
          }
        });

        let target = $(document);
        target.on('click', '.video-pin-time:not(.pin-time-audio)', function () {
          $('audio').each((i, e) => e.pause());
          let scene_id = $(this).parents('.video-comment-content').data('scene-id');
          if (scene_id !== 'None') {
            let pin_time = $(this).parent().find('.video-pin-start');
            let target_pin = pin_time.parents('.video-pin-time');
            let start_time = TimeToSeconds(pin_time.text());
            if (isNaN(start_time)) {
              start_time = 0;
            }
            let video_item_list = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-list');
            let video_item_control = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-control');
            let video_item_thumbnail_list = video_item_control.find('.video-item-thumbnail-list');
            let video_item_bullet_list = video_item_control.find('.video-item-bullet-list');
            //remove active
            video_item_list.find('.video-item-component').removeClass('active');
            video_item_thumbnail_list.find('.video-item-thumbnail').removeClass('active');
            video_item_bullet_list.find('.video-item-bullet').removeClass('active');
            //find and active true element
            let video_item_component = video_item_list.find('.video-item-component[data-scene-id=' + scene_id + ']').eq(0);
            let index = video_item_component.parents('.video-item-list').data('index');
            let variation_id = video_item_component.parents('.video-item-list').data('variation-id');
            let video_item_thumbnail = video_item_thumbnail_list.find('.video-item-thumbnail[data-index=' + index + ']').eq(0);

            let video_item_bullet = video_item_bullet_list.find('.video-item-bullet[data-index=' + index + ']').eq(0);
            if (video_item_component) {
              video_item_component.addClass('active');
              video_item_thumbnail.addClass('active');
              video_item_bullet.addClass('active');
            }
            //active true variation
            video_item_component.parents('.video-item-list').siblings('.video-item-list').removeClass('active');
            video_item_component.parents('.video-item-list').siblings('.video-item-list').addClass('hide');
            video_item_component.parents('.video-item-list').removeClass('hide').addClass('active');
            //update true video_item_bullet_list
            let max_index = video_item_bullet_list.find('.video-item-bullet').length - 1;
            let current_index = video_item_thumbnail.data('index');
            if (current_index <= 0) {
              $(this).parents('.project-video-item').find('.video-item-bullet-prev').addClass('disable');
              $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').attr('data-current_index', 0);
            } else if (current_index >= max_index) {
              $(this).parents('.project-video-item').find('.video-item-bullet-next').addClass('disable');
              $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').attr('data-current_index', max_index);
            } else {
              $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').removeClass('disable').attr('data-current_index', current_index);
            }
            let video = video_item_component.find('.video-item-component-content video').get(0);
            if ($(this).siblings(".video-comment-audio-wave").length === 0) {
              if (target_pin.hasClass('playing')) {
                stopVideo(video)
              } else {
                setTimeout(function () {
                  target_pin.addClass('playing');
                }, 300);
                playVideo(video, start_time);
                $(video).on('pause', function () {
                  target_pin.removeClass('playing');
                })
              }
            }
            let current_version_index = video_item_component.index();
            let max_version_index = video_item_component.siblings().length + 1;
            if (current_version_index === 0) {
              video_item_component.find('.version-tag').attr('data-content', '');
              video_item_component.find('.version-tag').css('background', '#009ace');
              video_item_component.find('video').removeClass('gray-scale');
            } else {
              video_item_component.find('.version-tag').css('background', '#53565A');
              video_item_component.find('video').addClass('gray-scale');
            }
          }
        });

        target.on('click', '.version-tag', function () {
          handleClickVersion($(this));
        });

        let max_index = $('.video-item-bullet').length - 1;
        target.on('click', '.video-item-bullet', function() {
        })

        initCollapseButton($('.project-video-item'));
        initThumbnailClick($('.project-video-item'));

        target.on('click', '.fa-download, .comment__download-icon-down', (e) => generateDownloadLink(e))

        preloadVideo()
      });

      function resizeVideo() {
        $('video').on('loadeddata', function () {
          let e = $(this)[0];
          let ratio = e.videoWidth / e.videoHeight * 100 / 1.5;
          e.style.width = ratio.toString() + '%';
        })
      }

      function preloadVideo() {
        $('.video-item-list:not(.hide) video:in-viewport').each(function (i, e) {
          if (e.preload !== 'auto') {
            e.preload = 'auto';
          }
        })
      }
    </script>
    {% endcompress %}
{% endblock %}
