{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static compress %}
{% load user_agents %}

{% block extrahead %}
    <script>
      $('html').attr('prefix', 'og: http://ogp.me/ns# fb: http://ogp.me/ns/ fb# article: http://ogp.me/ns/article#')
    </script>
    <meta property="og:url" content="https://soremo.jp" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="{{ scene.title.product_scene.name }} > {{ scene.title.title }}" />
    <meta property="og:description" content="{% if share_comment %}{{ comments.last.comment }}{% endif %}" />
    <meta property="og:site_name" content="{% if project.code_name %}{{ project.code_name }}{% else %}{{ project.name }}{% endif %}"/>
    <meta property="og:image" content="{{ scene.title.last_version|get_thumbnail }}" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/video_modal.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/uploading-button.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/project_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/modal_expired.css' %}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail_share.css' %}"/>
    {% endcompress %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/css/splide.min.css">

{% endblock %}

{% block content %}
  <style>
    #modal-image-popup, #modal-video-popup, #modal-document-popup {
      top: 40px
    }

    #modal-image-popup .modal-content, #modal-video-popup .modal-content, #modal-document-popup.modal-content {
      max-width: 100vh;
      min-height: auto;
    }
  .pdf-component {
    margin-top: 0;
  }
  </style>
    <main class="owner-top {% if not request|is_pc %} on-mobile{% endif %} {% if has_variations %} has-variations{% endif %} scene-style scene-detail-share">
        <div class="container container-new">
            <div class="new-video-menu">
                <div class="project-list">
                    <div class="project-item active {% if not request|is_pc %} on-mobile{% endif %}"
                         data-project-id="{{ project.pk }}"
                         data-unresolved-comment="{{ project.product_owner_comments.count }}" data-page="top_page">
                      <div class="popover-overlay"></div>
                      <div class="popover project-video-item">
                      </div>
                        <div class="project-item__content">
                            <div class='loader' style='display: none;'>
                                <img src="{% static 'images/icon-loading-outline-w.svg' %}" alt="">
                            </div>
                            <div class="project-tab project-tab-new" data-tab="new">
                                <div class="project-item__video-list">

                                </div>
                            </div>

                            <div class="project-tab project-tab-update" data-tab="update">
                                <div class="project-item__video-list">

                                </div>
                            </div>

                          <div class="project-tab project-tab-progress active {% if view_only %} view_only{% endif %}" data-tab="progress">
                            <div class="tab--video-progress tab--video-all">
                            </div>

                            <div class="pd-section pd-section--detail pd-scene-title-detail show-comment-unresolved hide"
                                 data-scene-title-id="" data-total="{{ total_page }}">
                              <div class="pd-section__content {% if not share_comment %} pd-section__content-not-share{% endif %}">
                                  <div class="line-header-share"></div>
                                  <div class="block-content-scene">
                                      <div class="pd-scene block-scene-video">
                                          {{ html_video }}
                                      </div>

                                      {% if share_comment %}
                                          <div class="pd-comment">
                                       <div class="name-responsive">
                <p class="txt-des-above"
                   data-product-scene-id="{{ scene.title.product_scene.pk }}">{{ scene.title.product_scene.name }}</p>
                <div class="d-flex">
                    <span class="txt-des m-0">{{ scene.title.title }}</span>
                </div>
                <div class="line-block-name"></div>
                                       </div>
                                              {% include 'top/_comment_scene_detail.html' %}
                                          </div>
                                      {% endif %}
                                  </div>
                              </div>
{#                              {% if share_comment%}#}
{#                                <div class="pd-section-file">#}
{#                                    <div class="pd-file-heading"><i class="icon icon--sicon-storage"></i>#}
{#                                        <div class="pd-file-toggle active"><i class="icon icon--sicon-next"></i>#}
{#                                        </div>#}
{#                                    </div>#}
{#                                    {{html_files}}#}
{#                                </div>#}
{#                              {% endif %}#}
                            </div>

                          </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% include 'messenger/_modal_open_file.html' %}
        {% include 'top/_modal_ACR_check.html' %}

        {% include 'top/_modal_show_folder.html' %}
    </main>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/init_socket_messenger.js' %}"></script>

    <script>
        let csrf = '{% csrf_token %}';
        let default_thumb = '{% static 'images/messenger-thumb.png' %}';
        let user = null;
        let is_pc = '{{ request|is_pc }}';
        let user_role = '{{ role }}';
        let scene_id = '{{ scene_id }}';
        let scene_title_id = '{{ scene_title_id }}';
        $(document).ready(function () {
            removeDuplicatedDate();
            $(document).on('click', '.cscene-heading__product', function () {
                let product_scene_id = $(this).attr('data-product-scene-id');
                $.ajax({
                    type: "GET",
                    url: "/scene/get_link_back_to_chapter",
                    data: {
                        "product_scene_id": product_scene_id
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        window.location = response.new_path;
                    }
                });
            })
        })

    </script>
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
    <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
    <script src="{% static 'js/top_page_admin.js' %}"></script>
    <script src="{% static 'js/top_page_member.js' %}"></script>
    <script src="{% static 'js/utils.js' %}"></script>
    <script src="{% static 'js/sortable.js' %}"></script>
    <script src="{% static 'js/video_modal.js' %}"></script>
    {% endcompress %}
{% endblock %}

{% block extra_script %}
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.0.7/dist/js/splide.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wavesurfer.js/3.3.3/plugin/wavesurfer.cursor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/combodate.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/action_banner.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/project_detail.js' %}"></script>
    <script src="{% static 'js/soremo.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/load_more_scene_comment.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/scene_share.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/qrcode.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/scene_detail_share.js' %}"></script>
    {% endcompress %}
{% endblock %}
