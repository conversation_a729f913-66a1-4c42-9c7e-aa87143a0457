{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 compress %}

{% block title %}{% endblock title %}

{% block extrahead %}
    {% compress css %}
    <style>
/*        #video-demo-container {
            width: 400px;
            margin: 40px;
        }*/

        #main-video {
            display: none;
            max-width: 400px;
        }

        #video-canvas, #video-canvas-resize, #pre_image {
            max-width: 400px;
        }

        .canvas, #video-canvas-resize {
            display: none;
        }

        .upload-content {
            margin-top: 40px;
        }

        #submit {
            margin-top: 36px;
            width: 100px;
        }

        .has-success .form-control {
            border-color: #ccc !important;
        }

        .has-success .control-label {
            color: #333 !important;
        }

        .has-success .help-block {
            color: #737373 !important;
        }

        .scene_upload {
            padding-top: 50px;
        }
    </style>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="container">
        <div class="m10 row scene_upload">
            <div class="col-xs-12 upload-content">
                <form method="post" id="myAwesomeForm" action="{% url 'app:scene_upload' %}"
                      enctype="multipart/form-data">
                    <div class="col-xs-12">
                        {% csrf_token %}
                        {% bootstrap_form form %}
                    </div>

                    <div id="video-demo-container" class="col-xs-12">
                        <div class="col-xs-6 dom-video">
                            <video id="main-video" controls>
                                <source type="video/mp4">
                            </video>
                        </div>
                        <input type="hidden" name="thumbnail_base_64" id="thumbnail_base_64">

                        <div class="col-xs-6 canvas">
                            <canvas id="video-canvas"></canvas>
                            <canvas id="video-canvas-resize"></canvas>
                        </div>

                        <img src="" id="pre_image">
                    </div>

                    <div class="col-xs-12">
                        {% buttons %}
                            <input type="submit" value="登録" id="submit" class="btn btn-primary btn_upload"
                                   />
                        {% endbuttons %}
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js"></script>
    {% compress js inline %}
    <script type="text/javascript">
        var title_dom = $('#id_title_val');
        $(document).ready(function () {

            title_dom.prop('required', true);

            var _CANVAS = document.querySelector("#video-canvas"),
                _CANVAS_RESIZE = document.querySelector("#video-canvas-resize"),
                _CTX = _CANVAS.getContext("2d"),
                _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d"),
                _VIDEO = document.querySelector("#main-video"),
                id_movie = '#id_movie';

            document.querySelector("#id_movie").addEventListener('change', function () {
                $('#id_list_width, #id_list_height').val('');
                $('#thumbnail_base_64').val('');
                $('.canvas').css('display', 'none');
                $('#video-demo-container .dom-video').each(function(index){
                    if(0 < index){
                        $(this).remove();
                    }
                });

                status_main_video='true'
                // Object Url as the video source
                var len_file = document.querySelector(id_movie).files.length
                if (len_file > 1){
                    $('#id_thumbnail').parent('.form-group').hide();
                    $('#pre_image').attr('src','');
                } else {
                    $('#id_thumbnail').parent('.form-group').show();
                }

                for(index=0; index< len_file; index++){
                    if (index > 0){
                        $('#video-demo-container').append('<div class="col-xs-6 dom-video"><video id="main-video-'+ index +'" controls style="max-width: 400px;">'
                            + '<source type="video/mp4" src="'+ URL.createObjectURL(document.querySelector(id_movie).files[index]) +'"></video></div>');
                    } else {
                        document.querySelector("#main-video source").setAttribute('src', URL.createObjectURL(document.querySelector(id_movie).files[index]));
                    }

                    // Load the video and show it
                    _VIDEO.load();
                    _VIDEO.style.display = 'inline';
                    _VIDEO.preload = 'metadata';
                    // // Load metadata of the video to get video duration and dimensions
                    _VIDEO.addEventListener('loadedmetadata', function () {
                        var video_duration = _VIDEO.duration,
                            duration_options_html = '';

                        // Set options in dropdown at 4 second interval
                        for (var i = 0; i < Math.floor(video_duration); i = i + 4) {
                            duration_options_html += '<option value="' + i + '">' + i + '</option>';
                        };

                        // Set canvas dimensions same as video dimensions
                        if(len_file > 1){
                            $('.canvas').css('display', 'none');
                        } else{
                            _CANVAS.width = _VIDEO.videoWidth;
                            _CANVAS.height = _VIDEO.videoHeight;
                            $('.canvas').css('display', 'block');
                        }

                        (function(index){
                            setTimeout(function () {
                                var imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
                                _CANVAS_RESIZE.width = imgResize.width;
                                _CANVAS_RESIZE.height = imgResize.height;
                                _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

                                _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
                                // $('.canvas').css('display', 'block');
                                $(_VIDEO).off('loadedmetadata')
                                $('#id_thumbnail').val('').clone(true);
                                $('#thumbnail_base_64').val(_CANVAS_RESIZE.toDataURL());
                            }, 1000);
                        })(index)

                    });

                    // _VIDEO.addEventListener('seeked', function () {
                    //     get_thumbnail(_VIDEO);
                    // });

                }

            });

            var get_thumbnail = function (_VIDEO) {
                var imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
                _CANVAS_RESIZE.width = imgResize.width;
                _CANVAS_RESIZE.height = imgResize.height;
                _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

                _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
                $(_VIDEO).off('loadedmetadata')
                $('#thumbnail_base_64').val($('#thumbnail_base_64').val() + '|' +_CANVAS_RESIZE.toDataURL());
                $('#id_thumbnail').val('').clone(true);
            }

            scene_id = window.location.search.split('&scene_id=')[1];
            if(scene_id){
                $('#id_version').val(scene_id);
            }

            $('#submit').unbind('click').bind('click', function () {
                $('video[id^="main-video"]').each(function(index){
                    $('#id_list_width').val($('#id_list_width').val() + ',' + this.videoWidth);
                    $('#id_list_height').val($('#id_list_height').val() + ',' + this.videoHeight);
                    if(index>0){
                        get_thumbnail(this)
                    }
                });
                if(title_dom.prop('type') != 'hidden' && title_dom.val() === null || title_dom.prop('type') != 'hidden' &&  title_dom.val().match(/^ *$|^　*$/) !== null) {

                    title_dom[0].setCustomValidity('正しいタイトルを入力してください。');
                    title_dom.on('input', function () {
                        $(this)[0].setCustomValidity('');
                    })
                } else if ($('#id_thumbnail').val() == '' && $('#thumbnail_base_64').val() == '') {
                    return false;
                } else {
                    var movie_dom = $('#id_movie');
                    if (movie_dom && movie_dom.val() !== "" && title_dom.prop('type') != 'hidden' && title_dom.val() !== "" || movie_dom && movie_dom.val() !== "" && title_dom.prop('type') == 'hidden') {
                        $('#id_list_width').val($('#id_list_width').val().substr(1));
                        $('#id_list_height').val($('#id_list_height').val().substr(1));
                        $.blockUI({message: '動画をアップロードしています。...'});
                        return true;
                    }
                }
            });
        });

        var resizeImg = function (width, height) {
            console.log(width, height, 'origin');

            var targetWidth = 200;
            if (width <= targetWidth) {
                return {width: width, height: height}
            }

            var ratio = targetWidth / width;

            return {
                width: parseInt(width * ratio),
                height: parseInt(height * ratio)
            }
        };

        var storefile = 'false'
        $('#id_thumbnail').on('click', function () {
            if(storefile !='false'){
                storefile = this.files
            }
        })

        $('#id_thumbnail').on('change', function () {
            if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
                var reader = new FileReader();
                storefile = this.files;
                reader.onload = function (e) {
                    $('#pre_image').attr('src', e.target.result);
                };
                reader.readAsDataURL(this.files[0]);
                $('.canvas').css('display', 'none');
                $('#pre_image').css('display', 'block');
            } else {
                if (storefile == 'false'){
                    alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
                    $('#id_thumbnail').val('').clone(true);
                } else {
                    this.files = storefile;
                }
            }
        })

        $('#id_movie').on('change', function(){
            if (this.files && this.files.length != 0) {
                for(x=0; x<this.files.length; x++){
                    if (!this.files[x].name.match(/\.(mp4|x-m4v|webm)$/)){
                        alert('ファイルのフォーマットが正しくありません。MP4, webmファイルをアップロードしてください。');
                        $('#id_movie').val('').clone(true);
                    }
                }
            } else {
                $("#main-video").css('display', 'none');
                return false;
            }
        })
    </script>
    {% endcompress %}
{% endblock content %}
