{% block content %}
    <ul class="scenes__list unstyled {{ current_product_scene.pk }}">
        {% for scene in product_scene %}
            <li class="scenes__item width-free {{ scene.status }} all_scene {{ scene.is_hidden }}" style="width: {{ scene.video_tag }}px">
                <div class="scenes__rate" value="{{scene.rating}}">
                    <ul class="stars">
                        {% for i in scene.rating %}
                        <li class="star selected" data-value="1">
                            <i class="fa fa-star fa-fw"></i>
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                {% if scene.done_filter %}
                    <span class="label-done"><i class="fas fa-heart"></i></span>
                {% elif scene.new_filter == -1 %}
                    <span class="label-icon label-new">new</span>
                {% elif scene.new_filter == 0 and scene.unread > 0 %}
                    <span class="label-icon label-num">{{ scene.unread }}</span>
                {% endif %}

                <div class="scenes__edit-item setting-edit">
                    <div class="setting-edit__box">
                        <a class="setting-edit__item edit_scene_video">
                            <i class="fas fa-pencil-alt"></i>
                        </a>
                        <a class="setting-edit__item delete_scene_video">
                            <i class="far fa-trash-alt"></i>
                        </a>
                    </div>
                    <div class="setting-edit__links">
                        <span>
                            <i class="fas fa-cog"></i>
                        </span>
                    </div>
                </div>
                <a href="javascript:void(0)" class="scene_list_link" data-title="{{ scene.top_scene.title_id }}">
                    <div class="scenes__item-img" {% if scene.top_scene.thumbnail %} style="background-image: url({{ scene.top_scene.thumbnail.url }})" {% endif %}>
                    </div>
                </a>

                <div class="scenes__item-info">
                    <div class="scenes__item-date">
                        {{ scene.top_scene.title.modified_str }}
                    </div>
                    <div class="scenes__item-title">
                        {{ scene.top_scene.title.title }}
                    </div>
                    <input type="text" class="edit_scene_video_input hide" style="width: 100%">
                    <div class="edit_scene_video_name_button hide {{ scene.top_scene.title_id }}">
                        <a style="color: green; cursor: pointer" class="edit_scene_video_name_accept">
                            <i class="far fa-check-circle"></i>
                        </a>
                        <a style="color: red; cursor: pointer" class="edit_scene_video_name_cancel">
                            <i class="far fa-times-circle"></i>
                        </a>
                    </div>

                    <div class="delete_scene_video_button hide {{ scene.top_scene.title_id }}">
                        <span style="color: darkred">本当に削除しますか?</span>
                        <a style="color: green; cursor: pointer" class="delete_scene_video_accept">
                            <i class="far fa-check-circle"></i>
                        </a>
                        <a style="color: red; cursor: pointer" class="delete_scene_video_cancel">
                            <i class="far fa-times-circle"></i>
                        </a>
                    </div>
                </div>
            </li>
        {% endfor %}

        {% if product.current_scene < product.max_scene %}
            <li class="scenes__item add-upload">
                <a class="link-update"
                   href="{% url 'app:scene_upload' %}?product_id={{ product.pk }}&product_scene_id={{ current_product_scene.pk }}">
                    <i class="fa fa-plus"></i>
                </a>
            </li>
        {% endif %}
    </ul>
    <script>
       $('.scene_list_link').on('click', function() {
           if(this.href === 'javascript:void(0)') {
               let current_filter = $('.scenes__content').data('filter');
               let scene_title = $(this).parents('.scenes__list').siblings('.scenes__title')
               let current_sort = scene_title.find('.sort-menu__toggle').data('checked')
               let current_order = scene_title.find('.scenes__sort-box').children('.hidden').data('value');
               let url = "{% url 'app:scene_list' %}?product_id={{ product.pk }}&title_id=" +
                   $(this).data('title') + "&product_scene_id={{ current_product_scene.pk }}" + "&filter=" +
                   current_filter + "&sort=" + current_sort + "&order=" + current_order;

               $(this).attr('href', url);
           }
       });
    </script>
{% endblock %}
