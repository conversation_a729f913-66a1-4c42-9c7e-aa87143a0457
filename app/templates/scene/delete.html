{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}

{% block extrahead %}
    <style>
        textarea.form-control {
            height: 60px;
        }

        .movieblock {
            padding: 10px;
            border-bottom: 1px solid #999;
        }

        .delete {
            margin-top: 40px;
        }
    </style>
{% endblock %}

{% block content %}
    <main class="container delete">
        <div class="m10 row">
            <div class="col-xs-6">
                <form action="" method="post">{% csrf_token %}
                    <p>削除してよろしいですか?</p>
                    {% buttons %}
                        <input type="submit" value="削除する" class="btn btn-primary">
                    {% endbuttons %}
                </form>
                {% if object.movie %}
                <video id="movie{{ object.pk }}" class="video-js" controls preload="metadata" width="400" height="200"
                       poster="" data-setup="{}">
                    <source src="{{ object.movie.url }}" type='video/mp4'>
                    <p class="vjs-no-js">
                        To view this video please enable JavaScript, and consider upgrading to a web browser that
                        <a href="http://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                    </p>
                </video>
                {%endif%}
            </div>
        </div>
    </main>
{% endblock content %}
