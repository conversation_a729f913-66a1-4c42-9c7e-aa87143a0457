import io
import os
import base64
import requests
import logging
import datetime
from urllib.parse import quote

import pdfkit

from django.conf import settings
from pyvirtualdisplay import Display
from django.http import JsonResponse, FileResponse, HttpResponse
from django.shortcuts import redirect
from django.utils.translation import gettext as _
from django.template.loader import render_to_string
from django.template.loader import get_template
from django import forms
from accounts.models import AuthUser, ProductUser
from app.models import Product, SectionCredit, ItemSectionCredit, OfferCreator
from app.app_forms.staff_credit_project_form import SectionCreditForm
from voice.utils import pdfkit_generate_pdf


def add_update_section_credit(request):
    project_id = request.POST.get('project_id')
    if not project_id:
        return JsonResponse({}, status=500)
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({}, status=500)
    current_user = request.user
    if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
                                                                               position=ProductUser.PRODUCER).exists():
        section_id = request.POST.get('section_id')
        section = None
        if section_id:
            section = SectionCredit.objects.filter(pk=section_id).first()
            if not section or section and section.project != project:
                return JsonResponse({}, status=500)
        form = SectionCreditForm(request.POST, instance=section)
        section_html = ''
        if form.is_valid():
            max_order = 1
            if project.items_staff_credit.exists() or project.sections.exists():
                last_section = project.sections.count() if project.sections.exists() else 0
                last_item = project.items_staff_credit.count() if project.items_staff_credit.exists() else 0
                max_order = last_section + last_item + 1
           
            section = form.save()
            section.project = project
            section.save()
            if not section_id:
                section.order = max_order
                section.save()
                section_html = render_to_string('credit/_section_container.html',
                                                {'item': section, 'language': request.POST.get('language'),
                                                 'editable': True})
            return JsonResponse({'html': section_html}, status=200)

    return JsonResponse({}, status=500)


def update_index_section_staff_credit(request):
    project_id = request.POST.get('project_id')
    arr_order = request.POST.getlist('arr_order[]')
    if arr_order:
        try:
            project = Product.objects.filter(pk=project_id).first()
            if not project:
                return JsonResponse({}, status=500)
            current_user = request.user
            if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user,
                                                                                       product=project,
                                                                                       position=ProductUser.PRODUCER).exists():
                index = 1
                for item_id in arr_order:
                    item = SectionCredit.objects.filter(pk=item_id)
                    if item.exists():
                        item = item.first()
                    else:
                        item = ItemSectionCredit.objects.filter(pk=item_id)
                        if item.exists():
                            item = item.first()
                    if item:
                        item.order = index
                        item.save()
                        index += 1
                return JsonResponse({}, status=200)
        except:
            pass
    return JsonResponse({}, status=500)


def delete_section_item_artist(request):
    project_id = request.POST.get('project_id')
    section_id = request.POST.get('section_id')
    if not project_id or not section_id:
        return JsonResponse({}, status=500)
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({}, status=500)
    current_user = request.user
    if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
                                                                               position=ProductUser.PRODUCER).exists():
        if section_id:
            item = SectionCredit.objects.filter(pk=section_id, project=project).first()
            if not item:
                item = ItemSectionCredit.objects.filter(pk=section_id, project=project).first()
                if not item:
                    return JsonResponse({}, status=500)
            item.delete()
            return JsonResponse({}, status=200)
    return JsonResponse({}, status=500)


def add_update_item_artist_section_credit(request):
    project_id = request.POST.get('project_id')
    user_id = request.POST.get('artist_id')

    artist = None
    project_artist = None
    current_user = request.user
    try:
        project = Product.objects.get(pk=project_id)
        if user_id:
            artist = AuthUser.objects.filter(pk=user_id, is_active=True).first()
            if not artist:
                return JsonResponse({}, status=500)
            project_artist = ProductUser.objects.filter(user_id=user_id, product=project).first()
            if not project_artist:
                return JsonResponse({}, status=500)

        item_id = request.POST.get('item_id')
        if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
                                                                                position=ProductUser.PRODUCER).exists():
            if item_id:
                item_section = ItemSectionCredit.objects.filter(pk=item_id).first()
                if not item_section:
                    return JsonResponse({}, status=500)
            else:
                max_order = 1
                if project.items_staff_credit.exists() or project.sections.exists():
                    last_section = project.sections.count() if project.sections.exists() else 0
                    last_item = project.items_staff_credit.count() if project.items_staff_credit.exists() else 0
                    max_order = last_section + last_item + 1
                item_section = ItemSectionCredit.objects.create(project_artist=project_artist, project=project,
                                                                order=max_order)
            if item_section:
                link_profile = True if request.POST.get('link_profile') == 'true' else False
                title = request.POST.get('title-en')
                position = request.POST.get('title')
                artist_name = request.POST.get('artist-name')
                artist_name_en = request.POST.get('artist-name-en')
                if not project_artist:
                    link_profile = False
                ItemSectionCredit.objects.filter(pk=item_section.pk).update(link_profile=link_profile, title=title,
                                                                            position=position, artist_name=artist_name,
                                                                            artist_name_en=artist_name_en,
                                                                            project_artist=project_artist)
                item_section = ItemSectionCredit.objects.filter(pk=item_section.pk).first()
                item_section_html = render_to_string('credit/_section_container.html', {'item': item_section,
                                                                                  'language': request.POST.get('language'),
                                                                                  'editable': True})
                return JsonResponse({'html': item_section_html}, status=200)
    except:
        pass
    return JsonResponse({}, status=500)


def get_staff_credit_for_project(request):
    project_id = request.GET.get('project_id')
    if not project_id:
        return JsonResponse({}, status=500)
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({}, status=500)
    current_user = request.user
    editable = False
    if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
                                                                               position=ProductUser.PRODUCER).exists():
        editable = True

    language = request.GET.get('language', 'jp')

    sections = project.sections.order_by('order')
    items = project.items_staff_credit.order_by('order')

    from itertools import chain
    linked_list = chain(sections, items)
    sorted_list = sorted(linked_list, key=lambda item: item.order)
    try:
        staff_credit_html = render_to_string('top/_modal_staff_credit_content.html',
                                         {'project': project, 'user': current_user, 'editable': editable,
                                          'language': language, 'sorted_list': sorted_list})
        return JsonResponse({'html': staff_credit_html}, status=200)
    except:
        pass
    return JsonResponse({}, status=500)


def get_all_artist_in_project(request):
    project_id = request.GET.get('project_id')
    if not project_id:
        return JsonResponse({}, status=500)
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({}, status=500)
    current_user = request.user

    item_id = request.GET.get('item_artist_id')
    action = request.GET.get('action')
    project_artist = None
    item_artist = None
    if action == 'edit' and not item_id:
        return JsonResponse({}, status=500)
    artists = project.get_artist_has_offer()
    context = {}
    if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
                                                                               position=ProductUser.PRODUCER).exists():
        if item_id:
            item_artist = ItemSectionCredit.objects.filter(pk=item_id).first()
            if not item_artist:
                return JsonResponse({}, status=500)
            if item_artist.project_artist:
                project_artist = project.productuser_set.filter(pk=item_artist.project_artist.pk,
                                                            user__is_active=True).first()
                # artists = artists.exclude(pk=item_artist.project_artist.user.pk)
            else:
                context.update({'artist_name_jp': item_artist.artist_name,
                                'artist_name_en': item_artist.artist_name_en,
                                'title': item_artist.position,
                                'title_en': item_artist.title})

        project_artists = project.productuser_set.filter(user__role=AuthUser.CREATOR, user__in=artists,
                                                         user__is_active=True).order_by('-rewarded',
                                                                                        'pk').distinct()

        modal_html = render_to_string('top/_modal_staff_credit_artist_content.html',
                                      {'project': project, 'user': current_user, 'project_artists': project_artists,
                                       'item_artist': item_artist, 'project_artist': project_artist})
        context.update({'html': modal_html})
        return JsonResponse(context, status=200)
    return JsonResponse({}, status=500)


def download_staff_credit_pdf(request):
    # Get the title and entry file from the request (assuming it's passed in JSON)
    project_id = request.GET.get('project_id')
    if not project_id:
        return JsonResponse({'error': 'Project ID is required'}, status=400)

    # Fetch the project using the provided project_id
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({'error': 'Project not found'}, status=404)

    # Get the sections and items
    sections = project.sections.order_by('order')
    items = project.items_staff_credit.order_by('order')

    # Combine sections and items into a sorted list
    sorted_list = []
    for section in sections:
        sorted_list.append(section.to_dict())  # Convert SectionCredit to dict
    for item in items:
        sorted_list.append({
            "position": item.position,
            "title": item.title,
            "artist_name": item.artist_name,
            "artist_name_en": item.artist_name_en,
            "order": item.order
        })

    sorted_list.sort(key=lambda x: x['order'])  # Sort by order

    project_name = project.get_name_by_code_name()
    time_name = datetime.datetime.now().strftime("%y%m%d")
    file_name = f'{project_name}_Credits_{time_name}.pdf'

    api_url = settings.VIVLIOSTYLE_SERVER + "/generate-pdf"
    payload = {
        "config": f"module.exports = {{title: 'Credits', entry: ['index.html']}};",
        "html_input": {
            "template_info": {
                "name": "credits"
            },
            "data_info": {
                "project": {
                    "code_name": project.code_name
                },
                "sorted_list": sorted_list
            }
        }
    }

    # Make the POST request to the Vivliostyle API
    response = requests.post(api_url, json=payload)

    # Handle the response from the Node.js server
    if response.status_code == 200:
        # Return the response data (PDF) to the viewer
        pdf_response = HttpResponse(response.content, content_type='application/pdf;charset=utf-8')
        pdf_response['Content-Disposition'] = "attachment; filename*=UTF-8''{}".format(quote(file_name))
        return pdf_response
    else:
        # If there's an error, return the error message
        return JsonResponse({"error": response.text}, status=response.status_code)