# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=PajinÃ¢ gunÃ¢j rukÃ¹u
previous_label=Sa gachin
next.title=PajinÃ¢ 'na' Ã±aan
next_label=Ne' Ã±aan

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ãanj
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=si'iaj {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=Nagi'iaj li'
zoom_out_label=Nagi'iaj li'
zoom_in.title=Nagi'iaj niko'
zoom_in_label=Nagi'iaj niko'
zoom.title=dÃ j nÃ¬ko ma'an
presentation_mode.title=Naduno' daj ga ma
presentation_mode_label=Daj gÃ  ma
open_file.title=Na'nÃ¯n' chrÃ» Ã±anj
open_file_label=Na'nÃ¯n
print.title=Nari' Ã±a du'ua
print_label=Nari' Ã±adu'ua
download.title=NadunÃ¯nj
download_label=NadunÃ¯nj
bookmark.title=Daj hua ma (Guxun' nej na'nÃ¯n' riÃ±a ventana nakÃ a)
bookmark_label=Daj hua ma

# Secondary toolbar and context menu
tools.title=Rasun
tools_label=Nej rasÃ¹un
first_page.title=gun' riÃ±a pajina asiniin
first_page_label=Gun' riÃ±a pajina asiniin
last_page.title=Gun' riÃ±a pajina rukÃ¹ ni'in
last_page_label=Gun' riÃ±a pajina rukÃ¹ ni'inj
page_rotate_cw.title=Tanikaj ne' huat
page_rotate_cw_label=Tanikaj ne' huat
page_rotate_ccw.title=Tanikaj ne' chÃ®nt'
page_rotate_ccw_label=Tanikaj ne' chint

cursor_text_select_tool.title=Dugi'iaj sun' sa ganahui texto
cursor_text_select_tool_label=Nej rasun arajsun' da' nahui' texto
cursor_hand_tool.title=Nachrun' nej rasun
cursor_hand_tool_label=Sa rajsun ro'o'

scroll_vertical.title=Garasun' dukuÃ¡n runÅ«u
scroll_vertical_label=DukuÃ¡n runÅ«u
scroll_horizontal.title=Garasun' dukuÃ¡n nikin' nahui
scroll_horizontal_label=DukuÃ¡n nikin' nahui
scroll_wrapped.title=Garasun' sa nachree
scroll_wrapped_label=Sa nachree

spread_none.title=Si nagi'iaj nugun'un' nej pagina hua ninin
spread_none_label=Ni'io daj hua pagina
spread_odd.title=Nagi'iaj nugua'ant nej pajina
spread_odd_label=Ni'io' daj hua libro gurin
spread_even.title=NakÄj dugui' ngÃ  nej pajinÃ¢ ayi'Ã¬ ngÃ  da' hÃ¹i hÃ¹i
spread_even_label=Nahuin nÃ¬ko nej

# Document properties dialog box
document_properties.title=Nej sa nikÄj Ã±anjâ¦
document_properties_label=Nej sa nikÄj Ã±anjâ¦
document_properties_file_name=Si yugui archÃ®bo:
document_properties_file_size=DÃ j yachÃ¬j archÃ®bo:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Si yugui:
document_properties_author=SÃ­ girirÃ :
document_properties_subject=Dugui':
document_properties_keywords=Nej nuguan' huÃ¬i:
document_properties_creation_date=Gui gurugui' man:
document_properties_modification_date=Nuguan' nahuin nakÃ :
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Guiri ro'
document_properties_producer=Sa ri PDF:
document_properties_version=PDF Version:
document_properties_page_count=Si GuendÃ¢ PÃ¢jina:
document_properties_page_size=DÃ j yachÃ¬j pÃ¢jina:
document_properties_page_size_unit_inches=riÃ±a
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=nadu'ua
document_properties_page_size_orientation_landscape=dÃ j huaj
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Da'ngÃ 'a
document_properties_page_size_name_legal=Nuguan' a'nÃ¯'Ã¯n
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=NanÃ¨t chre ni'iajt riÃ±a Web:
document_properties_linearized_yes=Ga'ue
document_properties_linearized_no=Si ga'ue
document_properties_close=NarÃ¡n

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Duyichin'

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=NadunÄ barrÃ¢ nÃ¹ yi'nÃ¯n
toggle_sidebar_label=NadunÄ barrÃ¢ nÃ¹ yi'nÃ¯n
findbar_label=NarÃ¬'

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
find_input.title=NarÃ¬'
find_previous_label=Sa gachÃ®n
find_next_label=Ne' Ã±aan
find_highlight=Daran' sa Ã±a'an 
find_match_case_label=Match case
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} si'iaj {{total}} guÃ±a gÃ¨ huaj
find_match_count[two]={{current}} si'iaj {{total}} guÃ±a gÃ¨ huaj
find_match_count[few]={{current}} si'iaj {{total}} guÃ±a gÃ¨ huaj
find_match_count[many]={{current}} si'iaj {{total}} guÃ±a gÃ¨ huaj
find_match_count[other]={{current}} of {{total}} matches
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Doj ngÃ  da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[one]=Doj ngÃ  da' {{limit}} sa nari' dugui'i
find_match_count_limit[two]=Doj ngÃ  da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[few]=Doj ngÃ  da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[many]=Doj ngÃ  da' {{limit}} nej sa nari' dugui'i
find_match_count_limit[other]=Doj ngÃ  da' {{limit}} nej sa nari' dugui'i
find_not_found=Nu narÃ¬'ij nugua'anj

# Error panel labels
error_more_info=Doj nuguan' a'min rayi'Ã® nan
error_less_info=DÃ²j nuguan' a'min rayi'Ã® nan
error_close=NarÃ¡n
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Message: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Naru'ui': {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ArchÃ®bo: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=LÃ®nia: {{line}}

# Predefined zoom values
page_scale_actual=DÃ j yÃ chi akuan' nÃ­n
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_ok=Ga'ue
password_cancel=Duyichin'

