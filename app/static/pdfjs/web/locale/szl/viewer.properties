# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Piyrwyjszo strÅna
previous_label=Piyrwyjszo
next.title=Nastympno strÅna
next_label=Dalij

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=StrÅna
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=zeÂ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ze {{pagesCount}})

zoom_out.title=ZmyÅsz
zoom_out_label=ZmyÅsz
zoom_in.title=Zwiynksz
zoom_in_label=Zwiynksz
zoom.title=SrogoÅÄ
presentation_mode.title=PrzeÅÅncz na tryb prezyntacyje
presentation_mode_label=Tryb prezyntacyje
open_file.title=Ãdewrzij zbiÅr
open_file_label=Ãdewrzij
print.title=Durkuj
print_label=Durkuj
download.title=Pobier
download_label=Pobier
bookmark.title=Aktualny widok (kopiuj abo Ã´dewrzij w nowym Ã´knie)
bookmark_label=Aktualny widok

# Secondary toolbar and context menu
tools.title=Noczynia
tools_label=Noczynia
first_page.title=IdÅº ku piyrszyj strÅnie
first_page_label=IdÅº ku piyrszyj strÅnie
last_page.title=IdÅº ku Ã´statnij strÅnie
last_page_label=IdÅº ku Ã´statnij strÅnie
page_rotate_cw.title=Zwyrtnij w prawo
page_rotate_cw_label=Zwyrtnij w prawo
page_rotate_ccw.title=Zwyrtnij w lewo
page_rotate_ccw_label=Zwyrtnij w lewo

cursor_text_select_tool.title=ZaÅÅncz noczynie Ã´biyranio tekstu
cursor_text_select_tool_label=Noczynie Ã´biyranio tekstu
cursor_hand_tool.title=ZaÅÅncz noczynie rÅnczka
cursor_hand_tool_label=Noczynie rÅnczka

scroll_vertical.title=UÅ¼ywej piÅnowego przewijanio
scroll_vertical_label=PiÅnowe przewijanie
scroll_horizontal.title=UÅ¼ywej poziÅmego przewijanio
scroll_horizontal_label=PoziÅme przewijanie
scroll_wrapped.title=UÅ¼ywej szichtowego przewijanio
scroll_wrapped_label=Szichtowe przewijanie

spread_none.title=Niy dowej strÅn w widoku po dwie
spread_none_label=Po jednyj strÅnie
spread_odd.title=PokoÅ¼ strÅny po dwie; niyporziste po lewyj
spread_odd_label=Niyporziste po lewyj
spread_even.title=PokoÅ¼ strÅny po dwie; porziste po lewyj
spread_even_label=Porziste po lewyj

# Document properties dialog box
document_properties.title=WÅosnoÅci dokumyntuâ¦
document_properties_label=WÅosnoÅci dokumyntuâ¦
document_properties_file_name=Miano zbioru:
document_properties_file_size=SrogoÅÄ zbioru:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}}Â KB ({{size_b}}Â B)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}}Â MB ({{size_b}}Â B)
document_properties_title=TytuÅ:
document_properties_author=AutÅr:
document_properties_subject=Tymat:
document_properties_keywords=Kluczowe sÅowa:
document_properties_creation_date=Data zrychtowanio:
document_properties_modification_date=Data zmiany:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Zrychtowane Ã´d:
document_properties_producer=PDF Ã´d:
document_properties_version=Wersyjo PDF:
document_properties_page_count=WieloÅÄ strÅn:
document_properties_page_size=SrogoÅÄ strÅny:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=piÅnowo
document_properties_page_size_orientation_landscape=poziÅmo
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Gibki necowy podglÅnd:
document_properties_linearized_yes=Ja
document_properties_linearized_no=Niy
document_properties_close=Zawrzij

print_progress_message=Rychtowanie dokumyntu do durkuâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Pociep

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=PrzeÅÅncz posek na rancie
toggle_sidebar_notification2.title=PrzeÅÅncz posek na rancie (dokumynt mo struktura/przidowki/warstwy)
toggle_sidebar_label=PrzeÅÅncz posek na rancie
document_outline.title=PokoÅ¼ struktura dokumyntu (tuplowane klikniyncie rozszyrzo/swijo wszyskie elymynta)
document_outline_label=Struktura dokumyntu
attachments.title=PokoÅ¼ przidowki
attachments_label=Przidowki
layers.title=PokoÅ¼ warstwy (tuplowane klikniyncie resetuje wszyskie warstwy do bazowego stanu)
layers_label=Warstwy
thumbs.title=PokoÅ¼ miniatury
thumbs_label=Miniatury
findbar.title=ZnojdÅº w dokumyncie
findbar_label=ZnojdÅº

additional_layers=Nadbytnie warstwy
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=StrÅna {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatura strÅny {{page}}

# Find panel button title and messages
find_input.title=ZnojdÅº
find_input.placeholder=ZnojdÅº w dokumyncieâ¦
find_previous.title=ZnojdÅº piyrwyjsze pokozanie sie tyj frazy
find_previous_label=Piyrwyjszo
find_next.title=ZnojdÅº nastympne pokozanie sie tyj frazy
find_next_label=Dalij
find_highlight=Zaznacz wszysko
find_match_case_label=Poznowej srogoÅÄ liter
find_entire_word_label=CoÅke sÅowa
find_reached_top=DoszÅo do samego wiyrchu strÅny, dalij Ã´d spodku
find_reached_bottom=DoszÅo do samego spodku strÅny, dalij Ã´d wiyrchu
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} ze {{total}}, co pasujÅm
find_match_count[two]={{current}} ze {{total}}, co pasujÅm
find_match_count[few]={{current}} ze {{total}}, co pasujÅm
find_match_count[many]={{current}} ze {{total}}, co pasujÅm
find_match_count[other]={{current}} ze {{total}}, co pasujÅm
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(total) ]}
find_match_count_limit[zero]=Wiyncyj jak {{limit}}, co pasujÅm
find_match_count_limit[one]=Wiyncyj jak {{limit}}, co pasuje
find_match_count_limit[two]=Wiyncyj jak {{limit}}, co pasujÅm
find_match_count_limit[few]=Wiyncyj jak {{limit}}, co pasujÅm
find_match_count_limit[many]=Wiyncyj jak {{limit}}, co pasujÅm
find_match_count_limit[other]=Wiyncyj jak {{limit}}, co pasujÅm
find_not_found=Fraza niy znaleziÅno

# Error panel labels
error_more_info=Wiyncyj informacyji
error_less_info=Mynij informacyji
error_close=Zawrzij
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=WiadÅmoÅÄ: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Sztapel: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ZbiÅr: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linijo: {{line}}
rendering_error=Przi renderowaniu strÅny pokozoÅ sie feler.

# Predefined zoom values
page_scale_width=Szyrzka strÅny
page_scale_fit=Napasowanie strÅny
page_scale_auto=AutÅmatyczno srogoÅÄ
page_scale_actual=Aktualno srogoÅÄ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error=Przi ladowaniu PDFa pokozoÅ sie feler.
invalid_file_error=ZÅy abo felerny zbiÅr PDF.
missing_file_error=Chybio zbioru PDF.
unexpected_response_error=NiyÃ´czekowano Ã´dpowiydÅº serwera.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Anotacyjo typu {{type}}]
password_label=WkludÅº hasÅo, coby Ã´dewrziÄ tyn zbiÅr PDF.
password_invalid=HasÅo je zÅe. SprÅbuj jeszcze roz.
password_ok=OK
password_cancel=Pociep

printing_not_supported=PozÅr: Ta przeglÅndarka niy coÅkiym Ã´bsuguje durk.
printing_not_ready=PozÅr: Tyn PDF niy ma za tela zaladowany do durku.
web_fonts_disabled=Necowe fÅnty sÅm zastawiÅne: niy idzie uÅ¼yÄ wkludzÅnych fÅntÅw PDF.

