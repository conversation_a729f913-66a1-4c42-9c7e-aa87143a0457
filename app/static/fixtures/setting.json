[{"model": "app.colorprojectsetting", "fields": {"id": 1, "active": true, "code": "banner-color1", "css_code": "background: #53565A;\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 2, "active": true, "code": "banner-color2", "css_code": "background: #3B5375;\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 3, "active": true, "code": "banner-color3", "css_code": "background: linear-gradient(to right, #3b5375, #33578b, #2a5aa1, #255db6, #2a5ecb);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 4, "active": true, "code": "banner-color4", "css_code": "background: linear-gradient(to left, #6fa8ed, #5a96e6, #4783de, #3771d5, #2a5ecb);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 5, "active": true, "code": "banner-color5", "css_code": "background: #6EB370;\n    background: linear-gradient(135deg, #6eb370, #26b89b, #00b8c5, #00b2e3, #6fa8ed);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 6, "active": true, "code": "banner-color6", "css_code": "background-color: #fcfcfc;\n    background: linear-gradient(to right, #6eb370, #94be72, #b8c979, #dad384, #fadd94);\n    ;\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 7, "active": true, "code": "banner-color7", "css_code": "background: linear-gradient(to right, #ffa254, #ffac83, #ffbbac, #ffcecf, #ffe3e8);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 8, "active": true, "code": "banner-color8", "css_code": "background: linear-gradient(135deg, #ea6586, #f2879e, #f8a6b6, #fdc5cf, #ffe3e8);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 9, "active": true, "code": "banner-color9", "css_code": "background: linear-gradient(135deg, #c68ed5, #d483c6, #df77b3, #e66d9e, #ea6586);\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 10, "active": true, "code": "banner-color10", "css_code": "background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 11, "active": true, "code": "banner-color11", "css_code": "background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 12, "active": true, "code": "banner-color12", "css_code": "background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 13, "active": true, "code": "banner-color13", "css_code": "background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));\n\n    >div:first-child {\n        color: white;\n    }", "text_color_css": "color: white;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 14, "active": true, "code": "banner-color14", "css_code": "background: linear-gradient(135deg, rgb(247, 246, 247), rgb(232, 239, 239), rgb(217, 232, 231), rgb(201, 224, 224), rgb(186, 217, 216));\n\n    >div:first-child {\n        color: black;\n    }", "text_color_css": "color: black;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 15, "active": true, "code": "banner-color15", "css_code": "background: linear-gradient(135deg, rgb(254, 254, 254), rgb(246, 248, 243), rgb(238, 241, 232), rgb(231, 235, 222), rgb(223, 229, 211));\n\n    >div:first-child {\n        color: black;\n    }", "text_color_css": "color: black;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 16, "active": true, "code": "banner-color16", "css_code": "background: linear-gradient(135deg, rgb(251, 251, 251), rgb(249, 246, 238), rgba(247, 241, 225, 0.5), rgba(245, 236, 213, 0.6), rgba(242, 231, 200, 0.7));\n\n    >div:first-child {\n        color: black;\n    }", "text_color_css": "color: black;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 17, "active": true, "code": "banner-color17", "css_code": "background: linear-gradient(135deg, rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));\n\n    >div:first-child {\n        color: black;\n    }", "text_color_css": "color: black;"}}, {"model": "app.colorprojectsetting", "fields": {"id": 18, "active": true, "code": "banner-color18", "css_code": "background: linear-gradient(315deg, #f0f0f0, #f3f3f3, #f6f6f6, #f9f9f9, #fcfcfc);\n\n    >div:first-child {\n        color: black;\n    }", "text_color_css": "color: black;"}}, {"model": "app.fontprojectsetting", "fields": {"id": 1, "active": true, "code": "code-font1", "css_code": "font-family: \"corporate-logo-ver2\", sans-serif;\n    font-weight: 700;\n    font-style: normal;\n\n    /* font-size: 30px; */\n    /* font-size: 38px; */\n    font-size: clamp(1.875rem, 1.375rem + 2.5vw, 2.375rem);\n\n    white-space: nowrap;", "sample_code": "日本語コードネームを表示"}}, {"model": "app.fontprojectsetting", "fields": {"id": 2, "active": true, "code": "code-font2", "css_code": "font-family: \"dnp-shuei-mincho-pr6n\", sans-serif;\n    font-weight: 400;\n    font-style: normal;\n\n    /* font-size: 24px; */\n    /* font-size: 36px; */\n    font-size: clamp(1.5rem, 0.75rem + 3.75vw, 2.25rem);\n\n    white-space: nowrap;", "sample_code": "日本語コードネームを表示"}}, {"model": "app.fontprojectsetting", "fields": {"id": 3, "active": false, "code": "code-font3", "sample_code": "CODE NAME", "css_code": "    font-family: \"din-2014\", sans-serif;\n    font-weight: 800;\n    font-style: normal;\n\n    /* font-size: 40px; */\n    /* font-size: 44px; */\n\n    font-size: clamp(2.5rem, 2.25rem + 1.25vw, 2.75rem);\n    text-transform: uppercase;"}}, {"model": "app.fontprojectsetting", "fields": {"id": 4, "active": true, "code": "code-font4", "sample_code": "CODE NAME", "css_code": "font-family: \"futura-pt\", sans-serif;\n    font-weight: 300;\n    font-style: italic;\n\n    /* font-size: 38px; */\n    /* font-size: 42px; */\n    font-size: clamp(2.375rem, 2.125rem + 1.25vw, 2.625rem);"}}, {"model": "app.fontprojectsetting", "fields": {"id": 5, "active": true, "code": "code-font5", "sample_code": "CODE NAME", "css_code": "font-family: \"trajan-pro-3\", serif;\n    font-weight: 300;\n    font-style: normal;\n\n    /* font-size: 32px; */\n    /* font-size: 34px; */\n    font-size: clamp(2rem, 1.875rem + 0.63vw, 2.125rem);\n\n    text-transform: uppercase;"}}, {"model": "app.fontprojectsetting", "fields": {"id": 6, "active": true, "code": "code-font6", "sample_code": "CODE NAME", "css_code": "font-family: \"lindsey-signature\", sans-serif;\n    font-weight: 400;\n    font-style: normal;\n    font-size: 36px;"}}]