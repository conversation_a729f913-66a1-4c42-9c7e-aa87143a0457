/* eslint camelcase: ["error", {ignoreGlobals: true}] */
/* global $, readDataUpload, noUiSlider, WaveSurfer, alert */
let has_file_upload = false;
Dropzone.autoDiscover = false;
let form;
let profile_creator_dom = $('.profile');
let disable_setting = false;
let need_approve = false;
let currentDom;
let formData = {
  "profile_id": "",
  "banner": "",
  "official_site": "",
  "twitter_link": "",
  "facebook_link": "",
  "instagram_link": "",
  "youtube_link": "",
  "stage_name": "",
  "type": "",
  "theme_quote": "",
  "x_banner": "",
  "y_banner": "",
  "width_banner": "",
  "height_banner": "",
  "avatar": "",
  "x": "",
  "y": "",
  "width": "",
  "height": "",
  "sale_contents": [
    {
      "id": "",
      "title": "",
      "price": "",
      "max_price": "",
      "desc": "",
      "image": "",
      "x": "",
      "y": "",
      "width": "",
      "height": "",
      "sale_type": "",
      "audios": [
        {
          "file": "",
        }
      ],
      "type": "new", //unchanged, new, edited, approved
      "tags_content": "",
      "customizable_sale_setting": "",
      "credit": "",
      "created_year": "",
      "start_time":"" ,
      "end_time": "",
      "song_attribute1_min": "",
      "song_attribute1_max": "",
      "song_attribute2_min": "",
      "song_attribute2_max": "",
      "content_type": "", //  1,2,3...(2mix, music, .... )
      "show_thumbnail": "", // color, image,
      "sale_youtube_link": "",
    }
  ],
    "footer": {
    "copyright": "",
    "item_menu": [{
      "type_footer": "1", // 1: footer menu 1; 2: footer menu 2
      "type": "new", // new/edited
      "order": "",
      "url": "",
      "title_jp": "",
      "title_en": "",
      "id": "", // real id or random id
    }],
    "social_link": [{
      "type": "new", // new/edited
      "type_social_link": "", // ['official_site', 'twitter_link', 'facebook_link', 'instagram_link', 'youtube_link', 'tiktok_link', 'document_link']
      "order": "",
      "url": "",
      "id": "", // real id or random id
    }],
    "list_delete_item_menu": [],
    "list_delete_social_link": []
  },
  "list_approve": [
  ],
  "list_unchanged": [
  ],
  "list_edited": [
  ]
}
let formDataApprove = {
    "profile_id": "",
    "banner": "",
    "official_site": "",
    "twitter_link": "",
    "facebook_link": "",
    "instagram_link": "",
    "youtube_link": "",
    "stage_name": "",
    "type": "",
    "theme_quote": "",
    "profile_quote": "",
    "x_banner": "",
    "y_banner": "",
    "width_banner": "",
    "height_banner": "",
    "avatar": "",
    "x": "",
    "y": "",
    "width": "",
    "height": "",
    "sale_contents": [],
    "list_approve": [],
    "list_unchanged": [],
    "list_edited": [],
    "list_deleted": [],
    "upgrade": false,
}

let listFileTemp = {

}

let listMenuMainItemDeleted = []

let listMenuSocialItemDeleted = []

let listMenuSubItemDeleted = []

const bannerCropper = {
  viewMode: 1,
  rotatable: false,
  aspectRatio: 5,
  minCropBoxWidth: 300,
  minCropBoxHeight: 150,
  minContainerHeight: 300
}

const avatarCropper = {
  viewMode: 1,
  rotatable: false,
  aspectRatio: 1,
  minCropBoxWidth: 200,
  minCropBoxHeight: 200,
  minContainerHeight: 300
}

const videoCropper = {
  viewMode: 1,
  rotatable: false,
  aspectRatio: 1.8,
  minCropBoxWidth: 200,
  minCropBoxHeight: 150,
  minContainerHeight: 300
}

let settingCurrentSession = {
  type: 1,
  attr1_min: 2,
  attr1_max: 2,
  attr2_min: 2,
  attr2_max: 2,
  attr1_base: '',
  attr2_base: '',
  chart: '',
  show_thumbnail: 'color',
  default_color: 'FCFCFC',
  file: {},
  file_preview: '',
  background: '',
}

let hovering = false;
let hovering_id = 0;
let hover_zooming = false;
let close_full_screen = false;
$(document).ready(function () {
  window.onpageshow = function(event) {
    if (event.persisted) {
      window.location.reload()
    }
  };

  if (user_pk == $('.profile').data('user')) {
    $(".sheader-link[data-show^='profile']").each(function () {
      $(this).addClass('current');
    })
  }

  $('#id_banner').attr({accept: 'image/*'});
  $('#id_avatar').on('change', function () {
    $('.profile__avatar-img').removeClass('creator');
    readDataUpload('#id_avatar', avatarCropper, function() {
      $('.profile__avatar').attr('data-edited', true);
      if (checkButtonSubmit() > 0) {
        showButtonSubmit(profile_creator_dom.attr('data-role'));
      }
    });

  })

  $('#id_banner').on('change', function () {
    readDataUpload('#id_banner', bannerCropper, function() {
      $('.profile__cover-image').attr('data-edited', true);
      if (checkButtonSubmit() > 0) {
        showButtonSubmit(profile_creator_dom.attr('data-role'));
      }
    });
  })

  $(document).on('click', '.sample-audio-item:not(.playing-navi) .sample-audio-thumbnail:not(.upload-new-audio) .sample-audio-playpause-button:not(.btn-preview-album)', function () {
    const thumbnail = $(this).parent()
    const audio = thumbnail.find('audio');
    let music = false, voice = false, sound = false;

    let type = thumbnail.siblings('.sample-audio-info').find('.sample-audio-title').attr('data-content-type');
    if(type=='music') {
      music = true
    } else if(type=='voice') {
      voice = true
    } else if(type=='sound_effect') {
      sound = true
    }

    checkPauseCondition(thumbnail);
    playPauseInNavigation('', 'play', audio);
    $('.playing-navi').each(function(i, e) {
      if(thumbnail.parent()[0] == e) {
        return
      }
      let a = $(e).find('audio');
      let currentTime = a.attr('currentTime');
      if(currentTime) {
        a[0].currentTime = currentTime;
        a[0].play();
      }

      let type = $(e).find('.sample-audio-info .sample-audio-title').attr('data-content-type');
      if(type=='music') {
        music = true
      } else if(type=='voice') {
        voice = true
      } else if(type=='sound_effect') {
        sound = true
      }
    })

    showToastPlayMix(music, voice, sound)
  })

  $(document).on('click', '.sample-audio-item.playing-navi  .sample-audio-thumbnail:not(.upload-new-audio) .sample-audio-playpause-button:not(.btn-preview-album)', function () {
    const audio = $(this).parent().find('audio');
    playPauseInNavigation('', 'pause', audio);
  })

  $('.profile__philosophy-quote, .profile__profile-quote').each(function(i, e) {
    highlightURLInText(e);
  })

  $('.sample-audio-container').on('click', '.upload-new-audio, .edit-sample-audio-btn', function () {
    $('#modal_album').remove();
    disable_setting = false;
    let saleContentType = '4';
    let formTitle = '作品を登録';
    let saleContentTitle = '';
    let saleContentDesc = '';
    let saleContentPrice = '';
    let formSubmitLabel = 'OK';
    let newItemDom;
    let currentItemDom;
    let currentSaleContentImage;
    let currentContentType = 'music';
    let currentContentMin1 = '2';
    let currentContentMax1 = '4';
    let currentContentMin2 = '2';
    let currentContentMax2 = '4';
    let saleContentCreatedYear = '',
      saleContentCredit = '',
      saleContentHashTag = '',
      saleContentShowThumbnail = '',
      saleContentCustomSaleNote = '';
    let saleYoutubeLink;
    let startPrice = '', endPrice = '', startTime = '', endTime = '';
    let is_edit = false;
    if($(this).is('.edit-sample-audio-btn')) {
      is_edit = true;
      currentItemDom = $(this).parents('.sample-audio-item');
      saleContentTitle = $(this).parents('.sample-audio-info').find('.sample-audio-title').text();
      saleYoutubeLink = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-sale-youtube-link');
      saleContentDesc = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-desc');
      saleContentType = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-type');
      saleContentPrice = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-price');
      saleContentCreatedYear = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-created-year');
      saleContentCredit = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-credit');
      saleContentHashTag = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-hashtag');
      saleContentShowThumbnail = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-show-thumbnail');
      saleContentCustomSaleNote = $(this).parents('.sample-audio-info').find('.sample-audio-title').attr('data-customizable-sale-setting');
      currentSaleContentImage = $(this).parents('.sample-audio-item').find('.sample-audio-thumbnail')[0].style.backgroundImage;
      currentContentType = currentItemDom.find('.sample-audio-title').attr('data-content-type');
      currentContentMin1 = currentItemDom.find('.sample-audio-title').attr('data-attribute-min1');
      currentContentMax1 = currentItemDom.find('.sample-audio-title').attr('data-attribute-max1');
      currentContentMin2 = currentItemDom.find('.sample-audio-title').attr('data-attribute-min2');
      currentContentMax2 = currentItemDom.find('.sample-audio-title').attr('data-attribute-max2');
      let startTimeVal = currentItemDom.find('.sample-audio-title').attr('data-auctions-start-time');
      let endTimeVal = currentItemDom.find('.sample-audio-title').attr('data-auctions-end-time');
      let startPriceVal = currentItemDom.find('.sample-audio-title').attr('data-auctions-start-price');
      let endPriceVal = currentItemDom.find('.sample-audio-title').attr('data-auctions-end-price');
      let defaultColorVal = currentItemDom.find('.sample-audio-title').attr('data-default-color');
      if(!currentSaleContentImage || currentSaleContentImage === 'initial' && /^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/gm.test(defaultColorVal)) {
        currentSaleContentImage = defaultColorVal;
      }

      if (startTimeVal) {
        startTime = 'value="' + startTimeVal + '"';
      }

      if (endTimeVal) {
        endTime = 'value="' + endTimeVal + '"';
      }

      if (startPriceVal && startPriceVal !== '0') {
        startPrice = 'value=' + parseInt(startPriceVal);
      }

      if (endPriceVal && endPriceVal !== '0') {
        endPrice = 'value=' + parseInt(endPriceVal);
      }
      if(!currentItemDom.attr('data-sale')) {
        currentItemDom.attr('data-sale', 'sale-' + currentItemDom.attr('data-sale-content-id'))
      }
    } else {
      let lastNode =  $(this).parents('.sample-audio-container').find('.sample-audio-item').first()[0];
      let hasCode = (Math.random() + 1).toString(36).substring(7);
      newItemDom = $(`<div class="sample-audio-item rel editable new-sale-content hide" data-sale="sale-${hasCode}" data-sale-content-id="sale-${hasCode}" data-sale-id="sale-${hasCode}" data-bookmark-state="False">
              <div class="sample-audio-thumbnail" style="background: #FCFCFC">
                <div class="sample-audio-playpause-button"></div>
                <audio loop src="" preload="none"></audio>
              </div>
              <div class="sample-audio-info">
                <div class="sample-audio-title"
                     data-desc=""
                     data-type=""
                     data-price=""
                     title=""></div>
                <div class="sample-audio-sale-type"></div>
                <div class="drag-sample-audio-btn drag-button btn--disabled" title="新規作品は保存する前に、並べ替えられません。">
                  <i class="fas fa-arrows-alt"></i>
                </div>
                <div class="delete-sample-audio-btn delete-button">
                  <i class="icon icon--sicon-trash"></i>
                </div>
                <div class="edit-sample-audio-btn edit-button">
                  <i class="icon icon--sicon-pencil"></i>
                </div>
              </div>
              <input type="file" class="new-audio-file hide">
              <div class="menu-checking side-rounded-menu hide">
                <div class="edit-btn menu-btn"></div>
                <div class="approve-btn menu-btn"></div>
              </div>
            </div>`).insertBefore(lastNode);
    }
    let album_modal = bootbox.confirm({
      title: `<div class="modal__title">${formTitle}</div>`,
      swapButtonOrder: false,
      size: "medium",
      backdrop: false,
      closeButton: false,
      animate:false,
      message: htmlForm(
        saleContentTitle,
        saleYoutubeLink,
        saleContentDesc,
        saleContentPrice,
        saleContentCreatedYear,
        saleContentCredit,
        saleContentHashTag,
        currentSaleContentImage,
        currentContentType,
        currentContentMin2,
        currentContentMax2,
        currentContentMin1,
        currentContentMax1,
        startTime,
        endTime,
        startPrice,
        endPrice,
        $(this).parents('.sample-audio-info').siblings('.sample-audio-thumbnail'),
        saleContentShowThumbnail,
        saleContentCustomSaleNote
      ),
      buttons: {
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-right btn-round'
        },
        confirm: {
          label: formSubmitLabel,
          className: 'btn--primary btn-right btn-round btn-big',
        },
      },
      callback: function (result) {
        if (result) {
          let targetDom;
          let is_edit = true;
          if (newItemDom) {
            targetDom = newItemDom;
            is_edit = false;
          } else if (currentItemDom) {
            targetDom = currentItemDom
          }
          let content_type = $('.sale-content-content-type .input-radio input:checked');
          if(!content_type.length) {
            $('.sale-content-content-type .input-radio input[value="music"]').trigger('click');
          }
          targetDom.find('.sample-audio-title').attr('data-content-content-type', targetDom.find('.sample-audio-title').attr('data-content-type'));
          targetDom.find('.sample-audio-title').attr('data-content-type', content_type.val());
          targetDom.find('.sample-audio-title').attr('data-content-attribute-min1', targetDom.find('.sample-audio-title').attr('data-attribute-min1'));
          targetDom.find('.sample-audio-title').attr('data-attribute-min1',  $('.show.upload__slider-item .upload__slider-bar').eq(1).attr('data-min_current'));
          targetDom.find('.sample-audio-title').attr('data-content-attribute-min2', targetDom.find('.sample-audio-title').attr('data-attribute-min2'));
          targetDom.find('.sample-audio-title').attr('data-attribute-min2',  $('.show.upload__slider-item .upload__slider-bar').eq(0).attr('data-min_current'));
          targetDom.find('.sample-audio-title').attr('data-content-attribute-max1', targetDom.find('.sample-audio-title').attr('data-attribute-max1'));
          targetDom.find('.sample-audio-title').attr('data-attribute-max1',  $('.show.upload__slider-item .upload__slider-bar').eq(1).attr('data-max_current'));
          targetDom.find('.sample-audio-title').attr('data-content-attribute-max2', targetDom.find('.sample-audio-title').attr('data-attribute-max2'));
          targetDom.find('.sample-audio-title').attr('data-attribute-max2',  $('.show.upload__slider-item .upload__slider-bar').eq(0).attr('data-max_current'));

          // check audio file
          let segmentActive = parseInt($('.segment-item.active').attr('data-option'))
          if (!listFileTemp[targetDom.attr('data-sale')] && (newItemDom || (!currentItemDom && listDeleteAudio[targetDom.attr('data-sale')])) && segmentActive === 1 ) {
            $('.upload-sale').addClass('has-error')
            return false;
          }

          let sale_type = $('input[name="sale-type"]:checked')
          // if (sale_type.val() === '3' && !$('input.sale-content-price-input').val().length) {
          //   showErrorMessage($('.sale-content-price.input-container'));
          //   return false;
          // } else {
            let sale_type_val = sale_type.val();
            let sale_type_text = sale_type_val !== '4' ? sale_type.siblings('.sale_type_text').html() : '';

            if (targetDom) {
              targetDom.find('.sample-audio-title').attr('data-old-type', targetDom.find('.sample-audio-title').attr('data-type'));
              targetDom.find('.sample-audio-title').attr('data-type', sale_type.val());
              // if (sale_type.val() === '3') {
              //   let price = $('input.sale-content-price-input').val();
              //   targetDom.find('.sample-audio-title').attr('data-price', price)
              //   targetDom.find('.sample-audio-sale-type').html(sale_type_text +
              //     ': ' + price + ' 円');
              // } else {
                targetDom.find('.sample-audio-sale-type').html(sale_type_text);
              // }
            }
          // }

          let title = $('#id_title');
          if(!title.val().trim()) {
            showErrorMessage(title.parents('.input-container'));
            return false;
          } else {
            targetDom.find('.sample-audio-title').attr('data-content-title', targetDom.find('.sample-audio-title').attr('data-title'))
            targetDom.find('.sample-audio-title').attr('data-title', title.val().trim());
            targetDom.find('.sample-audio-title').html(title.val().trim())
          }

          let desc = $('#id_desc');
          // if(!desc.val().trim()) {
          //   showErrorMessage(desc.parents('.input-container'));
          //   return false;
          // } else {
            targetDom.find('.sample-audio-title').attr('data-content-desc', targetDom.find('.sample-audio-title').attr('data-desc'));
            targetDom.find('.sample-audio-title').attr('data-desc', desc.val().trim());
          // }

          let created_year = $('#id_created_year');
          // if(!created_year.val().trim()) {
          //   showErrorMessage(created_year.parents('.input-container'));
          //   return false;
          // } else {
            targetDom.find('.sample-audio-title').attr('data-content-created-year', targetDom.find('.sample-audio-title').attr('data-created-year'))
            targetDom.find('.sample-audio-title').attr('data-created-year', created_year.val().trim())
          // }

          let credit = $('#id_credit');
          // if(!credit.val().trim()) {
          //   showErrorMessage(credit.parents('.input-container'));
          //   return false;
          // } else {
            targetDom.find('.sample-audio-title').attr('data-content-credit', targetDom.find('.sample-audio-title').attr('data-credit'));
            targetDom.find('.sample-audio-title').attr('data-credit', credit.val().trim());
          // }

          let hashtag = $('#id_hashtag');
          // if(!hashtag.val().trim()) {
          //   showErrorMessage(hashtag.parents('.input-container'));
          //   return false;
          // } else {
            targetDom.find('.sample-audio-title').attr('data-content-hashtag', targetDom.find('.sample-audio-title').attr('data-hashtag'))
            targetDom.find('.sample-audio-title').attr('data-hashtag', hashtag.val().trim())
          // }

          let custom_sale_setting =  $('#id_customizable_sale_setting');
          // if(!custom_sale_setting.val().trim()) {
          //   // showErrorMessage(custom_sale_setting.parents('.input-container'));
          //   // return false;
          // } else {
            targetDom.find('.sample-audio-title').attr('data-content-customizable-sale-setting', targetDom.find('.sample-audio-title').attr('data-customizable-sale-setting'))
            targetDom.find('.sample-audio-title').attr('data-customizable-sale-setting', custom_sale_setting.val().trim())
          // }

          // let audio_containter = $('.sale-content-modal-row.flex-column');
          // if(!audio_containter.find('.sale-content-audio').length) {
          //   showErrorMessage(audio_containter);
          //   return false;
          // }

          targetDom.removeClass('hide');

          if(currentItemDom) {
            currentItemDom.attr({'data-edited': true});
          } else if (newItemDom) {
            newItemDom.attr({'data-new': true});
          }

          // let title_dom = targetDom.find('.sample-audio-title');
          // let slider = $('.upload__slider-item.show .upload__slider-bar')
          // title_dom.attr('data-content-type', $('#sale_content_content_type').val());
          // title_dom.attr('data-attribute-min1', slider.eq(0).attr('data-min_current'));
          // title_dom.attr('data-attribute-max1', slider.eq(0).attr('data-max_current'));
          // title_dom.attr('data-attribute-min2', slider.eq(1).attr('data-min_current'));
          // title_dom.attr('data-attribute-max2', slider.eq(1).attr('data-max_current'));

          // Set thumbnail
          let sale_content_image = $('#id_sale_content_image');
          let selectedColor = $('.sale-content-color-selection .color-block.active');
          let sale_image = listFileImage[targetDom.attr('data-sale')];
          targetDom.find('.sample-audio-title').attr('data-content-show-thumbnail', targetDom.find('.sample-audio-title').attr('data-show-thumbnail'));
          targetDom.find('.sample-audio-title').attr('data-content-default-color', targetDom.find('.sample-audio-title').attr('data-default-color'));
          targetDom.find('.sample-audio-title').attr('data-content-background', targetDom.find('.sample-audio-title').attr('data-background'));
          if ($('#thumbnail_color').is(':checked') && selectedColor.length) {
              targetDom.find('.sample-audio-title').attr('data-show-thumbnail', 'color');
              targetDom.find('.sample-audio-title').attr('data-default-color', '#' + selectedColor.attr('data-color'));
              targetDom.find('.sample-audio-thumbnail').css('background', '#' + selectedColor.attr('data-color'));
              targetDom.find('.sample-audio-title').attr('data-background', 'background: #' + selectedColor.attr('data-color'));
              targetDom.find('.sample-audio-thumbnail input.new-sample-audio-thumbnail, ' +
                  '.sample-audio-thumbnail input.width_sale_content, ' +
                  '.sample-audio-thumbnail input.height_sale_content, ' +
                  '.sample-audio-thumbnail input.x_sale_content, ' +
                  '.sample-audio-thumbnail input.y_sale_content').remove();
          } else {
              if (sale_image) {
                  targetDom.find('.sample-audio-title').attr('data-show-thumbnail', 'image');
                  let background = $('.sale-content-image')[0].style.backgroundImage;
                  targetDom.find('.sample-audio-title').attr('data-background', background);
                  targetDom.find('.sample-audio-thumbnail').css('background-image', background);
                  targetDom.find('.sample-audio-thumbnail').css('background-size', 'cover');
                  targetDom.find('.sample-audio-thumbnail').css('background-position', 'center');
                  targetDom.find('.sample-audio-thumbnail').append(`
                    <input type="file" class="hide new-sample-audio-thumbnail">
                    <input type="text" class="hide x_sale_content">
                    <input type="text" class="hide y_sale_content">
                    <input type="text" class="hide width_sale_content">
                    <input type="text" class="hide height_sale_content">`);
                  targetDom.find('.new-sample-audio-thumbnail')[0].files = sale_content_image[0].files;
                  targetDom.find('.sample-audio-thumbnail .x_sale_content').val(listX[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .y_sale_content').val(listY[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .width_sale_content').val(listHeight[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .height_sale_content').val(listWidth[targetDom.attr('data-sale')]);
              }
          }

          if($('.btn-modal-audio-setting.video-setting').length || ($('.upload-sale .account__file.sale-content-audio audio').attr('data-file-type') === 'movie' && $('.upload-sale .account__file.sale-content-audio').css('display') !== 'none')) {
            targetDom.find('.sample-audio-thumbnail').attr('data-file-type', 'movie');
            targetDom.find('.sample-audio-thumbnail video').remove();
            let source;
            if(listFileTemp[targetDom.attr('data-sale')]) {
              source = URL.createObjectURL(listFileTemp[targetDom.attr('data-sale')]);
            } else {
              source = currentItemDom.find('.sample-audio-thumbnail audio').get(0).src
            }
            targetDom.find('.sample-audio-thumbnail').append(`
              <video preload="none" style="width: 100%; height: 100%; max-height: 100%; background: white; display: none;">
                <source src="${source}">
              </video>
            `)

            let background = $('.sale-content-video-image')[0].style.backgroundImage;
            targetDom.find('.sample-audio-title').attr('data-background', background);
            targetDom.find('.sample-audio-title').attr('data-show-thumbnail', 'image');
            targetDom.find('.sample-audio-thumbnail').css('background-image', background);
            targetDom.find('.sample-audio-thumbnail').css('background-size', 'cover');
            targetDom.find('.sample-audio-thumbnail').css('background-position', 'center');
            if(!targetDom.find('.sample-audio-thumbnail input.x_sale_content').length) {
              targetDom.find('.sample-audio-thumbnail').append(`
                    <input type="file" class="hide new-sample-audio-thumbnail">
                    <input type="text" class="hide x_sale_content">
                    <input type="text" class="hide y_sale_content">
                    <input type="text" class="hide width_sale_content">
                    <input type="text" class="hide height_sale_content">`);
                  targetDom.find('.sample-audio-thumbnail .x_sale_content').val(listX[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .y_sale_content').val(listY[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .width_sale_content').val(listHeight[targetDom.attr('data-sale')]);
                  targetDom.find('.sample-audio-thumbnail .height_sale_content').val(listWidth[targetDom.attr('data-sale')]);
            }
          } else {
            if($('.btn-modal-audio-setting.audio-setting').length || ($('.upload-sale .account__file.sale-content-audio audio').attr('data-file-type') === 'audio' && $('.upload-sale .account__file.sale-content-audio').css('display') !== 'none')) {
              targetDom.find('.sample-audio-thumbnail').attr('data-file-type', 'audio');
            } else {
              targetDom.find('.sample-audio-thumbnail').attr('data-file-type', '');
            }
            targetDom.find('.sample-audio-thumbnail video').remove();
          }

          // let checkboxs = $('.auctions-field input[type=checkbox]')
          let list_attr = ['data-auctions-start-time', 'data-auctions-end-time', 'data-auctions-start-price', 'data-auctions-end-price'];
          let list_old_attr = ['data-content-auctions-start-time', 'data-content-auctions-end-time', 'data-content-auctions-start-price', 'data-content-auctions-end-price'];
          let list_input = ['auction_start_time_datetimepicker', 'auction_end_time_datetimepicker', 'auction_price_time_input', 'auction_end_price_input'];
          list_input.forEach(function(item, index) {
            let input = $("input#" + item);
            targetDom.find('.sample-audio-title').attr(list_old_attr[index], targetDom.find('.sample-audio-title').attr(list_attr[index]));
            if(input && input.val().length) {
              targetDom.find('.sample-audio-title').attr(list_attr[index], input.val().replaceAll(',',''));
            } else {
              targetDom.find('.sample-audio-title').attr(list_attr[index], '');
            }
          })

          let start_price = targetDom.find('.sample-audio-title').attr('data-auctions-start-price')
          let end_price = targetDom.find('.sample-audio-title').attr('data-auctions-end-price')
          if(start_price && end_price) {
            targetDom.find('.sample-audio-sale-type').html(convertPrice(start_price) + "円 - " + convertPrice(end_price) + "円");
          } else if (start_price) {
            targetDom.find('.sample-audio-sale-type').html(convertPrice(start_price) + "円 - ");
          } else if (end_price) {
            targetDom.find('.sample-audio-sale-type').html(" - " + convertPrice(end_price) + "円");
          }

          if(listFileTemp[targetDom.attr('data-sale')]) {
            listFile[targetDom.attr('data-sale')] = listFileTemp[targetDom.attr('data-sale')];
            targetDom.find('.sample-audio-thumbnail audio').attr('src', URL.createObjectURL(listFile[targetDom.attr('data-sale')]));
            delete listFileTemp[targetDom.attr('data-sale')];
            targetDom.find('.sample-audio-thumbnail audio').attr('data-album', targetDom.attr('data-sale').replace('sale-', ''));
            targetDom.find('.sample-audio-thumbnail audio').attr('data-name', listFile[targetDom.attr('data-sale')].name);
            if(listFile[targetDom.attr('data-sale')].name.match(/\.(mp4|MP4|mov|MOV)$/)) {
              targetDom.find('.sample-audio-thumbnail audio').attr('data-file-type', 'movie');
              targetDom.find('.sample-audio-thumbnail .sample-audio-playpause-button').addClass('btn-preview-album');
            } else if (listFile[targetDom.attr('data-sale')].name.match(/\.(mp3|MP3|wav|WAV)$/)) {
              targetDom.find('.sample-audio-thumbnail audio').attr('data-file-type', 'audio');
              targetDom.find('.sample-audio-thumbnail .sample-audio-playpause-button').removeClass('btn-preview-album');
            } else if (listFile[targetDom.attr('data-sale')].name.match(/\.(PDF|pdf)$/)) {
              targetDom.find('.sample-audio-thumbnail audio').attr('data-file-type', 'pdf');
              targetDom.find('.sample-audio-thumbnail .sample-audio-playpause-button').addClass('btn-preview-album');
            } else if (listFile[targetDom.attr('data-sale')].name.match(/\.(png|PNG|jpg|JPG)$/)) {
              targetDom.find('.sample-audio-thumbnail audio').attr('data-file-type', 'image');
              targetDom.find('.sample-audio-thumbnail .sample-audio-playpause-button').addClass('btn-preview-album');
            }
          }
          submitBlock('album', $('#modal_album .bootbox-accept'))
        } else {
          if (newItemDom) {
            newItemDom.remove();
          }
          delete listFileTemp[targetDom.attr('data-sale')];
        }
      }
    });
    let targetDom;
    if (newItemDom) {
      targetDom = newItemDom;
    } else if (currentItemDom) {
      targetDom = currentItemDom
    }
         let firstClick = true;
     $('.segment-item').click(function() {
       if ($(this).hasClass('active')) {
         return; // Do nothing if the tab is already active selectTypeUpload
       }
      // Reset all segments to inactive
      $('.segment-item').removeClass('active');

      $(this).addClass('active');

       let selectedOption = $(this).data('option');
       const inputIdTitle = $('#id_title');
       const dropzoneCheck = $('.fallback.dropzone')
       if (selectedOption === 1) {
         $('.block-link-youtube').hide();
         $('.sale-content-modal-row.upload-sale').show();
         $('.sale-content-modal-row:not(.block-required)').show()
         $('.bootbox-body hr').show()
         $('.bootbox.modal.bootbox-confirm .modal-dialog .modal-footer').removeClass('has-youtube-link')
         let accountFile = $('.upload-sale .account__file');
         let checkFileExisted = accountFile.css('display') !== 'none'
         if (inputIdTitle.val() && has_file_upload ) {
           if (accountFile.length < 1 && checkFileExisted && !has_file_upload) {
             $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
           } else {
             $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').removeClass('btn--disabled');
           }
         } else {
           $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
         }
         checkModalSubmitButton(!listFileTemp[targetDom.attr('data-sale')], !listFileTemp, !has_file_upload);
       } else {
         $('.bootbox-body hr').hide()
         $('.sale-content-modal-row:not(.block-required)').hide()
         $('.block-link-youtube').show();
         $('.sale-content-modal-row.upload-sale').hide();
         $('.bootbox.modal.bootbox-confirm .modal-dialog .modal-footer').addClass('has-youtube-link')
         const inputYoutubeLink = $('#sale_youtube_link');
         let resultValidate = false;
         if (!inputYoutubeLink.val() && !firstClick){
           firstClick = false;
           resultValidate = checkVideoAvailability(inputYoutubeLink.val(), inputYoutubeLink);
         }else {
          resultValidate = true;
         }
         if (inputYoutubeLink.val() && inputIdTitle.val() && resultValidate) {
           $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').removeClass('btn--disabled');
         } else {
           $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
         }
       }
    });
    if (is_edit) {
      $('.segment-control .segment-item').removeClass('active');
      if (saleYoutubeLink) {
        listFile = {}
        $('.upload-sale .account__file').remove()
        has_file_upload = false;
        $('.segment-control .segment-item[data-option="2"]').trigger('click');
      } else {
        $('.segment-control .segment-item[data-option="1"]').trigger('click');
      }
    }

    album_modal.attr("id", "modal_album");
    album_modal.removeClass('fade');
    album_modal.addClass("modal__fullscreen");
    album_modal.find('.modal-content:not(.audio-setting)').addClass('container');
    album_modal.find('.btn-save-audio-setting').on('click', function() {
      let show_thumbnail = $('#modal-detail-audio-setting .show-thumbnail-setting input:checked').val()
      if(show_thumbnail === 'image' && settingCurrentSession.show_thumbnail !== 'image') {
        if(!listFileImage[targetDom.attr('data-sale')]) {
          $('.upload-audio-image-container').addClass('has-error');
          return false;
        }
      }
      let default_color, file;
      if(show_thumbnail === 'color') {
        let inputColor = $('#modal-detail-audio-setting .show-thumbnail-setting input:checked');
        let selectedColor = inputColor.siblings('.sale-content-color-selection').find('.row:not(.hide) .active');
        if(inputColor.length && !selectedColor.length) {
          selectedColor = inputColor.siblings('.sale-content-color-selection').find('.row:not(.hide) .color-block').first()
          selectedColor.trigger('click');
        }
        default_color = selectedColor.attr('data-color')

        file = {}
      } else {
        default_color = '';
        file = listFileImage[targetDom.attr('data-sale')]
      }
      settingCurrentSession = {
        type: $('#modal-detail-audio-setting .sale-content-content-type input:checked').val(),
        attr2_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current'),
        attr2_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current'),
        attr1_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current'),
        attr1_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current'),
        attr2_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(),
        attr1_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(),
        chart: $('#modal-detail-audio-setting .audio-chart').html(),
        show_thumbnail: $('#modal-detail-audio-setting .show-thumbnail-setting input:checked').val(),
        default_color: default_color,
        file: file,
        file_preview: $('#modal-detail-audio-setting .upload-audio-image-container .mcomment-attached').html(),
        background: $('#modal-detail-audio-setting .sale-content-image').css('background'),
      }
      $('#modal-detail-audio-setting').modal('hide');
    })

    album_modal.find('.btn-save-video-setting').on('click', function() {
      let show_thumbnail = $('#modal-detail-video-setting .show-thumbnail-setting input:checked').val()
      if(show_thumbnail === 'image' && settingCurrentSession.show_thumbnail !== 'image') {
        if(!listFileImage[targetDom.attr('data-sale')]) {
          $('.upload-audio-image-container').addClass('has-error');
          return false;
        }
      }

      $('#modal-detail-video-setting .show-thumbnail-setting input[value="image"]').trigger('click');

      let default_color, file;
      default_color = '';
      file = listFileImage[targetDom.attr('data-sale')]
      settingCurrentSession = {
        type: $('#modal-detail-audio-setting .sale-content-content-type input:checked').val(),
        attr2_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current'),
        attr2_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current'),
        attr1_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current'),
        attr1_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current'),
        attr2_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(),
        attr1_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(),
        chart: $('#modal-detail-audio-setting .audio-chart').html(),
        show_thumbnail: 'image',
        default_color: default_color,
        file: file,
        file_preview: $('#modal-detail-video-setting .upload-audio-image-container .mcomment-attached').html(),
        background: $('#modal-detail-video-setting .sale-content-video-image').css('background'),
      }
      $('#modal-detail-video-setting').modal('hide');
    })

    album_modal.off('keyup').on('keyup', function(e) {
      if(e.key == 'Escape') {
        e.stopImmediatePropagation();
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    })
    album_modal.find('.btn-close-audio-setting').on('click', function() {
      $('#modal-detail-audio-setting .sale-content-content-type input[value='+ settingCurrentSession.type +']').trigger('click');
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(1).html(settingCurrentSession.attr2_min);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current', settingCurrentSession.attr2_min)
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max').eq(1).html(settingCurrentSession.attr2_max);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current', settingCurrentSession.attr2_max)
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(settingCurrentSession.attr2_base);

      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(0).html(settingCurrentSession.attr1_min);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current', settingCurrentSession.attr1_min)
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max').eq(0).html(settingCurrentSession.attr1_max);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current', settingCurrentSession.attr1_max)
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(settingCurrentSession.attr1_base);

      $('#modal-detail-audio-setting .audio-chart').html(settingCurrentSession.chart);

      if($('#modal-detail-audio-setting .upload__range-slider .show').has('.voice-slider')) {
        convertSliderToVoiceValue($('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(1),  $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max'))
      }

      $('#modal-detail-audio-setting .show-thumbnail-setting input[value='+ settingCurrentSession.show_thumbnail +']').trigger('click');
      if(settingCurrentSession.show_thumbnail === 'color') {
        $('#modal-detail-audio-setting .show-thumbnail-setting .sale-content-color-selection .row:not(.hide) .color-block[data-color="'+ settingCurrentSession.default_color +'"]').trigger('click');
      } else {
        if (settingCurrentSession.file) {
          listFileImage[targetDom.attr('data-sale')] = settingCurrentSession.file
        } else {
          delete listFileImage[targetDom.attr('data-sale')]
        }
      }

      $('#modal-detail-audio-setting .upload-audio-image-container .mcomment-attached').html(settingCurrentSession.file_preview),
      $('#modal-detail-audio-setting .sale-content-image').css('background', settingCurrentSession.background)

      $('.upload__slider-bar').each(function () {
        $(this)[0].noUiSlider.destroy();
      });
      initSliderSaleContent();
      $('#modal-detail-audio-setting').modal('hide');
    })

    album_modal.find('.btn-close-video-setting').on('click', function() {
      $('#modal-detail-audio-setting .sale-content-content-type input[value='+ settingCurrentSession.type +']').trigger('click');
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(1).html(settingCurrentSession.attr2_min);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current', settingCurrentSession.attr2_min)
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max').eq(1).html(settingCurrentSession.attr2_max);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current', settingCurrentSession.attr2_max)
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(settingCurrentSession.attr2_base);

      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(0).html(settingCurrentSession.attr1_min);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current', settingCurrentSession.attr1_min)
      $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max').eq(0).html(settingCurrentSession.attr1_max);
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current', settingCurrentSession.attr1_max)
      $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(settingCurrentSession.attr1_base);

      $('#modal-detail-audio-setting .audio-chart').html(settingCurrentSession.chart);

      if($('#modal-detail-audio-setting .upload__range-slider .show').has('.voice-slider')) {
        convertSliderToVoiceValue($('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-min').eq(1),  $('#modal-detail-audio-setting .upload__range-slider .show .upload__slider-content .upload__slider-max'))
      }

      $('#modal-detail-audio-setting .show-thumbnail-setting input[value='+ settingCurrentSession.show_thumbnail +']').trigger('click');
      if(settingCurrentSession.show_thumbnail === 'color') {
        $('#modal-detail-audio-setting .show-thumbnail-setting .sale-content-color-selection .row:not(.hide) .color-block[data-color="'+ settingCurrentSession.default_color +'"]').trigger('click');
      } else {
        if (settingCurrentSession.file) {
          listFileImage[targetDom.attr('data-sale')] = settingCurrentSession.file
        } else {
          delete listFileImage[targetDom.attr('data-sale')]
        }
      }

      $('#modal-detail-video-setting .upload-audio-image-container .mcomment-attached').html(settingCurrentSession.file_preview),
      $('#modal-detail-video-setting .sale-content-video-image').css('background', settingCurrentSession.background)

      $('.upload__slider-bar').each(function () {
        $(this)[0].noUiSlider.destroy();
      });
      initSliderSaleContent();
      $('#modal-detail-video-setting').modal('hide');
    })

    album_modal.find('#modal-detail-audio-setting, #modal-detail-video-setting').on('hidden.bs.modal', function () {
      $('body').addClass('modal-open');
      $('.modal-open .modal').css('overflow-y', 'scroll');
    })


    album_modal.find('.btn-modal-audio-setting').on('click', function () {
      if(!$(this).hasClass('btn-disabled')) {
        if($(this).hasClass('audio-setting') || ($('.upload-sale .account__file.sale-content-audio audio').attr('data-file-type') === 'audio' && $('.upload-sale .account__file.sale-content-audio').css('display') !== 'none')) {
          //audio
          $('#modal-detail-audio-setting').modal('show');
          let show_thumbnail = $('#modal-detail-audio-setting .show-thumbnail-setting input:checked').val()
          let default_color, file;
          if(show_thumbnail === 'color') {
            default_color = $('#modal-detail-audio-setting .show-thumbnail-setting input:checked .sale-content-color-selection .row:not(.hide) .active').attr('data-color')
            file = {}

          } else {
            default_color = '';
            file = listFileImage[targetDom.attr('data-sale')]
          }
          settingCurrentSession = {
            type: $('#modal-detail-audio-setting .sale-content-content-type input:checked').val(),
            attr2_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current'),
            attr2_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current'),
            attr1_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current'),
            attr1_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current'),
            attr2_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(),
            attr1_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(),
            chart: $('#modal-detail-audio-setting .audio-chart').html(),
            show_thumbnail: $('#modal-detail-audio-setting .show-thumbnail-setting input:checked').val(),
            default_color: default_color,
            file: file,
            file_preview: $('#modal-detail-audio-setting .upload-audio-image-container .mcomment-attached').html(),
            background: $('#modal-detail-audio-setting .sale-content-image').css('background'),
          }
        } else {
          //video
          $('#modal-detail-video-setting').modal('show');
          // TODO: get video
          if(listFileTemp[targetDom.attr('data-sale')]) {
            $('#modal-detail-video-setting video source').attr('src', URL.createObjectURL(listFileTemp[targetDom.attr('data-sale')]))
            $('#modal-detail-video-setting video').get(0).load();
          // } else if (listFile[targetDom.attr('data-sale')]) {
          //   $('#modal-detail-video-setting video source').attr('src', URL.createObjectURL(listFile[targetDom.attr('data-sale')]))
          //   $('#modal-detail-video-setting video').get(0).load();
          } else {
            if($('.sample-audio-item[data-sale='+ targetDom.attr('data-sale') +']').length && $('.sample-audio-item[data-sale='+ targetDom.attr('data-sale') +']').find('.sample-audio-thumbnail').attr('data-file-type') === 'movie') {
              let version = '&v=1';
              let src = $('.sample-audio-item[data-sale='+ targetDom.attr('data-sale') +']').find('.sample-audio-thumbnail video source')[0].src;
              if(src.startsWith('blob')) {
                version = ''
              }
              $('#modal-detail-video-setting video source').attr('src', $('.sample-audio-item[data-sale='+ targetDom.attr('data-sale') +']').find('.sample-audio-thumbnail video source')[0].src  + version);
              $('#modal-detail-video-setting video').get(0).load();
            }
          }

          let show_thumbnail = $('#modal-detail-video-setting .show-thumbnail-setting input:checked').val()
          let file;
          if(show_thumbnail === 'color') {
            file = {}
            // $('#modal-detail-video-setting .sale-content-image').addClass('hide');
          } else {
            // $('#modal-detail-video-setting .sale-content-image').removeClass('hide');
            file = listFileImage[targetDom.attr('data-sale')]
          }

          initVideoThumbnailSelectionByTime(targetDom.attr('data-sale'));
          settingCurrentSession = {
            type: $('#modal-detail-audio-setting .sale-content-content-type input:checked').val(),
            attr2_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-min_current'),
            attr2_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2').attr('data-max_current'),
            attr1_min: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-min_current'),
            attr1_max: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1').attr('data-max_current'),
            attr2_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_2 .noUi-base').html(),
            attr1_base: $('#modal-detail-audio-setting .upload__range-slider .show .attribute_1 .noUi-base').html(),
            chart: $('#modal-detail-audio-setting .audio-chart').html(),
            show_thumbnail: $('#modal-detail-video-setting .show-thumbnail-setting input:checked').val(),
            default_color: '',
            file: file,
            file_preview: $('#modal-detail-video-setting .upload-audio-image-container .mcomment-attached').html(),
            background: $('#modal-detail-video-setting .sale-content-video-image').css('background'),
          }
        }
        $('.modal-open .modal').css('overflow-y', 'hidden');
      }
    })

    setTimeout(() => {$('#modal_album')[0].scrollTo(0,0)}, 100);

    //set background
    if(/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/gm.test(currentSaleContentImage)) {
      $('.color-block[data-color=' + currentSaleContentImage.replace('#', '').toUpperCase() + ']').addClass('active');
    }

    //click default color
    $('.sale-content-color-selection').on('click', '.color-block', function() {
      $(this).parents('input[name=sale_content_thumbnail_type]').trigger('click');
      $('.color-block').removeClass('active');
      $(this).addClass('active');
      $('.sale-content-modal-row .sale-content-image')[0].style.background = this.style.background;
    });

    if(disable_setting) {
      album_modal.find('.btn-modal-audio-setting').addClass('btn--disabled');
    }

    initDropzoneAudioImage('#id_sale_content_image', targetDom);
    initDropzoneVideoImage('#id_sale_content_video_image', targetDom);
    inithashTag();
    initSliderSaleContent();
    initDateTimePickerAuctions();
    initDropzoneAudio('#new-audio', targetDom);
    addCommaToPrice($('#auction_price_time_input'))
    addCommaToPrice($('#auction_end_price_input'))
    checkModalSubmitButton(!listFileTemp[targetDom.attr('data-sale')], newItemDom, (!currentItemDom && listDeleteAudio[targetDom.attr('data-sale')]));
    let saleLinkYoutube = $('#sale_youtube_link');
    $('#id_title').on('input', function () {
      if (parseInt($('.segment-item.active').attr('data-option')) === 1) {
        if (is_edit) {
          checkModalSubmitButton(!listFileTemp[targetDom.attr('data-sale')], !newItemDom, (!currentItemDom && listDeleteAudio[targetDom.attr('data-sale')]));
        } else {
          checkModalSubmitButton(!listFileTemp[targetDom.attr('data-sale')], newItemDom, (!currentItemDom && listDeleteAudio[targetDom.attr('data-sale')]));
        }
      } else {
          validateYoutubeLink(saleLinkYoutube)
      }
    });
    saleLinkYoutube.on('input', function () {
         validateYoutubeLink(saleLinkYoutube)
    });

    $('.sale-content-modal-row.flex-column').on('click', '.sale-content-audio-play', function () {
      const target = $(this);
      const audio = $(this).siblings('audio')
      audio.on('canplay', function () {
        audio.off('canplay')
        audio[0].play()
        target.removeClass('sale-content-audio-loading')
        target.addClass('sale-content-audio-pause')
        $('.sale-content-audio').find('.sale-content-audio-time').html(msToTime($('.sale-content-audio audio')[0].duration));
      })

      audio[0].play()
      audio[0].muted = false
      audio[0].loop = true
      $('.sale-content-audio').find('.sale-content-audio-time').html(msToTime($('.sale-content-audio audio')[0].duration));

      if (audio[0].readyState > 1) {
        target.removeClass('sale-content-audio-play')
        target.addClass('sale-content-audio-pause')
      } else {
        target.removeClass('sale-content-audio-play')
        target.addClass('sale-content-audio-loading')
      }
    })

    $('.sale-content-modal-row.flex-column').on('click', '.sale-content-audio-pause', function () {
      const target = $(this);
      const audio = $(this).siblings('audio')
      audio.off('canplay')
      audio.each(function (i, e) {
        e.pause()
      })
      $(this).removeClass('sale-content-audio-pause')
      $(this).addClass('sale-content-audio-play')
    })

    $('.sale-content-type input[name=sale-type][value='+ saleContentType +']').trigger('click')

    $('input#new-audio').on('change', function () {
      if(this.files.length) {
        $(this).parents('.input-container').find('.sale-content-audio').remove();
        $(this).parents('.input-container').removeClass('has-error');
        $('.sale-content-audio-add').addClass('hide');
        targetDom.find('.sample-audio-thumbnail audio').remove();
        targetDom.find('.sample-audio-thumbnail').append(`
        <audio src="${URL.createObjectURL(this.files[0])}"
               data-name="${this.files[0].name}"></audio>`)
        $('.playlist-title').parent().append(generateAudioItem(targetDom.find('.sample-audio-thumbnail')));
        targetDom.find('input.new-audio-file')[0].files = this.files;
      }
    })

    $('#id_sale_content_image').on('change', function () {
      readDataUpload('#id_sale_content_image', avatarCropper, function () {})
      $('.color-block').removeClass('active');
    })

    if(currentItemDom) {
      let content_type = currentItemDom.find('.sample-audio-title').attr('data-content-type');

      if (content_type) {
        let selected_option = $('#sale_content_content_type option[value="'+ content_type+'"]');
        $('#sale_content_content_type option').removeAttr('selected');
        if (selected_option.length) {
          selected_option.attr('selected', true);
        } else {
          $('#sale_content_content_type option').first().attr('selected', true);
        }
      }
    }

    album_modal.find('#modal-detail-audio-setting input[name="sale_content_content_type"]').on('change', function() {
      let slider = $(this).val();
      if(slider === '2mix') {
        slider = 'music'
      }

      $('.upload__range-slider').find('.show').removeClass('show');
      $('.upload__range-slider').find('.' + slider.replace('_effect', '-effects') + '-slider').addClass('show');
      $('.sale-content-color-selection .row').addClass('hide');
      $('.sale-content-color-selection .' + $(this).val()).removeClass('hide');
      if ($('input[name=sale_content_thumbnail_type]:checked').val() === 'color' && ($('.color-block.active').length && $('.color-block.active').parents('.row').is('.hide') || !$('.color-block.active').length)) {
        $('.row:not(.hide) .color-block').first().trigger('click');
      }
      let target_dom = $('.upload__range-slider').find('.' + slider.replace('_effect', '-effects') + '-slider')
      let min1_value_slider = parseFloat(target_dom.last().find('.attribute_2').attr('data-min_current'));
      let max1_value_slider = parseFloat(target_dom.last().find('.attribute_2').attr('data-max_current'));
      let min2_value_slider = parseFloat(target_dom.first().find('.attribute_1').attr('data-min_current'));
      let max2_value_slider = parseFloat(target_dom.first().find('.attribute_1').attr('data-max_current'));
      switch(slider) {
        case 'music':
          $('.audio-chart .label3').html('アコースティック')
          $('.audio-chart .label4').html('エレクトロニック')
          $('.audio-chart .label2').html('サウンド')
          $('.audio-chart .label1').html('メロディアス')
          break
        case 'sound_effect':
          $('.audio-chart .label3').html('リアリティ')
          $('.audio-chart .label4').html('創作')
          $('.audio-chart .label2').html('繊細')
          $('.audio-chart .label1').html('アグレッシブ')
          break
        case 'voice':
          $('.audio-chart .label3').html('年少')
          $('.audio-chart .label4').html('年長')
          $('.audio-chart .label2').html('低い声')
          $('.audio-chart .label1').html('高い声')
          break
        default:
          $('.audio-chart .label3').html('アコースティック')
          $('.audio-chart .label4').html('エレクトロニック')
          $('.audio-chart .label2').html('サウンド')
          $('.audio-chart .label1').html('メロディアス')
          break
      }

      let height_value = ((max2_value_slider - min2_value_slider) * 25).toString() + '%';
      let width_value = ((max1_value_slider - min1_value_slider) * 25).toString() + '%';
      let right_value = ((5 - max1_value_slider) * 25).toString() + '%';
      let bottom_value = ((min2_value_slider - 1) * 25).toString() + '%';
      $('.square').css('width', width_value);
      $('.square').css('right', right_value);
      $('.square').css('height', height_value);
      $('.square').css('bottom', bottom_value);
    })

    album_modal.find('#modal-detail-video-setting input[name="sale_content_thumbnail_video_type"]').on('change', function() {
      if($('#modal-detail-video-setting input[name="sale_content_thumbnail_video_type"]:checked').val() === 'color') {
        $('#modal-detail-video-setting .sale-content-image, #modal-detail-video-setting .sale-content-video-image').addClass('hide');
        let video = $('#modal-detail-video-setting .show-thumbnail-setting video');
        if (video.length) {
          let currentTime = video[0].currentTime;
          video[0].currentTime = currentTime;
        }
      } else {
        $('#modal-detail-video-setting .sale-content-video-image').removeClass('hide');
      }
    });
  })

  $('.sample-audio-container').on('click', '.delete-sample-audio-btn', function () {
    let sale_content_dom = $(this).parents('.sample-audio-item');
    bootbox.confirm({
      size: "small",
      title: "削除確認",
      message: `復元出来ないアクションとなりますので、本当に削除しますか?`,
      buttons: {
        confirm: {
          label: '削除',
          className: 'btn--tertiary btn-primary'
        },
        cancel: {
          label: 'いいえ',
          className: 'btn--primary btn-basic'
        }
      },
      callback: function (result) {
        if (result) {
          sale_content_dom.addClass('hide');
          if (sale_content_dom.attr('data-new') === 'true') {
            sale_content_dom.remove()
          } else {
            sale_content_dom.attr('data-deleted', true)
            sale_content_dom.removeAttr('data-edited')
          }
          submitBlock('album', $('.bootbox.modal .btn--tertiary.bootbox-accept'))
        }
      }
    });
  })

  $('.edit-philosophy-btn').on('click', function () {
    let currentThemeQuote = $(this).siblings('.profile__philosophy-quote').attr('data-title');
    bootbox.confirm({
      size: "large",
      closeButton: false,
      title: 'アーティストステートメント <span class="form-label__optional">[任意]</span><div class="form-hint" style="width: 100%;">自分の背景や制作の動機、作品や活動に共通するテーマ、これからやりたいことを簡潔に伝えましょう。</div>',
      message: `<div class="input-container">
                    <textarea id='id_theme_quote' name='theme_quote'
                          onfocus='this.style.height = this.scrollHeight + "px";'
                          oninput='this.style.height = ""; this.style.height = this.scrollHeight + "px";'
                          class="profile-edit-modal">${currentThemeQuote}</textarea>
                    <div class="error-message">Philosophyのフォーマットが正しくありません。</div>
                </div>`,
      buttons: {
        confirm: {
          label: 'OK',
          className: 'btn--primary btn-primary'
        },
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-basic'
        }
      },
      callback: function (result) {
        if (result) {
          let theme = $(this).find('textarea').val().trim();
          $('.profile__philosophy-quote').attr('data-title', theme);
          $('.profile__philosophy-quote').html(theme);
          if (theme != $('.profile__philosophy-quote').attr('data-content')) {
            $('.profile__philosophy-container').attr('data-edited', true);
          }
          if (checkButtonSubmit() > 0) {
            showButtonSubmit(profile_creator_dom.attr('data-role'));
          }
        }
      }
    });
  })

  $('.edit-profile-profile-btn').on('click', function (e) {
    e.stopPropagation();
    if($(this).parents('.profile__profile-quote').is('.new')) {
      let new_dom = $(this).parents('.profile__profile-quote');
      new_dom.attr({'data-edited': true, 'data-check': true});
      new_dom.removeAttr('data-approve data-rejected');
      $(this).siblings('.menu-checking').addClass('hide');
    }
    
    // 現在の値を確実に取得し、undefinedやnullの場合は空文字列に変換
    let profileQuoteElement = $(this).parents('.profile__profile-quote');
    let currentProfileTitle = profileQuoteElement.attr('data-title') || '';
    let currentProfileTitleEN = profileQuoteElement.attr('data-title-en') || '';
    let currentProfile = profileQuoteElement.attr('data-title-text') || '';
    let currentProfileEN = profileQuoteElement.attr('data-title-text-en') || '';
    let currentProfileToggle = profileQuoteElement.attr('data-toggle-header') || 'false';
    
    // デバッグ用ログ（本番では削除）
    console.log('Original values:', {
      title: currentProfileTitle,
      titleEN: currentProfileTitleEN,
      text: currentProfile,
      textEN: currentProfileEN,
      toggle: currentProfileToggle
    });
    
    // 初期値をグローバル変数に保存（コールバック内からアクセス可能にする）
    window.originalProfileValues = {
      title: currentProfileTitle,
      titleEN: currentProfileTitleEN,
      text: currentProfile,
      textEN: currentProfileEN,
      toggle: currentProfileToggle.toLowerCase()
    };
    
    let disable = '';
    let toggleChecked = currentProfileToggle.toLowerCase() === 'true' ? 'checked' : '';
    if (!currentProfileTitle.trim() && !currentProfileTitleEN.trim()) {
      toggleChecked = '';
      disable = 'btn--disabled'
    }

    let modal = bootbox.confirm({
      size: "medium",
      closeButton: false,
      title: ' ',
      message: `<div class="profile-input-container flex_column align-left">
                  <div class="form-group col-sm-12" style="padding: 0; margin-bottom: 16px;">
                    <span class="heading" style="line-height: 200%; margin-bottom: 8px;">見出し<span class="account__jp-astarisk-op"
                                                            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
                    <div class="bodytext-11 hint-text">ABOUT、PROFILE、WORKS、DISCOGRAPHYなど。</div>
                    <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0">
                      <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
                        <input type="text" name="block_name_jp" placeholder="プロフィール（日本語 OR 英語）" class="form-control" id="id_block_name_jp"
                              maxlength="15" value="">

                      </div>
                      <div class="profile-title-input col-sm-5 col-xs-12" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
                        <input type="text" name="block_name_en" placeholder="PROFILE（英語）" class="form-control"
                              id="id_block_name_en" maxlength="15" value="">
                      </div>
                    </div>
                  </div>

                  <div class="form-group col-sm-12 col-xs-12 profile-toggle-input" style="padding: 0; margin-bottom: 16px;">
                    <div class="custom-switch bodytext-13">
                      <label class="form-check-label ${disable}">
                        <div class="form-check-group">
                          <input class="form-check-input switch-checkbox" type="checkbox" name="switch-add-to-header"
                                id="switch-add-to-header">
                          <span class="switch-slider"></span>
                        </div>
                        <span class="switch-label bodytext-13">トップメニューにリンク</span>
                      </label>
                    </div>
                  </div>

                  <div class="form-group col-sm-12" style="padding: 0;">
                    <span class="heading" style="line-height: 200%; margin-bottom: 8px;">内容<span class="account__jp-astarisk-op"
                                                            style="margin-left: 8px; line-height: 200%; margin-bottom: 8px; font-size: 8px;">[任意]</span></span>
                    <div class="bodytext-11 hint-text">あなたのストーリーをここに。</div>
                    <div class="col-sm-12 mg-top-xs" style="margin-top: 12px; padding: 0;">
                      <div class="col-sm-12 col-xs-12"  style="padding: 0;">
                        <textarea type="text" name="profile_jp" placeholder="日本語" class="form-control" id="id_profile"
                              maxlength="3000"  style="min-height: 200px;"></textarea>
                      </div>
                      <div class="col-sm-12 col-xs-12" style="margin-top: 12px; padding: 0;">
                        <textarea type="text" name="profile_en" placeholder="ENGLISH" class="form-control"
                              id="id_profile_en" maxlength="3000" style="min-height: 200px;"></textarea>
                      </div>
                    </div>
                  </div>
                </div>`,
      buttons: {
        confirm: {
          label: 'OK',
          className: 'btn--primary btn-primary'
        },
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-basic'
        }
      },
      callback: function (result) {
        if (result) {
          // フォームから現在の値を取得
          let newTitle = $('#id_block_name_jp').val() || '';
          let newTitleEN = $('#id_block_name_en').val() || '';
          let newText = $('#id_profile').val() || '';
          let newTextEN = $('#id_profile_en').val() || '';
          let newToggle = $('#switch-add-to-header').is(':checked');
          
          // 元の値を取得
          let originalValues = window.originalProfileValues;
          
          // デバッグ用ログ（本番では削除）
          console.log('New values:', {
            title: newTitle,
            titleEN: newTitleEN,
            text: newText,
            textEN: newTextEN,
            toggle: newToggle.toString()
          });
          console.log('Comparing with original:', originalValues);
          
          // 変更があったかどうかをチェック（trimして比較）
          let hasChanges = (
            newTitle.trim() !== originalValues.title.trim() ||
            newTitleEN.trim() !== originalValues.titleEN.trim() ||
            newText.trim() !== originalValues.text.trim() ||
            newTextEN.trim() !== originalValues.textEN.trim() ||
            newToggle.toString() !== originalValues.toggle
          );
          
          console.log('Has changes:', hasChanges);
          
          // 変更がない場合は処理を終了（既存データを保持）
          if (!hasChanges) {
            console.log('No changes detected, preserving existing data');
            // グローバル変数をクリーンアップ
            delete window.originalProfileValues;
            return;
          }
          
          // 更新処理を実行
          $('.profile__profile-quote').attr('data-title', newTitle.trim());
          $('.profile__profile-quote').attr('data-title-en', newTitleEN.trim());
          $('.profile__profile-quote').attr('data-title-text', newText.trim());
          $('.profile__profile-quote').attr('data-title-text-en', newTextEN.trim());

          if(!newTitle.trim() && !newTitleEN.trim()) {
            if(newToggle) {
              $('#switch-add-to-header:checked').trigger('click');
            }
            $('.profile__profile .profile__profile-title').addClass("hide")
          } else {
            $('.profile__profile .profile__profile-title').removeClass("hide")
            $('.header-profile-artist .sheader-link.sheader-link-block[href="#profile"]').text(newTitle.trim())
          }
          $('.profile__profile-quote').attr('data-toggle-header', newToggle);
          $('.profile__profile .profile__profile-title').text(newTitle.trim());
          $('.profile__profile .profile__profile-quote span').text(newText.trim());

          if(newToggle && originalValues.toggle !== 'true') {
            $('.header-profile-artist .header-profile-pc-right').prepend(`<div class="header-profile-pc-right-link"><a class="sheader-link sheader-link-block" href="#profile">${newTitle.trim()}</a></div>`)
            $('.header-profile-artist .header-profile-sp-dropdown .header-profile-sp-button-close').after(`<div class="header-profile-sp-link"><a class="sheader-link sheader-link-block" href="#profile">${newTitle.trim()}</a></div>`)
          }

          if (!newToggle) {
            $('.header-profile-artist .header-profile-pc-right-link .sheader-link.sheader-link-block[href="#profile"], .header-profile-artist .header-profile-sp-link .sheader-link.sheader-link-block[href="#profile"]').parent().remove();
          }

          if(!newText.trim() && !newTextEN.trim()) {
            $('.profile__profile-quote').addClass('no-text')
          } else {
            $('.profile__profile-quote').removeClass('no-text')
          }

          if(!newTitle.trim() && !newText.trim() && !newTitleEN.trim() && !newTextEN.trim()) {
            $('.profile__profile-quote').addClass('no-content')
          } else {
            $('.profile__profile-quote').removeClass('no-content')
          }

          changeContent();

          $('.profile__profile-quote').attr('data-edited', true)
          submitBlock('profile_text', $('.profile-input-container').parents('.bootbox.modal').find('.bootbox-accept'))
        } else {
          if($('.profile__profile-quote').is('.new')) {
            let new_dom = $('.profile__profile-quote');
            let newblockNameJP = $('.profile__profile-quote').attr('data-title') != $('.profile__profile-quote').attr('data-content-title');
            let newBlockNameEN = $('.profile__profile-quote').attr('data-title-en') != $('.profile__profile-quote').attr('data-content-title-en');
            let newTextJP = $('.profile__profile-quote').attr('data-title-text') != $('.profile__profile-quote').attr('data-content-title-text');
            let newTextEN = $('.profile__profile-quote').attr('data-title-text-en') != $('.profile__profile-quote').attr('data-content-title-text-en');
            let newToggle = $('.profile__profile-quote').attr('data-toggle-header').toLowerCase() != $('.profile__profile-quote').attr('data-content-toggle-header').toLowerCase();
            if(newBlockNameEN || newblockNameJP || newTextEN || newTextJP || newToggle) {
              return;
            }
            new_dom.attr({'data-edited': '', 'data-check': ''});
            new_dom.removeAttr('data-approve data-rejected data-edited');
          }
        }
        
        // グローバル変数をクリーンアップ
        delete window.originalProfileValues;
        $('.profile__profile-quote .menu-checking').addClass('hide');
      }
    });

    // Bootboxモーダルが完全に表示された後に初期値を設定
    modal.on('shown.bs.modal', function() {
      console.log('Modal shown, setting initial values');
      
      // 初期値を確実に設定
      $('#id_block_name_jp').val(currentProfileTitle);
      $('#id_block_name_en').val(currentProfileTitleEN);
      $('#id_profile').val(currentProfile);
      $('#id_profile_en').val(currentProfileEN);
      
      if (toggleChecked) {
        $('#switch-add-to-header').prop('checked', true);
      }
      
      // トグルの状態に応じてボタンの有効/無効を制御
      function updateToggleState() {
        let titleJP = $('#id_block_name_jp').val().trim();
        let titleEN = $('#id_block_name_en').val().trim();
        let toggleLabel = $('.form-check-label');
        
        if (!titleJP && !titleEN) {
          $('#switch-add-to-header').prop('checked', false);
          toggleLabel.addClass('btn--disabled');
        } else {
          toggleLabel.removeClass('btn--disabled');
        }
      }
      
      // 初期状態をチェック
      updateToggleState();
      
      // 入力フィールドの変更を監視
      $('#id_block_name_jp, #id_block_name_en').on('input', updateToggleState);
      
      console.log('Initial values set:', {
        title: $('#id_block_name_jp').val(),
        titleEN: $('#id_block_name_en').val(),
        text: $('#id_profile').val(),
        textEN: $('#id_profile_en').val(),
        toggle: $('#switch-add-to-header').is(':checked')
      });
    });

    $('#id_block_name_jp').parents('.modal.bootbox').addClass('long-modal modal-centered')
    $('#id_block_name_jp').parents('.modal-body').addClass('nice-scroll')
  })

  $('.edit-profile-name-btn').on('click', function () {
    let currentStageName = $(this).parents('.profile__name').attr('title');
    bootbox.confirm({
      size: "small",
      title: "芸名",
      message: `<div class="input-container">
                    <input id='id_stage_name' name='stage_name'
                       class="profile-edit-modal-input" value="${currentStageName}"/>
                    <div class="error-message">芸名のフォーマットが正しくありません。</div>
                </div>`,
      buttons: {
        confirm: {
          label: '更新',
          className: 'btn--primary btn-primary'
        },
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-basic'
        }
      },
      callback: function (result) {
        if (result) {
          let stage_name = $(this).find('input').val().trim();
          if (!stage_name) {
            $(this).find('.input-container').addClass('has-error');
            $('.has-error input').on('input', function () {
              $(this).parents('.input-container').removeClass('has-error');
            });
            return false;
          } else {
            $('.profile__name').attr('title', stage_name).attr('data-edited', true);
            $('.profile__name span').html(stage_name);
          }
          if (checkButtonSubmit() > 0) {
            showButtonSubmit(profile_creator_dom.attr('data-role'));
          }
        }
      }
    });
  })

  $('.edit-profile-job-btn').on('click', function () {
    let currentJob = $(this).parents('.profile__job').attr('title');
    bootbox.confirm({
      size: "small",
      title: "肩書き",
      message: `<div class="input-container">
                  <input id='id_job' name='job'
                       class="profile-edit-modal-input" value="${currentJob}"/>
                  <div class="error-message">肩書きのフォーマットが正しくありません。</div></div>`,
      buttons: {
        confirm: {
          label: '更新',
          className: 'btn--primary btn-primary'
        },
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-basic'
        }
      },
      callback: function (result) {
        if(result) {
          let job = $(this).find('input').val().trim();
          if (!job) {
            $(this).find('.input-container').addClass('has-error');
            $('.has-error input').on('input', function() {
              $(this).parents('.input-container').removeClass('has-error');
            });
            return false;
          } else {
            $('.profile__job').attr('title', job).attr('data-edited', true);
            $('.profile__job span').html(job);
          }
          if (checkButtonSubmit() > 0) {
            showButtonSubmit(profile_creator_dom.attr('data-role'));
          }
        }
      }
    });
  })

  $('.profile__social-item .edit-link-btn').on('click', function () {
    let currentLink = $(this).parents('.profile__social-item').attr('data-url');
    let title = $(this).parents('.profile__social-item').attr('title');
    let name = $(this).parents('.profile__social-item').attr('data-id');
    let source = $(this).parents('.profile__social-item');
    let placeholder = '';
    switch(name) {
      case 'official_site':
        placeholder = 'https://www.soremo.jp';
        break
      case 'twitter_link':
        placeholder = 'SOREMONTER';
        break
      case 'facebook_link':
        placeholder = 'https://www.facebook.com/soremo.jp';
        break
      case 'instagram_link':
        placeholder = 'soremonster';
        break
      case 'youtube_link':
        placeholder = 'https://www.youtube.com/channel/UCTg27E5KvEjXKkOYsPwFE1w';
        break;
      default:
        break
    }
    bootbox.confirm({
      size: "medium",
      title: title,
      message: `<div class="input-container">
                  <input id='id_${name}'
                         name='${name}'
                         placeholder="${placeholder}"
                         class="profile-edit-modal-input"
                         value="${currentLink}"/>
                   <div class="error-message">URLのフォーマットが正しくありません。</div>
                 </div>`,
      buttons: {
        confirm: {
          label: '更新',
          className: 'btn--primary btn-primary'
        },
        cancel: {
          label: 'キャンセル',
          className: 'btn--tertiary btn-basic'
        }
      },
      callback: function (result) {
        if(result) {
          if(!checkAndAutoCompleteURL($(this).find('input').get(0), source)) {
            $(this).find('.input-container').addClass('has-error');
            $('.has-error input').on('input', function() {
              $(this).parents('.input-container').removeClass('has-error');
            });
            return false;
          } else {
            if (checkButtonSubmit() > 0) {
              showButtonSubmit(profile_creator_dom.attr('data-role'));
            }
          }
        }
      }
    });
  })

  $('.new').on('click', function (e) {
    if($(this).is('.sample-audio-item')) {
      var pOffset = $(this).offset();
      var x = e.pageX - pOffset.left;
      var y = e.pageY - pOffset.top;
      console.log(x, y)
      if(x < 65 && x > 12 && y > 15 && y < 45) {
        e.stopPropagation();
        showMenuApprove($(this));
      }
    } else {
        if (!checkTargetActiveButtonNew($(e.target))) {
            return
        }
      showMenuApprove($(this));
    }
  });


  $('.menu-checking .menu-btn').off().on('click', function(e) {
    e.stopImmediatePropagation();
    let new_dom = $(this).parents('.new');
    let checking_dom = $(this).parents('.menu-checking');
    if($(this).is('.edit-btn')) {
      new_dom.attr({'data-edited': true, 'data-check': true});
      new_dom.removeAttr('data-approve data-rejected');
      $(this).parents('.menu-checking').addClass('hide');
      if($(this).parents('.new').is('.header-fullscreen-container')) {
        $('.header-fullscreen-container').trigger('click');
      } else if($(this).parents('.new').is('footer.profile-footer')) {
        $('footer.profile-footer').trigger('click');
      } else {
        $(this).parents('.new').find('.edit-button').trigger('click');
      }
      if (checkButtonSubmit() > 0) {
        showButtonSubmit(profile_creator_dom.attr('data-role'));
      }
    } else if ($(this).is('.approve-btn')) {
      new_dom.attr({'data-approve': true, 'data-check':true});
      new_dom.removeAttr('data-edited data-rejected');
      if (checkButtonApprove($(this))) {
        let new_field = $('.new').length;
        let check_field = $('.new[data-check^=true]').length;
        let approve_field = $('.new[data-approve^=true]').length;
        let edit_field = $('[data-edited^=true]').length;
        if (edit_field > 0 || new_field === check_field && approve_field !== new_field) {
          if (checkButtonSubmit() > 0) {
            showButtonSubmit(profile_creator_dom.attr('data-role'));
          } else {
            $('.btn-submit-profile').addClass('hide disable');
            $('.btn-submit-profile').parents('.submit-profile-btn-container').addClass('hide disable');
            $('footer').removeClass('additional_margin_bottom');
          }
        }
        form.append('json', JSON.stringify(formDataApprove));
        $.ajax({
          type: "POST",
          url: '/accounts/approve_field_creator_profile',
          data: form,
          contentType: false,
          processData: false,
          success: function (data) {
            if (data.upgrade) {
                window.location.reload();
            }
            $('.profile').attr('data-modified', data.modified);
            // toastr.success('承認しました');
            checking_dom.remove();
            new_dom.removeClass('new').addClass('editable');
            new_dom.attr('data-approve', '');
            if (data.old_sale_content !== '0') {
              let old_dom = $('.sample-audio-item[data-sale-content-id=' + data.old_sale_content + ']');
              $(data.sale_content_html).insertBefore(old_dom);
              old_dom.remove();
            }

            if (data.header_text_update) {
              $('.header-fullscreen-container').remove();
              $('.profile').prepend(data.header_text_update)
              setBackground()
              resizeFullscreen()
              resizePhrase('25vw')
              animateCatchPhrase()
              changeContent()
              checkNewlineFooterMenu();
            } if (data.satement_update) {
              $('.statement.container').remove();
              $(data.satement_update).insertAfter($('.header-fullscreen-container'));
              initQuote();
              checkNoContent()
              changeContent()
              checkNewlineFooterMenu();
            } if (data.footer_text_update) {
              $('.profile-footer').remove();
              $(data.footer_text_update).insertAfter($('main'));
              changeContent()
              checkNewlineFooterMenu();
              if(checkCurrentLan()) {
                $('.change-language-button').attr('data-language', 'en')
                $('footer .change-language-button').find('span').text('JP')
              }
            } if (checkButtonSubmit() > 0) {
              showButtonSubmit(profile_creator_dom.attr('data-role'));
            }
          },
          error: function () {
            toastr.error("ERROR");
            new_dom.removeAttr('data-approve data-check data-edited data-rejected');
          }
        });
      }

    } else if ($(this).is('.reject-btn')) {
      new_dom.attr({'data-rejected': true, 'data-check': true});
      new_dom.removeAttr('data-approve data-edited');
      checkButtonSubmit();
    }
  })

  $('.sample-audio-container.can-sort').sortable({
    containment: "document",
    items: ".sample-audio-item:not(.upload-audio):not(.new-sale-content)",
    handle: ".drag-button",
    cursor: "move",
    axis: "x",
    start: function (event, ui) {
      ui.placeholder.height(ui.helper.outerHeight());
    },
    change: function (event, ui) {
    },
    update: function (event, ui) {
      let arr_order = [];
      let profile_id = $('.profile').attr('data-profile-id');

      $(this).find('.sample-audio-item').each(function (index, item) {
        arr_order.push(item.getAttribute('data-sale-content-id'));
      });

      $.ajax({
        type: "POST",
        url: "/update_sale_content_index",
        data: {
          'profile_id': profile_id,
          'arr_order': arr_order
        },
        success: function(){
          // toastr.success('並び替えました');
        },
        fail: function (data) {
          toastr.error('エラーが発生しました', 'チャプター並び替え');
        }
      })
    },
  }).disableSelection();

  // $(document).on('click', '#id_desc_div', function (e) {
  //   $('#id_desc').removeClass('hide');
  //   let div_height = $("#id_desc_div").height() + 20;
  //   $('#id_desc').css('height', div_height);
  //   $('#id_desc').focus();
  //   $('#id_desc_div').addClass('hide');
  // })

  // $(document).on('focusout', '#id_desc', function (e) {
  //   let str = $('#id_desc').val();
  //   str = str.replace(/(<.+?>)/gi, '');
  //   str = str.replace(/(?:\r\n|\n\r|\r|\n)/g, '<br/> ');
  //   str = str.replace(/#([々〆〤一-龠ぁ-ゔァ-ヴーａ-ｚＡ-Ｚ０-９a-zA-Z0-9]{1,60})/g, ' <span class="hash-tag">#$1</span>');
  //   $('#id_desc').addClass('hide');
  //   $('#id_desc_div').removeClass('hide');
  //   $('#id_desc_div').html(str);
  // })

  showAlbumPreview();

  $(document).on('mouseenter mouseleave', '.sample-audio-thumbnail', function(e) {
      if($('.audio-navi').is('.showing')) {
        return
      }

      if(e.type === 'mouseenter') {
        if ($(this).attr('data-file-type') === 'audio') {
          if($(this).parents('.playing-navi').length) {
            return;
          }
          let target = $(this)
          let timeout = 200;
          if(hover_zooming) {
            timeout = 400;
          }
          let current_hover_id = hovering_id;
          hovering = true;
          setTimeout(() => {
            console.log(hovering, hovering_id, current_hover_id)
            if(hovering && current_hover_id == hovering_id && !$('.audio-navi.showing').length) {
              playPauseOnHover('play', target.find('.sample-audio-playpause-button'))
            }
          }, timeout)
        } else if(!hover_zooming && $(this).attr('data-file-type') === 'movie' && $(this).find('video').length) {
          let target = $(this)
          let current_hover_id = hovering_id;
          hovering = true;
          setTimeout(() => {
            if(!hovering || current_hover_id != hovering_id || $('.audio-navi.showing').length) {
              return
            }
            $('video, audio').each(function(i, e) {
              e.pause()
            })
            $('.playing, .loading').removeClass('playing loading');
            $(this).find('video').get(0).play();
            $(this).find('video').css('display', 'block');
            $(this).addClass('playing');

            let counter = hovering_id;
            let video = $(this).find('video').get(0);
            let viewportOffset = video.getBoundingClientRect();
            let top = viewportOffset.top
            let left = viewportOffset.left
            let width = video.clientWidth
            let height = video.clientHeigth

            hovering = true;
            close_full_screen = false;
            setTimeout(function() {
                console.log(hovering, hovering_id, counter)
                if(hovering && counter === hovering_id) {
                    target.css({'position': 'fixed', 'top': top, 'left': left, 'max-width': width, 'height': height});
                    hover_zooming = true;
                    genVideoInfo(target);
                    target.addClass('hover-zoom-netflix-prepare');
                    target.parent().addClass('video-placeholder');
                    setTimeout(()=>{
                        target.addClass('hover-zoom-netflix');
                    }, 100);

                    setTimeout(() => {
                        target.addClass('hover-zoom-netflix-center');
                    }, 600);
                    $('body').append(`<div class='video-netflix-overlay'></div>`)
                }
            }, 10000)
          }, 200)
        }
      }

      if(e.type === 'mouseleave') {
        hovering_id++;
        if ($(this).attr('data-file-type') === 'audio') {
          if($(this).parents('.playing-navi').length) {
            return;
          }
          $(this).removeClass('loading playing');
          console.log('pause')
          playPauseOnHover('pause', $(this).find('.sample-audio-playpause-button'))
        } else if(!hover_zooming && $(this).attr('data-file-type') === 'movie' && $(this).find('video').length && !close_full_screen) {
          $(this).find('video').get(0).pause();
          $(this).removeClass('playing');
          hovering = false;
          removeVideoInfo($(this));
          $(this).css({'position': 'relative', 'top': 'auto', 'left': 'auto', 'max-width': 'none', 'height': 'none'})
          $(this).parents('.video-placeholder').removeClass('video-placeholder');
          $(this).removeClass('hover-zoom-netflix hover-zoom-netflix-center hover-zoom-netflix-prepare')
        }
      }
  });

  $(document).on('click', '.video-netflix-overlay', function() {
    $('.hover-zoom-netflix').removeClass('hover-zoom-netflix-center');
    $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')
    $('.hover-zoom-netflix').removeClass('playing');

    setTimeout(() => {
        $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
        $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
        $('.hover-zoom-netflix-prepare').find('.list-new-works__content_hover').addClass('hide');
        $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
        $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
    }, 200)

    setTimeout(() => {
        $('.hover-zoom-out-netflix-prepare-2').css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
        $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
        $('.hover-zoom-out-netflix-prepare-2').parents('.video-placeholder').removeClass('video-placeholder');
        removeVideoInfo($('.hover-zoom-out-netflix-prepare-2'));
    }, 600)

    hover_zooming = false;
    hovering_id++;
    hovering = false;
    close_full_screen = true;
    $('body .video-netflix-overlay').remove();
  })

  document.addEventListener("fullscreenchange", function() {
    if (!document.fullscreenElement) {
      $('.video-netflix-overlay:not(.topic-netflix-overlay)').trigger('click');
    } else {
      hovering_id++;
      close_full_screen = true;
    }
  });

  $(document).on('click', '.sample-audio-thumbnail video', function() {
    $(this).parent().find('.btn-preview-album').trigger('click');
    close_full_screen = true;
  })

  $('#modal-contact-artist').on('hidden.bs.modal', function() {
    $('.video-netflix-overlay').trigger('click')
  })

  $(document).on('click', '.download-album-file', function() {
    let albumID = $(this).attr('data-album-id');
    if(albumID) {
      download_sale_content_file(albumID)
    }
  })

  initStatementBlock();
  reSizeWindow()
  initReadyProfileFooter()
  initProfileText()
  checkAndroid();
  removeOverLayModal();
  setTimeout(() => {
    playFromURL();
  }, 800)
})

function checkAndAutoCompleteURL (target, source) {
  if (typeof document.createElement('input').checkValidity === 'function') {
    let social_origin = '';
    if($(target).is('#id_twitter_link')) {
      social_origin = "https://www.twitter.com";
    } else if ($(target).is('#id_instagram_link')) {
      social_origin = "https://www.instagram.com";
    }

    let raw_input = $(target).val().trim();
    if (!raw_input) {
      return false;
    }

    let url = raw_input;
    if (social_origin !== '') {
      let social_link = new URL(raw_input, social_origin);
      if (social_link.origin !== social_origin) {
        social_link = new URL(social_link.pathname, social_origin);
      }

      url = social_link.toString();
    }
    let pattern = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
    if (pattern.test(url)) {
      source.attr('data-url', url).attr('data-edited', true);
      source.find('a').attr('href', url);
      return true
    } else {
      return false;
    }
  }
}

function htmlForm(title='',
                  sale_youtube_link='',
                  desc='',
                  price,
                  created_year,
                  credit,
                  hashtag,
                  image,
                  slider,
                  attr1_min,
                  attr1_max,
                  attr2_min,
                  attr2_max,
                  start_time,
                  end_time,
                  start_price,
                  end_price,
                  target,
                  show_thumbnail,
                  custom_note) {
  let background = '';
  if(image) {
    background = `background-image: ${image}; background-size: cover;`;
  }
    return  `
<div class="segment-control">
  <div class="segment-item active" data-option="1">アップロード</div>
  <div class="segment-item" data-option="2">YouTubeのURL</div>
</div>
    <div class="sale-content-modal-row flex-column input-container input-form-container upload-sale">
        <div class="form-hint playlist-title">オーディオ、動画、画像、PDFをドラッグ＆ドロップで登録できます。</div>
        ${generateAudioItem(target)}
        <label class="sale-content-audio-add" for="new-audio" style="width: 100%">
          <div class="account_upload-file mattach mattach-form">
            <div class="mcomment-attached">
              <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                <div class="mattach-previews mattach-previews-form collection">
                  <div class="mattach-template mattach-template-form collection-item item-template">
                    <div class="mattach-info" data-dz-thumbnail="">
                      <div class="mcommment-file">
                        <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                        <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                class="icon icon--sicon-close"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div id="new-audio" class="fallback dropzone">
            </div>
          </div>
        </label>
        <div class="error-message">ファイルがありません。</div>
        <div class="btn btn--secondary btn-modal-audio-setting" style="padding: 8px 16px !important; margin: 8px 0;">設定</div>
        ${renderModalAudioSetting(attr1_min, attr1_max, attr2_min, attr2_max, background, slider, show_thumbnail)}
        ${renderModalVideoSetting(background, show_thumbnail)}
    </div>
    
     <div class="sale-content-modal-row input-container input-form-container block-link-youtube block-required" style="display: none">
      <label for="sale_youtube_link" class="form-label" style="width: 100%;">YouTubeのURL<span class="form-label__required">[必須]</span></label>
      <input type="text" id="sale_youtube_link" class="form-control" placeholder="https://youtu.be/7AnY3hKxQ2A?si=02vQlChaSZXKuayr" value="${sale_youtube_link}" maxlength="1000">
      <div class="error-message">YouTubeリンクのフォーマットが正しくありません</div>
    </div>

    <div class="sale-content-modal-row input-container input-form-container block-required">
      <label for="id_title" class="form-label" style="width: 100%;">作品名<span class="form-label__required">[必須]</span></label>
      <input type="text" id="id_title" class="form-control" placeholder="a song for you" value="${title}" maxlength="1000">
      <div class="error-message">フォーマットが正しくありません。</div>
    </div>

    <div class="sale-content-modal-row input-container input-form-container">
      <label for="id_created_year" class="form-label" style="width: 100%;">制作年<span class="form-label__optional">[任意]</span></label>
      <input type="number" id="id_created_year" class="form-control"
        placeholder="${new Date().getFullYear()}" value="${created_year}"
        style="max-width: 100px; margin-right: 8px" min="1" max="9999" oninput="checkYear(this)"> 年
      <div class="error-message">フォーマットが正しくありません。</div>
    </div>

    <div class="sale-content-modal-row input-container input-form-container">
      <label for="id_credit" class="form-label"  style="width: 100%;">クレジット<span class="form-label__optional">[任意]</span></label>
      <div class="form-hint" style="width: 100%;">複数アーティストで制作した作品を掲載する場合は、クレジットを登録しましょう。</div>
      <textarea id="id_credit" class="form-control" maxlength="1000"
        placeholder="Music and Arrangement by\nLyrics by\nVocal\nGuitar\nBass\nDrums\nRecorded at\nRecording & Mixing Engineer" rows="4" value="${credit}">${credit}</textarea>
      <div class="error-message">フォーマットが正しくありません。</div>
    </div>

    <div class="sale-content-modal-row input-container input-form-container">
      <label for="id_desc" style="width: 100%;" class="form-label">解説<span class="form-label__optional">[任意]</span></label>
      <textarea id="id_desc" class="form-control" maxlength="1000"
        placeholder="テーマや狙い、それがどのように表現されているかを伝えましょう。" rows="4">${desc}</textarea>
      <div class="error-message">フォーマットが正しくありません。</div>
    </div>

    <div class="sale-content-modal-row input-container input-form-container">
      <label for="id_hashtag" style="width: 100%;" class="form-label">ハッシュタグ<span class="form-label__optional">[任意]</span></label>
      <div class="form-hint" style="width: 100%;">ワードの区切りは、#で設定してください。</div>
      <textarea id="id_hashtag" class="form-control" rows="1">${hashtag}</textarea>
      <div class="error-message">フォーマットが正しくありません。</div>
    </div>

    <hr style="border: none; border-top: 1px solid #F0F0F0; margin-top: 40px;">

    <div class="sale-content-modal-row input-form-container">
      <div class="form-label__heading">販売方針</div>
      <div class="sale-content-type">
        <label class="input-radio">
          <input type="radio" name="sale-type" value="1" index="0" id="normal_sold"><div class="sale_type_text">そのまま販売</div>
          <p class="form-hint">作品に手を加えず、買取（著作権譲渡）で販売します。</p>
          <div class="check-mark"></div>
        </label>

        <label class="input-radio">
          <input type="radio" name="sale-type" value="2" index="1" id="semi_sold"><div class="sale_type_text">セミオーダーで販売</div>
          <p class="form-hint">作品の一部をカスタマイズして販売します。\n購入された後も、販売を続けることができます。</p>
          <div class="check-mark"></div>
          <input type="text" id="id_customizable_sale_setting" style="width: 200px; position: relative; opacity: 1;"
            class="form-control" placeholder="歌詞を変えて注文" maxlength="500" value=${custom_note}>
        </label>

        <label class="input-radio">
          <input type="radio" name="sale-type" value="3" index="2" id="usage_right_sold"><div class="sale_type_text">利用権を販売</div>
          <p class="form-hint">作品をそのまま、非独占で利用権販売します。\n購入された後も、販売を続けることができます。</p>
          <div class="check-mark"></div>
        </label>

        <label class="input-radio">
          <input type="radio" name="sale-type" value="4" index="3" id="not_sold"><div class="sale_type_text">販売しない</div>
          <p class="form-hint">サンプル視聴のみの取り扱いとします。</p>
          <div class="check-mark"></div>
        </label>
        <hr style="border: none; border-top: 1px solid #F0F0F0;  margin-top: 24px;">
      </div>
      ${renderFormAuctionPrice(start_price, end_price)}
    </div>

    <hr style="border: none; border-top: 1px solid #F0F0F0; margin-top: 12px;">

    <div class="sale-content-modal-row input-form-container">
      <div class="form-label__heading">期間<span class="form-label__optional">[任意]</span></div>
      <div class="form-hint" style="width: 100%;">掲載開始期間と終了期間を設定し、オークションスタイルで販売しましょう。</div>
      ${renderFormAuctionTime(start_time, end_time)}
    </div>`
}

function generateAudioItem(target) {
  let html = '';
  if(target.length) {
    if(target.parents('.sample-audio-item').length && target.parents('.sample-audio-item').attr('data-sale').length && listFile[target.parents('.sample-audio-item').attr('data-sale')]) {
      let file = listFile[target.parents('.sample-audio-item').attr('data-sale')];
      if (!file.name.match(/\.(mp3|MP3|wav|WAV|mp4|MP4|MOV|mov)$/)) {
        disable_setting = true;
      }
      let audio = target.find('audio')
      let file_type = ''
      if(file.name.match(/\.(mp4|MP4|MOV|mov)$/)) {
        file_type = 'movie'
      } else if (file.name.match(/\.(mp3|MP3|wav|WAV)$/)) {
        file_type = 'audio'
      }
      has_file_upload = true;
      html +=`<div class="account__file sale-content-audio" data-album-id="${audio.attr('data-album')}">
            <i class="icon icon--sicon-clip"></i>
            <div class="account__file-name" title="${file.name}">${file.name}</div>
            <audio src="" class="hide sale-content-audio" data-file-type="${file_type}"></audio>
          </div>`
    } else {
      target.find('audio').each(function (i, e) {
        let duration = '-:--';
        if(e.duration != 'NaN') {
          duration = e.duration;
        }
        if(!($(e).attr('data-file-type') === 'audio') && !($(e).attr('data-file-type') === 'movie')) {
            disable_setting = true;
        }
        html +=`<div class="account__file sale-content-audio download-album-file" data-album-id="${$(e).attr('data-album')}">
            <i class="icon icon--sicon-clip"></i>
            <div class="account__file-name" title="${$(e).attr('data-name')}">${$(e).attr('data-name')}</div>
            <audio src="${e.src}" class="hide sale-content-audio" data-file-type="${$(e).attr('data-file-type')}">${duration}</audio>
          </div>`
          has_file_upload = true;
      })
    }
  } else {
    disable_setting = true;
  }

  return html;
}

function checkButtonSubmit() {
  formData = {
    "profile_id": "",
    "modified": "",
    "banner": "",
    "official_site": "",
    "twitter_link": "",
    "facebook_link": "",
    "instagram_link": "",
    "youtube_link": "",
    "stage_name": "",
    "type": "",
    "theme_jp": "",
    "theme_en": "",
    "is_show_avatar": "",
    "is_show_name": "",
    "is_show_title": "",
    "profile_quote": "",
    "x_banner": "",
    "y_banner": "",
    "width_banner": "",
    "height_banner": "",
    "avatar": "",
    "x": "",
    "y": "",
    "width": "",
    "height": "",
    "sale_contents": [],
    "list_approve": [],
    "list_unchanged": [],
    "list_edited": [],
    "list_deleted": [],
    "catchphrase_jp_1": "",
    "catchphrase_jp_2": "",
    "catchphrase_jp_3": "",
    "catchphrase_en_1": "",
    "catchphrase_en_2": "",
    "catchphrase_en_3": "",
    "display_tag_line_1": "",
    "artist_name_jp": "",
    "artist_name_en": "",
    "display_tag_line_2": "",
    "title_jp": "",
    "title_en": "",
    "type_header": "",
    "footer": {
      "copyright": "",
      "item_menu": [],
      "social_link": [],
      "list_delete_item_menu": [],
      "list_delete_item_social": [],
      "list_delete_item_sub": []
    }
  }
  need_approve = false;
  changes = 0;
  let count = 0;
  form = new FormData();
  $('.btn-submit-profile').addClass('hide disable');
  $('.btn-submit-profile').parents('.submit-profile-btn-container').addClass('hide disable');
  $('footer').removeClass('additional_margin_bottom');
  let profile_creator_dom = $('.profile');
  if (profile_creator_dom.length && profile_creator_dom.attr('data-profile-id')) {
    formData.profile_id = profile_creator_dom.attr('data-profile-id');
  } else {
    return 0;
  }

  if (profile_creator_dom.length && profile_creator_dom.attr('data-modified')) {
    formData.modified = profile_creator_dom.attr('data-modified');
  } else {
    return 0;
  }

  let banner_dom = $('.header-fullscreen-container');
  if (banner_dom.is('.new') && banner_dom.attr('data-check') !== 'true') {
    need_approve = true;
  } else if(banner_dom.attr('data-edited') === "true") {
    let is_change = false;
    formData.catchphrase_jp_1 = $('.profile-artist-catch-phrase').attr('data-catchphrase-1')
    formData.catchphrase_jp_2 = $('.profile-artist-catch-phrase').attr('data-catchphrase-2')
    formData.catchphrase_jp_3 = $('.profile-artist-catch-phrase').attr('data-catchphrase-3')
    formData.catchphrase_en_1 = $('.profile-artist-catch-phrase').attr('data-catchphrase-1-en')
    formData.catchphrase_en_2 = $('.profile-artist-catch-phrase').attr('data-catchphrase-2-en')
    formData.catchphrase_en_3 = $('.profile-artist-catch-phrase').attr('data-catchphrase-3-en')
    formData.display_tag_line_1 = $('.profile-artist-info-name').attr('data-tagline-1-toggle').toLocaleLowerCase()
    formData.artist_name_jp = $('.profile-artist-info-name').attr('data-tagline-1')
    formData.artist_name_en = $('.profile-artist-info-name').attr('data-tagline-1-en')
    formData.display_tag_line_2 = $('.profile-artist-info-title').attr('data-tagline-2-toggle').toLocaleLowerCase()
    formData.title_jp = $('.profile-artist-info-title').attr('data-tagline-2')
    formData.title_en = $('.profile-artist-info-title').attr('data-tagline-2-en')
    formData.type_header = $('.header-fullscreen-container').attr('data-style')

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-1') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-1')) {
      pushUnique(formData.list_edited, 'catchphrase_jp_1');
      is_change = true;
    }

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-2') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-2')) {
      pushUnique(formData.list_edited, 'catchphrase_jp_2');
      is_change = true;
    }

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-3') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-3')) {
      pushUnique(formData.list_edited, 'catchphrase_jp_3');
      is_change = true;
    }

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-1-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-1-en')) {
      pushUnique(formData.list_edited, 'catchphrase_en_1');
      is_change = true;
    }

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-2-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-2-en')) {
      pushUnique(formData.list_edited, 'catchphrase_en_2');
      is_change = true;
    }

    if($('.profile-artist-catch-phrase').attr('data-catchphrase-3-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-3-en')) {
      pushUnique(formData.list_edited, 'catchphrase_en_3');
      is_change = true;
    }

    if($('.profile-artist-info-name').attr('data-tagline-1-toggle').toLocaleLowerCase() != $('.profile-artist-info-name').attr('data-content-tagline-1-toggle').toLocaleLowerCase()) {
      pushUnique(formData.list_edited, 'display_tag_line_1');
      is_change = true;
    }

    if($('.profile-artist-info-name').attr('data-tagline-1') != $('.profile-artist-info-name').attr('data-content-tagline-1')) {
      pushUnique(formData.list_edited, 'artist_name_jp');
      is_change = true;
    }

    if($('.profile-artist-info-name').attr('data-tagline-1-en') != $('.profile-artist-info-name').attr('data-content-tagline-1-en')) {
      pushUnique(formData.list_edited, 'artist_name_en');
      is_change = true;
    }

    if($('.profile-artist-info-title').attr('data-tagline-2-toggle').toLocaleLowerCase() != $('.profile-artist-info-title').attr('data-content-tagline-2-toggle').toLocaleLowerCase()) {
      pushUnique(formData.list_edited, 'display_tag_line_2');
      is_change = true;
    }

    if($('.profile-artist-info-title').attr('data-tagline-2') != $('.profile-artist-info-title').attr('data-content-tagline-2')) {
      pushUnique(formData.list_edited, 'title_jp');
      is_change = true;
    }

    if($('.profile-artist-info-title').attr('data-tagline-2-en') != $('.profile-artist-info-title').attr('data-content-tagline-2-en')) {
      pushUnique(formData.list_edited, 'title_en');
      is_change = true;
    }

    if($('.header-fullscreen-container').attr('data-style') != $('.header-fullscreen-container').attr('data-content-style')) {
      pushUnique(formData.list_edited, 'type_header');
      is_change = true;
    }

    if(currentSelectLogo.length) {
      form.append('logo', currentSelectLogo[0]);
      is_change = true;
    }

    if(currentSelectKVPC.length) {
      form.append('key_visual_pc', currentSelectKVPC[0]);
      is_change = true;
    }

    if(currentSelectKVSP.length) {
      form.append('key_visual_sp', currentSelectKVSP[0]);
      is_change = true;
    }

    if(currentSelectBanner.length) {
      form.append('banner', currentSelectBanner[0]);
      is_change = true;
    }

    if(is_change) {
      count++;
    } else {
      banner_dom.attr('data-edited', '')
    }
  }

  let theme_dom = $('.statement');
  if (theme_dom.is('.new') && theme_dom.attr('data-check') !== 'true') {
    need_approve = true;
  } else if (theme_dom.attr('data-edited') === "true") {
    let is_change = false;
    formData.theme_jp = $('.statement .statement-quote').attr('data-quote')
    if($('.statement .statement-quote').attr('data-quote') != $('.statement .statement-quote').attr('data-content-quote')) {
      formData.list_edited.push('theme_jp');
      is_change = true;
    }

    formData.theme_en = $('.statement .statement-quote').attr('data-quote-en')
    if($('.statement .statement-quote').attr('data-quote-en') != $('.statement .statement-quote').attr('data-content-quote-en')) {
      formData.list_edited.push('theme_en');
      is_change = true;
    }

    formData.is_show_avatar =  $('.statement .statement-info').attr('data-toggle-avatar');
    if($('.statement .statement-info').attr('data-toggle-avatar').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-avatar').toLocaleLowerCase()) {
      formData.list_edited.push('is_show_avatar');
      is_change = true;
    }

    formData.is_show_name =  $('.statement .statement-info').attr('data-toggle-name');
    if($('.statement .statement-info').attr('data-toggle-name').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-name').toLocaleLowerCase()) {
      formData.list_edited.push('is_show_name');
      is_change = true;
    }

    formData.is_show_title =  $('.statement .statement-info').attr('data-toggle-title');
    if($('.statement .statement-info').attr('data-toggle-title').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-title').toLocaleLowerCase()) {
      formData.list_edited.push('is_show_title');
      is_change = true;
    }

    if(is_change) {
      count++;
    } else {
      theme_dom.attr('data-edited', '')
    }

  }

  let currentProfileTitle = $('.profile__profile-quote').attr('data-content-title') || '';
  let currentProfileTitleEN = $('.profile__profile-quote').attr('data-content-title-en') || '';
  let currentProfile = $('.profile__profile-quote').attr('data-content-title-text') || '';
  let currentProfileEN = $('.profile__profile-quote').attr('data-content-title-text-en') || '';
  let currentProfileToggle = $('.profile__profile-quote').attr('data-content-toggle-header') || 'false';

  let profile_dom = $('.profile__profile-quote');
  if (profile_dom.is('.new') && profile_dom.attr('data-check') !== 'true') {
    need_approve = true;
  } else if (profile_dom.attr('data-edited') === "true") {
    if(checkChangesProfile(currentProfile, currentProfileEN, currentProfileTitle, currentProfileTitleEN, currentProfileToggle)) {
      count++
    }
  }

  //footer
  if(listMenuMainItemDeleted.length || listMenuSocialItemDeleted.length || listMenuSubItemDeleted.length) {
    formData.footer.list_delete_item_menu = listMenuMainItemDeleted;
    formData.footer.list_delete_item_social = listMenuSocialItemDeleted;
    formData.footer.list_delete_item_sub = listMenuSubItemDeleted;
    count++;
    pushUnique(formData.list_edited, 'footer');
  }
  //check main
  $('.profile-footer-upper .profile-footer-menu-item').each(function(i, e) {
    if($(e).attr('data-title') != $(e).attr('data-content-title') || $(e).attr('data-title-en') != $(e).attr('data-content-title-en') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
      count++;
      pushUnique(formData.list_edited, 'footer');
      let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
      let object = {
        "type_footer": "1",
        "type": type,
        "order": $(e).attr('data-order'),
        "url": $(e).attr('data-url'),
        "title_jp": $(e).attr('data-title'),
        "title_en": $(e).attr('data-title-en'),
        "id": $(e).attr('data-id'),
      }
      formData.footer.item_menu.push(object)
    }
  })

  //check social
  $('.profile-footer-upper-right .profile-footer-menu-icon').each(function(i, e) {
    if($(e).attr('data-social-icon') != $(e).attr('data-content-social-icon') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
      count++;
      pushUnique(formData.list_edited, 'footer');
      let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
      let object = {
        "type": type,
        "order": $(e).attr('data-order'),
        "type_social_link": $(e).attr('data-social-icon'), //'home', 'twitter', 'fb', 'insta', 'youtube', 'tiktok', 'note'
        "url": $(e).attr('data-url'),
        "id": $(e).attr('data-id'),
      }
      formData.footer.social_link.push(object)
    }
  })

  //check main
  $('.profile-footer-lower-menu .profile-footer-menu-item').each(function(i, e) {
    if($(e).attr('data-title') != $(e).attr('data-content-title') || $(e).attr('data-title-en') != $(e).attr('data-content-title-en') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
      count++;
      pushUnique(formData.list_edited, 'footer');
      let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
      let object = {
        "type_footer": "2",
        "type": type,
        "order": $(e).attr('data-order'),
        "url": $(e).attr('data-url'),
        "title_jp": $(e).attr('data-title'),
        "title_en": $(e).attr('data-title-en'),
        "id": $(e).attr('data-id'),
      }
      formData.footer.item_menu.push(object)
    }
  })

  //copyright
  let cp_right = $('.profile-footer-copyright')
  if(cp_right.attr('data-copyright') != cp_right.attr('data-content-copyright')) {
    count++;
    pushUnique(formData.list_edited, 'footer');
  }
  formData.footer.copyright = cp_right.attr('data-copyright')

  let sale_content_container = $('.sample-audio-container');

  let need_confirm_sale_contents = sale_content_container.find('.sample-audio-item.rel:not(.new-sale-content).new');
  if(need_confirm_sale_contents.length && need_confirm_sale_contents.attr('data-check') !== 'true') {
    need_approve = true;
  }

  let deleted_sale_content = sale_content_container.find('.sample-audio-item.rel:not(.new-sale-content)[data-deleted="true"]');
  if (deleted_sale_content.length) {
    deleted_sale_content.each(function (i, e) {
      let type = 'deleted';
      pushUnique(formData.list_deleted, 'sale_content');
      let sale_content_id = $(e).attr('data-sale-content-id');
      formData.sale_contents.push({'id': sale_content_id, 'type': type})
    });
    count++;
  }

  let edited_sale_content = sale_content_container.find('.sample-audio-item.rel:not(.new-sale-content)[data-edited="true"]');
  if (edited_sale_content.length) {
    edited_sale_content.each(function (i, e) {
      let type;
      if($(e).attr('data-approve') === 'true') {
        type = 'approved';
        pushUnique(formData.list_approve, 'sale_content');
      } else if ($(e).attr('data-edited') === 'true') {
        if (checkNewSaleContent($(e)) || edited_sale_content.find('input.new-audio-file')[0].files.length > 0) {
          type = 'edited';
          pushUnique(formData.list_edited, 'sale_content');
          count++;
        }
      } else if ($(e).attr('data-rejected') === 'true') {
        type = 'unchanged';
        pushUnique(formData.list_unchanged, 'sale_content');
      }
      let sale_content_id = $(e).attr('data-sale-content-id');
      formData.sale_contents.push(saleContentObject(sale_content_id, e, type))
    });
  }

  let new_sale_contents = sale_content_container.find('.sample-audio-item.rel.new-sale-content');
  if(new_sale_contents.length) {
    new_sale_contents.each(function (i, e) {
      let sale_content_id = 'new_' + i;
      formData.sale_contents.push(saleContentObject(sale_content_id, e, 'new'));
      pushUnique(formData.list_edited, 'sale_content');
    })
    count++;
  }
  return count;
}


function checkButtonApprove(target) {
  formDataApprove = {
    "profile_id": "",
    "banner": "",
    "official_site": "",
    "twitter_link": "",
    "facebook_link": "",
    "instagram_link": "",
    "youtube_link": "",
    "stage_name": "",
    "type": "",
    "theme_quote": "",
    "profile_quote": "",
    "x_banner": "",
    "y_banner": "",
    "width_banner": "",
    "height_banner": "",
    "avatar": "",
    "x": "",
    "y": "",
    "width": "",
    "height": "",
    "sale_contents": [],
    "list_approve": [],
    "list_unchanged": [],
    "list_edited": [],
    "list_deleted": [],
    "upgrade": false,
    "footer": ""
  }

  let new_field = $('.new').length;
  let approve_field = $('.new[data-approve^=true]').length;
  if (new_field === approve_field) {
    formDataApprove.upgrade = true;
  }
  form = new FormData();
  let profile_creator_dom = $('.profile');
  if (profile_creator_dom.length && profile_creator_dom.attr('data-profile-id') && profile_creator_dom.attr('data-modified')) {
    formDataApprove.profile_id = profile_creator_dom.attr('data-profile-id');
    formDataApprove.modified = profile_creator_dom.attr('data-modified');

  } else {
    return false;
  }

  let banner_dom = $('.header-fullscreen-container');
  if (banner_dom.is('.new') && banner_dom.attr('data-check') ==='true' && banner_dom.attr('data-approve') === "true") {
    pushUnique(formDataApprove.list_approve, 'banner');
  }

  let theme_dom = $('.statement');
  if (theme_dom.is('.new') && theme_dom.attr('data-check') === 'true' && theme_dom.attr('data-approve') === "true") {
    formDataApprove.list_approve.push('theme_quote');
  }

  let name_dom = $('.profile__name');
  if (name_dom.is('.new') && name_dom.attr('data-check') === 'true' && name_dom.attr('data-approve') === "true") {
    formDataApprove.stage_name = name_dom.attr('title');
    pushUnique(formDataApprove.list_approve, 'stage_name');
  }

  let job_dom = $('.profile__job');
  if (job_dom.is('.new') && job_dom.attr('data-check') === 'true' && job_dom.attr('data-approve') === "true") {
    formDataApprove.type = job_dom.attr('title');
    pushUnique(formDataApprove.list_approve, 'type');
  }

  let profile_dom = $('.profile__profile-quote');
  if (profile_dom.is('.new') && profile_dom.attr('data-check') === 'true' && profile_dom.attr('data-approve') === "true") {
    formDataApprove.profile_quote = profile_dom.attr('data-title');
    pushUnique(formDataApprove.list_approve, 'profile_quote');
  }

  let footer = $('footer.profile-footer');
  if (footer.is('.new') && footer.attr('data-check') === 'true' && footer.attr('data-approve') === "true") {
    formDataApprove.footer = 'footer';
    pushUnique(formDataApprove.list_approve, 'footer');
  }

  let sale_content = target.parents('.sample-audio-item');
  if (sale_content.length > 0) {
    if (sale_content.is('.new') && sale_content.attr('data-check') === 'true' && sale_content.attr('data-approve') === "true") {
      let type;
      type = 'approved';
      pushUnique(formDataApprove.list_approve, 'sale_content');
      let sale_content_id = sale_content.attr('data-sale-content-id');
      formDataApprove.sale_contents.push(saleContentObject(sale_content_id, sale_content, type))
    }
  }
  return true;
}

function pushUnique(array, new_item) {
  if (!array.includes(new_item)) {
    array.push(new_item);
  }
}

function showButtonSubmit(role) {
  let label = 'この内容で申請';
  if (role === 'curator') {
    label = 'この内容で提案';
  }

  $('.submit-profile-btn-container .form-hint').html('変更を申請する必要があります。');

  if(!formData.list_edited.length && (formData.list_approve.length > 0 || formData.list_unchanged.length)) {
    label = 'OK';
  }
  if (!formData.list_edited.length && !formData.list_approve.length && formData.list_deleted.length) {
    label = '保存する'
  }
  $('.btn-submit-profile').html(label);

  $('.btn-submit-profile').removeClass('hide disable');

  if(need_approve) {
    $('.btn-submit-profile').addClass('disable');
    $('.submit-profile-btn-container .form-hint').html('先に、NEWのところを確認しましょう。');
  }

  $('.btn-submit-profile').parents('.submit-profile-btn-container').removeClass('hide disable');
  $('footer').addClass('additional_margin_bottom');
}

function checkNewSaleContent(currentItemDom) {
  if(listFile[currentItemDom.attr('data-sale')] || listFileTemp[currentItemDom.attr('data-sale')] || listFileImage[currentItemDom.attr('data-sale')]) {
    return true
  }
  let sample_audio_dom = currentItemDom.find('.sample-audio-title');

  let startTimeVal = sample_audio_dom.attr('data-content-auctions-start-time');
  if (!startTimeVal || startTimeVal === "None") {
    startTimeVal = ""
  }
  let endTimeVal = sample_audio_dom.attr('data-content-auctions-end-time');
  if (!endTimeVal || endTimeVal === "None") {
    endTimeVal = ""
  }
  let startPriceVal = sample_audio_dom.attr('data-auctions-start-price');

  let endPriceVal = sample_audio_dom.attr('data-auctions-end-price');

  let startPrice = sample_audio_dom.attr('data-content-auctions-start-price');
  if (!startPrice || startPrice === "None") {
    startPrice = ""
  }
  let endPrice = sample_audio_dom.attr('data-content-auctions-end-price');
  if (!endPrice || endPrice === "None") {
    endPrice = ""
  }
  if (sample_audio_dom.attr('title') !== sample_audio_dom.attr('data-content-title')) {
    return true
  }

  if (sample_audio_dom.attr('data-desc') !== sample_audio_dom.attr('data-content-desc')) {
    return true
  }

  if (sample_audio_dom.attr('data-created-year') !== sample_audio_dom.attr('data-content-created-year')) {
    return true
  }

  if (sample_audio_dom.attr('data-credit') !== sample_audio_dom.attr('data-content-credit')) {
    return true
  }

  if (sample_audio_dom.attr('data-hashtag') !== sample_audio_dom.attr('data-content-hashtag')) {
    return true
  }

  if (sample_audio_dom.attr('data-customizable-sale-setting') !== sample_audio_dom.attr('data-content-customizable-sale-setting')) {
    return true
  }

  if (sample_audio_dom.attr('data-type') !== sample_audio_dom.attr('data-old-type')) {
    return true
  }

  if (sample_audio_dom.attr('data-price') !== sample_audio_dom.attr('data-content-price')) {
    return true
  }

  if (sample_audio_dom.attr('data-content-type') !== sample_audio_dom.attr('data-content-content-type')) {
    return true
  }

  if (sample_audio_dom.attr('data-attribute-min1') !== sample_audio_dom.attr('data-content-attribute-min1')) {
    return true
  }

  if (sample_audio_dom.attr('data-attribute-min2') !== sample_audio_dom.attr('data-content-attribute-min2')) {
    return true
  }

  if (sample_audio_dom.attr('data-attribute-max1') !== sample_audio_dom.attr('data-content-attribute-max1')) {
    return true
  }

  if (sample_audio_dom.attr('data-attribute-max2') !== sample_audio_dom.attr('data-content-attribute-max2')) {
    return true
  }

  if (sample_audio_dom.attr('data-auctions-start-time') !== startTimeVal) {
    return true
  }

  if (sample_audio_dom.attr('data-auctions-end-time') !== endTimeVal) {
    return true
  }

  let old_price = parseInt(startPriceVal.replaceAll(',', ''))
  let new_price = parseInt(startPrice)
  if (old_price !== new_price) {
    if (!(startPriceVal === '' && startPrice === '')) {
      return true
    }
  }

  let old_price_end = parseInt(endPriceVal.replaceAll(',', ''))
  let new_price_end = parseInt(endPrice)
  if (old_price_end !== new_price_end) {
    if (!(endPriceVal === '' && endPrice === '')) {
      return true
    }
  }

  if (sample_audio_dom.attr('data-show-thumbnail') !== sample_audio_dom.attr('data-content-show-thumbnail')) {
    return true
  }

  if (sample_audio_dom.attr('data-show-thumbnail') === 'color' && sample_audio_dom.attr('data-content-default-color') !== sample_audio_dom.attr('data-default-color')) {
    return true
  }

  return false;
}

function showErrorMessage(target) {
  target.addClass('has-error');
  $('.has-error input, .has-error textarea').on('input', function () {
    $(this).parents('.input-container').removeClass('has-error');
  });
}

function saleContentObject(id, e, type) {
  let sale_content_title = $(e).find('.sample-audio-title').attr('data-title');
  let sale_content_desc = $(e).find('.sample-audio-title').attr('data-desc');
  let sale_content_sale_type = $(e).find('.sample-audio-title').attr('data-type');
  let sale_content_x = '';
  let sale_content_y = '';
  let sale_content_width = '';
  let sale_content_height = '';
  let sale_youtube_link = '';

  let x = $(e).find('.x_sale_content');
  let y = $(e).find('.y_sale_content');
  let width = $(e).find('.width_sale_content');
  let height = $(e).find('.height_sale_content');
  let default_color;
  if (listFileImage[$(e).attr('data-sale')] && x.length && y.length && width.length && height.length) {
    form.append('sale_content_image_' + id, listFileImage[$(e).attr('data-sale')]);
    sale_content_x = x.val();
    sale_content_y = y.val();
    sale_content_width = width.val();
    sale_content_height = height.val();
  } else {
    default_color = $(e).find('.sample-audio-title').attr('data-default-color');
    if(!default_color) {
      default_color = '#FCFCFC';
    }
  }
  let sale_content_audio = $(e).find('input.new-audio-file');
  form.append('sale_content_audio_' + id, listFile[$(e).attr('data-sale')]);

  let object = {
    'id': id,
    'title': sale_content_title,
    'desc': sale_content_desc,
    'x': sale_content_x,
    'y': sale_content_y,
    'width': sale_content_width,
    'height': sale_content_height,
    'sale_type': sale_content_sale_type,
    'type': type,
  }

  let content_type = $(e).find('.sample-audio-title').attr('data-content-type');
  if(!content_type.length) {
    content_type = 'music'
  }
  let attr1_min = $(e).find('.sample-audio-title').attr('data-attribute-min1');
  if(!attr1_min.length) {
    attr1_min = '1'
  }

  let attr2_min = $(e).find('.sample-audio-title').attr('data-attribute-min2');
  if(!attr2_min.length) {
    attr2_min = '1'
  }

  let attr1_max = $(e).find('.sample-audio-title').attr('data-attribute-max1');
  if(!attr1_max.length) {
    attr1_max = '2'
  }

  let attr2_max = $(e).find('.sample-audio-title').attr('data-attribute-max2');
  if(!attr2_max.length) {
    attr2_max = '2'
  }
  let segment_active = parseInt($('.segment-item.active').attr('data-option'))
  if (segment_active === 2){
    sale_youtube_link = $('#sale_youtube_link').val();
  }
  object.content_type = content_type;
  object.song_attribute1_min = attr1_min;
  object.song_attribute2_min = attr2_min;
  object.song_attribute1_max = attr1_max;
  object.song_attribute2_max = attr2_max;
  object.sale_youtube_link = sale_youtube_link;

  let list_attr = ['data-auctions-start-time', 'data-auctions-end-time', 'data-auctions-start-price', 'data-auctions-end-price'];
  let db_list_attr = ['start_time', 'end_time', 'price', 'max_price'];
  list_attr.forEach(function(s, i) {
    let attr = $(e).find('.sample-audio-title').attr(s);
    if(attr) {
      if( i === 2 || i === 3) {
        attr = attr.replaceAll(',', '');
      }
      object[db_list_attr[i]] = attr;
    } else {
      object[db_list_attr[i]] = '';
    }
  })

    let created_year = $(e).find('.sample-audio-title').attr('data-created-year');
    object.created_year = created_year;

    let has_tags = $(e).find('.sample-audio-title').attr('data-hashtag');
    object.tags_content = has_tags;

    let credit = $(e).find('.sample-audio-title').attr('data-credit');
    object.credit = credit;

    let customizable_sale_setting = $(e).find('.sample-audio-title').attr('data-customizable-sale-setting');
    object.customizable_sale_setting = customizable_sale_setting;

    let show_thumbnail = $(e).find('.sample-audio-title').attr('data-show-thumbnail');
    if (show_thumbnail) {
        object.show_thumbnail = show_thumbnail;
    }

  if(default_color) {
    object.default_thumbnail = default_color;
  }

  return object
}

function renderSliderAudioAttr(min1, max1, min2, max2, show) {
  let music_show = '', sound_show = '', voice_show = '';
  let label3 = 'アコースティック', label4 = 'エレクトロニック', label1 = 'サウンド', label2 = 'メロディアス';
  let label_min = min2, label_max = max2;
  switch (show) {
    case 'music':
      music_show = ' show';
      label3 = 'アコースティック';
      label4 = 'エレクトロニック';
      label1 = 'サウンド';
      label2 = 'メロディアス'
      break
    case 'sound_effect':
      sound_show = ' show';
      label3 = 'リアリティ';
      label4 = '創作';
      label1 = '繊細';
      label2 = 'アグレッシブ'
      break
    case 'voice':
      voice_show = ' show';
      label3 = '年少';
      label4 = '年長';
      label1 = '低い声';
      label2 = '高い声';

      label_min = reCalculateValue(min2)
      label_max = reCalculateValue(max2)
      break
    default:
      music_show = ' show'
      label3 = 'アコースティック';
      label4 = 'エレクトロニック';
      label1 = 'サウンド';
      label2 = 'メロディアス'
      break
  }

  return `
          <div class="upload upload__range-slider">
              <div class="upload__slider-item music-slider ${music_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">サウンド</div>
                      <div class="upload__slider-label-max">メロディアス</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${min1}</div>
                      <div class="upload__slider-bar attribute_1" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="3" data-min_current="${min1}" data-max_current="${max1}"></div>
                      <div class="upload__slider-max">${max1}</div>
                  </div>
              </div>
    
              <div class="upload__slider-item music-slider ${music_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">アコースティック</div>
                      <div class="upload__slider-label-max">エレクトロニック</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${min2}</div>
                      <div class="upload__slider-bar attribute_2" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="3" data-min_current="${min2}" data-max_current="${max2}"></div>
                      <div class="upload__slider-max">${max2}</div>
                  </div>
              </div>
    
              <div class="upload__slider-item sound-effects-slider ${sound_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">繊細</div>
                      <div class="upload__slider-label-max">アグレッシブ</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${min1}</div>
                      <div class="upload__slider-bar attribute_1" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="3" data-min_current="${min1}" data-max_current="${max1}"></div>
                      <div class="upload__slider-max">${max1}</div>
                  </div>
              </div>
    
              <div class="upload__slider-item sound-effects-slider ${sound_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">リアリティ</div>
                      <div class="upload__slider-label-max">創作</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${min2}</div>
                      <div class="upload__slider-bar attribute_2" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="3" data-min_current="${min2}" data-max_current="${max2}"></div>
                      <div class="upload__slider-max">${max2}</div>
                  </div>
              </div>
    
              <div class="upload__slider-item voice-slider ${voice_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">低い声</div>
                      <div class="upload__slider-label-max">高い声</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${min1}</div>
                      <div class="upload__slider-bar attribute_1" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="5" data-min_current="${min1}" data-max_current="${max1}"></div>
                      <div class="upload__slider-max">${max1}</div>
                  </div>
              </div>
    
              <div class="upload__slider-item voice-slider ${voice_show}">
                  <div class="upload__slider-label">
                      <div class="upload__slider-label-min">年少</div>
                      <div class="upload__slider-label-max">年長</div>
                  </div>
                  <div class="upload__slider-content">
                      <div class="upload__slider-min">${label_min}</div>
                      <div class="upload__slider-bar attribute_2" data-min="1" data-max="5" data-min_range="1"
                           data-max_range="5" data-min_current="${min2}" data-max_current="${max2}"></div>
                      <div class="upload__slider-max">${label_max}</div>
                  </div>
              </div>
          </div>
          <div class="audio-chart">
            <div class="graph-label label3">${label3}</div>
            <div class="chart-container">
              <div class="graph-label label1">${label2}</div>
              <div class="chart-slider-container">
                <div class="chart-slider">
                  <div class="square"></div>
                </div>
              </div>
              <div class="graph-label label2">${label1}</div>
            </div>
            <div class="graph-label label4">${label4}</div>
          </div>`
}

// function renderHashTag(desc) {
//   let str = desc;
//   str = str.replace(/(?:\r\n|\n\r|\r|\n)/g, '<br /> ');
//   str = str.replace(/#([々〆〤一-龠ぁ-ゔァ-ヴーａ-ｚＡ-Ｚ０-９a-zA-Z0-9]{1,60})/g, ' <span class="hash-tag">#$1</span>');
//   return str;
// }

function initSliderSaleContent() {
  $('.upload__slider-bar').each(function () {
    const el = $(this)
    const min_value = el.data('min')
    const max_value = el.data('max')
    const max_range = el.data('max_range')
    const min_range = el.data('min_range')
    const min_current = el.data('min_current')
    const max_current = el.data('max_current')
    noUiSlider.create(el[0], {
      animate: true,
      animationDuration: 0,
      behavior: 'tap-drag',
      start: [min_current, max_current],
      connect: true,
      step: 1,
      range: {
        min: min_value,
        max: max_value
      }
    }, true).on('update', function (values, handle, unencoded, tap, positions) {
      let max = parseFloat(values[1])
      const min = parseFloat(values[0])
      const currentHandle = handle === 0 ? 'min' : 'max'
      const slider_dom = $(this.target).parents('.upload__slider-content')
      const min_indicator = slider_dom.find('.upload__slider-min')
      const max_indicator = slider_dom.find('.upload__slider-max')
      if(slider_dom.parents('.voice-slider').length && slider_dom.find('.attribute_2').length) {
        convertVoiceToSliderValue(min_indicator, max_indicator);
      }
      if (currentHandle === 'max') {
        if (max - min > max_range) {
          // Slide to right
          const offset = (max - min) - max_range
          el[0].noUiSlider.set([min + offset, max])
          el.attr('data-max_current', max)
          max_indicator[0].innerText = max
          const new_min = max - max_range
          el.attr('data-min_current', new_min)
          min_indicator[0].innerText = new_min
        } else if (max - min < min_range) {
          if (min === min_value) {
            let new_max = min + min_range;
            el[0].noUiSlider.set([min_value, new_max])
            el.attr('data-max_current', new_max)
            max_indicator[0].innerText = new_max
            el.attr('data-min_current', min_value)
          } else if (max === max_value) {
            let new_min = max - min_range;
            el[0].noUiSlider.set([new_min, max_value])
            el.attr('data-max_current', max_value)
            max_indicator[0].innerText = new_min
            el.attr('data-min_current', new_min)
          } else {
            // Slide to left
            const offset = min_range - (max - min)
            el[0].noUiSlider.set([min - offset, max])
            el.attr('data-max_current', max)
            max_indicator[0].innerText = max
            const new_min = max - min_range
            el.attr('data-min_current', new_min)
            min_indicator[0].innerText = new_min
          }
        } else {
          el.attr('data-max_current', max);
          max_indicator[0].innerText = max;
        }
      } else if (currentHandle === 'min') {
        // Slide to left
        if (min + max_range < max) {
          const offset = max - (min + max_range)
          el[0].noUiSlider.set([min, max - offset])
          el.attr('data-min_current', min)
          min_indicator[0].innerText = min
          const new_max = min + max_range
          el.attr('data-max_current', new_max)
          max_indicator[0].innerText = new_max
        } else if (min + min_range > max) {
          if (min + min_range <= max_value) {
            el[0].noUiSlider.set([min, min + min_range])
            // const new_min = max_value - min_range
            el.attr('data-min_current', min)
            min_indicator[0].innerText = min
            el.attr('data-max_current', min + min_range)
            max_indicator[0].innerText = min + min_range
          } else {
            el[0].noUiSlider.set([max - min_range, max])
            // const new_min = max_value - min_range
            el.attr('data-min_current', max - min_range)
            min_indicator[0].innerText = max - min_range
            el.attr('data-max_current', max)
            max_indicator[0].innerText = max
          }
        } else {
          el.attr('data-min_current', min)
          min_indicator[0].innerText = min
        }
      }
      let min_value_slider = parseFloat(min_indicator.text());
      let max_value_slider = parseFloat(max_indicator.text());
      let diff_value = ((max_value_slider - min_value_slider) * 25).toString() + '%';
      let top_value = ((5 - max_value_slider) * 25).toString() + '%';
      if (el.hasClass('attribute_1')) {
        top_value = ((min_value_slider - 1) * 25).toString() + '%';
        $('.square').css('height', diff_value);
        $('.square').css('bottom', top_value)
      } else {
        $('.square').css('width', diff_value);
        $('.square').css('right', top_value)
      }

      if(slider_dom.parents('.voice-slider').length && slider_dom.find('.attribute_2').length) {
        convertSliderToVoiceValue(min_indicator, max_indicator);
      }
    })
  })
}

function renderFormAuctionPrice(start_price='', end_price='') {
  // let start_price_checked = '',
  //   end_price_checked = '',
  //   start_price_disabled = 'disabled',
  //   end_price_disabled = 'disabled';

  // if(start_price !== '') {
  //   start_price_checked = 'checked';
  //   start_price_disabled = '';
  // }

  // if(end_price !== '') {
  //   end_price_checked = 'checked';
  //   end_price_disabled = '';
  // }

  return `<div class="sale-content-modal-row input-container input-form-container">
            <div class="sale-content-modal-row auctions-field">
              <label for="auction_price_time_input" class="form-label" style="width: 100%;">開始価格<span class="form-label__optional">[任意]</span></label>
              <div class="form-hint" style="width: 100%;">設定価格以下のオファーは届かないようにできます。</div>
              <input type="text" min="100" id="auction_price_time_input" maxlength="15"
                placeholder="50,000" class="form-control" ${start_price} style="max-width: 160px; margin-right: 8px; text-align: right;"> 円（税抜）
              <div class="error-message">フォーマットが正しくありません。</div>
            </div>

            <div class="sale-content-modal-row auctions-field">
              <label for="auction_end_price_input" class="form-label" style="width: 100%;">終了価格<span class="form-label__optional">[任意]</span></label>
              <div class="form-hint" style="width: 100%;">設定価格を提示すれば、すぐ売却できるようにします。</div>
              <input type="text" min="100" id="auction_end_price_input" maxlength="15"
                placeholder="200,000" class="form-control" ${end_price} style="max-width: 160px; margin-right: 8px; text-align: right;"> 円（税抜）
              <div class="error-message">フォーマットが正しくありません。</div>
            </div>
          </div>`
}

function renderFormAuctionTime(start_time='', end_time='') {
  // let start_time_checked = '',
  //   end_time_checked = '',
  //   start_time_disabled = 'disabled',
  //   end_time_disabled = 'disabled';
  // if(start_time !== '') {
  //   start_time_checked = 'checked';
  //   start_time_disabled = '';
  // }

  // if(end_time !== '') {
  //   end_time_checked = 'checked';
  //   end_time_disabled = '';
  // }

  let today = new Date();
  let day_start_time = today.getFullYear() + '/' + (today.getMonth() + 1)  + '/' + today.getDate() + ' ' + today.getHours() + ":" + today.getMinutes();
  let day_end_time = today.getFullYear() + '/' + (today.getMonth() + 2) + '/' + today.getDate() + ' ' + today.getHours() + ":" + today.getMinutes();

  return `<div class="sale-content-modal-row input-container input-form-container">
            <div class="sale-content-modal-row auctions-field">
              <label for="auction_start_time_datetimepicker" class="form-label" style="width: 100%;">開始日時<span class="form-label__optional">[任意]</span></label>
              <input type="text" placeholder="${day_start_time}" ${start_time} id="auction_start_time_datetimepicker"
              class="auctions_datetimepicker form-control" value="" style="max-width: 160px; margin-right: 8px">
              <div class="error-message">フォーマットが正しくありません。</div>
            </div>

            <div class="sale-content-modal-row auctions-field">
              <label for="auction_end_time_datetimepicker" class="form-label" style="width: 100%;">終了日時<span class="form-label__optional">[任意]</span></label>
              <input type="text" placeholder="${day_end_time}" ${end_time} id="auction_end_time_datetimepicker"
              class="auctions_datetimepicker form-control" value="" style="max-width: 160px; margin-right: 8px">
              <div class="error-message">フォーマットが正しくありません。</div>
            </div>
          </div>`
}

function initDateTimePickerAuctions() {
  $('.auctions_datetimepicker').datetimepicker();
}

function renderDefaultThumbnailSelection(type) {
  let mix = ' hide', music = ' hide', sound= ' hide', voice = ' hide';
  switch(type) {
    case 'music':
      music = '';
      break;
    case 'sound_effect':
      sound = '';
      break
    case 'voice':
      voice = '';
      break
    default:
      mix = '';
      break
  }
  return `<div class="sale-content-color-selection">
            <div class="row color-default 2mix ${mix}">
               <div class="color-block" data-color="FCFCFC" style="background: #FCFCFC"></div>
               <div class="color-block" data-color="F0F0F0" style="background: #F0F0F0"></div>
               <div class="color-block" data-color="A7A8A9" style="background: #A7A8A9"></div>
               <div class="color-block" data-color="53565A" style="background: #53565A"></div>
               <div class="color-block" data-color="333333" style="background: #333333"></div>
            </div>

            <div class="row music ${music}">
               <div class="color-block" data-color="F2FBFC" style="background: #F2FBFC"></div>
               <div class="color-block" data-color="E5F7F9" style="background: #E5F7F9"></div>
               <div class="color-block" data-color="B2E7EE" style="background: #B2E7EE"></div>
               <div class="color-block" data-color="7FD6E2" style="background: #7FD6E2"></div>
               <div class="color-block" data-color="00AEFF" style="background: #00AEFF"></div>
            </div>
            
             <div class="row sound_effect ${sound}">
               <div class="color-block" data-color="FEFEF2" style="background: #FEFEF2"></div>
               <div class="color-block" data-color="FCFCE5" style="background: #FCFCE5"></div>
               <div class="color-block" data-color="F6F5B2" style="background: #F6F5B2"></div>
               <div class="color-block" data-color="EFED80" style="background: #EFED80"></div>
               <div class="color-block" data-color="FDDC01" style="background: #FDDC01"></div>
            </div>
            
             <div class="row voice ${voice}">
                <div class="color-block" data-color="FFF5F5" style="background: #FFF5F5"></div>
                <div class="color-block" data-color="FFEBEB" style="background: #FFEBEB"></div>
                <div class="color-block" data-color="FFC5C5" style="background: #FFC5C5"></div>
                <div class="color-block" data-color="FF9F9F" style="background: #FF9F9F"></div>
                <div class="color-block" data-color="FF008C" style="background: #FF008C"></div>
            </div>
          </div>`
}

let listFile = {};
let listFileImage = {};

function initDropzoneAudio(idDrop, targetDom) {
    let thisForm = $(
        '.upload-sale'
    );
    // if (thisForm.find('#dropzoneUpLoadVideoTopic').length) {
    var previewNode = thisForm.find(
        '.mattach-template-form'
    );
    var previewTemplate = previewNode.parent().html();
    previewNode.parent().empty();
    Dropzone.autoDiscover = false;

    $dropzoneUploadSale = new Dropzone(idDrop, {
        maxFilesize: 15000,
        timeout: 900000,
        autoDiscover: false,
        acceptedFiles: '.mp4, .MP4, .mov, .MOV, application/pdf, .mp3, .wav, .png, .PNG, .jpg, .JPG',
        previewsContainer:
            '.upload-sale .mattach-previews-form',
        previewTemplate: previewTemplate,
        url: '/',
        autoProcessQueue: false,
        autoQueue: false,
        clickable: '.upload-sale .sale-content-audio-add',
        maxFiles: 1,
        dictDefaultMessage:
            '<i class="icon icon--sicon-add-cirlce"></i>\n' +
            '<p>ファイルを選択</p>',
    });

    $dropzoneUploadSale.on('maxfilesexceeded', function (file) {
        $dropzoneUploadSale.removeAllFiles();
        $dropzoneUploadSale.addFile(file);
    });

    $dropzoneUploadSale.on('addedfile', function (file, e) {
        $('.upload-sale').removeClass('has-error');
        if ($dropzoneUploadSale.files.length > 1) {
            $dropzoneUploadSale.removeAllFiles(true);
            $dropzoneUploadSale.addFile(file);
        }

        if (
            $dropzoneUploadSale.files &&
            $dropzoneUploadSale.files[0] &&
            $dropzoneUploadSale.files[0].name.match(
                /\.(mp4|MP4|mov|MOV|mp3|MP3|wav|WAV|PDF|pdf|png|PNG|jpg|JPG)$/
            )
        ) {
            let file_dom = $(file.previewElement);
            real_name_information = file.name;
            let file_preview = thisForm
                .find('.mattach-preview-container-form-upload-logo')
                .find('.mcommment-file__name-form');
            for (let i = 0; i < file_preview.length; i++) {
                $(file_preview[i]).text('');
                $(file_preview[i]).append(
                    '<i class="icon icon--sicon-clip"></i>' + real_name_information
                );
                break;
            }
            file_name = this.files[0].name;
            if (thisForm.find('.account__file').length) {
                thisForm.find('.account__file').hide();
            }

            // uploadFileTopicS3(file_dom);

            listFileTemp[targetDom.attr('data-sale')] = $dropzoneUploadSale.files[0]

            if($dropzoneUploadSale.files[0].name.match(/\.(mp3|MP3|wav|WAV)$/)) {
              thisForm.find('.btn-modal-audio-setting').removeClass('btn--disabled')
              thisForm.find('.btn-modal-audio-setting').addClass('audio-setting')
              thisForm.find('.btn-modal-audio-setting').removeClass('video-setting')
             has_file_upload = true;
            } else if($dropzoneUploadSale.files[0].name.match(/\.(mp4|MP4|MOV|mov)$/)) {
              thisForm.find('.btn-modal-audio-setting').removeClass('btn--disabled')
              thisForm.find('.btn-modal-audio-setting').addClass('video-setting')
              thisForm.find('.btn-modal-audio-setting').removeClass('audio-setting')
                    has_file_upload = true;
            } else {
                    has_file_upload = false;
              thisForm.find('.btn-modal-audio-setting').addClass('btn--disabled')
              thisForm.find('.btn-modal-audio-setting').removeClass('audio-setting video-setting')
            }
        } else if (!$dropzoneUploadSale.files) {
            return false;
        } else {
            alert('mp4, mov, pdf, mp3, wav, png, jpgのみアップロードできます。');
            $dropzoneUploadSale.removeAllFiles(true);
        }
        checkModalSubmitButton();
    });

    $dropzoneUploadSale.on('removedfile', function (file, e) {
      has_file_upload = false;
      delete listFileTemp[targetDom.attr('data-sale')];
      $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
    });
}

function renderModalAudioSetting(attr1_min, attr1_max, attr2_min, attr2_max, background, slider, show_thumbnail) {
  let mix = '', music= '', sound = '', voice = '';
  switch(slider) {
    case '2mix':
      mix = 'checked'
      break
    case 'music':
      music = 'checked'
      break
    case 'sound_effect':
      sound = 'checked'
      break
    case 'voice':
      voice = 'checked'
      break
    default:
      mix = 'checked'
      break
  }

  let color = '', image = '';
  if (show_thumbnail === 'image') {
    image = 'checked'
  } else {
    color = 'checked'
  }
  return `<div class="modal popup-container fade" id="modal-detail-audio-setting" role="dialog" style="z-index: 1000" data-backdrop="true" data-keyboard="false">
            <div class="modal-dialog popup-dialog">
              <div class="modal-content popup-content audio-setting">
                <div class="sale-content-modal-row input-form-container">
                  <div class="form-label__heading">オーディオの種類</div>
                  <div class="form-hint" style="width: 100%;">2mix以外を選択すると、他の要素と組み合わせて視聴できます。</div>
                  <div class="sale-content-content-type">
                    <label class="input-radio br-8">
                      <input type="radio" name="sale_content_content_type" value="2mix" index="0" id="2mix" ${mix}><div class="sale_content_type_text">2mix</div>
                      <div class="check-mark"></div>
                    </label>
                    <label class="input-radio">
                      <input type="radio" name="sale_content_content_type" value="music" index="1" id="music" ${music}><div class="sale_content_type_text">MUSIC</div>
                      <div class="check-mark"></div>
                    </label>
                    <label class="input-radio">
                      <input type="radio" name="sale_content_content_type" value="sound_effect" index="2" id="sound_effect" ${sound}><div class="sale_content_type_text">SOUND EFFECTS</div>
                      <div class="check-mark"></div>
                    </label>
                    <label class="input-radio">
                      <input type="radio" name="sale_content_content_type" value="voice" index="3" id="voice" ${voice}><div class="sale_content_type_text">VOICE</div>
                      <div class="check-mark"></div>
                    </label>
                  </div>
                </div>

                <div class="sale-content-modal-row input-form-container">
                <div class="form-label__heading">属性</div>
                    ${renderSliderAudioAttr(attr1_min,attr1_max,attr2_min,attr2_max, slider)}
                </div>
                
                <div class="sale-content-modal-row input-form-container show-thumbnail-setting">
                  <div class="form-label__heading" style="font-size: 13px">サムネイル</div>
                  <label class="input-radio">
                    <input type="radio" name="sale_content_thumbnail_type" value="color" index="0" id="thumbnail_color" ${color}><div class="sale_content_type_text">おまかせ</div>
                    <div class="check-mark"></div>
                    ${renderDefaultThumbnailSelection(slider)}
                  </label>

                  <label class="input-radio" style="width: 100%;">
                    <input type="radio" name="sale_content_thumbnail_type" value="image" index="1" id="thumbnail_image" ${image}><div class="sale_content_type_text">サムネイル画像をアップロード</div>
                    <div class="form-hint" style="width: 100%;">正方形のアスペクト比で画像をアップロードしましょう。</div>
                    <div class="check-mark"></div>
                  
                    <div class="upload-audio-image-container input-container">
                      <label for="id_sale_content_image" style="width: 100%" id="upload-audio-image">
                        <div class="account_upload-file mattach mattach-form" style="width: 100%;">
                          <div class="mcomment-attached">
                            <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                              <div class="mattach-previews mattach-previews-form collection">
                                <div class="mattach-template mattach-template-form collection-item item-template">
                                  <div class="mattach-info" data-dz-thumbnail="">
                                    <div class="mcommment-file">
                                      <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                      <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                      <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                              class="icon icon--sicon-close"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div id="id_sale_content_image" class="fallback dropzone">
                          </div>
                        </div>
                      </label>
                      <div class="error-message">画像がありません。</div>
                      <input type="text" class="hide x_sale_content_bb">
                      <input type="text" class="hide y_sale_content_bb">
                      <input type="text" class="hide width_sale_content_bb">
                      <input type="text" class="hide height_sale_content_bb">
                    </div>

                    <div class="sale-content-image" style='margin: auto; margin-top: 16px; ${background}'>
                    </div>
                  </label>
                </div>
                <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
                  <button type="button" class="btn btn--tertiary btn-close-audio-setting">キャンセル</button>
                  <button type="button" class="btn btn--primary btn-save-audio-setting">OK</button>
                </div>
              </div>
            </div>
          </div>`
}

let listDeleteAudio = {};
let listDeleteImage = {};
function initDropzoneAudioImage(idDrop, targetDom) {
  let thisForm = $(
      '#modal-detail-audio-setting .upload-audio-image-container'
  );
  // if (thisForm.find('#dropzoneUpLoadVideoTopic').length) {
  var previewNode = thisForm.find(
      '.mattach-template-form'
  );
  var previewTemplate = previewNode.parent().html();
  previewNode.parent().empty();
  Dropzone.autoDiscover = false;

  $dropzoneUploadImage = new Dropzone(idDrop, {
      maxFilesize: 15000,
      timeout: 900000,
      autoDiscover: false,
      acceptedFiles: '.jpg, .png',
      previewsContainer:
          '.upload-audio-image-container .mattach-previews-form',
      previewTemplate: previewTemplate,
      url: '/',
      autoProcessQueue: false,
      autoQueue: false,
      clickable: '.upload-audio-image-container #upload-audio-image',
      maxFiles: 1,
      dictDefaultMessage:
          '<i class="icon icon--sicon-add-cirlce"></i>\n' +
          '<p>ファイルを選択</p>',
  });

  $dropzoneUploadImage.on('maxfilesexceeded', function (file) {
      $dropzoneUploadImage.removeAllFiles();
      $dropzoneUploadImage.addFile(file);
  });

  $dropzoneUploadImage.on('addedfile', function (file, e) {
      $('#modal-detail-audio-setting input[name=sale_content_thumbnail_type][value=image]').trigger('click');
      $('#modal-detail-audio-setting .upload-audio-image-container').removeClass('has-error');
      if ($dropzoneUploadImage.files.length > 1) {
          $dropzoneUploadImage.removeAllFiles(true);
          $dropzoneUploadImage.addFile(file);
      }

      if (
          $dropzoneUploadImage.files &&
          $dropzoneUploadImage.files[0] &&
          $dropzoneUploadImage.files[0].name.match(
              /\.(jpg|png|JPG|PNG)$/
          )
      ) {
          let file_dom = $(file.previewElement);
          real_name_information = file.name;
          let file_preview = thisForm
              .find('.mattach-preview-container-form-upload-logo')
              .find('.mcommment-file__name-form');
          for (let i = 0; i < file_preview.length; i++) {
              $(file_preview[i]).text('');
              $(file_preview[i]).append(
                  '<i class="icon icon--sicon-clip"></i>' + real_name_information
              );
              break;
          }
          file_name = this.files[0].name;
          if (thisForm.find('.account__file').length) {
              thisForm.find('.account__file').hide();
          }

          readDataUploadImage(file, avatarCropper, targetDom);
          listFileImage[targetDom.attr('data-sale')] = $dropzoneUploadImage.files[0]

      } else if (!$dropzoneUploadImage.files) {
          return false;
      } else {
          alert('png, jpgのみアップロードできます。');
          $dropzoneUploadImage.removeAllFiles(true);
      }
  });

  $dropzoneUploadImage.on('removedfile', function (file, e) {
      delete listFileImage[targetDom.attr('data-sale')];
  });

  thisForm.find('.account__file .icon--sicon-close').on('click', function () {
      thisForm.find('.account__file').addClass('hide');
      listDeleteImage[targetDom.attr('data-sale')] = true
  });
}

let listX = {};
let listY = {};
let listHeight = {};
let listWidth = {};

function readDataUploadImage(file, crop, targetDom) {
    let $image = $('#image');
    let cropBoxData, canvasData;
    let fileReader = new FileReader();

    fileReader.onloadend = function (e) {
        $image.attr('src', e.target.result);
        $('#modalCrop').modal('show');
    };
    fileReader.readAsDataURL(file);

    $("#modalCrop").modal({
        show: false,
        backdrop: 'static'
    });

    $('#modalCrop').off().on('shown.bs.modal', function () {
        $image.cropper({
            viewMode: crop.viewMode,
            rotatable: crop.rotatable,
            aspectRatio: crop.aspectRatio,
            minCropBoxWidth: crop.minCropBoxWidth,
            minCropBoxHeight: crop.minCropBoxHeight,
            minContainerHeight: crop.minContainerHeight,
            ready: function () {
                $image.cropper('setCanvasData', canvasData);
                $image.cropper('setCropBoxData', cropBoxData);
            }
        });
        // $('.modal:not(#modalCropBanner)').hide();
    }).on('hidden.bs.modal', function () {
        cropBoxData = $image.cropper('getCropBoxData');
        canvasData = $image.cropper('getCanvasData');
        $('#modal-project-setting').show();
        $image.cropper('destroy');
    });

    $('.js-crop-and-upload').unbind().bind("click", function () {
        var cropData = $image.cropper("getData");
        var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
        $('.sale-content-image, .sale-content-video-image').css('background-image', 'url(' + croppedImageDataURL + ')');
            listX[targetDom.attr('data-sale')] = cropData['x'];
            listY[targetDom.attr('data-sale')] = cropData['y'];
            listHeight[targetDom.attr('data-sale')] = cropData['width'];
            listWidth[targetDom.attr('data-sale')] = cropData['height'];
            $('#modalCrop').modal('hide');
    });


    // Enable zoom in button
    $('.js-zoom-in').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').click(function () {
        $image.cropper('zoom', -0.1);
    });
}

function checkYear(input) {
  if (input.value.length > 4) {
    input.value = input.value.slice(0,4);
  }
}

function inithashTag() {
  $('#id_hashtag').on('focus', function(e) {
    let value = $('#id_hashtag').val();
    if(value === '') {
      $('#id_hashtag').val('#')
    }
  });

  $('#id_hashtag').on('input', function(e) {
    if ($('#id_hashtag').val().trim()[0] !== '#') {
      $('#id_hashtag').val('#' + $('#id_hashtag').val().trim());
    }
  });

  $('#id_hashtag').on('keyup', function(e) {
    if(e.which === 32) {
      $('#id_hashtag').val($('#id_hashtag').val()+ '#');
      let valueInputLength = $('#id_hashtag').val().trim().length;
      $('#id_hashtag').get(0).setSelectionRange(valueInputLength, valueInputLength);
    }
  });
}

function addCommaToPrice(input) {
  checkVal(input)
  input.on('change', function (e) {
    checkVal($(this))
  });
}

function checkVal(input) {
  input.val(input.val().replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ','));
  let current_val = parseInt(input.val().replaceAll(',',''));
  if( current_val > 0 && current_val < 100) {
    input.val('100');
  }
}

function convertPrice(price) {
  return parseInt(price).toString().replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

function checkModalSubmitButton(condition1=false, condition2=false, condition3=false) {
  if (condition1 && (condition2 || condition3)) {
    $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
    return false;
  }

  let title = $('#id_title');
  if(!title.val().trim()) {
    $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
    return false;
  }
  let segmentActive = parseInt($('.segment-item.active').attr('data-option'))
  let saveYoutubeLink =  $('#sale_youtube_link');
  let saveYoutubeLinkValue = saveYoutubeLink.val();
  if (segmentActive === 2) {
    let disabledOkButton = false;
    if (!saveYoutubeLinkValue || !saveYoutubeLinkValue.trim()){
      disabledOkButton = true;
    }else {
      disabledOkButton = false;
    }
    if (disabledOkButton) {
         saveYoutubeLink.parent().find('.error-message').addClass('show')
         saveYoutubeLink.parent().addClass('has-error')
        $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').addClass('btn--disabled');
        return false;
    }else {
         saveYoutubeLink.parent().find('.error-message').removeClass('show')
         saveYoutubeLink.parent().removeClass('has-error')
    }
  }

  $('.modal__fullscreen .modal-footer .btn--primary.bootbox-accept').removeClass('btn--disabled');
  return true;
}

function checkVideoAvailability(youtubeUrl, saleLinkYoutube) {
    // Extract video ID from the YouTube URL
    const videoId = getVideoIdFromUrl(youtubeUrl);
    if (videoId) {
        // Check video availability using YouTube Data API
        return checkVideoExistence(videoId)
            .then(result => {
              if (result) {
                saleLinkYoutube.parent().find('.error-message').removeClass('show')
                saleLinkYoutube.parent().removeClass('has-error')
                  checkModalSubmitButton()
              } else {
                saleLinkYoutube.parent().find('.error-message').addClass('show')
                saleLinkYoutube.parent().addClass('has-error')
                  checkModalSubmitButton(true, true, true)
              }
                return result;
            })
            .catch(error => {
              saleLinkYoutube.parent().find('.error-message').addClass('show')
              saleLinkYoutube.parent().addClass('has-error')
                  checkModalSubmitButton(true, true, true)
                console.error('Error:', error);
                return false;
            });
    } else {
      saleLinkYoutube.parent().find('.error-message').addClass('show')
      saleLinkYoutube.parent().addClass('has-error')
        checkModalSubmitButton(true, true, true)
        return false;
    }
}

function getVideoIdFromUrl(youtubeUrl) {
    // Regular expression to extract video ID from various YouTube URL formats
    const regExp = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const match = youtubeUrl.match(regExp);

    // If there's a match, return the video ID, otherwise return null
    return match ? match[1] : null;
}

function checkVideoExistence(videoId) {
    return new Promise((resolve, reject) => {
        // YouTube Data API key (replace with your own API key)
        const apiKey = 'AIzaSyDCMgNHV7qoz3qTtwgSBdih8nniwORpqMM';
        // Make a request to the YouTube Data API to retrieve information about the video
        fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${apiKey}&part=snippet,contentDetails,status`)
            .then(response => response.json())
            .then(data => {
                const videoDetails = data.items[0];

                if (videoDetails) {
                    const videoTitle = videoDetails.snippet.title;
                    const videoStatus = videoDetails.status.embeddable;

                    if (videoStatus) {
                        console.log(`Video with ID ${videoId} is embeddable and can be played. Title: ${videoTitle}`);
                        resolve(true);
                    } else {
                        console.error(`Video with ID ${videoId} is not embeddable.`);
                        resolve(false);
                    }
                } else {
                    console.error(`Video with ID ${videoId} does not exist or is not accessible.`);
                    resolve(false);
                }
            })
            .catch(error => {
                console.error('Error checking video existence:', error);
                reject(error); // Sử dụng reject để bắt lỗi
            });
    });
}
function convertSliderToVoiceValue(min_dom, max_dom) {
  let min = parseInt(min_dom[0].innerText);
  let max = parseInt(max_dom[0].innerText);
  min = reCalculateValue(min);
  max = reCalculateValue(max);
  min_dom[0].innerText = min;
  max_dom[0].innerText = max;
}

function convertVoiceToSliderValue(min_dom, max_dom) {
  let min = parseInt(min_dom[0].innerText);
  let max = parseInt(max_dom[0].innerText);
  min = calculateValue(min);
  max = calculateValue(max);
  min_dom[0].innerText = min;
  max_dom[0].innerText = max;
}

function calculateValue(value) {
  switch (value) {
    case 0:
      return 1
    case 12:
      return 2
    case 18:
      return 3
    case 40:
      return 4
    case 80:
      return 5
    default:
      return 1
  }
}

function reCalculateValue(value) {
  switch (value) {
    case 1:
      return 0
    case 2:
      return 12
    case 3:
      return 18
    case 4:
      return 40
    case 5:
      return 80
    default:
      return 0
  }
}

function checkPauseCondition(sample) {
  let album_type = sample.siblings('.sample-audio-info').find('.sample-audio-title').attr('data-content-type');
  switch (album_type) {
    case 'music':
      $('.playing-navi').each(function (i, e) {
        if(e == sample.parent()[0]) {
          return
        }
        let curr_type = $(e).find('.sample-audio-info .sample-audio-title').attr('data-content-type');
        if(curr_type === 'music' || curr_type === '2mix') {
          $(e).removeClass('playing-navi')
          $(e).find('audio')[0].pause();
        } else {
          let audio =  $(e).find('audio')
          audio.attr('currentTime', audio[0].currentTime)
        }
      });
      $('.sample-audio-thumbnail.playing, .sample-audio-thumbnail.loading').removeClass('playing loading')
      break
    case 'voice':
      $('.playing-navi').each(function (i, e) {
        if(e == sample.parent()[0]) {
          return
        }
        let curr_type = $(e).find('.sample-audio-info .sample-audio-title').attr('data-content-type');
        if(curr_type === 'voice' || curr_type === '2mix') {
          $(e).removeClass('playing-navi')
          $(e).find('audio')[0].pause();
        } else {
          let audio =  $(e).find('audio')
          audio.attr('currentTime', audio[0].currentTime)
        }
      });
      $('.sample-audio-thumbnail.playing, .sample-audio-thumbnail.loading').removeClass('playing loading')
      break
    case 'sound_effect':
      $('.playing-navi').each(function (i, e) {
        if(e == sample.parent()[0]) {
          return
        }
        let curr_type = $(e).find('.sample-audio-info .sample-audio-title').attr('data-content-type');
        if(curr_type === 'sound_effect' || curr_type === '2mix') {
          $(e).removeClass('playing-navi')
          $(e).find('audio')[0].pause();
        } else {
          let audio =  $(e).find('audio')
          audio.attr('currentTime', audio[0].currentTime)
        }
      });
      $('.sample-audio-thumbnail.playing, .sample-audio-thumbnail.loading').removeClass('playing loading')
      break
    default:
      $('.playing-navi').each(function (i, e) {
        if(e == sample.parent()[0]) {
          return
        }
        $(e).removeClass('playing-navi')
        $(e).find('audio')[0].pause();
      });
      $('.sample-audio-thumbnail.playing, .sample-audio-thumbnail.loading').removeClass('playing loading')
      break
  }
}

function renderModalVideoSetting(background, show_thumbnail) {
  let color, image, hide_image = '';
  if (show_thumbnail === 'image') {
    image = 'checked';
  } else {
    color = 'checked';
    hide_image = ' hide';
  }
  return `<div class="modal popup-container fade" id="modal-detail-video-setting" role="dialog" style="z-index: 1000" data-backdrop="true" data-keyboard="false">
  <div class="modal-dialog popup-dialog">
    <div class="modal-content popup-content video-setting">
      <div class="sale-content-modal-row input-form-container show-thumbnail-setting">
        <div class="form-label__heading" style="font-size: 13px">サムネイル</div>
        <label class="input-radio">
          <input type="radio" name="sale_content_thumbnail_video_type" value="color" index="0" id="thumbnail_video_time" ${color}>
          <div class="bodytext-13 video-time" style="margin-bottom: 12px;">動画から設定:</div>
          <div class="check-mark"></div>
          ${renderVideoThumbnailSelection(color)}
        </label>

        <label class="input-radio" style="width: 100%;">
          <input type="radio" name="sale_content_thumbnail_video_type" value="image" index="1" id="thumbnail_video_image" ${image}>
          <div class="bodytext-13" style="margin-bottom: 12px;">アップロード</div>
          <div class="form-hint" style="width: 100%;">長方形のアスペクト比で画像をアップロードしましょう。</div>
          <div class="check-mark"></div>
        
          <div class="upload-audio-image-container input-container">
            <label for="id_sale_content_video_image" style="width: 100%" id="upload-video-image">
              <div class="account_upload-file mattach mattach-form" style="width: 100%;">
                <div class="mcomment-attached">
                  <div class="mattach-preview-container mattach-preview-container-form-upload-logo">
                    <div class="mattach-previews mattach-previews-form collection">
                      <div class="mattach-template mattach-template-form collection-item item-template">
                        <div class="mattach-info" data-dz-thumbnail="">
                          <div class="mcommment-file">
                            <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                            <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                            <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="id_sale_content_video_image" class="fallback dropzone">
                </div>
              </div>
            </label>
            <div class="error-message">画像がありません。</div>
            <input type="text" class="hide x_sale_content_bb">
            <input type="text" class="hide y_sale_content_bb">
            <input type="text" class="hide width_sale_content_bb">
            <input type="text" class="hide height_sale_content_bb">
          </div>

          <div class="sale-content-image hide" style='margin: auto; margin-top: 16px; ${background}'>
          </div>
        </label>
        <div class="sale-content-video-image ${hide_image}" style='${background}'></div>
      </div>
      <div class="popup-footer" style="text-align: right; padding-top: 24px; border-top: 1px solid #f0f0f0;">
        <button type="button" class="btn btn--tertiary btn-close-video-setting">キャンセル</button>
        <button type="button" class="btn btn--primary btn-save-video-setting">OK</button>
      </div>
    </div>
  </div>
</div>`
}

function renderVideoThumbnailSelection(color) {
  let class_hide = '';
  return `<div>
    <video preload="none" id="sale-content-video" style="width: 100%" crossorigin="anonymous" controls>
      <source src="">
      Your browser does not support the video tag.
    </video>
    <div class="canvas" style="width: 100%">
        <canvas id="video-canvas" class="hide" style="width: 100%;"></canvas>
        <canvas id="video-canvas-resize" class="hide"></canvas>
    </div>
  </div>`
}

function initDropzoneVideoImage(idDrop, targetDom) {
  let thisForm = $(
      '#modal-detail-video-setting .upload-audio-image-container'
  );

  var previewNode = thisForm.find(
      '.mattach-template-form'
  );
  var previewTemplate = previewNode.parent().html();
  previewNode.parent().empty();
  Dropzone.autoDiscover = false;

  $dropzoneUploadVideoImage = new Dropzone(idDrop, {
      maxFilesize: 15000,
      timeout: 900000,
      autoDiscover: false,
      acceptedFiles: '.jpg, .png',
      previewsContainer:
          '#modal-detail-video-setting .upload-audio-image-container .mattach-previews-form',
      previewTemplate: previewTemplate,
      url: '/',
      autoProcessQueue: false,
      autoQueue: false,
      clickable: '#modal-detail-video-setting .upload-audio-image-container #upload-video-image',
      maxFiles: 1,
      dictDefaultMessage:
          '<i class="icon icon--sicon-add-cirlce"></i>\n' +
          '<p>ファイルを選択</p>',
  });

  $dropzoneUploadVideoImage.on('maxfilesexceeded', function (file) {
      $dropzoneUploadVideoImage.removeAllFiles();
      $dropzoneUploadVideoImage.addFile(file);
  });

  $dropzoneUploadVideoImage.on('addedfile', function (file, e) {
      $('#modal-detail-video-setting input[name=sale_content_thumbnail_video_type][value=image]').trigger('click');
      $('#modal-detail-video-setting .upload-audio-image-container').removeClass('has-error');
      if ($dropzoneUploadVideoImage.files.length > 1) {
          $dropzoneUploadVideoImage.removeAllFiles(true);
          $dropzoneUploadVideoImage.addFile(file);
      }

      if (
          $dropzoneUploadVideoImage.files &&
          $dropzoneUploadVideoImage.files[0] &&
          $dropzoneUploadVideoImage.files[0].name.match(
              /\.(jpg|png|JPG|PNG)$/
          )
      ) {
          let file_dom = $(file.previewElement);
          real_name_information = file.name;
          let file_preview = thisForm
              .find('.mattach-preview-container-form-upload-logo')
              .find('.mcommment-file__name-form');
          for (let i = 0; i < file_preview.length; i++) {
              $(file_preview[i]).text('');
              $(file_preview[i]).append(
                  '<i class="icon icon--sicon-clip"></i>' + real_name_information
              );
              break;
          }
          file_name = this.files[0].name;
          if (thisForm.find('.account__file').length) {
              thisForm.find('.account__file').hide();
          }

          readDataUploadImage(file, videoCropper, targetDom);
          listFileImage[targetDom.attr('data-sale')] = $dropzoneUploadVideoImage.files[0]

      } else if (!$dropzoneUploadVideoImage.files) {
          return false;
      } else {
          alert('png, jpgのみアップロードできます。');
          $dropzoneUploadVideoImage.removeAllFiles(true);
      }
  });

  $dropzoneUploadVideoImage.on('removedfile', function (file, e) {
      delete listFileImage[targetDom.attr('data-sale')];
  });

  thisForm.find('.account__file .icon--sicon-close').on('click', function () {
      thisForm.find('.account__file').addClass('hide');
      listDeleteImage[targetDom.attr('data-sale')] = true
  });
}

function initVideoThumbnailSelectionByTime(data_sale) {
    let _VIDEO = $('#modal-detail-video-setting video').get(0);
    _VIDEO.style.display = 'inline';
    _VIDEO.preload = 'auto';
    $(_VIDEO).on('canplay', function () {
        $(_VIDEO).off('canplay');
        let _CANVAS_DOM = $(_VIDEO).siblings('.canvas');
        let _CANVAS = _CANVAS_DOM.find("#video-canvas").get(0);
        let _CANVAS_RESIZE = _CANVAS_DOM.find("#video-canvas-resize").get(0);
        let _CTX = _CANVAS.getContext("2d");
        let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");

        // Set canvas dimensions same as video dimensions
        _CANVAS.width = _VIDEO.videoWidth;
        _CANVAS.height = _VIDEO.videoHeight;
        _CANVAS_DOM.css('display', 'block');

        function getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO) {
          let imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
          _CANVAS_RESIZE.width = imgResize.width;
          _CANVAS_RESIZE.height = imgResize.height;
          _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

          _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
        }

        function resizeImg (width, height) {

          let targetWidth = 480;
          if (width <= targetWidth) {
              return {width: width, height: height}
          }

          let ratio = targetWidth / width;

          return {
              width: parseInt(width * ratio),
              height: parseInt(height * ratio)
          }
        };

        setTimeout(function () {
            _VIDEO.pause();
            getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
            if($('#modal-detail-video-setting input[name=sale_content_thumbnail_video_type]:checked').val() === 'color') {
                listFileImage[data_sale] = dataURLtoFile(_CANVAS_RESIZE.toDataURL(),'thumbnail.png');
                $('.sale-content-video-image').css('background-image', 'url(' + _CANVAS_RESIZE.toDataURL() + ')');
                listX[data_sale] = 0;
                listY[data_sale] = 0;
                listWidth[data_sale]= 270;
                listHeight[data_sale] = 480;
                $('.video-time').html('動画から設定: <span style="color: #009ace">00:00</span>');
            }

            $(_VIDEO).on('seeked', function () {
                _VIDEO.pause();
                $('.canvas').css('display', 'block');
                getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                if($('#modal-detail-video-setting input[name=sale_content_thumbnail_video_type]:checked').val() === 'color') {
                    $('.sale-content-video-image').css('background-image', 'url(' + _CANVAS_RESIZE.toDataURL() + ')');
                    listFileImage[data_sale] = dataURLtoFile(_CANVAS_RESIZE.toDataURL(),'thumbnail.png');
                    listX[data_sale] = 0;
                    listY[data_sale] = 0;
                    listWidth[data_sale]= 270;
                    listHeight[data_sale] = 480;
                    $('.video-time').html('動画から設定: ' + '<span style="color: #009ace">' + convertSecondsToTime(_VIDEO.currentTime) + '</span>');
                }
            });
        }, 500);
    });
}

function dataURLtoFile(dataUrl, filename) {
    var arr = dataUrl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);

    while(n--){
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, {type:mime});
}

function convertSecondsToTime(seconds) {
  if (!isNaN(seconds)) {
      var time;
      time = new Date(seconds * 1000).toISOString().substr(11, 8);

      var time_arr = time.split(':');

      if ( time_arr.length > 2 && time_arr[0] == '00' ) {
        time_arr[1] = time_arr[1].replace(/^0/, ''); // 分の先頭の0を取り除く
        time = time_arr[1] + ':' + time_arr[2];
      } else {
        time_arr[1] = time_arr[1].replace(/^0/, ''); // 分の先頭の0を取り除く
        time = time_arr[0] + ':' + time_arr[1] + ':' + time_arr[2];
      }

      return time;
  }
}

function genVideoInfo(target) {
  let info
  if(target.parents('.list-new-works__item-container').length){
    info = $('.sample-audio-item[data-sale-content-id='+ target.parents('.list-new-works__item-container').attr('data-sale-id') +'] .sample-audio-info .sample-audio-title');
  } else {
    info = target.siblings('.sample-audio-info').find('.sample-audio-title');
  }

  target.append(`<div class="list-new-works__content_hover">
  <div class="list-new-works__heading"><div class="list-new-works__title heading--18">${info.text()}</div><span class="list-new-works__title__created-year label--8">${info.attr('data-created-year')}</span></div>
  <div class="list-new-works__artist bodytext-13">${$('.header-fullscreen').attr('data-artist-name')}</div>
  <div class="list-new-works__credit bodytext-11">${info.attr('data-credit')}</div>
  <div class="list-new-works__desc bodytext-13">${info.attr('data-desc')}</div>
</div>`)
}

function removeVideoInfo(target) {
  target.find('.list-new-works__content_hover').remove();
}

function showMenuApprove(target) {
  let menu_dom = target.find('.menu-checking');
    if (menu_dom.is('.hide')) {
      $('.menu-checking').addClass('hide');
      menu_dom.removeClass('hide');
    } else {
      menu_dom.addClass('hide');
    }
}

function checkChangesProfile(text, textEN, title, titleEN, toggle) {
  let newblockNameJP = $('.profile__profile-quote').attr('data-title') || '';
  let newBlockNameEN = $('.profile__profile-quote').attr('data-title-en') || '';
  let newTextJP = $('.profile__profile-quote').attr('data-title-text') || '';
  let newTextEN = $('.profile__profile-quote').attr('data-title-text-en') || '';
  let newToggle = $('.profile__profile-quote').attr('data-toggle-header') || 'false';

  if(text !== newTextJP || textEN !== newTextEN || title !== newblockNameJP || titleEN !== newBlockNameEN || toggle.toLowerCase() != newToggle.toLowerCase()) {
    formData.profile_text.is_link_menu = newToggle.toLowerCase();
    formData.profile_text.content_jp = newTextJP;
    formData.profile_text.content_en = newTextEN;
    formData.profile_text.section_name_jp = newblockNameJP;
    formData.profile_text.section_name_en = newBlockNameEN;
    pushUnique(formData.profile_text.list_edited, 'section_name_jp');
    pushUnique(formData.profile_text.list_edited, 'section_name_en');
    pushUnique(formData.profile_text.list_edited, 'content_jp');
    pushUnique(formData.profile_text.list_edited, 'content_en');
    pushUnique(formData.profile_text.list_edited, 'is_link_menu');
    return true;
  } else {
    $('.profile__profile-quote').attr('data-edited', false);
    return false;
  }
}

var currWindowWidth = $(window).width();

function reSizeWindow() {
  $(window).on('resize', function() {
    if(currWindowWidth != $(window).width()) {
      currWindowWidth = $(window).width();
      resizeFullscreen();
      resizePhrase('25vw');
      resizeQuote()
      checkNewlineFooterMenu();
      autoScroll()
    }
  })
}

function initProfileText() {
  let quote = $('.profile__profile .profile__profile-quote')
  if(quote.attr('data-title') == '' && quote.attr('data-title-en') == '' && quote.attr('data-title-text') == '' && quote.attr('data-title-text-en') == '') {
    $('.profile__profile .profile__profile-quote').addClass('no-content')
  }
}

function playPauseOnHover(type='pause', target) {
  let id = $('.audio-navi').attr('data-id')
  let audio_id = target.parents('.sample-audio-item').attr('data-sale-id')
  if(id != audio_id) {
    if(type == 'play') {
      $('.playing-navi').each(function(i, e) {
        let a = $(e).find('audio');
        $(e).removeClass('playing-navi')
      })
      const thumbnail = target.parent()
      const audio = thumbnail.find('audio');
      $('video, audio').each(function(i, e) {
        e.pause()
      })

      audio.on('canplay', function () {
        audio.off('canplay')
        thumbnail.removeClass('loading')
        thumbnail.addClass('playing');
      })

      audio[0].play()
      audio[0].muted = false
      audio[0].loop = true

      if (audio[0].readyState > 1) {
        thumbnail.removeClass('loading')
        thumbnail.addClass('playing')
      } else {
        thumbnail.removeClass('playing')
        thumbnail.addClass('loading')
      }
    } else {
      const audio = target.parent().find('audio');
      audio.off('canplay');
      audio.each(function (i, e) {
        e.pause()
      })
      audio.parents('.sample-audio-thumbnail').removeClass('playing loading')
      audio.parents('.playing-navi').removeClass('playing-navi')
    }
  } else {
    if(type == 'play' && !wavesurfer.isPlaying()) {
      playPauseInNavigation('', 'play');
    }
  }
}

function checkAndroid() {
  if($('.header-fullscreen').is('.mobile') && /Android/.test(window.navigator.userAgent)) {
    $('.header-fullscreen').addClass('android')
  } else {
    $('.header-fullscreen').removeClass('android')
  }
}

function submitBlock(block, button) {
  if (button.not('.disable, .hide, .processing')) {
    button.addClass('processing');
    let upload_button_wrapper = $(".upload-button-wrapper");
    prepareFormData(block)
    let has_file = false;
     let segment_active = parseInt($('.segment-item.active').attr('data-option'))

    if (segment_active === 1 || block === 'header') {
      for (const value of form.values()) {
        if (value instanceof File) {
          has_file = true;
          break
        }
      }
    }else {
      for (let item of formData.album.sale_contents){
        item['default_thumbnail'] = "#FCFCFC"
        item['x'] = ""
        item['y'] = ""
        item['width'] = ""
        item['height'] = ""
        item['default_thumbnail'] = "#FCFCFC"
        delete ['show_thumbnail'];
      }
      for (let [name, value] of form.entries()) {
        if (value instanceof File) {
          form.delete(name);
        }
      }
    }
    form.append('json', JSON.stringify(formData));
    if(has_file) {
      $.ajax({
        type: "POST",
        url: "/accounts/update_creator_profile",
        data: form,
        contentType: false,
        processData: false,
        beforeSend: function() {
          toastr.info('情報を保存しています。');
          upload_button_wrapper.css('display', 'flex');
          upload_button_wrapper.addClass('clicked');
          upload_button_wrapper.find('.fill .process').css('width', '2%');
        },
        xhr: function () {
          var xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener("progress", function (evt) {
            if (evt.lengthComputable) {
              let percentComplete = (evt.loaded / evt.total) * 70;
              upload_button_wrapper.find('.fill .process').css('width', percentComplete + '%');
            }
          }, false);
          return xhr;
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
          updateProfileSuccess(response)
        },
        complete: function () {
          updateProfileComplete(button, upload_button_wrapper)
        }
      });
    } else {
      $.ajax({
        type: "POST",
        url: "/accounts/update_creator_profile",
        data: form,
        contentType: false,
        processData: false,
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
          updateProfileSuccess(response)
        },
        complete: function () {
          updateProfileComplete(button, upload_button_wrapper)
        }
      });
    }
  }
}

function prepareFormData(block) {
  formData = {
    "profile_id": "",
    "modified": "",
    "banner": "",
    "official_site": "",
    "twitter_link": "",
    "facebook_link": "",
    "instagram_link": "",
    "youtube_link": "",
    "stage_name": "",
    "type": "",
    "profile_quote": "",
    "x_banner": "",
    "y_banner": "",
    "width_banner": "",
    "height_banner": "",
    "avatar": "",
    "x": "",
    "y": "",
    "width": "",
    "height": "",
    "list_approve": [],
    "list_unchanged": [],
    "list_edited": [],
    "list_deleted": [],

    "footer": {
      "copyright": "",
      "item_menu": [],
      "social_link": [],
      "list_delete_item_menu": [],
      "list_delete_item_social": [],
      "list_delete_item_sub": []
    },
    "header": {
      "catchphrase_jp_1": "",
      "catchphrase_jp_2": "",
      "catchphrase_jp_3": "",
      "catchphrase_en_1": "",
      "catchphrase_en_2": "",
      "catchphrase_en_3": "",
      "display_tag_line_1": "",
      "artist_name_jp": "",
      "artist_name_en": "",
      "display_tag_line_2": "",
      "title_jp": "",
      "title_en": "",
      "type_header": "",
      "list_edited": [],
      "list_deleted_image": []
    },
    "statement": {
      "theme_jp": "",
      "theme_en": "",
      "is_show_avatar": "",
      "is_show_name": "",
      "is_show_title": "",
      "list_edited": []
    },
    "album": {
      "list_edited": [],
      "list_deleted": [],
      "sale_contents": [],
    },
    "profile_text": {
      "list_edited": [],
      "is_link_menu": "",
      "content_jp": "",
      "content_en": "",
      "section_name_jp": "",
      "section_name_en": ""
    }
  }
  form = new FormData();
  let profile_creator_dom = $('.profile');
  if (profile_creator_dom.length && profile_creator_dom.attr('data-profile-id')) {
    formData.profile_id = profile_creator_dom.attr('data-profile-id');
  }

  if (profile_creator_dom.length && profile_creator_dom.attr('data-modified')) {
    formData.modified = profile_creator_dom.attr('data-modified');
  }

  //header
  if(block === 'header') {
    pushUnique(formData.list_edited, 'header')
    let banner_dom = $('.header-fullscreen-container');
    if (banner_dom.attr('data-edited') === "true") {
      let is_change = false;
      formData.header.catchphrase_jp_1 = $('.profile-artist-catch-phrase').attr('data-catchphrase-1')
      formData.header.catchphrase_jp_2 = $('.profile-artist-catch-phrase').attr('data-catchphrase-2')
      formData.header.catchphrase_jp_3 = $('.profile-artist-catch-phrase').attr('data-catchphrase-3')
      formData.header.catchphrase_en_1 = $('.profile-artist-catch-phrase').attr('data-catchphrase-1-en')
      formData.header.catchphrase_en_2 = $('.profile-artist-catch-phrase').attr('data-catchphrase-2-en')
      formData.header.catchphrase_en_3 = $('.profile-artist-catch-phrase').attr('data-catchphrase-3-en')
      formData.header.display_tag_line_1 = $('.profile-artist-info-name').attr('data-tagline-1-toggle').toLocaleLowerCase()
      formData.header.artist_name_jp = $('.profile-artist-info-name').attr('data-tagline-1')
      formData.header.artist_name_en = $('.profile-artist-info-name').attr('data-tagline-1-en')
      formData.header.display_tag_line_2 = $('.profile-artist-info-title').attr('data-tagline-2-toggle').toLocaleLowerCase()
      formData.header.title_jp = $('.profile-artist-info-title').attr('data-tagline-2')
      formData.header.title_en = $('.profile-artist-info-title').attr('data-tagline-2-en')
      formData.header.type_header = $('.header-fullscreen-container').attr('data-style')

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-1') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-1')) {
        pushUnique(formData.header.list_edited, 'catchphrase_jp_1');
        is_change = true;
      }

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-2') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-2')) {
        pushUnique(formData.header.list_edited, 'catchphrase_jp_2');
        is_change = true;
      }

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-3') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-3')) {
        pushUnique(formData.header.list_edited, 'catchphrase_jp_3');
        is_change = true;
      }

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-1-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-1-en')) {
        pushUnique(formData.header.list_edited, 'catchphrase_en_1');
        is_change = true;
      }

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-2-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-2-en')) {
        pushUnique(formData.header.list_edited, 'catchphrase_en_2');
        is_change = true;
      }

      if($('.profile-artist-catch-phrase').attr('data-catchphrase-3-en') != $('.profile-artist-catch-phrase').attr('data-content-catchphrase-3-en')) {
        pushUnique(formData.header.list_edited, 'catchphrase_en_3');
        is_change = true;
      }

      if($('.profile-artist-info-name').attr('data-tagline-1-toggle').toLocaleLowerCase() != $('.profile-artist-info-name').attr('data-content-tagline-1-toggle').toLocaleLowerCase()) {
        pushUnique(formData.header.list_edited, 'display_tag_line_1');
        is_change = true;
      }

      if($('.profile-artist-info-name').attr('data-tagline-1') != $('.profile-artist-info-name').attr('data-content-tagline-1')) {
        pushUnique(formData.header.list_edited, 'artist_name_jp');
        is_change = true;
      }

      if($('.profile-artist-info-name').attr('data-tagline-1-en') != $('.profile-artist-info-name').attr('data-content-tagline-1-en')) {
        pushUnique(formData.header.list_edited, 'artist_name_en');
        is_change = true;
      }

      if($('.profile-artist-info-title').attr('data-tagline-2-toggle').toLocaleLowerCase() != $('.profile-artist-info-title').attr('data-content-tagline-2-toggle').toLocaleLowerCase()) {
        pushUnique(formData.header.list_edited, 'display_tag_line_2');
        is_change = true;
      }

      if($('.profile-artist-info-title').attr('data-tagline-2') != $('.profile-artist-info-title').attr('data-content-tagline-2')) {
        pushUnique(formData.header.list_edited, 'title_jp');
        is_change = true;
      }

      if($('.profile-artist-info-title').attr('data-tagline-2-en') != $('.profile-artist-info-title').attr('data-content-tagline-2-en')) {
        pushUnique(formData.header.list_edited, 'title_en');
        is_change = true;
      }

      if($('.header-fullscreen-container').attr('data-style') != $('.header-fullscreen-container').attr('data-content-style')) {
        pushUnique(formData.header.list_edited, 'type_header');
        is_change = true;
      }

      if(currentSelectLogo.length) {
        form.append('logo', currentSelectLogo[0]);
        is_change = true;
      }

      if(currentSelectKVPC.length) {
        form.append('key_visual_pc', currentSelectKVPC[0]);
        is_change = true;
      }

      if(currentSelectKVSP.length) {
        form.append('key_visual_sp', currentSelectKVSP[0]);
        is_change = true;
      }

      if(currentSelectBanner.length) {
        form.append('banner', currentSelectBanner[0]);
        is_change = true;
      }

      let deleting_logo = $(document).find('#profile-header-fullscreen-logo-dz').attr('deleting_logo')
      let deleting_kvpc = $(document).find('#profile-header-fullscreen-kvpc-dz').attr('deleting_kvpc')
      let deleting_kvsp = $(document).find('#profile-header-fullscreen-kvsp-dz').attr('deleting_kvsp')
      let deleting_banner = $(document).find('#profile-header-fullscreen-banner-dz').attr('deleting_banner')
      if(deleting_logo) {
        is_change = true;
        if(!formData.header.list_deleted_image.includes('logo')){
          formData.header.list_deleted_image.push('logo')
        }
        $(document).find('#profile-header-fullscreen-logo-dz').attr('deleting_logo', '');
        $('.logo-container').addClass('hide');
        $(document).find('#profile-header-fullscreen-logo-dz').closest('.mattach-form').find('.mcommment-file__name-form').text('');
        $('.header-profile-pc-logo').attr('data-logo-name', '')
      }
      if(deleting_banner) {
        is_change = true;
        if(!formData.header.list_deleted_image.includes('banner')){
          formData.header.list_deleted_image.push('banner')
        }
        $(document).find('#profile-header-fullscreen-banner-dz').attr('deleting_banner', '');
        $('.header-fullscreen-container .profile-artist-catch-phrase').attr('data-banner', '')
        $(document).find('#profile-header-fullscreen-banner-dz').closest('.mattach-form').find('.mcommment-file__name-form').text('');
        document.documentElement.style.setProperty("--banner", '');
        resizeFullscreen();
      }
      if(deleting_kvpc) {
        is_change = true;
        if(!formData.header.list_deleted_image.includes('key_visual_pc')){
          formData.header.list_deleted_image.push('key_visual_pc')
        }
        $(document).find('#profile-header-fullscreen-kvpc-dz').attr('deleting_kvpc', '');
        $(document).find('#profile-header-fullscreen-kvpc-dz').closest('.mattach-form').find('.mcommment-file__name-form').text('');
        $('.header-fullscreen-container').attr('data-key-visual-pc', '');
        $('.header-fullscreen-container').attr('data-key-visual-pc-name', '');
        document.documentElement.style.setProperty("--key-visual-pc", '');
        $('#bgVideoPC source').attr('src', '');
        $('#bgVideoPC').removeClass('shown');
        resizeFullscreen();
      }
      if(deleting_kvsp) {
        is_change = true;
        if(!formData.header.list_deleted_image.includes('key_visual_sp')){
          formData.header.list_deleted_image.push('key_visual_sp')
        }
        $(document).find('#profile-header-fullscreen-kvsp-dz').attr('deleting_kvsp', '')
        $(document).find('#profile-header-fullscreen-kvsp-dz').closest('.mattach-form').find('.mcommment-file__name-form').text('');
        $('.header-fullscreen-container').attr('data-key-visual-sp', '');
        $('.header-fullscreen-container').attr('data-key-visual-sp-name', '');
        document.documentElement.style.setProperty("--key-visual-sp", '');
        $('#bgVideoSP source').attr('src', '');
        $('#bgVideoSP').removeClass('shown');
        resizeFullscreen();
      }

      if(!is_change) {
        banner_dom.attr('data-edited', '')
      }
    }
  } else if (block === 'statement') {
    pushUnique(formData.list_edited, 'statement')
    let theme_dom = $('.statement');
    let is_change = false;
    formData.statement.theme_jp = $('.statement .statement-quote').attr('data-quote')
    if($('.statement .statement-quote').attr('data-quote') != $('.statement .statement-quote').attr('data-content-quote')) {
      formData.statement.list_edited.push('theme_jp');
      is_change = true;
    }

    formData.statement.theme_en = $('.statement .statement-quote').attr('data-quote-en')
    if($('.statement .statement-quote').attr('data-quote-en') != $('.statement .statement-quote').attr('data-content-quote-en')) {
      formData.statement.list_edited.push('theme_en');
      is_change = true;
    }

    formData.statement.is_show_avatar =  $('.statement .statement-info').attr('data-toggle-avatar');
    if($('.statement .statement-info').attr('data-toggle-avatar').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-avatar').toLocaleLowerCase()) {
      formData.statement.list_edited.push('is_show_avatar');
      is_change = true;
    }

    formData.statement.is_show_name =  $('.statement .statement-info').attr('data-toggle-name');
    if($('.statement .statement-info').attr('data-toggle-name').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-name').toLocaleLowerCase()) {
      formData.statement.list_edited.push('is_show_name');
      is_change = true;
    }

    formData.statement.is_show_title =  $('.statement .statement-info').attr('data-toggle-title');
    if($('.statement .statement-info').attr('data-toggle-title').toLocaleLowerCase() != $('.statement .statement-info').attr('data-content-toggle-title').toLocaleLowerCase()) {
      formData.statement.list_edited.push('is_show_title');
      is_change = true;
    }

    if(!is_change) {
      theme_dom.attr('data-edited', '')
    }
  } else if(block === 'profile_text') {
    pushUnique(formData.list_edited, 'profile_text')
    let currentProfileTitle = $('.profile__profile-quote').attr('data-content-title') || '';
    let currentProfileTitleEN = $('.profile__profile-quote').attr('data-content-title-en') || '';
    let currentProfile = $('.profile__profile-quote').attr('data-content-title-text') || '';
    let currentProfileEN = $('.profile__profile-quote').attr('data-content-title-text-en') || '';
    let currentProfileToggle = $('.profile__profile-quote').attr('data-content-toggle-header') || 'false';

    checkChangesProfile(currentProfile, currentProfileEN, currentProfileTitle, currentProfileTitleEN, currentProfileToggle)
  } else if (block === 'footer') {
    pushUnique(formData.list_edited, 'footer')
    //footer
    if(listMenuMainItemDeleted.length || listMenuSocialItemDeleted.length || listMenuSubItemDeleted.length) {
      formData.footer.list_delete_item_menu = listMenuMainItemDeleted;
      formData.footer.list_delete_item_social = listMenuSocialItemDeleted;
      formData.footer.list_delete_item_sub = listMenuSubItemDeleted;
      pushUnique(formData.list_edited, 'footer');
    }
    //check main
    $('.profile-footer-upper .profile-footer-menu-item').each(function(i, e) {
      if($(e).attr('data-title') != $(e).attr('data-content-title') || $(e).attr('data-title-en') != $(e).attr('data-content-title-en') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
        pushUnique(formData.list_edited, 'footer');
        let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
        let object = {
          "type_footer": "1",
          "type": type,
          "order": $(e).attr('data-order'),
          "url": $(e).attr('data-url'),
          "title_jp": $(e).attr('data-title'),
          "title_en": $(e).attr('data-title-en'),
          "id": $(e).attr('data-id'),
        }
        formData.footer.item_menu.push(object)
      }
    })

    //check social
    $('.profile-footer-upper-right .profile-footer-menu-icon').each(function(i, e) {
      if($(e).attr('data-social-icon') != $(e).attr('data-content-social-icon') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
        pushUnique(formData.list_edited, 'footer');
        let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
        let object = {
          "type": type,
          "order": $(e).attr('data-order'),
          "type_social_link": $(e).attr('data-social-icon'), //'home', 'twitter', 'fb', 'insta', 'youtube', 'tiktok', 'note'
          "url": $(e).attr('data-url'),
          "id": $(e).attr('data-id'),
        }
        formData.footer.social_link.push(object)
      }
    })

    //check main
    $('.profile-footer-lower-menu .profile-footer-menu-item').each(function(i, e) {
      if($(e).attr('data-title') != $(e).attr('data-content-title') || $(e).attr('data-title-en') != $(e).attr('data-content-title-en') || $(e).attr('data-url') != $(e).attr('data-content-url') || $(e).attr('data-order') != $(e).attr('data-content-order') || $(e).attr('data-id').includes('new_')) {
        pushUnique(formData.list_edited, 'footer');
        let type = $(e).attr('data-id').includes('new_') ? 'new' : 'edited'
        let object = {
          "type_footer": "2",
          "type": type,
          "order": $(e).attr('data-order'),
          "url": $(e).attr('data-url'),
          "title_jp": $(e).attr('data-title'),
          "title_en": $(e).attr('data-title-en'),
          "id": $(e).attr('data-id'),
        }
        formData.footer.item_menu.push(object)
      }
    })

    //copyright
    let cp_right = $('.profile-footer-copyright')
    if(cp_right.attr('data-copyright') != cp_right.attr('data-content-copyright')) {
      pushUnique(formData.list_edited, 'footer');
    }
    formData.footer.copyright = cp_right.attr('data-copyright')
  } else {
    pushUnique(formData.list_edited, 'album')
    let sale_content_container = $('.sample-audio-container');

    let deleted_sale_content = sale_content_container.find('.sample-audio-item.rel:not(.new-sale-content)[data-deleted="true"]');
    if (deleted_sale_content.length) {
      deleted_sale_content.each(function (i, e) {
        let type = 'deleted';
        pushUnique(formData.album.list_deleted, 'sale_content');
        let sale_content_id = $(e).attr('data-sale-content-id');
        formData.album.sale_contents.push({'id': sale_content_id, 'type': type})
      });
    }

    let edited_sale_content = sale_content_container.find('.sample-audio-item.rel:not(.new-sale-content)[data-edited="true"]');
    if (edited_sale_content.length) {
      edited_sale_content.each(function (i, e) {
        let type;
        if ($(e).attr('data-edited') === 'true') {
          if (checkNewSaleContent($(e)) || edited_sale_content.find('input.new-audio-file')[0].files.length > 0) {
            type = 'edited';
            pushUnique(formData.album.list_edited, 'sale_content');
          }
        }
        let sale_content_id = $(e).attr('data-sale-content-id');
        formData.album.sale_contents.push(saleContentObject(sale_content_id, e, type))
      });
    }

    let new_sale_contents = sale_content_container.find('.sample-audio-item.rel.new-sale-content');
    if(new_sale_contents.length) {
      new_sale_contents.each(function (i, e) {
        let sale_content_id = 'new_' + i;
        formData.album.sale_contents.push(saleContentObject(sale_content_id, e, 'new'));
        pushUnique(formData.album.list_edited, 'sale_content');
      })
    }
  }
}

function updateProfileSuccess(response) {
  if (response.block === 'album') {
    $('.sample-audio-item.rel.new-sale-content').attr('data-sale', response.album_new_id)
    $('.sample-audio-item.rel.new-sale-content').attr('data-sale-content-id', response.album_new_id)
    $('.sample-audio-item.rel.new-sale-content').attr('data-sale-id', response.album_new_id)
    $('.sample-audio-item.rel.new-sale-content').attr('data-new', '')
    $('.sample-audio-item.rel.new-sale-content').after(response.html)
    $('.sample-audio-item.rel.new-sale-content').remove()
    renderYoutubeView()
  } else if (response.block === 'sale_content') {
    $('.sample-audio-item.rel.new-sale-content').after(response.html)
    $('.sample-audio-item.rel.new-sale-content').remove()
    $('.sample-audio-item.rel:not(.new-sale-content)[data-deleted="true"]').remove();
    let current_element = $('.sample-audio-item.rel:not(.new-sale-content)[data-edited="true"]')
    if(current_element.length) {
      current_element.first().after(response.html)
      current_element.remove()
    }
    renderYoutubeView()
  }

  // Handle different block types from response
  if (response.header_update) {
    $('.header-fullscreen-container').remove();
    $('.profile').prepend(response.header_update);
    setBackground();
    resizeFullscreen();
    resizePhrase('25vw');
    animateCatchPhrase();
    changeContent();
    checkNewlineFooterMenu();
  }
  
  if (response.statement_update) {
    $('.statement.container').remove();
    $(response.statement_update).insertAfter($('.header-fullscreen-container'));
    initQuote();
    checkNoContent();
    changeContent();
    checkNewlineFooterMenu();
  }
  
  if (response.profile_text_update) {
    $('.profile__profile-quote').closest('.profile__profile').remove();
    $(response.profile_text_update).insertBefore($('main'));
    changeContent();
    checkNewlineFooterMenu();
  }
  
  if (response.footer_update) {
    $('.profile-footer').remove();
    $(response.footer_update).insertAfter($('main'));
    changeContent();
    checkNewlineFooterMenu();
    if(checkCurrentLan()) {
      $('.change-language-button').attr('data-language', 'en');
      $('footer .change-language-button').find('span').text('JP');
    }
  }

  if (response.edit_type === 'delete') {
    toastr.success('アルバムが削除しました。他の保存するものがありません。');
  } else if (response.edit_type === 'nothing') {
    toastr.success('保存するものがありません。');
  } else if (response.action === 'delete') {
    toastr.success('保存しました');
  }
}

function updateProfileComplete(button, upload_button_wrapper) {
  button.removeClass('processing');
  upload_button_wrapper.find('.fill .process').css('width', '100%');
  setTimeout(function () {
    upload_button_wrapper.removeClass('clicked').addClass('success')
  }, 1000);
  setTimeout(function () {
    upload_button_wrapper.removeClass('success').css('display', 'none');
    upload_button_wrapper.find('.fill .process').css('width', '0');
  }, 2000);
}

function getYouTubeVideoId(url) {
  if (!url) {
    console.error('URL is undefined or null');
    return null;
  }

  // Regular expression to match YouTube video ID
  let regex = /[?&]v=([^?&]+)/;
  let match = url.match(regex);

  // If there is a match, return the video ID
  if (match) {
    return match[1];
  } else {
    // Handle the case when there is no match (invalid URL)
    console.error('Invalid YouTube URL');
    return null;
  }
}

function onYouTubeIframeAPIReady(idElement, idVideo) {
  if (idElement) {
    player = new YT.Player(idElement, {
      height: '144',
      width: '256',
      videoId: idVideo,
      playerVars: {
        'autoplay': 0,
        'controls': 0,
        'showinfo': 0,
        'rel': 0,
      },
      'onReady': function (event) {
        onPlayerReady(event, idElement);
      }
    });
  }

}

function onPlayerReady(event, idElement) {
  let thumbnail = document.getElementById(idElement);
  let playButton = document.createElement('div');
  playButton.id = 'play-button';
  thumbnail.appendChild(playButton);

  thumbnail.addEventListener('mouseenter', function () {
    event.target.playVideo();
  });

  thumbnail.addEventListener('mouseleave', function () {
    event.target.pauseVideo();
  });
}

function renderYoutubeView() {
  if ($('.sale-youtube-video').length > 0){
    for (let item of $('.sale-youtube-video')) {
    let idElement = $(item).attr('data-id-element');
    let videoURL = $(item).attr('data-video-url');
    let idVideo = getYouTubeVideoId(videoURL)
    let player;
    let tag = document.createElement('script');
    // tag.src = 'https://www.youtube.com/iframe_api';
    let firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    onYouTubeIframeAPIReady(idElement, idVideo)
  }
  }
}

function validateYoutubeLink(saleLinkYoutube) {
  checkVideoAvailability($(saleLinkYoutube).val(), saleLinkYoutube);
}



