function initStatementBlock() {
    initQuote();
    initEditQuote();
    initSubmitStatement();
    initCloseStatement();
    checkNoContent()
}

function initQuote() {
    resizeQuote()
}

function breakLine(targetQuote) {
    let is_jp = $('.header-fullscreen').is('.lang-en');
    let quote = $('.statement-quote').attr('data-quote');
    let quote_en = $('.statement-quote').attr('data-quote-en');
    let quote_final = is_jp ? quote_en : quote
    if(quote_final == '') {
        quote_final = is_jp ? quote : quote_en
    }
    let breakpoint = 100;
    let regex = /.{1,100}/g;
    if($(window).width() < 768) {
        breakpoint = 40;
        regex = /.{1,40}/g;
    }
    let lines = quote_final.split('\n')
    targetQuote.empty();
    lines.forEach(function(e) {
        if(e.length > breakpoint) {
            let arr = e.match(regex);
            arr.forEach(function(el) {
                targetQuote.append(`<p></p>`)
                targetQuote.find('p').last().text(el)
            })
        } else {
            targetQuote.append(`<p></p>`)
            targetQuote.find('p').last().text(e)
        }
    })
}

function resizeQuote() {
    breakLine($('.statement-container .statement-quote'))
    let screen_width = $(window).width()
    $('.statement-container .statement-quote p').css('font-size', 'clamp(0.9rem, 1rem + 1.2vw, 2.25rem)');
    var el = $('.statement-container .statement-quote p')[0]
    var style = window.getComputedStyle(el, null).getPropertyValue('font-size');
    var fontSize = parseFloat(style);
    let width = $('.statement-container .statement-quote').width()
    let max_width = 0;
    $('.statement-container .statement-quote p').each(function(i, e) {
        max_width = e.scrollWidth > max_width ? e.scrollWidth : max_width;
    })
    let adjusted;
    if(max_width > width) {
        adjusted = fontSize*(width/max_width) - 2;
        adjusted = adjusted < 26 ? adjusted : 26;
        $('.statement-container .statement-quote p').css('font-size', adjusted + 'px')
    } else {
        fontSize = fontSize < 26 ? fontSize : 26;
        $('.statement-container .statement-quote p').css('font-size', fontSize + 'px')
    }

    if(screen_width > 767) {
        if($('.header-fullscreen-container').is('.style2')) {
            $('.statement-container').css('min-height', screen_width/2.35)
        } else {
            $('.statement-container').css('min-height', screen_width*9/16)
        }
    } else {
        if($('.header-fullscreen-container').is('.style2')) {
            $('.statement-container').css('min-height', screen_width/2.35*2.414)
        } else {
            $('.statement-container').css('min-height', screen_width*16/9)
        }
    }
}

function initEditQuote() {
    $(document).on('click', '.editable.statement, .new.statement', function (e) {
        if ($(e.target).is('.approve-btn') || $(e.target).is('.new-button')) {
            return;
        } else if ($(this).is('.new')) {
            $(this).attr({'data-check': true});
            $(this).removeAttr('data-approve data-rejected');
            $(this).find('.menu-checking').addClass('hide');
        }
        showModalStatement();
    });
}

function showModalStatement() {
    prepareModalStatement();
    $('#modal_profile_statement').modal('show')
}

function initSubmitStatement() {
    $(document).on('click', '.btn-submit-profile-statement', function () {
        //TODO handle multi-click
        updateDataStatement()
        submitBlock('statement', $(this))
    });
};

function initCloseStatement() {
    $(document).on('click', '.btn-close-profile-statement', function () {
        $('#modal_profile_statement').modal('hide')
    });
};

function prepareModalStatement() {
    $('#id_statement').val($('.statement .statement-quote').attr('data-quote'))
    $('#id_statement_en').val($('.statement .statement-quote').attr('data-quote-en'))
    $('#switch-avatar').prop("checked", $('.statement .statement-info').attr('data-toggle-avatar').toLowerCase() == 'true');
    $('#switch-artist-name').prop("checked", $('.statement .statement-info').attr('data-toggle-name').toLowerCase() == 'true');
    $('#switch-title').prop("checked", $('.statement .statement-info').attr('data-toggle-title').toLowerCase() == 'true');
};

function updateDataStatement() {
    let is_change = false;
    let statement_jp = $('#id_statement').val().trim();
    let current_statement_jp = $('.statement .statement-quote').attr('data-quote')

    if (statement_jp != current_statement_jp) {
        $('.statement .statement-quote').attr('data-quote', statement_jp)
        // $('.statement .statement-quote').attr('data-content-quote', current_statement_jp)
        is_change = true;
    }

    let statement_en = $('#id_statement_en').val().trim();
    let current_statement_en = $('.statement .statement-quote').attr('data-quote-en')
    if (statement_en != current_statement_en) {
        $('.statement .statement-quote').attr('data-quote-en', statement_en)
        // $('.statement .statement-quote').attr('data-content-quote-en', current_statement_en)
        is_change = true;
    }

    let toggle_ava = $('#switch-avatar').prop("checked");
    let current_toggle_ava = $('.statement .statement-info').attr('data-toggle-avatar').toLowerCase();
    if (toggle_ava != current_toggle_ava) {
        $('.statement .statement-info').attr('data-toggle-avatar', toggle_ava);
        // $('.statement .statement-info').attr('data-content-toggle-avatar', current_toggle_ava);
        is_change = true;
    }

    toggle_ava ? $('.statement-main').removeClass('no-avatar') : $('.statement-main').addClass('no-avatar');

    let toggle_name = $('#switch-artist-name').prop("checked");
    let current_toggle_name = $('.statement .statement-info').attr('data-toggle-name').toLowerCase();
    if (toggle_name != current_toggle_name) {
        $('.statement .statement-info').attr('data-toggle-name', toggle_name);
        // $('.statement .statement-info').attr('data-content-toggle-name', current_toggle_name);
        is_change = true;
    }

    toggle_name ? $('.statement-info-name').removeClass('hide') : $('.statement-info-name').addClass('hide')

    let toggle_title = $('#switch-title').prop("checked");
    let current_toggle_title = $('.statement .statement-info').attr('data-toggle-title').toLowerCase();
    if (toggle_title != current_toggle_title) {
        $('.statement .statement-info').attr('data-toggle-title', toggle_title);
        // $('.statement .statement-info').attr('data-content-toggle-title', current_toggle_title);
        is_change = true;
    }

    toggle_title ? $('.statement-info-title').removeClass('hide') : $('.statement-info-title').addClass('hide')

    if(is_change) {
        $('.statement').attr('data-edited', true);
        // breakLine($('.statement-container .statement-quote'))
        changeContent()
        checkNoContent()
        checkNewlineFooterMenu()
    }

    $('#modal_profile_statement').modal('hide')
};

function checkNoContent() {
    let quote = $('.statement .statement-quote').attr('data-quote')
    let quote_en = $('.statement .statement-quote').attr('data-quote-en')
    let toggle_ava = $('.statement .statement-info').attr('data-toggle-avatar').toLowerCase()
    let toggle_name = $('.statement .statement-info').attr('data-toggle-name').toLowerCase()
    let name = $('.statement .statement-info .statement-info-name').attr('data-artist-name')
    let name_en = $('.statement .statement-info .statement-info-name').attr('data-artist-name-en')
    let toggle_title = $('.statement .statement-info').attr('data-toggle-title').toLowerCase()
    let title = $('.statement .statement-info .statement-info-title').attr('data-title')
    let title_en = $('.statement .statement-info .statement-info-title').attr('data-title-en')
    let name_condition = (!name && !name_en) || toggle_name == 'false';
    let title_condition = (!title && !title_en) || toggle_title == 'false';
    if(!quote && !quote_en && toggle_ava == 'false' && name_condition && title_condition) {
        $('.statement').addClass('no-content')
    } else {
        $('.statement').removeClass('no-content')
    }

    if(name_condition && title_condition) {
        $('.statement .statement-info').addClass('off')
    } else {
        $('.statement .statement-info').removeClass('off')
    }
}
