$(document).ready(function () {
    initCustomScrollbar();
    let list_contacts = $('.messenger__item');

    //load message
    list_contacts.on('click', function() {
        let offer_ids = $(this).data('offers');
        $(".messenger__column-right").html("");
        $('.messenger__item--selected').removeClass('messenger__item--selected');
        $(this).addClass('messenger__item--selected');
        for (let i = 0; i < offer_ids.length; i ++) {
            loadMessage(offer_ids[i], 'admin')
        }
    });
});

function loadMessage(offer_id, user_role) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/messenger/load_offer",
        data: {
            'offer_id': offer_id,
            'role': user_role
        },
        success: function (data) {
            if (window.localStorage.getItem("performance")) {
            initSocket(offer_id);
            $(".messenger__column-right").append("<div class='offer-" +offer_id+"'><div class=\"messenger-detail\" ></div></div>");
            let active_offer = $('.offer-'+offer_id);
            active_offer.find(' .messenger-detail').html(data.html);
            initCustomScrollbar();

            active_offer.find('.messenger-form__confirm input[type="checkbox"]').off().on('click', function(){
              if ( $(this).is(':checked') ) {
                active_offer.find('.messenger-form__action .button').removeClass('disabled');
              } else {
                active_offer.find('.messenger-form__action .button').addClass('disabled');
              }
            });

            //send_button
            active_offer.find('.messenger-detail__input textarea').on('keyup', function () {
                $(this).height('inherit');
                var height =  parseInt(this.scrollHeight)
                    + parseInt($(this).css('border-top-width'))
                    - parseInt($(this).css('padding-top'))
                    + parseInt($(this).css('border-bottom-width'))
                    - parseInt($(this).css('padding-bottom'));
                if ($(this).val().length) {
                    if(height < 38) {
                        height += 20
                    }
                    $(this).parent().find('.messenger-detail__button-send .button--disabled').removeClass('button--disabled');
                    $(this).parents('.messenger-detail__input').height(height + 'px');
                    $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo','bottom');

                } else {
                    $(this).parent().find('.button.button--text.button--text-primary').addClass('button--disabled');
                    $(this).parents('.messenger-detail__input').height('38px');
                }
            });

            //create_message
            active_offer.find('.messenger-detail__button-send').on('click', function () {
                let messageContent = active_offer.find('.messenger-detail__input textarea').val();
                if (messageContent !== '') {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        data: {
                            'message': messageContent,
                            'offer_id': offer_id
                        },
                        url: '/offer_message/create',
                        success: function (data) {
                            active_offer.find('.messenger-detail__input textarea').val('');
                            active_offer.find('.messenger-detail__button-send a.button--text-primary').not('.button--disabled').addClass('button--disabled');
                        }
                    });
                    active_offer.insertBefore($(".messenger__column-right").children().eq(0));
                    $(window).scrollTop(0);
                }
                
            });

            // check seen message
            active_offer.find('.messenger-detail__input').on('click', function () {
                if ($(this).parents('.messenger-director__list').hasClass('not-seen')) {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        datatype: "json",
                        url: "/offer_message/update_seen",
                        data: {
                            'offer_id': offer_id,
                        },
                        success: function () {
                            console.log('update seen successful');
                        }
                    })
                }
            });

            //accept_offer
            active_offer.find('.messenger-director__item-action-accept').on('click', function() {
                if(!$(this).children().is('.disabled')) {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id
                        },
                        url: '/messenger/offer_accept',
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            window.location.href = response.url;
                            $(`<div class="align-center">
                                   <a class="messenger-accept" href="#">御取引が成立しました</a>
                               </div>`).insertAfter(active_offer.find('.messenger-director__item-action-accept'));
                            active_offer.find('.messenger-director__item-action-accept').remove();
                        }
                    });
                }
            });

            let message = active_offer.find('.messenger-director__item-mess');
            $.each(message, function(i,v) {
                if(!$(v).is('.align-center')) {
                    let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                    v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                }
            })
        }
    }})
}

function initCustomScrollbar() {
    $('.custom-scrollbar').mCustomScrollbar({
        theme: 'minimal-dark'
    });

    $('.custom-scrollbar-horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });

    $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom', {
        scrollEasing: 'easeOut'
    });
}
