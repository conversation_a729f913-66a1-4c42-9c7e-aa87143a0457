var list_file_id_form = {};
var list_files_folders_form = {};
var list_folder_id_form = {};
var $myDropZone;
var list_file_remove_form = [];
var list_temp_folder_id_form = {};
let list_file_name_form = [];
let list_folder_name_form = [];
let list_temp_folder_name_form = [];
let list_name = [];
var is_exceeded_length_form = false;

Dropzone.autoDiscover = false;

$(document).ready(function () {
    DropZoneUpload();
});


function DropZoneUpload() {
    if ($('#uploadFile').length) {
        Dropzone.autoDiscover = false;
        var previewNode = $('.mattach-template-form');
        var mattach = $('.mattachform');

        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();

        $myDropZone = new Dropzone('#uploadFile', {
            url: "/",
            autoDiscover: false,
            previewsContainer: '.mattach-previews-form',
            previewTemplate: previewTemplate,
            maxFilesize: 4500,
            timeout: 900000,
            clickable: '#uploadFile',
            autoProcessQueue: false,
            autoQueue: false,
            dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
        });


        $myDropZone.on('addedfile', function (file, e) {
            file_names = [];
            $myDropZone["sended"] = false;
            let file_dom = $(file.previewElement);
            let path = "";
            if (is_exceeded_length_form) {
                if (!$myDropZone.printed_err) {
                    toastr.error("Folder'name is too long.");
                }
                let page = getPage(file_dom);
                let folder_ids = Object.values(list_temp_folder_id_form);
                for (let folder_id of folder_ids) {
                    delete_folder(folder_id, page);
                }
                file.not_created = true;
                $myDropZone.removeFile(file);
                list_temp_folder_id_form = {};
                list_files_folders_form = {};
                list_temp_folder_name_form = [];
            } else {
                list_folder_id_form = {...list_folder_id_form, ...list_temp_folder_id_form};
                list_folder_name_form.concat(list_temp_folder_name_form);
                if (!jQuery.isEmptyObject(list_files_folders_form)) {
                    for (const key in list_files_folders_form) {
                        if (list_files_folders_form[key].includes(file.name)) {
                            path = key;
                            let index = list_files_folders_form[key].indexOf(file.name);
                            list_files_folders_form[key].splice(index, 1);
                            if (!list_files_folders_form[key].length) {
                                delete list_files_folders_form[key];
                            }
                            break;
                        }
                    }
                    if (jQuery.isEmptyObject(list_files_folders_form)) {
                        list_files_folders_form = {};
                        list_temp_folder_name_form = [];
                        list_temp_folder_id_form = {};
                    }
                }
                let file_preview = $('.mattach-preview-container-form').find(".mcommment-file__name-form");
                for (let i = 0; i < file_preview.length; i++) {
                    if ($(file_preview[i]).text() == file.name) {
                        let real_path = path.substring(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                        $(file_preview[i]).text('');
                        if (real_path === '') {
                            $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_path + file.name);
                        } else {
                            let folder_name = real_path.split('/')[0];
                            let count_name = 0;
                            list_name.map((x) => {
                                if (x === folder_name) {
                                    count_name++;
                                }
                            });
                            let count_view = 0;
                            $('.mcommment-file__name-form').each(function () {
                                if ($(this).text() === folder_name) {
                                    count_view++;
                                }
                            })
                            if (count_view < count_name) {
                                $(file_preview[i]).append('<i class="icon icon--sicon-storage"></i>' + folder_name);

                            } else {
                                $(file_preview[i]).parents('.mattach-template-form').remove();
                            }
                        }
                        break;
                    }
                }
                if (path === '') {
                    list_file_name_form.push(file.name)
                }
                uploadFileS3(file, file_dom, path);
            }
        });

        $myDropZone.on('drop', function (e) {
            is_exceeded_length_form = false;
            $myDropZone.printed_err = false;
            let items = e.dataTransfer.items;
            let today = new Date();
            let epoch = Math.floor(today / 1000);
            let dateTime = "---" + epoch;
            let page = getPage(mattach);

            for (let i = 0; i < items.length; i++) {
                var item = items[i].webkitGetAsEntry();
                if (item.isFile) {
                    //list_file_name.push(item.name);
                    traverseFileTree(item, "", 0, "", page);
                } else {
                    list_temp_folder_name_form.push(item.name);
                    list_name.push(item.name);
                    traverseFileTree(item, item.name + dateTime + "/", 0, "", page);
                }
            }
        });

        $myDropZone.on('removedfile', function (file) {
            $myDropZone.printed_err = true;
            if (file.not_created) {
                return
            }
            if (!file["not_created"] && !$myDropZone["sended"]) {
                file_id = Object.keys(list_file_id_form).find(key => list_file_id_form[key] === file.name);
                if (file.name.indexOf(list_file_name_form) >= 0) {
                    list_file_name_form.pop(file.name.indexOf(list_file_name_form));
                }
                if (file_id) {
                    delete list_file_id_form[file_id];
                    $.ajax({
                        type: "GET",
                        data: {
                            'file_id': file_id,
                            'message_type': getPage(mattach)
                        },
                        url: '/upload/remove_file',
                        success: function (data) {
                            let list_folder_removed = data.removed_folder_id.split(",");
                            for (let key in list_folder_id_form) {
                                if (list_folder_removed.includes(list_folder_id_form[key])) {
                                    delete list_folder_id_form[key]
                                }
                            }
                        },
                    });
                }
            }
        });

    }
}

function getPage() {
    let page = ''
    page = 'offer';
    return page
}

function getProgressUploaded(){
    let file_previews = $(".mattach-previews").find(".mattach-template");
    let total = 0;
    let uploaded = 0;
    file_previews.each(function(i, item){
        total += parseInt($(item).attr("data-total"));
        uploaded += parseInt($(item).attr("data-loaded"));
    });
    return Math.max(2, uploaded/total * 70);
}

function uploadFileS3(file, file_dom, path) {
    file_dom.find('.determinate').css('width', '0%');
    let page = getPage(file_dom);
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage" + path + "/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            let key_file = data.presigned_post.fields["key"];
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s')
                    file_dom.find('.determinate').css('width', percentComplete);
                    if (file_dom.length) {
                        if (!file_dom.attr("data-total")) {
                            file_dom.attr("data-total", evt.total);
                        }
                        file_dom.attr("data-loaded", evt.loaded);
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        let data = new FormData();
                        let folder_id = "";
                        if (path) {
                            folder_id = list_folder_id_form[path];
                            if (path.includes("/")) {
                                path = path.slice(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            } else {
                                path = path.slice(0, path.indexOf("---"));
                            }
                            path += "/";
                        }
                        data.append('csrf', csrf);
                        data.append('message_type', page);
                        data.append('key_file', key_file);
                        data.append('file_name', file.name);
                        data.append('folder_id', folder_id);
                        data.append('csrfmiddlewaretoken', window.CSRF_TOKEN);
                        $.ajax({
                            type: "POST",
                            data: data,
                            dataType: "json",
                            contentType: false,
                            processData: false,
                            url: '/upload/create_file',
                            beforeSend: function (xhr) {
                                // xhr.setRequestHeader('X-CSRFToken', ${csrf});
                            },
                            success: function (data) {
                                Object.assign(list_file_id_form, data);
                                file_dom.attr('data-file-id', Object.keys(data)[0]);
                                file_dom.find('.determinate').css('width', '100%');
                            },
                        });
                    } else {
                        alert("Could not upload file.");
                    }
                }
            };
            xhr.send(postData);
        }
    })
}

function traverseFileTree(item, name, flag, folder_id, page) {
    if (item.isFile && name) {
        if (list_files_folders_form[name]) {
            list_files_folders_form[name].push(item.name);
        } else {
            list_files_folders_form[name] = [item.name]
        }
    } else if (item.isDirectory) {
        let current_path = name;
        if (item.name.trim().length > 128) {
            is_exceeded_length_form = true;
        }
        if (flag) {
            current_path += item.name + '/';
        }
        var dirReader = item.createReader();
        let data = new FormData();
        data.append('csrfmiddlewaretoken', window.CSRF_TOKEN);
        data.append('parent_id', folder_id);
        data.append('name', item.name);
        data.append('full_path', 'storage/' + current_path);
        data.append('message_type', page);
        $.ajax({
            type: "POST",
            data: data,
            dataType: "json",
            async: false,
            contentType: false,
            processData: false,
            url: '/upload/create_folder',
            success: function (data) {
                console.log('folder created');
                let folder_pk = data.id;
                list_temp_folder_id_form[current_path] = folder_pk;
                dirReader.readEntries(function (entries) {
                    for (let i = 0; i < entries.length; i++) {
                        traverseFileTree(entries[i], current_path, 1, folder_pk, page);
                    }
                });
            },
        });
    }
}
