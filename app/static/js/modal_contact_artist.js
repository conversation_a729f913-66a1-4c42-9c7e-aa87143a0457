$(document).ready(function () {
    createLinkForContactArtist();
    initMessageArtist();
});

var artist_id;
function createLinkForContactArtist() {
    // user has action click in sale content
    if (is_logged_in !== 'True' || user_role === 'master_client') {
        contactArtistProfile();
        nextStepContactArtist();
        validateMessage();
    }

    function contactArtistProfile() {
        $(document).on('click', '.btn-open-modal-contact-artist', function (e) {
            e.preventDefault();
            let checkRedirectContact = Boolean(parseInt($(this).attr('check-redirect-contact')));
            if(checkRedirectContact) {
                window.location.href = '/landingPage/contact'
            } else {
                let modalContractArtist = $('#modal-contact-artist-profile');
                modalContractArtist.modal('show');
            }
        });
        
        $(document).on('hidden.bs.modal', '#modal-contact-artist', function () {
            let modalDom = $(this);
            modalDom.find('input, textarea').val('');
        });
    }

    function nextStepContactArtist() {
        $(document).on('click', '#modal-contact-artist-profile .btn-popup-send', function () {
            let messageDom = $('#modal-contact-artist-profile').find('#id_description');
            let messageValue = messageDom.val().trim().replaceAll('\n', '\\n');
            let profileDom = $('.profile');
            artist_id = profileDom.attr('data-user');

            if (!artist_id || messageValue === '') {
                return
            }


            if ($myDropZone.files.length > 0 && $myDropZone.files[0]) {
                let file_loaded = Object.values(list_file_id_form);
                let file_loading = $myDropZone.files.length - file_loaded.length;

                if (file_loading > 0) {
                    $('#modal-contact-artist-profile').modal('hide');
                    $('.upload-button-wrapper').css('display', 'flex');
                    $('.upload-button-wrapper').addClass('clicked');
                    $('.upload-button-wrapper .fill .process').css('width', '2%');
                    var waiting_file_loading = setInterval(function () {
                        let current_file_loaded = Object.values(list_file_id_form);
                        let current_file_loading = $myDropZone.files.length - current_file_loaded.length;
                        let progress = getProgressUploaded();
                        $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                        if (!current_file_loading) {
                            clearInterval(waiting_file_loading);
                            goToLink(messageValue, artist_id);
                        }
                    }, 100);
                } else {
                    goToLink(messageValue, artist_id);
                }
            } else {
                goToLink(messageValue, artist_id);
            }
        });


    }

    function goToLink(messageValue, artist_id) {
        $('#modal-contact-artist').modal('hide');
        let list_file = Object.keys(list_file_id_form);
        let list_folder = Object.values(list_folder_id_form);
        redirectScreenCreateProject('2', messageValue, artist_id, 0, '', '', list_file, list_folder)
    }

    function validateMessage() {
        let modalContact = $('#modal-contact-artist-profile');
        $(document).on('input', '#modal-contact-artist-profile #id_description', function () {
            let valueMessage = $(this).val().trim();
            if (valueMessage) {
                modalContact.find('.btn-popup-send').removeClass('disabled')
            } else {
                modalContact.find('.btn-popup-send').addClass('disabled')
            }
        })
    }

}

function initMessageArtist() {
    // add placeholder
    var initMessage = `お仕事をご相談したいです。
詳細は、資料をお送りしましたので、ご確認ください。
折り返しをお待ちしています。` ;
    let textareaMessage = $('textarea#id_description');
    textareaMessage.attr('placeholder', initMessage);
    textareaMessage.val(initMessage);
}

