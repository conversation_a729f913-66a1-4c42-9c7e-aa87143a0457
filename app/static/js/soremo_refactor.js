//=require malihu-custom-scrollbar-plugin/jquery.mCustomScrollbar.concat.min.js
//01EG89H6SS2141VNGDDEHBMV4Q
let maudio_arr = [];
let list_file_id = {};
let list_files_folders = {};
let list_folder_id = {};
let mzdrop = '';
let list_file_remove = [];
let list_temp_folder_id = {};
let is_exceeded_length = false;
let first_load_page = true;
let messenger_page = '';
var list_scenes = [];
let regexXss = /^<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/;
var id_time = new Date().getTime().toString();
let valInput = ''
let currentOffer = ''
let should_scroll = true;
let lastTimeScroll = 0;
let clickedOffer = false;
let lastCheckedRadio = null;
let isClicked = false;

const paddingHeightActivePreview = 10;
const paddingWidthActivePreview = 10;
const widthSP = 767;
const widthIpad = 992;
const max_width_sp_device = 695;
const draft = {
    'プロフェッショナリズム': [
        '迅速かつ的確な対応に感謝します。',
        '常に高品質な仕事を提供してくれます。',
        '丁寧でプロフェッショナルな対応に感謝します。',
        '迅速でプロフェッショナルな対応に感謝します。',
        '${reviewee}さんの美学を詰め込んだ仕事に、深い尊敬と信頼を寄せています。',
        '${reviewee}さんのプロ意識と献身性は、このプロジェクトの誇りです。'
    ],
    '安定性': [
        '${reviewee}さんの安定したパフォーマンスに支えられました。',
        '一貫した高品質な仕事ぶりに支えられました。',
        '着実で確実な仕事ぶりが、プロジェクトの成功を支えてくれました。',
    ],
    '創造性': [
        '常に新鮮で創造力豊かなアプローチが素晴らしいです。',
        '斬新な視点が火花となり、プロジェクトの可能性が広がりました',
        '独自の切り口が示され、アイデアの解像度がぐっと上がりました',
        '新しい風を吹き込んでくれました。プロジェクトに新しい息吹を与えてくれます。',
        '大胆な提案が刺激となり、思考のジャンプが軽やかに続きました。',
    ],
    'オリジナリティ': [
        '${reviewee}さんの独創的なクリエイションに、深い尊敬と信頼を寄せています。',
        '唯一無二のアプローチが映え、作品に鮮烈な個性が宿りました。',
        '独創的なアイデアが続き、創作への期待感が高まりました。',
        '個性的なタッチが加わり、プロジェクトに新風がそよぎました。',
        '他にない視点が示され、価値観の地平がふわりと開けました。',
        '独自性がくっきり浮かび、ブランドの輪郭が自然に強まりました。',
        '独創的なアイデアに驚かされました。常に新鮮でクリエイティブな刺激をいただきます。',
    ],
    '納期遵守': [
        '${reviewee}さんが厳しいスケジュールに応えてくださったことで、プロジェクトは救われました。',
        '迅速な納品に感謝します。',
    ],
    '技術': [
        '${reviewee}さんの技術がプロジェクトを成功に導いてくれました。',
        '${reviewee}さんの高度な技術力により、難しい課題も見事に解決されました。',
        '${reviewee}さんの技術的な専門性と深い知識に感銘を受けました。',
        '${reviewee}さんの最新技術への対応力と応用力が素晴らしかったです。',
        '${reviewee}さんの技術的なセンスと細部へのこだわりが、クオリティを格段に高めてくれました。',
        '${reviewee}さんの卓越した技術力により、複雑な要求も見事に実現していただきました。',
    ],
    '柔軟さ': [
        '柔軟なレスポンスのおかげで、より高いゴールに到達できました。',
        '状況変化にしなやかに寄り添い、進行のストレスが薄れました。',
        '柔軟な提案が重なり、アイデアの選択肢が豊かに膨らみました。',
        '要望への応答が素早く、意思決定が軽やかに弾みました。',
        '変更点を快く受け止め、制作の流れが滞りなく続きました。',
        '適応力の高さが光り、難所がするりと乗り越えられました。',
    ],
    'モチベーション': [
        '${reviewee}さんの情熱が静かに伝わり、メンバーのモチベーションが心地よく連鎖しました。',
        '${reviewee}さんの粘り強い探究心が功を奏し、難所の課題がするりとほどけました。',
        '${reviewee}さんのエネルギッシュな勢いに感化されました。',
        '${reviewee}さんの意欲的な姿勢が刺激となりました。',
    ],
    'コミュニケーション': [
        '${reviewee}さんの細やかな気配りで、制作の流れがすっと整いました',
        '${reviewee}さんの柔軟なレスポンスが心地よく、意見交換がのびやかに弾みました。',
        '${reviewee}さんの明快なコミュニケーションがプロジェクトを成功に導いてくれました。',
        '${reviewee}さんの明快なレスポンスが続き、意見交換がのびやかに弾みました。',
        '${reviewee}さんの情報共有のおかけで、綿密なクリエイションが生まれました。',
        '${reviewee}さんの質問への返答が的確で、迷いが消えました。',
        '${reviewee}さんの対話の温かさが伝わり、チームの安心感が穏やかに広がりました。',
        '${reviewee}さんの素早いレスポンスが効き、アイデアの発酵がぐっと加速しました。',
    ],
    'チームワーク': [
        '${reviewee}さんの協調が自然に芽生え、プロジェクトが軽やかに進みました。',
        '${reviewee}さんの役割が噛み合い、成果が期待以上に伸びました。',
        '${reviewee}さんの助け合いの姿勢が続き、目標までの道筋が明るく開けました。',
        '${reviewee}さんの連携が滑らかに重なり、完成度が一段と高まりました。',
        '${reviewee}さんのチーム全体の鼓動がそろい、作業リズムが心地よく整いました。',
    ],
    '暗黙知': [
        '${reviewee}さんの確かな審美眼が加わり、世界観が豊かに深まりました。',
        '${reviewee}さんの経験に基づく暗黙知が役立ちました。',
        '${reviewee}さんの経験に裏打ちされた暗黙知に助けられました。',
        '${reviewee}さんの豊富な知見がプロジェクトの道筋を照らしました。',
        '${reviewee}さんの勘所を押さえた対応が迷いを消しました。',
        '${reviewee}さんの意図をくみ取った対応がコミュニケーションの手間を減らしました。',
    ],
    '目的の理解': [
        'プロジェクトの目的を深く理解した仕事をしてくれます。',
        '目的をしっかり理解し、それに基づいた適切な対応に感謝します。',
        '目標を理解し、それに向けて最善を尽くしてくれます。',
        'プロジェクトの目的を的確に理解し、それに応じた対応に感謝します。',
    ],
    '提案力': [
        '的確で実現可能な提案が、プロジェクトに大いに役立ちました。',
        '効果的な提案がプロジェクトの成功に寄与しました。',
        '鋭い着眼点が示され、選択肢が豊かに広がりました。',
        '提案が深みを帯び、プロジェクトの可能性が膨らみました。',
    ],
    'オファー': [
        '${reviewee}さんの細やかな配慮が行き届き、作業に専念できました。',
        '${reviewee}さんの明快なゴール設定により、自由に創意工夫しやすい仕事でした。',
        '${reviewee}さんに信頼していただき、のびやかに作業に取り組めました。',
        '${reviewee}さんの明快なフィードバックにより、より高みに到達できました。',
        '${reviewee}さんのスケジュールの配慮により、パフォーマンスを発揮できました。',
        '新しいチャレンジが出来ました。',
        '無理のない内容で安心して仕事ができました。',
    ],
};

$(document).ready(function () {
    messenger_page = $('.p-martist, .project-item').attr('data-page');
    filterOffer();
    styleGuide();
    appHeight();
    //initmCustomScrollbar();
    modalInModal();
    //createThread();
    actionThread();
    actionMessage();
    searchOffer();
    previewFile();
    getDownloadAudio();
    getDownloadFileScene();
    projectFolder();
    actionOffers();
    removeOverLayModal();

    window.addEventListener('beforeunload onunload onbeforeunload', async function (e) {
        e.preventDefault();
        e.stopPropagation();
        if ($(document).find('textarea.mcomment-input-text').length) {
            await doneTyping($(document).find('textarea.mcomment-input-text').val())
        }
    })
});

function actionOffers() {
    $(document).on('mouseenter mouseleave', '.accordion-header', function (e) {
        $(this).find('.scene-title__action').toggleClass('active-action-offers', e.type === 'mouseenter')
    })
}

function styleGuide() {
    // Toggle Password
    $('body').off('click', '.sform-group__append i.toggle-password').on('click', '.sform-group__append i.toggle-password', function () {
        var input_group = $(this).closest('.sform-group__input-group');
        var input_icon = $(this);
        var input = input_group.find('input');

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            input_icon.removeClass('icon--sicon-eye-close').addClass('icon--sicon-eye-open');
        } else {
            input.attr('type', 'password');
            input_icon.addClass('icon--sicon-eye-close').removeClass('icon--sicon-eye-open');
        }
    });

    // Step number
    $(document.body).off('click', '.step .step-button').on('click', '.step .step-button', function () {
        var t = $(this),
            o = t.closest('.step').find('.step-number').val(),
            i = '' === o ? 0 : parseInt(o, 10);
        t.is('.step-button--plus') ? i++ : i > 0 && t.is('.step-button--minus') && i--,
            t
                .closest('.step').find('.step-number')
                .val(i)
                .trigger('change');
    });

    // Tooltip
    // $('.stooltip').tooltip({
    //     html: true
    // });

    // Input Search
    $('.sform-control[type="search"]').on('keyup', function () {
        var $this = $(this);

        if ($this.val()) {
            $this.closest('.sform-group__input-group').find('.search-delete').show();
        } else {
            $this.closest('.sform-group__input-group').find('.search-delete').hide();
        }
    });

    $('.search-delete').on('click', function () {
        $(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').val('');
        if ($(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').is('#pm-search')) {
            $('.tab--video-progress').removeClass('hide');
            $('.project-chapter-item-search').remove();
        } else {
            $('.skills-item').removeClass('selected');
            let allItem = $('.nav-item .notification.notification--blue');
            allItem.addClass('hide');
            allItem.attr('value', '0');
            allItem.html('0');
            $('.psearch-section.psearch-artist-wrap').remove();
        }
        $(this).hide();
    });

    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    let startDate = new Date();
    let endDate = new Date().addDays(5);
    // $('#deadline-date').daterangepicker({
    //     startDate: startDate,
    //     endDate: endDate,
    // });
    flatpickr("#deadline-date", {
        mode: "single",
        dateFormat: "Y/m/d",
        defaultDate: moment(endDate).format('YYYY/MM/DD'),
        showMonths: 1,
        onOpen: function (selectedDates, dateStr, instance) {
            $(instance.element)
                .next(".c-icon-date-range")
                .addClass("is-icon-active");
        },
        onClose: function (selectedDates, dateStr, instance) {
            $(instance.element)
                .next(".c-icon-date-range")
                .removeClass("is-icon-active");
        },
        onChange: function (selectedDates, dateStr, instance) {
            let startDate = selectedDates[0];
            let endDate = selectedDates[1];
            $(instance.element).attr(
                "data-start-time",
                moment(startDate).format("yyyy-MM-DD HH:mm:ss")
            );
            if (endDate)
                $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                );
        },
        // minDate: "today",
        // maxDate: new Date().fp_incr(120),
    });
    $('.select-deadline_time').val('10:00');

};

function initmCustomScrollbar() {
    $('.custom-scrollbar.custom-scrollbar--vertical').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'y'
    });

    $('.custom-scrollbar.custom-scrollbar--horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });

    setTimeout(function () {
        if ($('.custom-scrollbar--bottom').length) {
            $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom', {
                scrollEasing: 'easeOut'
            });
        }
    }, 500);
};

function sScrollbarBottom() {
    $('.mscrollbar--bottom').each(function () {
        let $this = $(this);

        setTimeout(function () {
            $this.scrollTop($this[0].scrollHeight);
        }, 500);
    });
};

function messengerAudio() {
    newWavesurferInit()
};

function updatePeakMessage(wavesurfer, file_id) {
    wavesurfer.on('waveform-ready', function () {
        let peaks = wavesurfer.backend.mergedPeaks;
        let peaks_string = peaks.join(" ");
        // for (let i = 0; i < peaks.length; i++) {
        //     peaks_string += String(Math.round(peaks[i] * Math.pow(10, 8)) / Math.pow(10, 8)) + " ";
        // }
        let values = {
            "file_id": file_id,
            "peaks": peaks_string,
        };
        if (file_id && peaks !== '') {
            $.ajax({
                type: "POST",
                url: "/messenger/update_peaks_file",
                data: values,
                dataType: 'json',
                success: function (data) {
                    console.log("success");
                    let message_dom;
                    if (data.file_id) {
                        message_dom = $('.mmessenger--audio-wave[data-file-id^=' + data.file_id + ']');
                        if (message_dom.length) {
                            message_dom.find('.s-audio-source').attr('data-peaks-loaded', data.peaks_loaded)
                        }
                    }
                },
                error: function (e) {
                    console.log(e);
                }
            });
        }
    });
}


function convertSecondsToTime(seconds) {
    if (!isNaN(seconds)) {
        var time;
        time = new Date(seconds * 1000).toISOString().substr(11, 8);

        var time_arr = time.split(':');

        if (time_arr.length > 2 && time_arr[0] == '00') {
            time_arr[1] = time_arr[1].replace(/^0/, ''); // 分の先頭の0を取り除く
            time = time_arr[1] + ':' + time_arr[2];
        } else {
            time_arr[1] = time_arr[1].replace(/^0/, ''); // 分の先頭の0を取り除く
            time = time_arr[0] + ':' + time_arr[1] + ':' + time_arr[2];
        }

        return time;
    }
}


let list_file_name = [];
let list_folder_name = [];
let list_temp_folder_name = [];

var typingTimer;                //timer identifier
var doneTypingInterval = 2000;
async function doneTyping(content, offerID = null) {
    if ($('.mcomment-input-text').attr('type_input') !== 'input') {
        if (!offerID) {
            return;
        }
    }
    let offer_id = !!offerID ? offerID : $(document).find('.mitem.mactive').attr('data-offer');
    if (window.location.href.toString().includes("tab=messenger") || window.location.href.toString().includes(`offer=${offer_id}`)) {
        let data_form = new FormData();
        let content_value = content;
        let project_id = $(document).find('.project-item.active').attr('data-project-id');

        data_form.append('project_id', project_id);
        data_form.append('content', content_value);
        data_form.append('offer_id', offer_id);
        data_form.append('file_id', JSON.stringify(list_file_id));
        data_form.append('folder_id', JSON.stringify(list_folder_id));
        data_form.append('type_comment', '1');
        data_form.append('id_time', id_time);

        // await $.ajax({
        //     type: "POST",
        //     contentType: false,
        //     processData: false,
        //     cache: false,
        //     url: "/message/save_draft_message",
        //     data: data_form,
        //     success: function (data) {
        //        console.log("success");
        //     },
        //     fail: function (data) {
        //         // toastr.error(gettext('Something went wrong!'));
        //     },
        //     error: function(xhr, status, error) {
        //         // var err = eval("(" + xhr.responseText + ")")
        //         // alert(err.message);
        //     },
        //     complete: function () {
        //     }
        // });
    }
}

function commentInput() {
    if ($('.mcommment').length > 0) {
        $('.mcommment').each(function () {
            var $comment_input = $(this);
            var comment_id = $comment_input.attr('id');
            var mattach = $('.mattach:not(.mattach-form)');
            var mattach_id = mattach.attr('id');

            Dropzone.autoDiscover = false;

            var previewNode = $comment_input.find('.mattach-template');

            var previewTemplate = previewNode.parent().html();
            previewNode.parent().empty();

            window.addEventListener('dragover', function (e) {
                e = e || event;
                e.preventDefault();
            }, false);
            window.addEventListener('drop', function (e) {
                e = e || event;
                e.preventDefault();
            }, false);

            mattach.find('.mattach-drop').hide();
            mattach.find('.mattach-overlay').hide();

            var comment_count = $('.mcommment').length;

            if (comment_count > 1) {
                mattach.closest('.maction').on('dragover', function (e) {
                    var dt = e.originalEvent.dataTransfer;
                    if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                        mattach.find('.mattach-overlay').show();
                        mattach.find('.mattach-drop').show();
                    }
                });

                mattach.closest('.maction').on('drop', function (e) {
                    mattach.find('.mattach-overlay').hide();
                    mattach.find('.mattach-drop').hide();
                    console.log('drop');
                });

                mattach.closest('.maction').on('dragleave', function (e) {
                    // mattach.find('.mattach-overlay').hide();
                    // mattach.find('.mattach-drop').hide();
                    console.log('drag leave');
                });
            } else {
                $(window).on('dragover', function (e) {
                    if (!$('#modal-edit-offer').hasClass('in')) {
                        var dt = e.originalEvent.dataTransfer;
                        if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                            if (!$('.modal.in').length) {
                                mattach.show();
                                mattach.find('.mattach-overlay').show();
                                mattach.find('.mattach-drop').show();
                            }
                        }
                    }
                });

                $(window).on('drop', function (e) {
                    mattach.hide();
                    mattach.find('.mattach-overlay').hide();
                    mattach.find('.mattach-drop').hide();
                });

                $(window).on('dragleave', function (e) {
                    // mattach.find('.mattach-overlay').hide();
                    // mattach.find('.mattach-drop').hide();
                });
            }
            mattach.find('.mattach-overlay').on('click', function (e) {
                mattach.hide();
                mattach.find('.mattach-overlay').hide();
                mattach.find('.mattach-drop').hide();
            });
            $('#' + mattach_id + '-form').append(csrf);
            mzdrop = new Dropzone('#' + mattach_id + '-form', {
                autoDiscover: false,
                previewTemplate: previewTemplate,
                maxFiles: 10,
                maxFilesize: 4500,
                timeout: 900000,
                params: { 'list_id': list_file_id },
                previewsContainer: '#' + comment_id + ' .mattach-previews',
                clickable: '#' + comment_id + ' .mattach-label',
                autoProcessQueue: false,
                autoQueue: false,
            });

            mzdrop.on('addedfile', function (file, e) {
                const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
                $(".c-fab ").css("margin-bottom", height + 80);
                $('.mcomment-send').addClass('active');
                file_names = [];
                mzdrop["sended"] = false;
                let key_file = "";
                let file_dom = $(file.previewElement);
                let path = "";
                if (is_exceeded_length) {
                    if (!mzdrop.printed_err) {
                        toastr.error("Folder'name is too long.");
                    }
                    let page = getPage(file_dom);
                    let folder_ids = Object.values(list_temp_folder_id);
                    for (let folder_id of folder_ids) {
                        delete_folder(folder_id, page);
                    }
                    file.not_created = true;
                    mzdrop.removeFile(file);
                    list_temp_folder_id = {};
                    list_files_folders = {};
                    list_temp_folder_name = [];
                } else {
                    list_folder_id = { ...list_folder_id, ...list_temp_folder_id };
                    list_folder_name.concat(list_temp_folder_name);
                    if (!jQuery.isEmptyObject(list_files_folders)) {
                        for (const key in list_files_folders) {
                            if (list_files_folders[key].includes(file.name)) {
                                path = key;
                                let index = list_files_folders[key].indexOf(file.name);
                                list_files_folders[key].splice(index, 1);
                                if (!list_files_folders[key].length) {
                                    delete list_files_folders[key];
                                }
                                break;
                            }
                        }
                        if (jQuery.isEmptyObject(list_files_folders)) {
                            list_files_folders = {};
                            list_temp_folder_name = [];
                            list_temp_folder_id = {};
                        }
                    }
                    let file_preview = $('.mattach-preview-container').find(".mcommment-file__name");
                    for (let i = 0; i < file_preview.length; i++) {
                        if ($(file_preview[i]).text() == file.name) {
                            let real_path = path.substring(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            $(file_preview[i]).text(real_path + file.name);
                            break;
                        }
                    }
                    if (path === '') {
                        list_file_name.push(file.name)
                    }
                    uploadFileS3(file, file_dom, path);
                }
            });

            mzdrop.on('dragenter', function () {
                mattach.find('.mattach-file').addClass('active');
            });

            mzdrop.on('dragover', function () {
                mattach.find('.mattach-file').addClass('active');
            });

            mzdrop.on('dragleave', function () {
                mattach.find('.mattach-file').removeClass('active');
            });

            mzdrop.on('drop', function (e) {
                is_exceeded_length = false;
                mzdrop.printed_err = false;
                $('.mattach-file').removeClass('active');
                mattach.hide();
                mattach.find('.mattach-overlay').hide();
                mattach.find('.mattach-drop').hide();
                $('.mcomment-bottom').trigger('click');
                let items = e.dataTransfer.items;
                let today = new Date();
                let epoch = Math.floor(today / 1000);
                let dateTime = "---" + epoch;
                let page = getPage(mattach);

                for (let i = 0; i < items.length; i++) {
                    var item = items[i].webkitGetAsEntry();
                    if (item.isFile) {
                        //list_file_name.push(item.name);
                        traverseFileTree(item, "", 0, "", page);
                    } else {
                        list_temp_folder_name.push(item.name);
                        traverseFileTree(item, item.name + dateTime + "/", 0, "", page);
                    }
                }
            });

            mzdrop.on('error', function (file) {
                console.log('error');
            });

            mzdrop.on('removedfile', function (file) {
                const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
                $(".c-fab ").css("margin-bottom", height + 80);
                mzdrop.printed_err = true;
                if (file.not_create) {
                    return
                }
                if (mzdrop.files.length == 0 && !$comment_input.find('.mcomment-input-text').val()) {
                    mattach.parents('.mcommment').find('.mcomment-send').removeClass('active');
                }
                if (!file["not_created"] && !mzdrop["sended"]) {
                    file_id = Object.keys(list_file_id).find(key => list_file_id[key] === file.name);
                    if (file.name.indexOf(list_file_name) >= 0) {
                        list_file_name.pop(file.name.indexOf(list_file_name));
                    }
                    if (file_id) {
                        delete list_file_id[file_id];
                        $.ajax({
                            type: "POST",
                            data: {
                                'file_id': file_id,
                                'message_type': getPage(mattach)
                            },
                            url: '/upload/remove_file',
                            success: function (data) {
                                let list_folder_removed = data.removed_folder_id.split(",");
                                for (let key in list_folder_id) {
                                    if (list_folder_removed.includes(list_folder_id[key])) {
                                        delete list_folder_id[key]
                                    }
                                }
                                $('.mcomment-input-text').attr('type_input', 'input');
                                setTimeout(async () => {
                                    if ($(document).find('textarea.mcomment-input-text').length) {
                                        await doneTyping($(document).find('textarea.mcomment-input-text').val());
                                    }
                                }, 2000);
                            },
                        });
                    }
                }
            });

            $(document).on('click', '.mcommment-file__delete', async function () {
                let offer_id = $(document).find('.mitem.mactive').attr('data-offer');
                currentOffer = offer_id;
                if (window.location.href.toString().includes("tab=messenger") || window.location.href.toString().includes(`offer=${offer_id}`)) {
                    let file_id = $(this).parents('.mattach-template').attr('data-file-id');
                    list_file_remove.push(file_id);
                    let file_name = $(this).parent().find('.mcommment-file__name').html();
                    file_name = file_name.split('/')[file_name.split('/').length - 1];
                    file_id = Object.keys(list_file_id).find(key => list_file_id[key] === file_name);
                    $(this).parents('.mattach-template').remove();
                    if (file_id) {
                        delete list_file_id[file_id];
                        $.ajax({
                            type: "POST",
                            data: {
                                'file_id': file_id,
                                'message_type': getPage(mattach)
                            },
                            url: '/upload/remove_file',
                            success: function (data) {
                                let list_folder_removed = data.removed_folder_id.split(",");
                                for (let key in list_folder_id) {
                                    if (list_folder_removed.includes(list_folder_id[key])) {
                                        delete list_folder_id[key]
                                    }
                                }
                            },
                        });
                        $(document).find('.mcomment-input-text').attr('type_input', 'input');
                        if ($(document).find('textarea.mcomment-input-text').length) {
                            await doneTyping($(document).find('textarea.mcomment-input-text').val());
                        }
                    }
                }
                const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
                $(".c-fab ").css("margin-bottom", height + 80);
            });

            mattach.parents('.mcommment').find('.mcomment-send').on('click', function (e) {
                e.preventDefault();
            });

            // https://codepen.io/vsync/pen/frudD
            $(document).on('input.mcomment-input-text', 'textarea.mcomment-input-text', function () {
                calculateHeightCommentInput(this)
                $(this).attr('type_input', 'input');
                clearTimeout(typingTimer);
                typingTimer = setTimeout(async () => {
                    if ($(document).find('textarea.mcomment-input-text').length) {
                        await doneTyping($('textarea.mcomment-input-text').val())
                    }
                }, doneTypingInterval);
                const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
                $(".c-fab ").css("margin-bottom", height + 80);
            });
        });
    }
};

function calculateHeightCommentInput(inputElement) {
    let editing = false;
    if ($('.mcomment-send.input-editing').length > 0) {
        editing = true;
    }
    $(inputElement).css('height', 'auto');
    var height = parseInt(inputElement.scrollHeight)
        + parseInt($(inputElement).css('border-top-width'))
        - parseInt($(inputElement).css('padding-top'))
        + parseInt($(inputElement).css('border-bottom-width'))
        - parseInt($(inputElement).css('padding-bottom'));

    if ($(inputElement).val()) {
        $(inputElement).parents('.mcommment').find('.mcomment-send').addClass('active');
    } else {
        if (!$(inputElement).parents('.mcomment-top').find(".mattach-template").length && !$('.mcommment .mcomment-send').hasClass('input-editing')) {
            $(inputElement).parents('.mcommment').find('.mcomment-send').removeClass('active');
        }
    }

    valInput = $(inputElement).val();
    var maxHeight = $(window).height() - 64 - 75 - 40 - 80 - 48;
    if ($('.prdt').length > 0) {
        //  $(inputElement).css('overflow', 'hidden')
        if (height > maxHeight) {
            const inputComment = $('.mcomment-input-text');
            inputComment.scrollTop(inputComment[0].scrollHeight)
            $(inputElement).height(maxHeight + 'px');
            $(inputElement).css('overflow', 'auto')
        } else if (height <= maxHeight) {
            $(inputElement).height(height + 'px');
        } else {
            if (editing) {
                $(inputElement).height(height + 'px');
            } else {
                $(inputElement).height(height + 'px');
            }
        }
    } else {
        $(inputElement).height(height + 'px');
        if (height > maxHeight) {
            $(inputElement).height(maxHeight + 'px');
        }
    }

}



function delete_folder(folder_id, page) {
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/upload/delete_folder",
        data: {
            'folder_id': folder_id,
            'page': page
        },
        success: function () {
            console.log('deleted folder');
        }
    })
}

function modalInModal() {
    $(document).on('hidden.bs.modal', function (event) {
        if ($('.modal:visible').length) {
            $('body').addClass('modal-open');
        }
    });
};

function createThread() {
    $('#create-thread-policy').on('change', function () {
        var $this = $(this);
        var policyChecked = $('#create-thread-policy:checked').length > 0;

        if (policyChecked) {
            $this.parents('.create-thread').find('.create-thread__action .btn').attr('disabled', false);
            $this.parents('.create-thread').find('.create-thread__action .btn').removeClass('btn--disabled');
        } else {
            $this.parents('.create-thread').find('.create-thread__action .btn').attr('disabled', true);
            $this.parents('.create-thread').find('.create-thread__action .btn').addClass('btn--disabled');
        }
    });

    $('.create-thread-submit').on('click', function () {
        $('#modal-create-thread, #modal-create-thread-2').modal('hide');
    });
};

function seenMessage(offer_id) {
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/offer_message/update_seen",
        data: {
            'offer_id': offer_id,
        },
        success: function () {
            // console.log('update seen successful');
        }
    })
}

function actionThread() {
    $(document).on('click', '.mlist .mitem', async function () {
        let offer_id = $(this).data('offer');
        if ($(this).hasClass('mactive')) {
            if ($('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                seenMessage(offer_id);
            }
        } else {
            const preOfferElement = $(this).parent().find('.mscene.mitem.mactive')
            if (preOfferElement.length) {
                if ($(document).find('textarea.mcomment-input-text').length) {
                    await doneTyping($(document).find('textarea.mcomment-input-text').val(), preOfferElement.data('offer'))
                }
                preOfferElement.removeClass('mactive');
            }
            $(this).addClass('mactive');
            if (!$('.madd-new #offer-filter').is(':checked') && !$(this).parents('.list--offers-search').length) {
                let item_dones = $('.mitem.mchecked[data-offer!=' + offer_id + ']');
                item_dones.each(function () {
                    if ($(this).find('.notification.notification--blue.hide').length) {
                        $(this).hide('slow', function () {
                            $(this).remove();
                        })
                    }
                })
            }

            is_loading_offer_message = false;
            current_load_offer_message = 0;
            loadMessage(offer_id);
            if ($('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                seenMessage(offer_id);
            }
            first_load_page = false;
            $(this).siblings().removeClass('mactive');
            // getDraftMessageDM(offer_id);
            let url = new URL(window.location);
            url.searchParams.set("offer", offer_id);
            let refresh = url.toString();
            setTimeout(function () {
                window.history.pushState({ path: refresh }, '', refresh);
            }, 500);
        }

        if ($(window).width() < max_width_sp_device) {
            // $(".navigation-top-app-bar.refactor").addClass("hide")
            $('.mcolumn--main').addClass('mshow');
            $('.switch-dm').css('visibility', 'hidden')
            $('.navigation-top-app-bar').removeClass('visible-wallet')
            // $('.mcolumn--left').css('display', 'none');
            $(this).parents('.martist').addClass('hide-banner');
            $('.pbanner__image').find('.pbanner__icon-expand-container').addClass('hide-button');
            if ($('.project-item.active').find('.block-navigation-bar').hasClass('hide') && !$('.block-navigation-bar').hasClass('hide')) {
                $(this).parents('.martist').css('margin-top', '-160px');
            } else {
                // $(this).parents('.martist').css('margin-top', '-280px');
            }
            $('.btn-tutorial-sp').addClass('hide-button-tutorial');
            calcTopListMessage();
            $('.prdt').addClass('open_sp_modal');
            if ($(window).width() < max_width_sp_device && $('.project-tab-messenger.active').length > 0) {
                resetPositionCommentBox();
            }
        }
    });

    $(document).on('click', '.action-panel-head', async function () {
        let offer_id = $(this).find(".offer-block-right.maction.maction-new").data('offer');
        if ($(".mlist .mitem").hasClass('mactive')) {
            if ($('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                seenMessage(offer_id);
            }
        }

        if ($(window).width() < max_width_sp_device) {
            $('.mcolumn--main').addClass('mshow');
            $('.switch-dm').css('visibility', 'hidden')
            // $('.mcolumn--left').css('display', 'none');
            $(".mlist .mitem").parents('.martist').addClass('hide-banner');
            $('.pbanner__image').find('.pbanner__icon-expand-container').addClass('hide-button');
            if ($('.project-item.active').find('.block-navigation-bar').hasClass('hide') && !$('.block-navigation-bar').hasClass('hide')) {
                $(".mlist .mitem").parents('.martist').css('margin-top', '-160px');
            } else {
                // $(this).parents('.martist').css('margin-top', '-280px');
            }
            $('.btn-tutorial-sp').addClass('hide-button-tutorial');
            calcTopListMessage();
            $('.prdt').addClass('open_sp_modal');
            if ($(window).width() < max_width_sp_device && $('.project-tab-messenger.active').length > 0) {
                resetPositionCommentBox();
            }
        }
    });

    $(document).on('click', '.mcolumn.mcolumn--main .mcolumn-next', function () {
        $('.mcolumn--right').addClass('mshow');
        $('.mcolumn--right').css('display', 'inherit');
        $('.mcolumn--main').removeClass('mshow');
        $('.mcolumn--main').css('display', 'none');
    });

    $(document).on('click', '.mcolumn-back', function () {
        if ($(this).closest('.mcolumn').hasClass('mcolumn--main') && $(this).parents('.martist').hasClass('hide-banner')) {
            if ($(window).width() < max_width_sp_device) {
                calcTopListMessage();
                $('.prdt').removeClass('open_sp_modal');
            }
            $('.pbanner__image').find('.pbanner__icon-expand-container').removeClass('hide-button');
            $(this).parents('.martist').css('margin-top', 'auto');
            $('.btn-tutorial-sp').removeClass('hide-button-tutorial');
            appHeight();
            // $('.mcolumn--left').css('display', 'inherit');
            $('.switch-dm').css('visibility', 'visible')
        } else if ($(this).closest('.mcolumn').hasClass('mcolumn--right')) {
            $('.mcolumn--main').addClass('mshow');
            $('.mcolumn--main').css('display', 'inherit');
            $('.mcolumn--right').css('display', 'none');
        }
        $(this).closest('.mcolumn').removeClass('mshow');
    });
};

function calcTopListMessage() {
    let header_global = $('.sheader');
    let banner_project = $('.new-banner-project');
    let height_header_global = header_global.outerHeight();
    let height_banner_project = banner_project.outerHeight();
    let total_space_top = height_header_global + height_banner_project;
    let top_header_global = parseInt(header_global.css('top').replace('px', ''));
    let message_tab = $('.project-tab.project-tab-messenger.active');
    if (message_tab.length > 0) {
        if (top_header_global < 0) {
            //message_tab.css('top', `${height_banner_project}px`)
        } else {
            //message_tab.css('top', `${total_space_top}px`)
        }
    }
}

function actionMessage() {
    if (window.localStorage.getItem("performance") || !window.location.search.includes("tab=product-comment")) {
        messengerAction();
    }
}

function messengerAction() {
    let $main_target = $(document);

    $main_target.off('click', '.mcomment-bottom').on('click', '.mcomment-bottom', function () {
        $(this).find('.mcomment-input-placeholder').hide();
        $(this).closest('.mcommment').find('.mcomment-top').show(200);
        $(this).closest('.mcommment').find('.mcomment-input-text').focus();
        //$('.prdt .DM-box-container .mmessage-list').css('padding-bottom', '120px')
    });

    showInforMessage();

    // edit
    $main_target.off('click', '.mmessage-edit').on('click', '.mmessage-edit', function (e) {
        should_scroll = false;
        e.stopPropagation();
        e.preventDefault();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        list_file_name = [];
        list_folder_name = [];
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.toggleClass('editing');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-pin').removeClass('active hide');
        $main_target.find('.mmessage.reply').removeClass('active reply');
        $main_target.find('.mcomment-input').removeClass('is-reply is-pin');
        $('.cscene-vertical').removeClass('active');
        $message_target.removeClass('reply');
        $('.mmessage[data-message-id!=' + message_id + ']').removeClass('editing');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').addClass('pd-main-message')
        }
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $message_target.find('.mmessage-edit').removeClass('active');
            $(this).addClass('active');
        }

        if ($(this).parents('.mmessage').hasClass('editing')) {
            let pin_dom = $message_target.find('.video-pin-time');
            if (pin_dom.length > 0) {
                $main_target.find('.mcomment-pin').trigger('click');
                let current_time = $message_target.find('.video-pin-start').text();
                $main_target.find('.mcomment-input-title span').text(current_time);
                let scene_id = pin_dom.parents('.s-audio').attr('data-scene-id');
                goToSceneActive(scene_id);
            }

            $main_target.find('.mcommment').addClass('border-editing');
            $main_target.find('.btn-remove-msg').addClass('d-block')
            $main_target.find('.block-remove-msg-editing').removeClass('d-none')
            $main_target.find('.mcomment-send').addClass('input-editing');
            let content = '';
            let $message = $(this).parents('.mmessage');
            
            // メッセージコンテンツを探す
            let $messageContent = $message.find('.mmessage-content').first();
            
            // ファイルが添付されているかチェック
            let hasFile = $message.find('.mmessenger--file').length > 0;
            let hasAudio = $message.find('.mmessenger--audio-wave').length > 0;
            
            // テキストを探す - 複数の方法を試す
            // 方法1: メッセンジャーコンテンツから直接探す
            let textContent = $messageContent.find('.mmessenger--text');
            
            if (textContent.length > 0) {
                // テキストメッセンジャー内の実際のテキスト要素を探す
                let actualTextElement = textContent.find('.s-text, .bodytext-13').filter(function() {
                    return $(this).text().trim().length > 0;
                }).first();
                
                if (actualTextElement.length > 0) {
                    content = actualTextElement.text().trim();
                }
            }
            
            // 方法2: 返信や特殊なケース
            if (!content) {
                let specialText = $message.find('.s-filetext, .s-audio-text').filter(function() {
                    return $(this).text().trim().length > 0;
                }).first();
                
                if (specialText.length > 0) {
                    content = specialText.text().trim();
                }
            }
            
            $main_target.find('.mcommment .mcomment-bottom').click();
            $main_target.find('.mcommment .mcomment-input-text').val(content);
            calculateHeightCommentInput($main_target.find('.mcommment .mcomment-input-text')[0])
            let files_dom = $(this).parents('.mmessage').find('.s-file, .s-audio-source');
            let files_uploaded_html = '';
            if (files_dom) {
                $(this).parents('.mmessage').find('.s-file, .s-audio-source').each(function () {
                    let file_name = '';
                    if ($(this).hasClass('s-file')) {
                        if ($('.owner-top').hasClass('scene-detail-page')) {
                            file_name = $(this).find('.file-name-cmt').html();
                        } else {
                            file_name = $(this).html();
                        }
                    } else {
                        file_name = $(this).attr('title');
                    }
                    let file_id = $(this).parents('.mmessenger--file').attr('data-file-id');
                    if (!file_id) {
                        file_id = $(this).parents('.mmessenger--audio-wave').attr('data-file-id');
                    }
                    if (file_name) {
                        if (file_id === file_contract || file_id === file_bill) {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        } else {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '<i class="icon icon--sicon-close"></i>' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        }

                    }
                })
            }
            $('.maction .mattach-previews.collection').empty().append(files_uploaded_html);

            $main_target.find('.mcommment-file__delete').on('click', function () {
                let file_id = $(this).parents('.mattach-template').attr('data-file-id');
                list_file_remove.push(file_id);
                $(this).parents('.mattach-template').remove();
            });

            $main_target.find('.mcomment-send').addClass('active');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
        const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
        $(".c-fab ").css("margin-bottom", height + 80);
        setTimeout(() => {
            should_scroll = true;
        }, 1500);
    });

    $main_target.off('click', '.mmessage-delete').on('click', '.mmessage-delete', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        $('.mcomment-message .mcommment-file').remove();
        e.stopPropagation();
        e.preventDefault();
        let url_page;
        let data = new FormData();
        should_scroll = false;
        data.append('message_id', message_id);

        if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
            url_page = '/messenger/delete_message';
        } else if (messenger_page === 'top_page') {
            if ($('.project-tab-product-comment').hasClass('active')) {
                data.append('type', 'project');
                let project_id = $('.project-item.active').attr('data-project-id');
                data.append('project_id', project_id);
            } else {
                data.append('type', 'scene');
            }
            url_page = "/top/delete_comment";
        }

        bootbox.confirm({
            message: gettext('Do you really want to delete this?'),
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        success: function (data) {
                            // console.log('OK');
                            autoLoadMore();
                        },
                        complete: function () {
                            resetFormContract();
                            setTimeout(() => {
                                should_scroll = true;
                            }, 1500);
                        }
                    });
                }
            }
        });
    });

    $main_target.on('click', '.mmessage-resolve', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        e.stopPropagation();
        e.preventDefault();
        let data = {};
        let resolved = $(this).hasClass('mmessage-resolved');

        let parent_dom = $main_target.parents('.pd-section');
        let $message_child = parent_dom.find('.mmessage[data-parent-id=' + message_id + ']');
        if (!resolved) {
            if (parent_dom.hasClass('show-comment-unresolved')) {
                if ($message_target.find('.s-audio-control.active').length) {
                    $message_target.find('.s-audio-control.active').trigger('click')
                }
                if ($message_child.find('.s-audio-control.active').length) {
                    $message_child.find('.s-audio-control.active').trigger('click')
                }
                $message_target.fadeOut('slow');
                $message_child.fadeOut('slow');
            }
            setTimeout(function () {
                $message_target.addClass('resolved');
                $message_child.addClass('resolved');
            }, 300);

            $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
            if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')) {
                $message_target.find('.mmessage-resolve .txt-item-comment').text('進行中に戻す')
            }
        } else {
            $message_target.css('display', 'flex');
            $message_child.css('display', 'flex');
            $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_target.removeClass('resolved');
            $message_child.removeClass('resolved');
            if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')) {
                $message_target.find('.mmessage-resolve .txt-item-comment').text('解決済みにする')
            }
        }

        if ($(this).parents('.pd-product-comment').length > 0) {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'project',
            };
        } else {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'scene',
            };
        }

        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/top/resolve_comment",
            data: data,
            success: function () {
                // console.log('ok');
            },
            error: function () {
                if (resolved) {
                    $message_target.addClass('resolved');
                    $message_child.addClass('resolved');
                    $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
                } else {
                    $message_target.css('display', 'flex');
                    $message_child.css('display', 'flex');
                    $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_target.removeClass('resolved');
                    $message_child.removeClass('resolved');
                }
                autoLoadMore();
            }
        })

    });

    $main_target.on('click', '.mmessage-reply', function (e) {
        e.preventDefault();
        e.stopPropagation();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        $main_target.find('.mcommment .mcomment-input-text').val('');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-send.active').removeClass('active');
        $main_target.find('.mmessage').removeClass('editing');
        $main_target.find('.mcomment-send').removeClass('input-editing');
        $main_target.find('.mcommment').removeClass('border-editing');
        $main_target.find('.mcomment-pin').removeClass('active hide');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').removeClass('pd-main-message')
        }
        // $main_target.find('.mmessage-reply.active').removeClass('active');
        $('.cscene-vertical').removeClass('active');
        $main_target.find('.mmessage').removeClass('reply');
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $main_target.find('.mmessage-reply').removeClass('active');
            $(this).addClass('active');

            var message_component_close = $(this).closest('.mmessage-component');
            // if (message_component_close.length < 1) {
            //     message_component_close = $(this).closest('.video-item-comment-content');
            // }
            let parent_id = $(this).parents('.mmessage').attr('data-message-id');
            $(this).parents('.mmessage').addClass('reply');
            $('.pd-comment').find('.mcomment-input').removeClass('is-pin').addClass('is-reply');
            $('.pd-comment').find('.mcomment-input').attr('data-parent-id', parent_id);
            $('.pd-comment').find('.mcomment-input-title').html('<i class="icon icon--sicon-reply"></i>');
            $('.pd-comment').find('.mcomment-bottom').trigger('click');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--received').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSceneDetailComment = $(this).parents('.mmessage--received').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
        let activeAudioSent = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudioSent);
    });

    $(document).on('click', '.mcomment-input-close', function (e) {
        e.preventDefault();
        const editMessage = $(".mmessage-edit.active");
        if (editMessage) {
            let activeAudio = parseInt(editMessage.parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
            editMessage.removeClass("active")
            checkActive(editMessage, activeAudio);
        }
        $('.mmessage-reply').removeClass('active');
        $(this).closest('.mcomment-input').removeClass('is-reply').removeClass('is-pin');
        $(this).closest('.mcomment-input').find('.mcomment-input-text').focus();
        resetInputMessage($main_target);
        const height = $('.mcomment-attached').height() + $('.mcomment-input-text').height() - 32;
        $(".c-fab ").css("margin-bottom", height + 80);
    });

    $main_target.on('click', '.mcomment-pin', function (e) {
        e.preventDefault();

        $('.mcomment-pin').toggleClass('active hide');
        var message_component_close = $(this).closest('.mmessage-component');

        message_component_close.find('.mcomment-input').toggleClass('is-pin');
        $('.cscene-vertical').toggleClass('active');
        $('.cscene-vertical').css({
            'border': 'none'
        })
        addBorderPreview(true);

        if ($(this).hasClass('active')) {
            let currentTime = '0:00';
            let video = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active video')[0];
            if (video) {
                currentTime = video.currentTime;
            } else {
                let audio_dom = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active .s-audio--white');
                if (audio_dom.length > 0) {
                    let current_wave_index = audio_dom.attr('data-wavesurfer');
                    let current_wave = wavesurfer_arr[current_wave_index];
                    currentTime = current_wave.getCurrentTime();
                } else {
                    currentTime = '';
                }
            }

            message_component_close.find('.mcomment-input-title').html('<i class="icon material-symbols-rounded">pin_drop</i>' + '<span>' + msToTime(currentTime) + '</span>');
            message_component_close.find('.mcomment-bottom').trigger('click');
        } else {
            let $main_target = $('.mcolumn.mcolumn--main, .pd-comment__main');
            resetInputMessage($main_target);
        }

    });

    $main_target.on('click', '.icon--sicon-pin', function () {
        $('.mcomment-pin').removeClass('active hide');
        $('.mcomment-input').removeClass('is-pin');
        $('.cscene-vertical').removeClass('active');

        addBorderPreview();
        $main_target.find('.mcomment-input-text.mcomment-autoExpand').focus();
    })
};

function checkActive(controlDom, activeAudio) {
    const parent = controlDom.closest(".mmessage-container.refactor");
    if (controlDom.hasClass('active')) {
        // controlDom.find('s-audio.s-audio--audio-wave.s-audio--black').addClass('active');
        setColorActive(wavesurfer_arr[activeAudio])
    } else {
        if (parent.find(".s-audio.s-audio--audio-wave").hasClass("active")) {
            setColorActive(wavesurfer_arr[activeAudio])
        } else {
            setColorInActive(wavesurfer_arr[activeAudio])
        }
        // if ($('.s-audio-control').hasClass('active')){
        //     $('.s-audio-control').removeClass('active')
        // }
    }
}

function setColorInActive(wavesurfer) {
    if (wavesurfer) {
        wavesurfer.setWaveColor('rgba(83, 86, 90, 0.3)');
        wavesurfer.setProgressColor('#53565A');
    }
}

function setColorActive(wavesurfer) {
    if (wavesurfer) {
        // 白い背景でも見えるように青系の色に変更
        wavesurfer.setWaveColor('rgba(0, 154, 206, 0.3)'); // #009ace の30%透明度
        wavesurfer.setProgressColor('#009ace'); // 青色
    }
}

function datePicker() {
    if ($('.mcalendar').length > 0) {
        $('.mcalendar').each(function () {
            var date = new Date();
            date.setDate(date.getDate());

            var $this = $(this);
            var dates_deadline = $this.attr('data-dates-deadline');
            if (typeof dates_deadline === 'undefined') {
                return
            }
            dates_deadline = dates_deadline.split(',');
            var dates_disable = $this.attr('data-dates-disable');
            dates_disable = dates_disable.split(',');

            $this.datepicker({
                inline: true,
                weekStart: 1,
                startDate: date,
                multidate: false,
                format: 'dd-mm-yyyy',
                locale: 'ja',
                todayHighlight: true,
                todayBtn: true,
                debug: true,
                icons: {
                    previous: 'icon icon--sicon-prev',
                    next: 'icon icon--sicon-next',
                },
                beforeShowDay: function (date) {
                    var d = date;
                    var curr_date = ('0' + d.getDate()).slice(-2);
                    var curr_month = ('0' + (d.getMonth() + 1)).slice(-2);
                    var curr_year = d.getFullYear();
                    var formattedDate = curr_year + '-' + curr_month + '-' + curr_date;

                    if ($.inArray(formattedDate, dates_deadline) != -1) {
                        return {
                            classes: 'active-deadline'
                        };
                    }
                    if ($.inArray(formattedDate, dates_disable) != -1) {
                        return {
                            classes: 'busy-day'
                        };
                    }
                    return;
                }
            }).on('show', function (e) {
                console.log(22);

            }).on('changeDate', function (e) {
                $('.minfo-task').html('');
                let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
                let choosed_day = $(this).datepicker('getDate');
                if (choosed_day == 'Invalid Date') {
                    choosed_day = new Date()
                }

                let deadline = $.datepicker.formatDate("yy-mm-dd", choosed_day);
                let creator_id = $('#creator_settings__form').attr('data-id');
                if ($(this).hasClass('mcalendar--small')) {
                    $.ajax({
                        type: "POST",
                        url: "/messenger/get_task_deadline",
                        data: {
                            'offer_id': offer_id,
                            'deadline': deadline,
                            'creator_id': creator_id
                        },
                        dataType: 'json',
                        success: function (data) {
                            console.log("success");
                            $('.minfo-task').append(data.task_html)
                        },
                        error: function (e) {
                            console.log(e);
                        }
                    });

                }

            });

            setTimeout(function () {
                $this.find('.datepicker-days .today.day').addClass('disabled');
                $this.find('.datepicker-days .today.day').trigger('click');

                $this.find('.prev').html('<i class="icon icon--sicon-prev"></i>');
                $this.find('.next').html('<i class="icon icon--sicon-next"></i>');
            }, 500);

            setTimeout(function () {
                $this.find('.datepicker-days tfoot .today').trigger('click');
            }, 500);
        });
    }
}

function previewFile() {
    $(document).off('click', '.minfo-file_info, .mmessenger, .tfile-infor').on('click', '.minfo-file_info, .mmessenger, .tfile-infor', function () {
        let type = $(this).attr('data-type');
        let link = $(this).attr('data-link');
        let name = $(this).attr('data-name');
        let file_id = $(this).attr('data-file-id');
        let production_file = $(this).attr('data-type-file');
        if (!file_id) {
            file_id = $(this).attr('data-scene-id');
            if (file_id) {
                let version = $('.cscene__version.slick-slide[data-scene-id^=' + file_id + ']');
                let variation = version.parents('.cscene__variation');
                let version_index = version.attr('data-index');
                let variation_index = variation.attr('data-index');
                $('#sliderHorizontalNav0').slick('slickGoTo', variation_index);
                variation.find('.cscene__version-dot[data-index^=' + version_index + ']').trigger('click');

                const buttonDom = $(`.variation-button-container[data-scene-id=${file_id}]`);
                const listDom = buttonDom.closest('.list-variation-container');
                if (listDom.hasClass('hide')) {
                    // if($(`.list-variation-container:not(.hide)`).length >= 3){
                    //     $($(`.list-variation-container:not(.hide)`)[0]).addClass('hide');
                    // }
                    listDom.removeClass('hide');

                    $(`.list-variation-container`).each(function (index, item) {
                        if ($(this).hasClass('hide')) {
                            $($('.take-overlay-container')[index]).find('input').removeAttr('checked');
                        } else {
                            $($('.take-overlay-container')[index]).find('input').attr('checked', true);
                        }
                    });
                }
                buttonDom.trigger('click');
                $(".variation-button-container.active").get(0).scrollIntoView({ behavior: 'smooth' });
            }
        }

        let data = new FormData();
        if (production_file === 'production_file') {
            data.append('production_file', production_file);
            let scene_title_id = $(this).attr('data-scene-title-id');
            $('#modal-document-popup').attr('data-scene-title-id', scene_title_id)
            $('#modal-image-popup').attr('data-scene-title-id', scene_title_id)
            $('#modal-video-popup').attr('data-scene-title-id', scene_title_id)
        }
        if (type === 'document') {
            data.append('file_id', file_id);
            previewFilePdf(data);
            $('#modal-document-popup').find('.file-name_modal').html(name);
            $('#modal-document-popup').attr('data-file-id', file_id);

            let offer_id = $('.button-confirm-contract-offer').parent().attr('data-offer');
            let project_id = $('.button-confirm-contract-offer').parent().attr('data-project');
            if (project_id && offer_id) {
                $(this).parents().find('.button-confirm-contract-offer').removeClass('disable');
            }
            previewFileStorage($(this), data)
        } else if (type === 'image') {
            data.append('file_id', file_id);
            $('#modal-image-popup').find('img').attr('src', link);
            $('#modal-image-popup').find('.image-popup__title').html(name);
            $('#modal-image-popup').attr('data-file-id', file_id);
            previewFileStorage($(this), data)
        } else if (type === 'video') {
            data.append('file_id', file_id);
            previewFileStorage($(this), data);
            $('#modal-video-popup').find('video').attr('src', link);
            $('#modal-video-popup').find('video').attr('preload', 'auto');
            $('#modal-video-popup').find('.video-popup__title').html(name);
            $('#modal-video-popup').attr('data-file-id', file_id);
            let e = $('#modal-video-popup').find('video')[0];
            let ratio = e.videoWidth / e.videoHeight * 100 / 2;
            e.style.width = ratio.toString() + '%';
        } else if (type === 'other') {
            let file_id = $(this).attr('data-file-id');
            if (!file_id) {
                file_id = $(this).parents('.smodal').attr('data-scene-title-id');
            }
            if (file_id) {
                data.append('file_id', file_id);
                downloadFile(data, this);
            }
        }
        $('.smodal-download').off('click').on('click', function () {
            let file_id = $(this).parents('.smodal').attr('data-file-id');
            if (!file_id) {
                file_id = $(this).parents('.smodal').attr('data-scene-title-id');
            }

            if (file_id) {
                data.append('file_id', file_id);
                downloadFile(data)
            }
        })
    });
    $(document).on('hidden.bs.modal', '#modal-document-popup', function (e) {
        $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
    })
}

function getDownloadAudio() {
    $(document).off('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")')
        .on('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")', function (e) {
            let data = new FormData();
            e.stopPropagation();
            let file_id;
            if ($(this).parents('.tfile-producttion-file').length > 0) {
                data.append('production_file', 'production_file');
                file_id = $(this).parents('.tfile-producttion-file').attr('data-scene-title-id')
            } else if ($(this).parents('.mfolder__sub').length) {
                file_id = $(this).parents('.mfolder__sub').data('file-id');
            } else if ($(this).parents('#folderModal').length) {
                file_id = $(this).parents('.list-group-item').attr('data-file-id');
            } else {
                file_id = $(this).parents('.mmessage').attr('data-file-id');
                if (!file_id) {
                    file_id = $(this).parents('.minfo-file_info').attr('data-file-id');
                    if (!file_id) {
                        file_id = $(this).parents('.tfile-infor').attr('data-file-id');
                    }
                }
                if (!file_id) {
                    file_id = $(this).parents('.tfile-infor').attr('data-scene-id');
                    data.append('type_infor', 'scene');
                }
            }

            if (file_id) {
                data.append('file_id', file_id);
                downloadFile(data, this)
            }
        })

    $(document).off('click', '.icon--sicon-folder-download').on('click', '.icon--sicon-folder-download', function (e) {
        e.stopPropagation();
        let folder = $(this).parent(".list-group-item");
        if (!folder.length) {
            folder = $(this).parent(".parent-folder");
        }
        if (!folder.length) {
            folder = $(this).parent(".mfolder__sub");
        }
        let folder_id = folder.find(".hasSub").attr("data-folder-id");
        DownloadFolder(folder_id, this);
    })
}

function DownloadFolder(folder_id, target) {
    let data = new FormData();
    data.append('folder_id', folder_id);
    let href = window.location.href;
    let folderDom = $(target).parents('.parent-folder').length ? $(target).parent('.parent-folder').parents('.mfolder') : $(target).parent(".list-group-item");
    var list_icon_download = folderDom.find('.icon--sicon-download:not(.icon--sicon-folder-download)');
    list_icon_download = list_icon_download.toArray();
    if (!list_icon_download.length) {
        folderDom = folderDom.parents('.group-tree-modal');
        list_icon_download = folderDom.find('.icon--sicon-download:not(.icon--sicon-folder-download)');
        list_icon_download = list_icon_download.toArray();
    }
    let page;
    if (href.includes("/scene/")) {
        page = 'scene-comment';
    } else if (href.includes("tab=product-comment")) {
        page = 'project-comment';
    } else if (user_role !== 'admin') {
        page = 'messenger_owner';
    } else {
        page = 'messenger';
    }
    data.append('page', page);
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer');
        data.append('offer_id', offer_id);
    }
    $(target).prop('disabled', true);
    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/get_link_download_folder",
        data: data,
        beforeSend: function (data) {
            // toastr.info('ダウンロードのリンクを作成しています。');
        },
        beforeSend: function (xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
        },
        success: function (response) {
            let links = response.links;
            links = links.split(",");
            var interval = setInterval(function () {
                let a_tag = document.createElement('a');
                a_tag.href = links[0];
                document.body.appendChild(a_tag);
                $(list_icon_download[0]).addClass('done');
                list_icon_download.shift();
                a_tag.click();
                links.shift();
                if (!links.length) {
                    clearInterval(interval);
                    $(target).addClass('done');
                    $(target).prop('disabled', false);
                    // toastr.success('ダウンロードを開始しました');
                }
            }, 1500);
        },
        error: function () {
            toastr.error('エラーが発生しました');
        }
    })
}

function downloadFile(data, target = 0, typeAction = '') {
    let type_file;
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        if (user_role !== 'admin') {
            type_file = 'message_owner';
        } else {
            type_file = 'message';
        }
    } else if (messenger_page === 'top_page') {
        if ($('.project-tab-product-comment').hasClass('active')) {
            type_file = 'project';
        } else {
            type_file = 'comment';
        }
    }
    data.append('type_file', type_file);

    if (target) {
        $(target).addClass('loading');
    }
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer');
        data.append('offer_id', offer_id);
    }

    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/get_product_file_download_link",
        data: data,
        beforeSend: function (data) {
            if (!typeAction) {
                // toastr.info('ダウンロードのリンクを作成しています。');
            }
        },
        beforeSend: function (xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
        },
        success: function (response) {
            $(target).removeClass('loading').addClass('done');
            if (!typeAction) {
                let a_tag = document.createElement('a');
                a_tag.href = response.url;
                document.body.appendChild(a_tag);
                a_tag.click();
                // toastr.success('ダウンロードを開始しました');
            }
        },
        error: function () {
            if (!typeAction) {
                toastr.error('エラーが発生しました');
                $(target).removeClass('loading done');
            }
        }
    })
}

function previewFilePdf(data) {
    $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
    let type_file;
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        if (user_role !== 'admin') {
            type_file = 'message_owner';
        } else {
            type_file = 'message';
        }
    } else if (messenger_page === 'top_page') {
        if ($('.project-tab-product-comment').hasClass('active')) {
            type_file = 'project';
        } else {
            type_file = 'comment';
        }
    }
    data.append('type_file', type_file);
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer')
    }
    data.append('offer_id', offer_id);

    $.ajax({
        type: 'POST',
        datatype: 'json',
        contentType: false,
        processData: false,
        url: '/get_link_pdf_to_preview',
        data: data,
        success: function (data) {
            $('#modal-document-popup').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
        }
    })
}

function previewFileStorage(itemFile, data) {
    if ($('.pbanner-tab--exchange').hasClass('active')) {
        downloadFile(data, 0, 'preview')
    }
}

let file_contract;
let file_bill;

function loadMessage(offer_id) {
    console.log('messenger/load_refactor');
    let url_load = '';
    // if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
    url_load = !window.localStorage.getItem("performance")
        ? '/api/messager/load-offer-message'
        : "/messenger/refactor_load_offer";
    // }
    maudio_arr = [];
    wavesurfer_arr = [];
    should_scroll = false;
    $.ajax({
        type: "GET",
        datatype: "json",
        url: url_load,
        async: false,
        data: {
            'offer_id': offer_id,
        },
        success: function (data) {
            $(".mcolumn.mcolumn--main").html('');
            $(".mcolumn.mcolumn--main").append("<div class='offer-content-message refactor offer-" + offer_id + "'><div class=\"messenger-detail\" ></div></div>");
            $(".mcolumn.mcolumn--right").html('');
            $(".mcolumn.mcolumn--right").append("<div class='offer-content-message refactor offer-" + offer_id + "_infor" + "'><div class=\"messenger-infor\" ></div></div>");
            let active_offer = $('.offer-' + offer_id);

            //we should check wherther or not we have the offer in comment box
            active_offer.find(' .messenger-detail').html(data.html);

            //update set class for messenger-detail if it have cclass messenger-file-component-container 
            if (data?.html?.includes("messenger-file-component-container")) {
                if (!active_offer.find(' .messenger-detail').hasClass("have-file")) {
                    active_offer.find(' .messenger-detail').addClass("have-file");
                }

                if (data.html.includes("\"text-offer\"")) {
                    if (!active_offer.find(' .messenger-detail').hasClass("have-text-offer")) {
                        active_offer.find(' .messenger-detail').addClass("have-text-offer");
                    }
                }
            } else {
                if (active_offer.find(' .messenger-detail').hasClass("have-file")) {
                    active_offer.find(' .messenger-detail').removeClass("have-file");
                }

                if (active_offer.find(' .messenger-detail').hasClass("have-text-offer")) {
                    active_offer.find(' .messenger-detail').removeClass("have-text-offer");
                }
            }
            let infor_offer = $('.offer-' + offer_id + '_infor');
            infor_offer.find(' .messenger-infor').html(data.infor_html);
            if (data.file_contract) {
                file_contract = data.file_contract
            }
            if (data.file_bill) {
                file_bill = data.file_bill
            }

            commentInput();
            messengerAudio();
            datePicker();
            removeDuplicatedDate();
            if ($('.mcolumn.mcolumn--right').hasClass('active')) {
                $('.mcolumn--right .mcolumn-header,.mcolumn--right .mcolumn-content').addClass('active');
            }

            // create message
            createMessage(active_offer);

            // edit
            editMessage(active_offer);

            scrollCommentBar();

            // seen message
            active_offer.on('click', '.mcomment-input-text, .mcomment-bottom', function () {
                if ($(this).parents('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                    let offer_id = $(this).parents('.maction').data('offer');
                    seenMessage(offer_id);
                }
            });

            $('.mscrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom');
            if (!$('.prdt').length > 0) {
                $('.mcomment-top').hide();
            }

            let message = active_offer.find('.s-text');
            $.each(message, function (i, v) {
                if (!$(v).is('.align-center')) {
                    let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                    v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                }
            });

            initmCustomScrollbar();
            sScrollbarBottom();
            $('main')[0].style.overflow = 'hidden';

            if ($('.mmessage-list').length > 0) {
                $('.mmessage-list')[0].style.overflow = 'auto';
            }

            $('.navigation-top-app-bar').removeClass('display-flex')

            if (user_role === 'admin' || user_role === 'master_admin' && $('.pbanner-tab.active[data-show=messenger]').length) {
                actionMessengerArtist(infor_offer, active_offer);
            }

            $('.mmessage').last().addClass('load-lasted-message');
            total_offer_message = parseInt(data.total_page);
            scrollOfferMessage();
            autoLoadMoreOfferMessage();
            scrollListComment($('.mmessage-list'))


            // resizeCommentInput()
        },
        complete: function () {
            if ($('.block-navigation-bar').hasClass("hide")) {
                document.querySelector('.footer-comment-block.refactor').style.bottom = '0px';
                document.querySelector('.action-panel-head-custom').style.bottom = '0px';
            }
            setWidthInput($('.mcommment[id="comment-input-1"]'));
            calcMoreActionComment($('.prdt .mmessage'));
            calcPositionDropdownComment2();
            clickBtnActionMessage()
            // resizeCommentInput()
            setTimeout(function () {
                calcPositionElementDM()
            }, 600)
            setTimeout(() => {
                let currentContractName = $('.contract-block.u-row-end.u-bg-white .contract-block-contract-file-name').text().split("_");
                try {
                    if (currentContractName.length > 2) {
                        currentContractName[currentContractName.length - 3] = currentContractName[currentContractName.length - 3] + "(案)";
                    } else {
                        currentContractName[currentContractName.length - 2] = currentContractName[currentContractName.length - 2] + "(案)";
                    }
                } catch (e) {
                    console.log(e)
                }
                $('.contract-block.u-row-end.u-bg-white .contract-block-contract-file-name').text(currentContractName.join("_"));
                should_scroll = true;
            }, 1500);
        }
    })
}

function calcPositionElementDM() {
    let navigationBar = $('.block-navigation-bar');

    let sheader_block = $('.sheader');
    let nav_top = $('.navigation-top-app-bar')
    let martist_block = $('.martist')
    let martist_master_client = $('.martist.role_master_client')
    let height_sheader_block = sheader_block.outerHeight();
    let height_nav_top = nav_top.outerHeight();
    let topHeaderValue = parseInt(sheader_block.css('top').replace('px', ''));
    if (topHeaderValue < 0) {
        // $('.messenger-detail').css('height', `calc(100vh - ${height_footer_comment_block + height_banner_block}px)`)
    } else {
        if ($(window).width() < max_width_sp_device) {
            if (navigationBar.hasClass('hide')) {
                martist_master_client.css('margin-top', `-${height_sheader_block}px`)
            } else {
            }
        } else {
            // content_message.css('top', `${height_banner_block + height_nav_top + height_sheader_block}px`)
        }
        // $('.messenger-detail').css('height', `calc(100vh - ${height_footer_comment_block + height_banner_block + height_sheader_block}px)`)
    }
    $('.mcolumn-back').on('click', function () {
        const getTopHeaderValue = parseInt(sheader_block.css('top').replace('px', ''));
        if (!$('.mcolumn--main').is(':hidden')) {
            $(".navigation-top-app-bar.refactor").removeClass("hide");
        }
        if (nav_top.is(':hidden')) {
            height_nav_top = 0;
        }
        if ($(window).width() < max_width_sp_device) {
            if (getTopHeaderValue < 0) {
                if (martist_block.length > 0 && martist_block.hasClass('hide-banner')) {
                    // content_message.css('top', `${height_nav_top + height_banner_block}px`)
                }
            } else {
                if (martist_block.length > 0 && martist_block.hasClass('hide-banner')) {
                    // content_message.css('top', `${height_banner_block + height_sheader_block + height_nav_top}px`)
                }
            }
        }
    })
}

function getProgressUploaded() {
    let file_previews = $(".mattach-previews").find(".mattach-template");
    let total = 0;
    let uploaded = 0;
    file_previews.each(function (i, item) {
        total += parseInt($(item).attr("data-total"));
        uploaded += parseInt($(item).attr("data-loaded"));
    });
    return Math.max(2, uploaded / total * 70);
}

function createMessage(active_offer) {
    let url_page = '';
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        url_page = '/offer_message/create';
    } else if (messenger_page === 'top_page') {
        url_page = '/top/create_comment';
    }
    active_offer.off('click').on("click", ".mcomment-send.active:not(.input-editing)", function (e) {
        e.stopPropagation();
        e.preventDefault();
        let button_send = $(this);
        if (!$(this).hasClass('input-editing')) {
            if (!$(this).hasClass('is-sending')) {
                $(this).addClass('is-sending');
                $('.mcomment-bottom').addClass('disabled-mcomment')
                sScrollbarBottom();
                let offer_id = $(this).parents('.maction').data('offer');
                let last_message_id = '';
                if ($('.mmessage-list').find('.mmessage:not(.mmessage-confirm)').length) {
                    if (!$('.mmessage-list').find('.mmessage:not(.mmessage-confirm)').last().hasClass('mmessage-system')) {
                        last_message_id = $('.mmessage-list').find('.mmessage:not(.mmessage-confirm, .mmessage-system)').last().data('message-id')
                    }
                }
                let active_offer = $('.offer-' + offer_id);
                let scene_id = '';
                let pin_time = '';
                let scene_title_id = '';
                let parent_id = '';
                let has_pin = false;
                if (!offer_id) {
                    scene_title_id = $(this).parents('.pd-scene-title-detail').attr('data-scene-title-id');
                    active_offer = $('.pd-scene-title-detail[data-scene-title-id=' + scene_title_id + ']');
                    if (active_offer.find('.mcomment-input').hasClass('is-reply')) {
                        parent_id = active_offer.find('.mcomment-input.is-reply').attr('data-parent-id');
                    }

                    if ($(this).parents('.mcommment').find('.mcomment-pin').hasClass('active')) {
                        has_pin = true;
                        scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                        pin_time = $(this).parents('.mcommment').find('.mcomment-input-title').eq(0).text();
                        if (!pin_time.length) {
                            if ($(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe')) {
                                pin_time = '';
                            } else {
                                pin_time = '00:00';
                            }
                        }
                    }
                }
                if ($('.pd-section--detail.pd-product-comment').length > 0) {
                    active_offer = $('.pd-section--detail.pd-product-comment');
                }
                let input_message = $(this).closest('.mcommment');
                let messageContent = active_offer.find('.mcomment-input-text.mcomment-autoExpand').val();
                if (messageContent.trim() === '') {
                    messageContent = '';
                }

                let data = new FormData();
                data.append('offer_id', offer_id);
                data.append('message', messageContent);
                // data.append('before_message_id', last_message_id);
                data.append('scene_id', scene_id);
                data.append('pin_time', pin_time);
                data.append('scene_title_id', scene_title_id);
                data.append('has_pin', has_pin);
                if ($(this).parents('.pd-product-comment').length > 0) {
                    data.append('type', 'project');
                    let project_id = $('.project-item.active').attr('data-project-id');
                    parent_id = active_offer.find('.mcomment-input.is-reply').attr('data-parent-id');
                    data.append('project_id', project_id);
                    data.append('parent_id', parent_id);

                } else {
                    data.append('type', 'scene');
                    data.append('parent_id', parent_id);
                }

                if (scene_id !== '') {
                    let html_folder = '';

                    for (folder in list_folder_name) {
                        if (!folder.includes('/')) {
                            html_folder += `<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                            <div style="display: flex">
                                            <div class="s-audio-control video-pin-time"><span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
            play_circle
          </span>
          <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                            </div>
                                            <div class="s-audio-time video-pin-start">${pin_time}</div>
                                            </div>
                                            <div class="s-audio-text s-audio-file">
                                            <div class="mmessenger mmessenger--file mmessenger--black">
                                            <div class="messenger-content">
                                            <div class="s-file s-file--file s-file--black">
                                            <i class="icon icon icon--sicon-storage"></i>${list_folder_name[folder]}</div>
                                            </div>
                                            </div>
                                            </div>
                                            </div>`
                        }
                    }


                    for (file_name in list_file_name) {
                        html_folder += `<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                        <div class="s-audio-control video-pin-time">
                                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                            play_circle
                                        </span>
                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                        </div>
                                        <div class="s-audio-time video-pin-start">${pin_time}</div>
                                        </div>
                                        <div class="s-audio-text s-audio-file">
                                        <div class="mmessenger mmessenger--file mmessenger--black">
                                        <div class="messenger-content">
                                        <div class="s-file s-file--file s-file--black">
                                        <i class="icon icon icon--sicon-clip"></i>${list_file_name[file_name]}</div>
                                        </div>
                                        </div>
                                        </div>
                                        </div>`
                    }

                    let html_content = '';
                    if (messageContent !== '') {
                        html_content = `<div class="mmessenger mmessenger--text mmessenger--black">
                                        <div class="messenger-content">
                                        <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                        <div class="s-audio-control video-pin-time">
                                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                            play_circle
                                        </span>
                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                        </div>
                                        <div class="s-audio-time video-pin-start">${pin_time}</div>
                                        </div>
                                        <div class="s-audio-text">${messageContent}</div>
                                        </div>
                                        </div>
                                        </div>`
                    }

                    let message_html = `<div class="mmessage-container refactor"><div class="mmessage mmessage--sent clicked new-message">
                                          <div class="mmessage-main">
                                            <div class="mmessage-content">`
                        + html_folder + html_content +

                        `</div>
                                          </div>
                                        </div></div>`;

                    $(message_html).insertBefore($('.pd-section--detail').find('.mlast__content'));
                } else {
                    if ($('.owner-top').hasClass('scene-detail-page')) {
                        scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                        data.append('scene_id', scene_id);
                    }
                }

                if (mzdrop.files.length > 0 && mzdrop.files[0]) {
                    let file_loaded = Object.values(list_file_id);
                    let file_loading = mzdrop.files.length - file_loaded.length;

                    if (file_loading) {
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', '2%');
                        var waiting_file_loading = setInterval(function () {
                            let current_file_loaded = Object.values(list_file_id);
                            let current_file_loading = mzdrop.files.length - current_file_loaded.length;
                            input_message.find('.mcomment-top').show();
                            let progress = getProgressUploaded();
                            $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                            if (!current_file_loading) {
                                data.append('list_file_id', Object.keys(list_file_id));
                                data.append('list_folder_id', Object.values(list_folder_id));
                                clearInterval(waiting_file_loading);
                                $.ajax({
                                    type: "POST",
                                    contentType: false,
                                    processData: false,
                                    cache: false,
                                    data: data,
                                    url: url_page,
                                    beforeSend: function (data) {
                                        // toastr.info('アップロード中…');
                                    },
                                    success: function (data) {

                                        sScrollbarBottom();

                                        $('.upload-button-wrapper .fill .process').css('width', '100%');
                                        if ($('.prdt').length < 1) {
                                            input_message.find('.mcomment-top').hide();
                                        }
                                        setTimeout(function () {
                                            // toastr.success(data.success_message);
                                            $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                                        }, 1000);
                                        setTimeout(function () {
                                            $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                                            $('.upload-button-wrapper .fill .process').css('width', '0');
                                        }, 2000);
                                    },
                                    complete: function () {
                                        mzdrop["sended"] = true;
                                        mzdrop.removeAllFiles();
                                        list_file_id = {};
                                        list_files_folders = {};
                                        list_folder_id = [];
                                        list_folder_name = [];
                                        list_file_name = [];
                                        button_send.removeClass('is-sending');
                                        $('.mcomment-bottom').removeClass('disabled-mcomment')
                                        resetInputMessage(active_offer);
                                        if ($('.scene-style').length > 0) {
                                            calcMoreActionComment($('.scene-style .mmessage'));
                                        } else if ($('.main-talk-room').length > 0) {
                                            calcMoreActionComment($('.main-talk-room .mmessage'));
                                        }
                                        calcPositionDropdownComment();
                                        calcPositionDropdownComment2();
                                        calcMoreActionComment($('.prdt .mmessage'));
                                        clickBtnActionMessage()
                                    }
                                });
                            }
                        }, 100);
                    } else {
                        data.append('list_file_id', Object.keys(list_file_id));
                        data.append('list_folder_id', Object.values(list_folder_id));
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            data: data,
                            url: url_page,
                            beforeSend: function (data) {
                                // toastr.info('アップロード中…');
                            },
                            success: function (data) {
                                // toastr.success("完了しました。");

                                sScrollbarBottom();
                            },
                            complete: function () {
                                mzdrop["sended"] = true;
                                mzdrop.removeAllFiles();
                                list_file_id = {};
                                list_files_folders = {};
                                list_folder_id = [];
                                button_send.removeClass('is-sending');
                                resetInputMessage(active_offer);
                                list_folder_name = [];
                                list_file_name = [];
                                $('.mcomment-bottom').removeClass('disabled-mcomment')
                                showLastCommentSceneDetailSP();
                                if ($('.scene-style').length > 0) {
                                    calcMoreActionComment($('.scene-style .mmessage'));
                                } else if ($('.main-talk-room').length > 0) {
                                    calcMoreActionComment($('.main-talk-room .mmessage'));
                                }
                                calcPositionDropdownComment();
                                calcPositionDropdownComment2();
                                calcMoreActionComment($('.prdt .mmessage'));
                                clickBtnActionMessage()
                            }
                        });
                    }

                } else if (Object.keys(list_file_id).length) {
                    data.append('list_file_id', Object.keys(list_file_id));
                    data.append('list_folder_id', Object.values(list_folder_id));
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        beforeSend: function (data) {
                            // toastr.info('アップロード中…');
                        },
                        success: function (data) {
                            // toastr.success("完了しました。");

                            sScrollbarBottom();
                            $(document).find('.mattach-previews').empty();
                        },
                        complete: function () {
                            mzdrop["sended"] = true;
                            mzdrop.removeAllFiles();
                            list_file_id = {};
                            list_files_folders = {};
                            list_folder_id = [];
                            button_send.removeClass('is-sending');
                            resetInputMessage(active_offer);
                            list_folder_name = [];
                            list_file_name = [];
                            $('.mcomment-bottom').removeClass('disabled-mcomment')
                            if ($('.scene-style').length > 0) {
                                calcMoreActionComment($('.scene-style .mmessage'));
                            } else if ($('.main-talk-room').length > 0) {
                                calcMoreActionComment($('.main-talk-room .mmessage'));
                            }
                            calcPositionDropdownComment();
                            calcPositionDropdownComment2();
                            calcMoreActionComment($('.prdt .mmessage'));
                            clickBtnActionMessage()
                        }
                    });
                } else if (messageContent.trim() !== '') {
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        success: function (data) {
                            sScrollbarBottom();
                        },
                        complete: function () {
                            button_send.removeClass('is-sending');
                            resetInputMessage(active_offer);
                            list_folder_name = [];
                            list_file_name = [];
                            $('.mcomment-bottom').removeClass('disabled-mcomment');
                            if ($('.scene-style').length > 0) {
                                calcMoreActionComment($('.scene-style .mmessage'));
                            } else if ($('.main-talk-room').length > 0) {
                                calcMoreActionComment($('.main-talk-room .mmessage'));
                            }
                            calcPositionDropdownComment();
                            calcPositionDropdownComment2();
                            calcMoreActionComment($('.prdt .mmessage'));
                            clickBtnActionMessage()
                        }
                    });
                } else if (messageContent.trim() === '') {
                    button_send.removeClass('is-sending');
                    resetInputMessage(active_offer);
                    list_folder_name = [];
                    list_file_name = [];
                    $('.mcomment-bottom').removeClass('disabled-mcomment')
                }
            }
            $(document).find('.mcomment-input-text').attr('type_input', 'input');
            list_file_id = {};
            list_folder_id = {};
            valInput = '';
            doneTyping('');
            setTimeout(function () {
                active_offer.find('.mcomment-input-placeholder').show();

                if (!$('.mcomment-top').hasClass('comment-top-area')) {
                    active_offer.find('.mcomment-top').hide();
                }
                if (!!$('.btn-tutorial-sp')) {
                    $('.btn-tutorial-sp').css('bottom', `${2 * Math.max(document.documentElement.clientWidth, window.innerWidth || 0) / 100}px`)
                }
            }, 100);
            if ((messenger_page === 'messenger_artist' || messenger_page === 'top_page') && !$(".block-navigation-bar").hasClass("hide")) {
                $("html, body").animate({ scrollTop: $('.mcommment').height() + 200 }, 1000);
            }
            should_scroll = false;
            $('.mcomment-input-text.mcomment-autoExpand').css('height', '');
        }
    });
}

function editMessage(active_offer) {
    let url_page = '';
    active_offer.on('click', '.mcomment-send.active.input-editing', function (e) {
        e.stopPropagation();
        e.preventDefault();
        let button_dom = $(this);
        should_scroll = false;
        if (!$(this).hasClass('is-sending')) {
            button_dom.addClass('is-sending');
            let offer_id = $(this).parents('.maction').data('offer');
            let message_id = active_offer.find('.mmessage.editing').attr('data-message-id');
            if (message_id) {
                let message_content = active_offer.find('.mcommment .mcomment-input-text').val();
                if (message_content.trim() === '') {
                    message_content = '';
                }
                let pin_time = '';
                let scene_title_id = '';
                let scene_id = '';
                let has_pin = false;

                if (!offer_id) {
                    scene_title_id = $(this).parents('.pd-scene-title-detail').attr('data-scene-title-id');
                    active_offer = $('.pd-scene-title-detail[data-scene-title-id=' + scene_title_id + ']');

                    if ($(this).parents('.mcommment').find('.mcomment-pin').hasClass('active')) {
                        has_pin = true;
                        scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                        pin_time = $(this).parents('.mcommment').find('.mcomment-input-title').eq(0).text();
                        if (!pin_time.length) {
                            if ($(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe')) {
                                pin_time = '';
                            } else {
                                pin_time = '00:00';
                            }
                        }
                    }
                }

                if ($('.project-tab-product-comment').hasClass('active')) {
                    active_offer = $('.pd-section--detail.pd-product-comment');
                }

                data = new FormData();
                if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
                    url_page = '/messenger/update_offer_message';
                    data.append('offer_id', $('.maction').attr('data-offer'));
                } else if (messenger_page === 'top_page') {
                    url_page = '/top/update_scene_comment';

                    if ($('.project-tab-product-comment').hasClass('active')) {
                        data.append('type', 'project');
                        let project_id = $('.project-item.active').attr('data-project-id');
                        data.append('project_id', project_id);
                    } else {
                        data.append('pin_time', pin_time);
                        data.append('scene_title_id', scene_title_id);
                        data.append('scene_id', scene_id);
                        data.append('has_pin', has_pin);
                    }
                }
                data.append('message_id', message_id);
                data.append('message_content', message_content);
                data.append('file', mzdrop.files[0]);
                data.append('list_file_remove', list_file_remove);
                let file_loaded = Object.values(list_file_id);
                let file_loading = mzdrop.files.length - file_loaded.length;
                let number_files = $(this).parents('.mcomment-message').find('.mattach-template').length;
                if (!number_files && !message_content) {
                    $(`.mmessage--sent[data-message-id='${message_id}']`).find('.mmessage-delete').trigger("click");
                    button_dom.removeClass('is-sending');
                } else {
                    if (file_loading) {
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', '2%');
                        var waiting_file_loading = setInterval(function () {
                            let current_file_loaded = Object.values(list_file_id);
                            let current_file_loading = mzdrop.files.length - current_file_loaded.length;
                            let progress = getProgressUploaded();
                            $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                            if (!current_file_loading) {
                                data.append('list_file_id', Object.keys(list_file_id));
                                data.append('list_folder_id', Object.values(list_folder_id));
                                clearInterval(waiting_file_loading);
                                $.ajax({
                                    type: "POST",
                                    contentType: false,
                                    processData: false,
                                    cache: false,
                                    url: url_page,
                                    async: false,
                                    data: data,
                                    success: function (data) {
                                        $('.upload-button-wrapper .fill .process').css('width', '100%');
                                        setTimeout(function () {
                                            // toastr.success(data.success_message);
                                            $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                                        }, 1000);
                                        setTimeout(function () {
                                            $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                                            $('.upload-button-wrapper .fill .process').css('width', '0');
                                        }, 2000);
                                    },
                                    complete: function () {
                                        mzdrop["sended"] = true;
                                        mzdrop.removeAllFiles();
                                        list_file_id = {};
                                        list_files_folders = {};
                                        list_folder_id = [];
                                        resetInputMessage(active_offer);
                                        button_dom.removeClass('is-sending');
                                        resetFormContract();
                                        clickBtnActionMessage()
                                    }
                                });
                                $("html, body").animate({ scrollTop: $('.mcommment').height() + 200 }, 1000);
                                setTimeout(function () {
                                    active_offer.find('.mcomment-input-placeholder').show();
                                    active_offer.find('.mcomment-top').hide();
                                }, 100)
                            }
                        }, 100);
                    } else {
                        data.append('list_file_id', Object.keys(list_file_id));
                        data.append('list_folder_id', Object.values(list_folder_id));
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            url: url_page,
                            async: false,
                            data: data,
                            success: function (data) {
                            },
                            complete: function () {
                                mzdrop["sended"] = true;
                                mzdrop.removeAllFiles();
                                list_file_id = {};
                                list_folder_id = [];
                                list_files_folders = {};
                                resetInputMessage(active_offer);
                                button_dom.removeClass('is-sending');
                                resetFormContract();
                                clickBtnActionMessage()
                            }
                        });
                        if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
                            $("html, body").animate({ scrollTop: $('.mcommment').height() + 200 }, 1000);
                        }
                        setTimeout(function () {
                            active_offer.find('.mcomment-input-placeholder').show();
                            if (!$('.prdt').length > 0) {
                                active_offer.find('.mcomment-top').hide();
                                active_offer.find('.mcomment-attached').css('padding', 0);
                            }
                        }, 100)
                    }
                }
            }
            setTimeout(() => {
                should_scroll = true;
            }, 1500);
        }
        $('.mcomment-input-text.mcomment-autoExpand').css('height', '');
        clickBtnActionMessage()
        calcMoreActionComment($('.prdt .mmessage'))
        calcPositionDropdownComment2();
    });
}

function resetInputMessage(active_offer) {
    list_file_remove = [];
    mzdrop["sended"] = true;
    mzdrop.removeAllFiles();
    list_file_id = {};
    list_folder_name = [];
    list_file_name = [];
    active_offer.find('.mcommment .mcomment-input-text').val('');
    // active_offer.find('.mcommment .mcomment-input-text').height('20px');
    $('.maction .mcommment-file').remove();
    active_offer.find('.mcomment-send.active').removeClass('active');
    active_offer.find('.mmessage').removeClass('editing');
    active_offer.find('.mmessage').removeClass('reply');
    active_offer.find('.mcomment-send').removeClass('input-editing');
    active_offer.find('.mcommment').removeClass('border-editing');
    active_offer.find('.btn-remove-msg').removeClass('d-block')
    active_offer.find('.block-remove-msg-editing').addClass('d-none')
    if ($(document).width() > maxWidthIpadDevice) {
        $('.prdt .mmessage-list').removeClass('pd-main-message')
    }
    active_offer.find('.mmessage-reply.active').removeClass('active');
    active_offer.find('.mcomment-pin').removeClass('active hide');
    active_offer.find('.mcomment-input').removeClass('is-reply').removeClass('is-pin');
    $('.cscene-vertical').removeClass('active');
    if ($('.prdt').length > 0) {
        let commentInputPrdt = active_offer.find('.mcommment .mcomment-input-text');
        commentInputPrdt.height('30px')
        commentInputPrdt.css('overflow', 'hidden')

    }
}

function searchOffer() {
    $(document).on('keydown', '#pm-search', function (e) {
        if ((e.key === 'Enter' || e.keyCode === 13) && $('.project-tab-messenger.active').length) {
            let keyword = $(this).val();
            if (keyword !== '') {
                let project_id = $('.project-item.active').attr('data-project-id');
                $.ajax({
                    type: "GET",
                    url: '/ajax/search_offer_creator',
                    data: {
                        'keyword': keyword,
                        'project_id': project_id
                    },
                    beforeSend: function () {
                        $('.list--offers-search').empty();
                    },
                    beforeSend: function (xhr, settings) {
                        xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    },
                    success: function (response) {
                        removeOfferActive();
                        $('.list--offers-project').addClass('hide');
                        $('.list--offers-search').empty();
                        $('.list--offers-search').append($(`<div class="count--list-offers-search">検索結果（${response.result_offer}件） </div>`));
                        $('.list--offers-search').append(response.list_offers_search_html);
                        activeOffer($(".mlist .list--offers-search"));
                    },
                    error: function (response) {
                        toastr.warning("エラーが発生しました");
                    },
                    complete: function () {
                        setWidthInput($('.mcommment[id="comment-input-1"]'));
                    }
                });
            } else {
                removeSearchOffer();
                setWidthInput($('.mcommment[id="comment-input-1"]'));
            }
        }
    });
}

function removeSearchOffer() {
    $('.list--offers-project').removeClass('hide');
    $('.list--offers-search').empty();
    activeOffer($(".mlist .list--offers-project"));
}

function removeOfferActive() {
    $('.messenger-detail .mcolumn-back').trigger('click');
    $('.mcolumn.mcolumn--main').empty();
    $('.mcolumn.mcolumn--right').empty();
    $('.mscene.mitem').removeClass('mactive');
}

function activeOffer(listOffer) {
    if (is_pc === 'True' && listOffer.find('.list-offer').children('.mitem').length > 0) {
        listOffer.find('.list-offer').children('.mitem').eq(0).trigger('click');
    } else {
        removeOfferActive();
    }
}

function showInforMessage() {
    $(document).on('mouseenter mouseleave', '.mmessage', function (e) {
        let parent = $(this).parents('.mmessage-list');
        if (!parent.hasClass('view_only')) {
            if ($(this).parents('.martist').length && !$('.scene-title__action .button-edit_offer').length) {
                $('.button-edit_offer.message-first__message').remove();
            }
            removeButtonDelete();
            let message_active = $(this);

            if ($(this).hasClass('mmessage--sent') || !$('input[name="switch-checkbox-comment"]').is(':checked')) {
                message_active.find('.mmessage-status').toggleClass('clicked', e.type === 'mouseenter');
                if (message_active.height() <= 75) {
                    if (!!$('.message-actions-container').children().length && !!message_active.find('.message-actions-container .mmessage-action').children().length) {
                        message_active.find('.mmessage-status').toggleClass('hide', e.type === 'mouseenter');
                    }
                } else {
                    message_active.css('cursor', 'default');
                    message_active.find('.mmessage-info').css({ height: message_active.find('.mmessage-main').height() })
                }
                message_active.find('.mmessage-action').toggleClass('show-action-hover', e.type === 'mouseenter');
                if (e.type === 'mouseleave') {
                    message_active.find('.mmessage-action').addClass('hide');
                    message_active.find('.mmessage-status').removeClass('hide');
                }
            }
        }
    });
}

function checkMessengerOwner() {
    return $('.pbanner-tab-message.active').length && user_role !== 'admin'
}

function removeButtonDelete() {
    if (file_contract) {
        let messageContractDom = $('.mmessenger[data-file-id=' + file_contract + ']').parents('.mmessage');
        messageContractDom.find('.mmessage-delete').remove()
    }
    if (file_bill) {
        let messageBillDom = $('.mmessenger[data-file-id=' + file_bill + ']').parents('.mmessage');
        messageBillDom.find('.mmessage-delete').remove()
    }
}

function appHeight() {
    if (!is_pc) {
        document.style.overflow = 'hidden';
        $('body')[0].style.overflow = 'hidden';
        $('main')[0].style.overflow = 'hidden';
        $('.mcontainer')[0].style.overflow = 'hidden';
    }
    const doc = document.documentElement;
    doc.style.setProperty('--app-height', `${window.innerHeight}px`)
    doc.style.height = 'auto';
}

function removeDuplicatedDate() {
    let last_date;
    let date;
    let list_item = [];
    let count = 0;
    $('.tfile-item-time').removeClass('hide');
    if ($('.pd-scene-title-detail').length) {
        $('.tfile-item-time').each(function (i, e) {
            if ($(this).hasClass('item-scene')) {
                count = $('.item-scene[data-time="' + e.innerHTML + '"]').length
            }
            if (count > 0) {
                if (last_date === e.innerHTML) {
                    list_item.push($(this));
                }
                if (list_item.length >= count) {
                    let sort_by_variation = function (a, b) {
                        let a_value = a.parentElement.getAttribute('data-variation-index');
                        let b_value = b.parentElement.getAttribute('data-variation-index');
                        if (a_value === b_value) {
                            return -1
                        } else {
                            return a_value.localeCompare(b_value);
                        }
                    };
                    let list = $('.item-scene[data-time="' + e.innerHTML + '"]').get();
                    list.sort(sort_by_variation);

                    for (let i = 1; i <= list.length - 1; i++) {
                        console.log(i + " " + list[i].parentElement.getAttribute('data-variation-index'))
                        $(list[i].parentNode).insertBefore($(list[i - 1].parentNode));
                    }
                    list_item = [];
                } else if (list_item.length === 0) {
                    list_item.push(e)
                }
                last_date = e.innerHTML;
            }
        })
    }

    $('.tfile-item-time').each(function (i, e) {
        if (date === e.innerHTML) {
            $(e).addClass('hide');
        }
        date = e.innerHTML;
    })
    $('.tfile-item-offer-white').find('.s-file').removeClass('s-file--gray');
    $('.tfile-item-offer-gray').find('.s-file').addClass('s-file--gray');
}

function traverseFileTree(item, name, flag, folder_id, page) {
    if (item.isFile && name) {
        if (list_files_folders[name]) {
            list_files_folders[name].push(item.name);
        } else {
            list_files_folders[name] = [item.name]
        }
    } else if (item.isDirectory) {
        let current_path = name;
        if (item.name.trim().length > 128) {
            is_exceeded_length = true;
        }
        if (flag) {
            current_path += item.name + '/';
        }
        var dirReader = item.createReader();
        let offer_id;
        if ($('.mitem.mactive').length) {
            offer_id = $('.mitem.mactive').attr('data-offer')
        }
        $.ajax({
            type: "POST",
            data: {
                'parent_id': folder_id,
                'name': item.name,
                'full_path': 'storage/' + current_path,
                'message_type': page,
                'offer_id': offer_id
            },
            async: false,
            url: '/upload/create_folder',
            success: function (data) {
                console.log('folder created')
                let folder_pk = data.id;
                list_temp_folder_id[current_path] = folder_pk;
                dirReader.readEntries(function (entries) {
                    for (let i = 0; i < entries.length; i++) {
                        traverseFileTree(entries[i], current_path, 1, folder_pk, page);
                    }
                });
            },
        });
    }
}

function uploadFileS3(file, file_dom, path) {
    file_dom.find('.determinate').css('width', '0%');
    let page = getPage(file_dom);
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage" + path + "/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            let key_file = data.presigned_post.fields["key"];
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s')
                    file_dom.find('.determinate').css('width', percentComplete);
                    if (file_dom.length) {
                        if (!file_dom.attr("data-total")) {
                            file_dom.attr("data-total", evt.total);
                        }
                        file_dom.attr("data-loaded", evt.loaded);
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        let data = new FormData();
                        let folder_id = "";
                        if (path) {
                            folder_id = list_folder_id[path];
                            if (path.includes("/")) {
                                path = path.slice(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            } else {
                                path = path.slice(0, path.indexOf("---"));
                            }
                            path += "/";
                        }
                        data.append('file', file);
                        data.append('message_type', page);
                        data.append('key_file', key_file);
                        data.append('file_name', file.name);
                        data.append('folder_id', folder_id);
                        if ($('.mitem.mactive').length) {
                            data.append('offer_id', $('.mitem.mactive').attr('data-offer'));
                        }
                        $.ajax({
                            type: "POST",
                            data: data,
                            contentType: false,
                            processData: false,
                            url: '/upload/create_file',
                            success: function (data) {
                                Object.assign(list_file_id, data);
                                file_dom.find('.determinate').css('width', '100%');
                                file_dom.find('.determinate').attr('data');
                                $('.mcomment-input-text').attr('type_input', 'input');
                                setTimeout(async () => {
                                    if ($(document).find('textarea.mcomment-input-text').length) {
                                        await doneTyping($(document).find('textarea.mcomment-input-text').val());
                                    }
                                }, 2000);
                                if (data.status === 'error') {
                                    toastr.error('ファイルをアップロード失敗しました。');
                                }
                            },
                            error: function (e) {
                                toastr.error('ファイルをアップロード失敗しました。');
                            }
                        });
                    }
                    else {
                        alert("Could not upload file.");
                    }
                }
            };
            xhr.send(postData);
        }
    })
}

function getPage(target) {
    let page = ''
    if (target.parents('.project-tab-product-comment').length) {
        page = 'product_comment'
    } else if (target.parents('.pd-scene-title-detail').length) {
        page = 'scene_comment'
    } else if (user_role !== 'admin') {
        page = 'message_owner'
    } else if (target.parents('.martist').length) {
        page = 'message'
    }
    return page
}

function projectFolder() {
    $(document).on('click', ".hasSub", function () {
        $(this).parent().toggleClass("subactivated");
        $(this).parent().children("ul:first").toggle();
        let sub_item = $(this);
        if ($(this).find("i").hasClass("glyphicon-folder-open")) {
            $(this).find("i").removeClass("glyphicon-folder-open").addClass("glyphicon-folder-close");
        } else {
            $(this).find("i").removeClass("glyphicon-folder-close").addClass("glyphicon-folder-open");
        }
    });

    $(".menufilter").keyup(function () {
        var searchTerm = $(".menufilter").val();
        var listItem = $(".foldertreeview").children("li");
        var searchSplit = searchTerm.replace(/ /g, "'):containsi('");

        $.extend($.expr[":"], {
            containsi: function (elem, i, match, array) {
                return (elem.textContent || elem.innerText || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
            },
        });

        $(".foldertreeview li")
            .not(":containsi('" + searchSplit + "')")
            .each(function (e) {
                $(this).hide();
            });

        $(".foldertreeview li:containsi('" + searchSplit + "')").each(function (e) {
            $(this).show();
        });
    });

    $(document).on('click', '.sfolder, .messager-folder', function (e) {
        $('#folderModal').modal('show');
        let type_message = '';
        let message_id;
        if ($(this).parents('.pd-product-comment').length > 0) {
            type_message = 'project'
        } else if ($(this).parents('.pd-scene-title-detail').length > 0) {
            type_message = 'scene'
        } else if (user_role !== 'admin') {
            type_message = 'message_owner'
        } else if ($(this).parents('.martist').length > 0) {
            type_message = 'message_artist'
        }

        $('#folderModal').attr('data-type-message', type_message);
        if ($(this).hasClass('messager-folder')) {
            folder_id = $(this).attr('data-file-id');
        } else {
            folder_id = $(this).attr('data-folder-id');
        }
        let offer_id;
        if ($('.mitem.mactive').length) {
            offer_id = $('.mitem.mactive').attr('data-offer')
        }
        $.ajax({
            type: "GET",
            url: "/top/get_item_in_message",
            data: {
                "folder_id": folder_id,
                'type_message': type_message,
                'offer_id': offer_id
            },
            success: function (data) {
                $('#folderModal').find('.group-tree-modal').empty();
                $('#folderModal').find('.group-tree-modal').append(data.html);
            }
        })
    });

    $('#folderModal').on('hidden.bs.modal', function () {
        $('.menufilter.form-control').val('');
    })

}

function filterOffer() {
    $(document).on('click', '.text-left-inhome', function () {
        if ($('.switch-dm-txt.text-right').hasClass('navbar-active')) {
            let filter_offer = 'waiting';
            $('.search-delete').trigger('click');
            let project_id = $('.project-item.active').attr('data-project-id');
            if (window.localStorage.getItem("performance") || !window.location.search.includes("tab=product-comment")) {
                const url = window.location.search;
                const params = new URLSearchParams(url);
                const offer = params.get('offer');
                get_messenger_artist(project_id, offer, filter_offer);
            }
        }
    });

    $(document).on('click', '.text-right-inhome', function () {
        if (!$('.switch-dm-txt.text-right').hasClass('navbar-active')) {
            let filter_offer = 'processing';
            $('.search-delete').trigger('click');
            let project_id = $('.project-item.active').attr('data-project-id');
            if (window.localStorage.getItem("performance") || !window.location.search.includes("tab=product-comment")) {
                const url = window.location.search;
                const params = new URLSearchParams(url);
                const offer = params.get('offer');
                get_messenger_artist(project_id, offer, filter_offer);
            }
        }
    });

    $(document).on('click', '.messenger-add', function () {
        clickedOffer = true;
        $('.navigation-top-app-bar').addClass('hide-top-bar');
        let project_id = $('.project-item.active').attr('data-project-id');
        $('.search-delete').trigger('click');
        get_messenger_artist(project_id, null, 'search');
        closeNav();
    });
    $(document).on('click', '.go__to-messenger', function () {
        let project_id = $('.project-item.active').attr('data-project-id');
        get_messenger_artist(project_id, null, 'waiting');
    });
}

function actionMessengerArtist(infor_offer, active_offer) {
    // check back
    infor_offer.off('click').on('click', '.accept_production_file', function () {
        let button_dom = $(this);
        let offer_id = button_dom.parents('.mcolumn-content').attr('data-offer');
        bootbox.confirm({
            message: " 検収しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn btn-success btn--primary'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn btn-danger btn--tertiary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id,
                            'status': 4
                        },
                        url: '/messenger/update_status_offer',
                        success: function (data) {
                            console.log('ok')
                        }
                    });
                }
            }
        });
    });

    $('#minfo-accept-policy, #minfo-accept-policy2').on('click', function () {
        if ($(this).is(':checked')) {
            $('#minfo-accept-policy, #minfo-accept-policy2').prop('checked', true);
            $('.mmessenger-director__item-action-accept').removeClass('disabled');
        } else {
            $('#minfo-accept-policy, #minfo-accept-policy2').prop('checked', false);
            $('.mmessenger-director__item-action-accept').addClass('disabled');
        }
    });

    // review
    infor_offer.on('click', '.btn-icon-unlike, .btn-icon-like, .btn-icon-normal', function () {
        let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
        let value = $(this).data('value');
        $.ajax({
            type: "POST",
            data: {
                'offer_id': offer_id,
                'value': value
            },
            url: '/messenger/update_review_offer',
            success: function (data) {
                // toastr.success('評価しました。');
                infor_offer.find('.minfo-contract').html(data.infor_html);
                infor_offer.find('.minfo-rating').remove();
            }
        });
    });

    // see contract
    infor_offer.on('click', '.see-contract', function () {
        let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
        get_content_modal_contract(offer_id)
    });

    active_offer.on('click', '.see-contract', function () {
        let offer_id = $(this).parents('.mmessage-confirm').attr('data-offer');
        get_content_modal_contract(offer_id)
    });

    editFormOffer();
}

function addBorderPreview(isPin = false, sceneCurrent = null) {
    let widthOnSP = 767;
    let slider = $('.cscene-horizontal.slick-initialized.slick-slider');
    let mainEl = $('main.owner-top');
    let slickActive = $('.cscene__version.slick-slide.slick-current.slick-active');
    let commentBottom = $('.mcomment-bottom')
    if ($(window).width() > widthOnSP) {
        let preview = slickActive.children().children();
        let previewActive = slickActive.children();
        let spaceHeightPreview, maxWidthViewPort;
        if (isPin) {
            if (sceneCurrent) {
                slickActive = sceneCurrent;
            }
            mainEl.addClass('pin-preview')
            spaceHeightPreview = slider.height() - paddingHeightActivePreview;
            maxWidthViewPort = slider.width() - paddingWidthActivePreview;
            commentBottom.addClass('padding-pin')
            pinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive);
        } else {
            mainEl.removeClass('pin-preview')
            spaceHeightPreview = slider.outerHeight();
            maxWidthViewPort = slider.outerWidth();
            commentBottom.removeClass('padding-pin')
            removePinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort);
        }
    } else {
        let slickActive = $('.cscene__version.slick-slide.slick-current.slick-active');
        let preview = slickActive.children().children();
        let previewActive = slickActive.children();
        let spaceHeightPreview, maxWidthViewPort;
        if (isPin) {
            mainEl.addClass('pin-preview')
            spaceHeightPreview = slider.height() - paddingHeightActivePreview;
            maxWidthViewPort = slider.width() - paddingWidthActivePreview;
            commentBottom.addClass('padding-pin')
            pinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive);
        } else {
            mainEl.removeClass('pin-preview')
            spaceHeightPreview = slider.outerHeight();
            maxWidthViewPort = slider.outerWidth();
            commentBottom.removeClass('padding-pin')
            removePinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort);
        }
    }
}

function removePinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort) {
    let slickSlider = $('.cscene-horizontal.slick-initialized.slick-slider')
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    previewActive.removeClass('space-top')
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.removeClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'transform': 'translateY(-50%)'
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        slickSlider.removeClass('active-scene')
    }
}

function pinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive) {
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    let slickSlider = $('.cscene-horizontal.slick-initialized.slick-slider');
    slickActive.addClass('clear-border');
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview + paddingWidthActivePreview}px`,
            'height': `${heightPreview + paddingHeightActivePreview}px`,
        })
        previewActive.addClass('active-scene')
        slickSlider.removeClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        slickSlider.addClass('active-scene')
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'transform': 'translateY(-51%)'
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        previewActive.removeClass('active-scene')
        previewActive.addClass('space-top')
    }
}

function pinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive) {
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    let slickList = previewActive.parent().parent().parent();
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview + paddingWidthActivePreview}px`,
            'height': `${heightPreview + paddingHeightActivePreview}px`,
        })
        previewActive.addClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        slickList.addClass('active-scene')
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
    }
}


function removePinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort) {
    let slickList = previewActive.parent().parent().parent();
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.removeClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        slickList.removeClass('active-scene')
    }
}

function showLastCommentSceneDetailSP() {
    const widthViewPortSP = 767;
    if ($(document).width() <= widthViewPortSP) {
        const listComment = $('.message-list-new .mmessage.clicked')
        listComment.removeClass('load-lasted-message');
        listComment.last().addClass('load-lasted-message')
    }
}

function getDownloadFileScene() {
    $(document)
        .off('click', '.block-download-file .scene-file-download')
        .on('click', '.block-download-file .scene-file-download', function (e) {
            let data = new FormData();
            e.stopPropagation();
            let file_id;
            if ($(this).parents('.tfile-producttion-file').length > 0) {
                data.append('production_file', 'production_file');
                file_id = $(this).parents('.tfile-producttion-file').attr('data-scene-title-id')
            } else {
                file_id = $(this).parents('.block-download-file').attr('data-file-id');
            }
            data.append('file_id', file_id);
            downloadFile(data, this)
        })

    $(document).on('click', '.tfile-info.btn-download-file', function (e) {
        let data = new FormData();
        e.stopPropagation();
        let file_id;
        if ($(this).parents('.tfile-infor').length > 0) {
            file_id = $(this).parents('.tfile-infor').attr('data-file-id');
        }
        data.append('file_id', file_id);
        downloadFile(data, this)
    })
}

function calcPositionDropdownComment() {
    let listMessageSent = $('.scene-style .mmessage--sent');
    let listMessageReceived = $('.scene-style .mmessage--received');
    const widthDropdownCmt = 200;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            dropdownMenu.css('left', `-${resultLeftDropdown + 10}px`)
        }
    })
    const messageListWidth = $('.pd-comment').width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            dropdownMenu.css('left', `-${resultLeftDropdown + 10}px`)
        } else {
            let resultLongReceivedCmt = messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find('.mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment');
                dropdownMenuReceived.css('left', `-${widthDropdownCmt - resultLongReceivedCmt}px`)
            }
        }
    })
}

function calcMoreActionComment(el) {
    if (el) {
        let listMessage = el;
        const heightShowMoreAction = 10;
        listMessage.each(function () {
            let heightMessageMain = $(this).find('.mmessage-main').outerHeight();
            let messageInfo = $(this).find('.mmessage-info');
            let dropdownCommentNew = $(this).find('.dropdown-comment-new');
            messageInfo.css('height', heightMessageMain)
            let messageInfoContainer = messageInfo.find('.message-info-container');
            if ((heightMessageMain / 2) - heightShowMoreAction <= messageInfoContainer.height()) {
                messageInfoContainer.css('height', 'auto')
                dropdownCommentNew.css('height', `${heightMessageMain - messageInfoContainer.height()}`)
                if (heightMessageMain - messageInfoContainer.height() >= (heightMessageMain / 2) + (heightShowMoreAction / 2)) {
                    dropdownCommentNew.css('height', 'calc(50% + 5px)')
                    messageInfoContainer.css('height', 'calc(50% - 5px)')
                }
            } else {
                dropdownCommentNew.css('height', 'calc(50% + 5px)')
                messageInfoContainer.css('height', 'calc(50% - 5px)')
            }
        })
    }
}

function calcPositionDropdownComment2() {
    let listMessageSent = $('.prdt .mmessage--sent');
    let listMessageReceived = $('.prdt .mmessage--received');
    const widthDropdownCmt = 180;
    const paddingDropdown = 34;
    const spaceLeftScreen = 50;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        let numberLeftDropdown = 0;
        if (resultLeftDropdown > 0) {
            numberLeftDropdown = -resultLeftDropdown - paddingDropdown;
        }
        let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
        if ($(window).width() < max_width_sp_device) {
            if (dropdownMenu.length > 0) {
                dropdownMenu.addClass('top-modal');
            }
        }
        dropdownMenu.css('left', `${numberLeftDropdown}px`)
    })
    const messageListWidth = $('.pd-comment').width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            let left_dropdown = resultLeftDropdown - paddingDropdown;
            if (left_dropdown > spaceLeftScreen) {
                left_dropdown = spaceLeftScreen;
            }
            dropdownMenu.css('left', `-${left_dropdown}px`)
        } else {
            let resultLongReceivedCmt = messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find('.mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment');
                dropdownMenuReceived.css('left', `-${widthDropdownCmt - resultLongReceivedCmt}px`)
            }
        }
    })
}

$(window).scroll(function () {
    if ($(window).scrollTop() + $(window).height() === $(document).height()) {
        // styleBtnPlanApprove(true)
        $('.prdt .dm-block-message .mcommment').css('bottom', '32px')
    } else {
        // styleBtnPlanApprove()
        $('.prdt .dm-block-message .mcommment').css('bottom', 0)
    }
});

function scrollCommentBar() {
    /*let lastScrollTop = 0;
    let footerComment = $('.footer-comment-block');
    let navigationBar = $('.block-navigation-bar');
    let newBannerProject = $('.new-banner-project');
    let sheader = $('.sheader');
    let navigationTopAppBar = $('.navigation-top-app-bar');
    let heightNavigationBar = 0;
    if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
        heightNavigationBar = navigationBar.outerHeight();
    }
    let heightNewBannerProject = newBannerProject.outerHeight();
    let heightNavigationTopAppBar = navigationTopAppBar.outerHeight();
    let heightSheader = sheader.outerHeight();
    let totalHeightBannerTopNav = heightNewBannerProject + heightNavigationTopAppBar;
    let totalHeightSpaceTop = heightNewBannerProject + heightNavigationTopAppBar + heightSheader;
    const space_margin_top = '40px';
    navigationBar.css('transition', 'bottom 0.2s')
    footerComment.css('transition', 'bottom 0.2s')
    btnAddOffer.css('transition', 'bottom 0.2s')
    $(window).scroll(function (event) {
        let messengerContent = $('.prdt .messenger-detail')
        let st = $(this).scrollTop();
        if (st > lastScrollTop) {
                console.log('Scroll xuống scrollCommentBar');
                messengerContent.css('height', `calc(100vh - ${totalHeightBannerTopNav}px)`);
                if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
                    footerComment.css('bottom', '0');
                    btnAddOffer.css('bottom', '0');
                    $('#mColumnWrap .mcolumn-content').css('height', '82vh')
                }
                navigationBar.css('bottom', `-${heightNavigationBar}px`)
                $('.column-list-offer').css('margin-top', space_margin_top)
                $('.mcolumn--right').css('margin-top', space_margin_top)
        } else {
                console.log('Scroll lên scrollCommentBar');
                if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
                    footerComment.css('bottom', `${heightNavigationBar}px`);
                    btnAddOffer.css('bottom', `${heightNavigationBar}px`);
                    // $('#mColumnWrap .mcolumn-content').css('height', '65vh')
                }
                navigationBar.css('bottom', '0')
                messengerContent.css('height', `calc(100vh - ${totalHeightSpaceTop}px)`);
                $('.column-list-offer').css('margin-top', '0')
                $('.mcolumn--right').css('margin-top', '0')
        }
        lastScrollTop = st;
    });*/
}

function calcHeightScheduleModal() {
    let sheader = $('.sheader');
    let scheduleBlock = $('#schedulaCalendarModal .sc-block')
    let topSheader = parseInt(sheader.css('top').replace('px', ''));
    if (topSheader >= 0) {
        //scroll up
        scheduleBlock.removeClass('height-schedule-header');
        scheduleBlock.addClass('height-schedule-show-header');
    } else {
        //scroll down
        scheduleBlock.removeClass('height-schedule-show-header');
        scheduleBlock.addClass('height-schedule-header');
    }
}
$(document).ready(function () {
    let navbar_active_class = 'navbar-active';
    $('.switch-checkbox-tr').on('change', function () {
        let switch_right = $('.switch-talk-room.text-right');
        let switch_left = $('.switch-talk-room.text-left');
        let checked = $(this).is(':checked');
        console.log('checked: ', checked)
        let orderMonthy = $('input#order-monthy');

        should_scroll = false;
        // orderMonthy.parent('.form-group-new').find('.switch-talk-room').removeClass('navbar-active')
        if (checked) {
            // orderMonthy.parent('.form-group-new').find('.text-right').addClass('navbar-active')
            $('.pd-product-comment').removeClass('show-comment-unresolved').addClass('show-comment-all')
            switch_left.removeClass(navbar_active_class);
            switch_right.addClass(navbar_active_class);
        } else {
            // orderMonthy.parent('.form-group-new').find('.text-left').addClass('navbar-active')
            $('.pd-product-comment').removeClass('show-comment-all').addClass('show-comment-unresolved')
            switch_right.removeClass(navbar_active_class);
            switch_left.addClass(navbar_active_class);
        }
    });
    $('.switch-talk-room').on('click', function (e) {
        if ($(this).hasClass('navbar-active')) {
            e.preventDefault();
            return;
        }
        should_scroll = false;
    })

    $('.switch-dm-txt').on('click', function (e) {
        console.log('click switch dm')
        if ($(this).hasClass('navbar-active')) {
            e.preventDefault();
            return;
        }
        should_scroll = false;
        $('.switch-dm').addClass('tab-disabled');
        $('.switch-dm-txt').removeClass('navbar-active');
        $(this).addClass('navbar-active');
        // setTimeout(() => {
        //     should_scroll = true; 
        // }, 1500);
    })
    if (is_pc === 'True') {
        window.addEventListener("wheel", (event, el) => {
            if (checkBlockScroll(event)) {
                if (event.deltaY > 0) {
                    //wheel down
                    setPositionTopAndBottomMenu(true);
                } else {
                    //wheel up
                    setPositionTopAndBottomMenu(false);
                }
            }
        });
    } else {
        let lastScrollTop = 0;
        let onScrolling = false;
        let lastScrollTs = 0;
        let minScrollTime = 200;//200ms
        // window.addEventListener("scroll",(event) => {
        //     console.log('scroll event')
        //     if (checkBlockScroll(event)) {
        //         //allow scroll every 200ms
        //         if ((lastScrollTs + minScrollTime) > Date.now()){
        //             onScrolling = true;
        //         }else{
        //             onScrolling = false;
        //             lastScrollTs = Date.now();
        //         }
        //         console.log(onScrolling);;
        //         // window.scrollBy(0,10);
        //         // console.log('1');
        //         if (!onScrolling){
        //             var st = window.pageYOffset || document.documentElement.scrollTop;
        //             console.log('st:', st, lastScrollTop);
        //             if (st > lastScrollTop){
        //                 setPositionTopAndBottomMenu(true);
        //             }else if (st < lastScrollTop){
        //                 // console.log('3');
        //                 setPositionTopAndBottomMenu(false);
        //             }
        //             lastScrollTop = st <= 0 ? 0 : st;
        //         }
        //     }
        // });

        var ts;

        window.addEventListener("touchstart", (event) => {
            if (event.target.classList.contains('show-file-icon')) {
                try {
                    triggerToggleFileTab()
                } catch (e) {
                }
            } else {
                ts = event.changedTouches[0].clientY;
            }
        })

        window.addEventListener("touchmove", (event) => {
            console.log('end');
            console.log(event);
            var te = event.changedTouches[0].clientY;
            console.log(ts, te)
            if ((lastScrollTs + minScrollTime) > Date.now()) {
                onScrolling = true;
            } else {
                onScrolling = false;
                lastScrollTs = Date.now();
            }

            if (checkBlockScroll(event)) {
                if (!onScrolling) {
                    if (ts > te + 5) {
                        setPositionTopAndBottomMenu(true);
                    } else {
                        if (ts < te - 5) {
                            setPositionTopAndBottomMenu(false);
                        }
                    }
                }
            }
        })

    }
})

function checkBlockScroll(event) {
    if (!$('.schedule-modal').has(event.target).length > 0 ||
        !$('.sidebar').has(event.target).length > 0) {
        return true;
    }
    return false;
}

function scrollListComment(el) {
    console.log('scroll');
    let navigationBar = $('.block-navigation-bar');

    let lastScrollTop = 0;
    let headerGlobal = $('.sheader')
    let topAppBar = $('.new-banner-project')
    let navTopAppBar = $('.navigation-top-app-bar')
    let leftSidebar = $('#left-sidebar')
    let budgetLog = $('#budgetLogSidebar')
    let pdSection_file = $('.pd-section-file')
    const space_margin_top = '40px';
    const space_margin_top_small = '2px';
    const height_input_box = 85;

    leftSidebar.css('transition', 'bottom 0.2s')
    leftSidebar.css('transition', 'top 0.2s')
    budgetLog.css('transition', 'top 0.2s')
    let height_project_banner = topAppBar.outerHeight();
    let height_sheader = headerGlobal.outerHeight();
    let height_nav_top_app_bar = 0;
    let height_block_nav_bottom_bar = 0;
    let first_scroll_up = true;
    let first_scroll_down = true;
    if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
        height_block_nav_bottom_bar = navigationBar.outerHeight();
    }
    if (navTopAppBar.length > 0) {
        height_nav_top_app_bar = navTopAppBar.outerHeight();
    }
    let total_height_banner_header = height_sheader + height_project_banner;
    let timeStamp_last_scroll = 0;
    el.scroll(function (e) {
        e.preventDefault()
        let st = $(this).scrollTop();
        let timeStamp_scroll = e.timeStamp;

        if (st > lastScrollTop && lastScrollTop > 0 && (e.timeStamp - lastTimeScroll) > 200 && should_scroll) {
            if (first_scroll_down) {
                first_scroll_down = false;
                first_scroll_up = true;
                lastTimeScroll = e.timeStamp;
                //setPositionTopAndBottomMenu(true)

                leftSidebar.css('top', `${height_project_banner}px`);
                $('.mmessage-component').removeClass('max-height-mmessage-component-up');
                if (!$('.mmessage-component').hasClass('max-height-mmessage-component-down')) {
                    $('.mmessage-component').addClass('max-height-mmessage-component-down');
                }

                if ($(window).width() < max_width_sp_device) {
                    if (navigationBar.hasClass('hide')) {
                        $('.martist.role_master_client').css('margin-top', '0')
                    }
                }
            }
        } else {
            if (lastScrollTop > 0 && (e.timeStamp - lastTimeScroll) > 200 && should_scroll) {
                first_scroll_up = false;
                first_scroll_down = true;
                lastTimeScroll = e.timeStamp;
                //setPositionTopAndBottomMenu(false)

                leftSidebar.css('top', `${total_height_banner_header}px`);
                $('.mmessage-component').removeClass('max-height-mmessage-component-down');
                if (!$('.mmessage-component').hasClass('max-height-mmessage-component-up')) {
                    $('.mmessage-component').addClass('max-height-mmessage-component-up');
                }

                if ($(window).width() < max_width_sp_device) {
                    if (navigationBar.hasClass('hide')) {
                        $('.martist.role_master_client').css('margin-top', '0')
                    }
                }
            }
        }
        setTimeout(function () {
            calcHeightCalendarModal();
        }, 300)
        if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
            setTimeout(function () {
                calcHeightColumnRight()
            }, 230)
        }
        timeStamp_last_scroll = timeStamp_scroll;
        // scroll max bottom
        lastScrollTop = st;
    });
}

function setPositionTopAndBottomMenu(isScrollDown) {
    let headerGlobal = $('.sheader');
    let topAppBar = $('.new-banner-project');
    let navigationBar = $('.block-navigation-bar');
    let navTopAppBar = $('.navigation-top-app-bar');
    let headerPlaceholder = $('.sheader-pc-placeholder');
    let contentBlock = $('.mrow.mrow-custom');
    let menuOfferBlock = $('.mcolumn.mcolumn--left.column-list-offer.resize-drag');
    let downloadBlock = $('.mcolumn.mcolumn--right');
    let contentTalkRoomBlock = $('.project-item__content.refactor');
    let pdSection_file = $('.pd-section-file');
    let sidebarRefactor = $('.sidebar.refactor');

    //check current state 1st
    let isDown = false;
    if (headerGlobal.hasClass('sheader-scroll-down')) {
        isDown = true;
    }

    if (isScrollDown && !isDown) {
        headerGlobal.removeClass('sheader-scroll-up')
        if (!headerGlobal.hasClass('sheader-scroll-down')) {
            headerGlobal.addClass('sheader-scroll-down')
            setTimeout(function () {
                calcHeightScheduleModal();
            }, 300)
        }
        headerPlaceholder.removeClass('down');
        if (!headerPlaceholder.hasClass('up')) {
            headerPlaceholder.addClass('up');
        }
        if (!$(".navigation-bar").hasClass("hide")) {
            // List file talk room
            pdSection_file.removeClass('max-height-pd-section-file-up');
            if (!pdSection_file.hasClass('max-height-pd-section-file-down')) {
                pdSection_file.addClass('max-height-pd-section-file-down');
            }

            topAppBar.removeClass('banner-project-up')
            if (!topAppBar.hasClass('banner-project-down')) {
                topAppBar.addClass('banner-project-down')
            }

            navigationBar.removeClass('bottom-navigation-up');
            if (!navigationBar.hasClass('bottom-navigation-down')) {
                navigationBar.addClass('bottom-navigation-down');
            }

            // navTopAppBar.removeClass('navigation-top-bar-up');
            // if (!navTopAppBar.hasClass('navigation-top-bar-down')) {
            //     navTopAppBar.addClass('navigation-top-bar-down');
            // }

            contentBlock.removeClass('content-block-up');
            if (!contentBlock.hasClass('content-block-down')) {
                contentBlock.addClass('content-block-down');
            }

            contentTalkRoomBlock.removeClass('content-block-up');
            if (!contentTalkRoomBlock.hasClass('content-block-down')) {
                contentTalkRoomBlock.addClass('content-block-down');
            }

            menuOfferBlock.removeClass('menu-offer-block-up');
            if (!menuOfferBlock.hasClass('menu-offer-block-down')) {
                menuOfferBlock.addClass('menu-offer-block-down');
            }
            downloadBlock.removeClass('download-block-up');

            sidebarRefactor.removeClass('sidebar-refactor-up');
            if (!sidebarRefactor.hasClass('sidebar-refactor-down')) {
                sidebarRefactor.addClass('sidebar-refactor-down');
            }
        }
    } else if (!isScrollDown && isDown) {
        headerGlobal.removeClass('sheader-scroll-down')
        if (!headerGlobal.hasClass('sheader-scroll-up')) {
            headerGlobal.addClass('sheader-scroll-up')
            setTimeout(function () {
                calcHeightScheduleModal();
            }, 300)
        }

        headerPlaceholder.removeClass('up');
        if (headerPlaceholder.hasClass('down')) {
            headerPlaceholder.addClass('down')
        }
        if (!$(".navigation-bar").hasClass("hide")) {
            // List file talk room
            pdSection_file.removeClass('max-height-pd-section-file-down');
            if (!pdSection_file.hasClass('max-height-pd-section-file-up')) {
                pdSection_file.addClass('max-height-pd-section-file-up');
            }

            topAppBar.removeClass('banner-project-down')
            if (!topAppBar.hasClass('banner-project-up')) {
                topAppBar.addClass('banner-project-up')
            }

            navigationBar.removeClass('bottom-navigation-down');
            if (!navigationBar.hasClass('bottom-navigation-up')) {
                navigationBar.addClass('bottom-navigation-up');
            }

            // navTopAppBar.removeClass('navigation-top-bar-down');
            // if (!navTopAppBar.hasClass('navigation-top-bar-up')) {
            //     navTopAppBar.addClass('navigation-top-bar-up');
            // }

            contentBlock.removeClass('content-block-down');
            if (!contentBlock.hasClass('content-block-up')) {
                contentBlock.addClass('content-block-up');
            }

            contentTalkRoomBlock.removeClass('content-block-down');
            if (!contentTalkRoomBlock.hasClass('content-block-up')) {
                contentTalkRoomBlock.addClass('content-block-up');
            }

            menuOfferBlock.removeClass('menu-offer-block-down');
            if (!menuOfferBlock.hasClass('menu-offer-block-up')) {
                menuOfferBlock.addClass('menu-offer-block-up');
            }

            if (!downloadBlock.hasClass('download-block-up')) {
                downloadBlock.addClass('download-block-up');
            }

            sidebarRefactor.removeClass('sidebar-refactor-down');
            if (!sidebarRefactor.hasClass('sidebar-refactor-up')) {
                sidebarRefactor.addClass('sidebar-refactor-up');
            }
        }
    }
}

function startDragChapter(event) {
    event.dataTransfer.setData("currentChapterId", event.target.getAttribute("data-ps-id"))
}

function dropChapter(ev) {
    if (ev.dataTransfer.getData("currentChapterId") != ev.target.getAttribute("data-ps-id")) {
        let data = ev.dataTransfer.getData("currentChapterId");
        //we get all the item
        let chapterSelectorList = $(".filter-item-project.chapter-item");
        let chapterSceneSelectorList = $(".pd-chapter.chapter-block");
        //copy current element and delete it from html
        let tmpSelector = false;
        let tmpSceneSelector = false;
        if (chapterSelectorList.length > 1) {
            let baseIndex = 0;
            let endIndex = 0;
            for (let i = 0; i < chapterSelectorList.length; i++) {
                if (chapterSelectorList[i].getAttribute("data-ps-id") == data) {
                    tmpSelector = chapterSelectorList[i];
                    tmpSceneSelector = chapterSceneSelectorList[i];
                    baseIndex = i;
                    chapterSelectorList[i].remove();
                    if (chapterSceneSelectorList[i]) {
                        chapterSceneSelectorList[i].remove();
                    }
                }
                if (chapterSelectorList[i].getAttribute("data-ps-id") == ev.target.getAttribute("data-ps-id")) {
                    endIndex = i;
                }
            }

            //we add item back
            let newChapterSelectorList = $(".filter-item-project.chapter-item");
            let newChapterSceneSelectorList = $(".pd-chapter.chapter-block");
            if (newChapterSelectorList.length > 0) {
                for (let j = 0; j < newChapterSelectorList.length; j++) {
                    if (newChapterSelectorList[j].getAttribute("data-ps-id") == ev.target.getAttribute("data-ps-id")) {
                        if (baseIndex > endIndex) {
                            newChapterSelectorList[j].before(tmpSelector);
                            if (tmpSceneSelector) {
                                newChapterSceneSelectorList[j].before(tmpSceneSelector);
                            }
                        } else {
                            newChapterSelectorList[j].after(tmpSelector);
                            if (tmpSceneSelector) {
                                newChapterSceneSelectorList[j].after(tmpSceneSelector);
                            }
                        }
                    }
                }
            }
        }

        //call api to change data
        //from base scene id to finish scene id
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/api/change-project-scene-order",
            data: {
                'base_scene_id': data,
                'finish_scene_id': ev.target.getAttribute("data-ps-id")
            },
            beforeSend: function () {
                //no need to do anything
            },
            success: function (data) {
                //no need to do anything
            },
            complete: function () {
                //no need to do anything
            }
        });
    }
    ev.preventDefault();
}

function dragOverChapter(ev) {
    ev.preventDefault();
}

function setUpToggleOffer(ev) {
    //check if parent element
    let current = $(ev.target);
    let parentHolder = current.parent();
    let parentActionBlock = null;
    if (parentHolder.hasClass("contract-block-action")) {
        parentActionBlock = parentHolder;
    } else {
        parentActionBlock = parentHolder.parent();
    }
    //get block to show
    let blockOfferAction = parentActionBlock.children(".contract-block-action-dropdown");
    if (blockOfferAction.hasClass("hide")) {
        blockOfferAction.removeClass("hide");
    } else {
        blockOfferAction.addClass("hide");
    }
}

function downloadFileOffer(offer_id) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_content_contract_modal",
        data: {
            'id': offer_id,
            'status': 1,
        },
        beforeSend: function (xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
        },
        success: function (response) {
            let a_tag = document.createElement('a');
            a_tag.href = response.file;
            document.body.appendChild(a_tag);
            a_tag.click();
        },
        error: function () {
            toastr.error('エラーが発生しました');
        }
    })
}

function acceptOfferArtist(offer_id) {
    if (!isClicked) {
        isClicked = true;
        bootbox.confirm({
            message: " 検収しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn btn-success btn--primary'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn btn-danger btn--tertiary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id,
                            'status': 4
                        },
                        url: '/messenger/update_status_offer',
                        success: function (data) {
                            console.log('ok')
                        }
                    });
                }
            }
        });

        setTimeout(() => {
            isClicked = false
        }, 1000)
    }
}

function openModalReview(reviewer, creator_name, admin_name, position) {
    $('body').append(ReviewRate(reviewer, reviewer === creator_name ? admin_name : creator_name, position));
    $('.rate-review').addClass('hide');
    var textarea = document.getElementById('review-text');
    var textareaquote = document.querySelector("#review-section-2 #review-text-quote");
    textarea.focus();
    selectTab();
    selectView();
    cancelSelectRadio();

    // テキストエリアの高さ自動調整
    textarea.addEventListener("input", (e) => {
        resizeElement(e.target, 576);
    }, false);
    $('#nextBtn1').click(() => {
        $('#review-section-1').addClass('is-hidden');
        $('#review-section-2').removeClass('is-hidden');
        resizeElement(textareaquote, 464);
    });
    $('#prevBtn2').click(() => {
        $('#review-section-1').removeClass('is-hidden');
        $('#review-section-2').addClass('is-hidden');
    })
    $('#nextBtn2').click(() => {
        const review = $('.form-review input[type="radio"]:checked').val() ? $('.form-review input[type="radio"]:checked').val() : '3';
        const offer_id = $('.mmessage-list').find('.contract-block').attr('data-offer');
        const note = $('#review-dialog #review-text-quote').text();
        let listScript3 = document.getElementsByTagName('script');
        let matchRegex3 = /initSocket\(([0-9]+[\D]*)\)/gm;
        let currentId3 = 0;
        for (let i = 0; i < listScript3.length; i++) {
            let scriptString = listScript3[i];
            let match = scriptString.innerText.match(matchRegex3);
            if (match) {
                currentId3 = match[0].replace("initSocket(", "").replace(")", "");
            }
        }
        let user_id = currentId3.split(",")[0];
        let status = $('.c-segment-control input[type="radio"]:checked').val();

        $.ajax({
            type: "POST",
            data: {
                'offer_id': offer_id,
                'review': review,
                "note": note,
                "user_id": user_id,
                "status": status,
            },
            url: '/api/update-offer-review',
            success: function () {
                $(".container-review").remove();
            }
        });
    })
}

function cancelSelectRadio() {
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.addEventListener('click', function (event) {
            if (lastCheckedRadio === this) {
                this.checked = false;
                lastCheckedRadio = null;
            } else {
                lastCheckedRadio = this;
            }
        });
    });
}

function cancelReview() {
    $(".container-review").remove();
    $('.rate-review').removeClass('hide');
}

function selectTab() {
    const reviewText = document.getElementById('review-text');
    const reviewTextQuote = $('#review-dialog #review-text-quote');
    nextBtn1.disabled = reviewText.value.trim() === '' || reviewText.value.trim().length > 1500;
    
    // レビュー対象者の名前を取得
    const revieweeElement = $('#review-dialog p').first();
    let revieweeName = '';
    if (revieweeElement.length > 0) {
        const match = revieweeElement.text().match(/この内容は、(.+)さんだけに共有されます。/);
        if (match && match[1]) {
            revieweeName = match[1];
        }
    }
    
    reviewText.addEventListener('input', () => {
        const nextBtn1 = document.getElementById('nextBtn1');
        nextBtn1.disabled = reviewText.value.trim() === '' || reviewText.value.trim().length > 1500;
        reviewTextQuote.text(reviewText.value);

        const messageElement = document.getElementById('length-warning');
        const remainingChars = reviewText.value.length - 1500;
        if (reviewText.value.length > 1500) {
            if (!messageElement) {
                document.getElementById('review-text').insertAdjacentHTML('afterend',
                    '<div id="length-warning" class="bodytext u-w100 u-row-center u-gap4 c-group u-bg-green u-text-white"><span class="material-symbols-rounded u-text-green">warning</span>少し長いので、あと ' + remainingChars + ' 文字くらい減らしてみませんか。</div>');
            } else {
                messageElement.innerHTML = '<span class="material-symbols-rounded u-text-white">warning</span>' + remainingChars + '文字くらい減らしてみませんか'; // メッセージを更新
            }
        } else {
            if (messageElement) {
                messageElement.remove();
            }
        }

    });
    document.querySelectorAll('.c-keyword').forEach(keyword => {
        keyword.addEventListener('click', function (e) {
            const selectedKeyword = e.target.textContent;
            if (document.getElementById('length-warning')) {
                document.getElementById('length-warning').remove();
            }
            let newText;
            do {
                newText = draft[selectedKeyword][Math.floor(Math.random() * draft[selectedKeyword].length)];
                // ${reviewee}を実際のユーザー名に置き換える
                if (revieweeName) {
                    newText = newText.replace(/\$\{reviewee\}/g, revieweeName);
                }
                reviewText.removeAttribute('style');
            } while (newText === document.getElementById('review-text').value);
            document.getElementById('review-text').value = newText;
            const nextBtn1 = document.getElementById('nextBtn1');
            nextBtn1.disabled = reviewText.value.trim() === '' || reviewText.value.trim().length > 1500;
            reviewTextQuote.text(reviewText.value);
        });
    });
}

function selectView() {
    document.querySelectorAll('input[name="review-option"]').forEach(radio => {
        radio.addEventListener('change', function () {
            const cQuote = $('#review-dialog .c-quote');
            const cIconQuote = $('#review-dialog .c-icon-quote');
            const position = $('#review-dialog .user-position')
            if (this.value === '1') {
                cQuote.removeClass('hide');
                cIconQuote.removeClass('hide');
                position.removeClass("hide");
            } else if (this.value === '2') {
                cQuote.removeClass('hide');
                cIconQuote.removeClass('hide');
                position.addClass("hide");
            } else if (this.value === '3') {
                cQuote.removeClass('hide');
                cIconQuote.addClass('hide');
                position.addClass("hide");
            }
        });
    });
}

function ReviewRate(reviewer, reviewee, position) {
    return `<div class="container-review"> 
        <dialog id="review-dialog" open>
            <section id="review-section-1" class="u-col-review u-gap24 u-w100">
                <p class="u-w100">レビューを送信しましょう。<br>
                    この内容は、${reviewee}さんだけに共有されます。
                </p>
                <div class="u-w100 u-row u-gap16 form-review">
                    <label class="c-radio-icon u-col-center u-gap4">
                        <input type="radio" name="rating" value="1" checked>
                        <span class="material-symbols-rounded u-fontsize-32">thumb_up</span>
                        <a class="bodytext-11">良かった</a>
                    </label>
                    <label class="c-radio-icon u-col-center u-gap4">
                        <input type="radio" name="rating" value="2">
                        <span class="material-symbols-rounded u-fontsize-32">thumb_down</span>
                        <a class="bodytext-11">残念だった</a>
                    </label>
                </div>
                <textarea id="review-text" rows="3" placeholder="演奏の細部に至るまでのこだわりが感じられ、特に微妙なニュアンスやダイナミクスの変化が素晴らしい。"></textarea>
                <p>✨たたきはこちら
                    <a class="c-keyword">プロフェッショナリズム</a>
                    <a class="c-keyword">安定性</a>
                    <a class="c-keyword">創造性</a>
                    <a class="c-keyword">納期遵守</a>
                    <a class="c-keyword">技術</a>
                    <a class="c-keyword">オリジナリティ</a>
                    <a class="c-keyword">柔軟さ</a>
                    <a class="c-keyword">モチベーション</a>
                    <a class="c-keyword">コミュニケーション</a>
                    <a class="c-keyword">チームワーク</a>
                    <a class="c-keyword">暗黙知</a>
                    <a class="c-keyword">目的の理解</a>
                    <a class="c-keyword">提案力</a>
                    <a class="c-keyword">オファー</a>
                </p>

                <div class="u-row-btn u-gap8 u-w100">
                    <button class="btn-prev-tertiary" id="prevBtn1" onclick="cancelReview()">キャンセル</button>
                    <button class="btn-next-primary" id="nextBtn1">次へ</button>
                </div>
            </section>
            <section id="review-section-2" class="u-col-review u-gap24 u-w100 is-hidden">
                <p class="u-w100">もし${reviewee}さんがレビューを気に入ったら、プロフィールサイトに推薦文として使わせていただけますか？</p>
                <div class="c-message-ours">
                    <div class="c-quote u-w100 u-relative" style="display: block;">
                        <span class="material-symbols-rounded c-icon-quote">format_quote</span>
                        <div class="u-row u-items-start">
                            <p id="review-text-quote" class="bodytext-quote u-text-justify u-overflow-y-auto">迅速かつ的確な対応に感謝します。いつも高いプロフェッショナリズムを発揮しており、信頼できるパートナーです。</p>
                        </div>
                        <div class="u-row-end u-gap16 u-mt8 user-position">
                            <p class="heading-16 c-quote-line">${reviewer}</p><span class="bodytext-11">${position !== "None" ? position : ""}</span>
                        </div>
                    </div>
                </div>
                <div class="u-row u-w100 c-segment-control">
                    <label><input type="radio" class="type-review" name="review-option" value="1" checked="">OK</label>
                    <label><input type="radio" class="type-review" name="review-option" value="2">匿名ならOK</label>
                    <label><input type="radio" class="type-review" name="review-option" value="3">許可しない</label>
                </div>
                <div class="u-row-btn u-gap8 u-w100">
                    <button class="btn-prev-tertiary" id="prevBtn2">戻る</button>
                    <button class="btn-next-primary" id="nextBtn2">送信</button>
                </div>
            </section>
        </dialog>
    </div>`
}
