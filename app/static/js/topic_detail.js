$(document).ready(function () {
    dragableToppic();
    actionVideoTopic();
    listAudioTopic();
    stopAudioTopic();
    downloadTopicFile();
    addSelectedAlbumForTopic();
    backPage();
    autoLoadListTopic();
    showDetailTopic();
    actionWithTopic();
    removeOverLayModal();
});

let current_splide = false;

function backPage() {
    $(document).on('click', '.button-back-old-page', function (e) {
        e.preventDefault();
        let topicDom = $(this).parents('.section-topic-detail');
        let topic_id = topicDom.attr('data-topic');
        let newUrl = $(this).attr('data-href') + '?topic=' + topic_id;
        window.location.href = newUrl;
    });
}

function addSelectedAlbumForTopic() {
    if(!$('.section-topic-detail').length) {
        return
    }
    const url_string = window.location.href;
    var url = new URL(url_string);
    let selected_album_index = url.searchParams.get('selected');
    if (selected_album_index) {
        selected_album_index.split(',').forEach(function(e, i) {
            if(parseInt(e) >= 0) {
                let albums = $('.list-circle__component');
                if (albums.length >= parseInt(e) + 1) {
                    albums.eq(e).addClass('last-selected');
                }
            }
        })
    }

    $('.section-container').each(function(i, e) {
        let selected = $(e).find('.last-selected')
        let last_selected = $(e).find('.last-selected').last()
        selected.removeClass('last-selected');
        last_selected.addClass('last-selected');
    })
}

function ajaxGetTopicDetail(topic_id) {
    if (topic_id) {
        $.ajax({
            method: 'GET',
            url: '/gallery/get_topic_detail',
            dataType: "json",
            data: {
                'topic_id': topic_id,
                'type': 'create_offer'
            },
            success: function (data) {
                $('#modal-detail-topic').modal('show');
                $('#modal-detail-topic .modal-content.popup-content').empty();
                $('#modal-detail-topic .modal-content.popup-content').append(data.html);
                addSelectedAlbum(topic_id);
            },
            error: function () {
                toastr.error('エラーが発生しました');
                $('#modal-detail-topic .modal-content.popup-content').empty();
            },
            complete: function () {
            }
        });
    }
}

function addSelectedAlbum(topic_id) {
    let topic = $('.list-topics__topics-container[data-topic=' + topic_id + ']');
    if (topic.length) {
        let selected = topic.find('.last-selected');
        if(selected.length) {
            let index = selected.index();
            let detail_albums = $('.topic-container-content.topic-container-content__right .list-circle__component');
            if (detail_albums.length > index) {
                detail_albums.removeClass('last-selected');
                detail_albums.eq(index).addClass('last-selected');
            }
        }
    }
}

function checkPlayPauseConditionForType(sample) {
    switch (sample.attr('data-type')) {
        case 'music':
            $('.playing[data-type="music"] audio, .playing[data-type="2mix"] audio, .loading[data-type="music"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            break;
        case 'voice':
            $('.playing[data-type="voice"] audio, .playing[data-type="2mix"] audio, .loading[data-type="voice"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            break;
        case 'sound_effect':
            $('.playing[data-type="sound_effect"] audio, .playing[data-type="2mix"] audio, .loading[data-type="sound_effect"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            break;
        default:
            $('.playing audio, .loading audio').each(function (i, e) {
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            break;
    }
}

let hovering_topic = false;
let hovering_topic_id = 0;
let hover_topic_zooming = false;
let close_full_screen_topic = false;
function actionVideoTopic() {
    $(document).on('mouseenter mouseleave', '.list-topics__content-top, .topic-content__media', function (e) {
        if($('.audio-navi').is('.showing')) {
            return
        }
        if ($(this).find('video').length) {
            if (e.type === 'mouseenter') {
                let current_hover_id = hovering_topic_id;
                let target = $(this)
                hovering_topic = true
                setTimeout(() => {
                    if(!hovering_topic || current_hover_id != hovering_topic_id || $('.audio-navi.showing').length) {
                        return
                    }
                    if(!hover_topic_zooming) {
                        let video = target.find('video').get(0);
                        if(target.is('.topic-content__media')) {
                            $(video).prop('height', $(video).height());
                        }
                        pauseAll();
                        video.play();
                        video.removeAttribute('controls');
                        video.muted = false;
                        hovering_topic = true;
                        close_full_screen_topic = false;
                    }
                }, 200)
            } else {
                if(!hover_topic_zooming && !close_full_screen_topic) {
                    let video = $(this).find('video').get(0);
                    video.pause();
                    hovering_topic = false;
                    hovering_topic_id++;
                    $(this).removeClass('hover-zoom-netflix-center')
                    $(this).removeClass('hover-zoom-netflix')
                    $(this).removeClass('hover-zoom-netflix-prepare')
                    $(this).css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
                    $(this).find('.list-new-works__content_hover').addClass('hide');
                }
            }
        }
    });

    $(document).on('click', '.topic-netflix-overlay', function() {
        $('.hover-zoom-netflix').removeClass('hover-zoom-netflix-center');
        $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')

        setTimeout(() => {
            $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
            $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
            $('.hover-zoom-netflix-prepare').find('.list-new-works__content_hover').addClass('hide');
            $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
            $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
        }, 200)

        setTimeout(() => {
            $('.hover-zoom-out-netflix-prepare-2').css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
            $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
        }, 800)

        hover_topic_zooming = false;
        hovering_topic_id++;
        hovering_topic = false;
        close_full_screen_topic = true;
        $('body .topic-netflix-overlay').remove();
    })

    $(document).on('click', '.list-topics__content-top video, .topic-content__media video', function() {
        if($('.gallery__content-container').length) {
            playPauseInNavigation('', 'pause')
            $('.playing-navi').each(function(i, e) {
                $(e).removeClass('playing-navi')
                $(e).find('audio')[0].pause()
            })
            pauseAll()
        }
        this.play()
        close_full_screen_topic = true;
        hovering_topic_id++;
        if (this.requestFullscreen) {
            this.requestFullscreen();
        } else if (this.webkitRequestFullscreen) { /* Safari */
            this.webkitRequestFullscreen();
        } else if (this.msRequestFullscreen) { /* IE11 */
            this.msRequestFullscreen();
        }
    });

    document.addEventListener("fullscreenchange", function() {
        if (!document.fullscreenElement) {
            $('.video-netflix-overlay:not(.topic-netflix-overlay)').trigger('click');
            $('.topic-netflix-overlay').trigger('click');
            $('.list-topics__content-bottom video, .section-content__list-media video').css('display', 'none')
        }
    });
}

function listAudioTopic() {
    $(document).on('click', '.list-topics__content-bottom__list-circle .list-circle__component:not(.playing, .loading, .btn-preview-album),' +
        ' .section-content__list-media .list-circle__component:not(.playing, .loading, .btn-preview-album)', function () {
        const thumbnail = $(this);
        const audio = thumbnail.find('audio');
        let type = thumbnail.data('type');

        if($('.gallery__content-container').length) {
            playPauseInNavigation('', 'pause')
        }

        $('.playing-navi').each(function (i,e) {
            $(e).find('audio')[0].pause();
            $(e).removeClass('playing-navi')
        })

        checkPlayPauseConditionForTypeTopic(thumbnail);

        $('.list-circle__component[data-type=' + type + ']').each(function () {
            $(this).removeClass('playing');
            $(this).removeClass('loading');
        });

        audio.on('canplay', function () {
            audio.off('canplay');
            if(thumbnail.is('.loading')) {
                thumbnail.removeClass('loading');
                thumbnail.addClass('playing');
                thumbnail.find('.list-circle__sub-component').css({'border': '1px solid #F0F0F0'});
                thumbnail.attr('process-status', 'play');
                processFnc(thumbnail, thumbnail.attr('process-data'), $(audio).get(0).duration / 100 * 1000);
            }
        });
        let  artist_name = thumbnail.attr('data-artist');
        let sale_id = thumbnail.attr('data-sale-id');

        if (artist_name) {
            toastr.info(artist_name, audio.attr('data-name'), {
                newestOnTop: true,
                timeOut: 3000,
                extendedTimeout: 10000,
            });

        }

        audio[0].play();
        thumbnail.attr('process-status', 'play');
        audio[0].muted = false;
        audio[0].loop = true;
        if (!!$(audio).get(0).duration) {
            processFnc(thumbnail, thumbnail.attr('process-data'), $(audio).get(0).duration / 100 * 1000);
        }

        if (audio[0].readyState > 1) {
            thumbnail.removeClass('loading')
            thumbnail.addClass('playing')
            thumbnail.find('.list-circle__sub-component').css({'border': '1px solid #F0F0F0'});
        } else {
            thumbnail.removeClass('playing')
            thumbnail.addClass('loading')
        }

        if($(this).parents('.list-topics__content-bottom__list-circle').length) {
            let selected = $(this).parents('.list-topics__content-bottom__list-circle').find('.last-selected');
            $('.list-circle__component.last-selected').removeClass('last-selected');
            let selection = $(this).attr('data-selection');
            selected.each(function(i, e) {
                if($(e).attr('data-selection') !== selection && $(e).is('.playing, .loading')) {
                    $(e).addClass('last-selected');
                }
            })
            $(this).addClass('last-selected');
        } else {
            $(this).parents('.section-content__list-media').find('.list-circle__component.last-selected').removeClass('last-selected');
            $(this).addClass('last-selected');
        }

        let has_music = false; has_voice = false; has_sound = false;
        has_music = $('.list-circle__component[data-type="music"].playing, .list-circle__component[data-type="music"].loading').length > 0
        has_voice = $('.list-circle__component[data-type="voice"].playing, .list-circle__component[data-type="voice"].loading').length > 0
        has_sound = $('.list-circle__component[data-type="sound_effect"].playing, .list-circle__component[data-type="sound_effect"].loading').length > 0
        showToastPlayMix(has_music, has_voice, has_sound)
    });

    $(document).on('click', '.list-topics__content-bottom__list-circle .list-circle__component.loading:not(.btn-preview-album),' +
        ' .section-content__list-media .list-circle__component.loading:not(.btn-preview-album)', function () {
       $(this).removeClass('loading');
    });

    $(document).on('click', '.list-topics__content-bottom__list-circle .playing, .section-content__list-media .list-circle__component.playing:not(.btn-preview-album)', function () {
        const audio = $(this).find('audio')
        audio.off('canplay')
        audio.each(function (i, e) {
            e.pause();
        })
        $(this).removeClass('playing loading');
        $(this).find('.list-circle__sub-component').css({'border': '1px solid #FFFFFF'});
        $(this).attr('process-status', 'pause');
        $(this).css({'border': '1px solid #F0F0F0'});
        $(this).attr('process-data', parseInt($(audio).get(0).currentTime / $(audio).get(0).duration * 100));
    });


    function processFnc(processBar, percents, processSpeed) {
        let processValue = percents;
        let processEndValue = 100;
        let speed = processSpeed;

        let process = setInterval(() => {
            if (processBar.attr('process-status') === 'play') {
                processValue++;
                processBar.attr('process-data', parseInt(processValue))
                processBar.css({
                    'background': `conic-gradient(
                    #FFFFFF ${parseInt(processValue) * 3.6}deg,
                    #F0F0F0 ${parseInt(processValue) * 3.6}deg
                )`
                });
            } else {
                clearInterval(process);
                processBar.css({'border': '1px solid #F0F0F0'});
            }

            if (processValue === processEndValue) {
                processValue = 0
                processBar.attr('process-data', processValue);
                processBar.css({'background': '#F0F0F0'});
            }
        }, speed);

    }
}

function checkPlayPauseConditionForTypeTopic(sample) {
    switch (sample.attr('data-type')) {
        case 'music':
            $('.playing[data-type="music"] audio, .loading[data-type="music"] audio, .playing[data-type="2mix"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).trigger('click');
                e.pause();
            });
            break
        case 'voice':
            $('.playing[data-type="voice"] audio, .loading[data-type="voice"] audio, .playing[data-type="2mix"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).trigger('click');
                e.pause();
            });
            break
        case 'sound_effect':
            $('.playing[data-type="sound_effect"] audio, .loading[data-type="sound_effect"] audio, .playing[data-type="2mix"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                $(e).trigger('click');
                e.pause();
            });
            break
        default:
            $('.playing audio, .loading audio').each(function (i, e) {
                $(e).trigger('click');
                e.pause();
            });
            break
    }
}

function stopAudioTopic() {
    $(document).on('hidden.bs.modal', '#modal-detail-topic', function () {
        const thisPlaying = $('.playing .list-search__item-playpause');
        if (thisPlaying.get().length) {
            const audio = thisPlaying.parent().find('audio');
            audio.off('canplay');
            audio.each(function (i, e) {
                e.pause();
            });
            thisPlaying.parent().removeClass('playing loading');
        }
    });

    $(document).on('show.bs.modal', '#modal-detail-topic', function () {
        const thisPlaying = $('.playing');
        if (thisPlaying.get().length) {
            const audio = thisPlaying.find('audio');
            audio.off('canplay');
            audio.each(function (i, e) {
                e.pause();
            })
            thisPlaying.removeClass('playing loading');
            thisPlaying.attr('process-status', 'pause');
            thisPlaying.css({'border': '1px solid #94E7FA'});
            thisPlaying.attr('process-data', parseInt($(audio).get(0).currentTime / $(audio).get(0).duration * 100));
        }
    });
}

function downloadTopicFile() {
    $(document).on('click', '.btn-download-topic-file', function () {
        let topic_id = $(this).parents('.section-topic-detail').attr('data-topic');
        ajaxDownloadTopic(topic_id);
    });

    function ajaxDownloadTopic(topic_id) {
        if (topic_id) {
            $.ajax({
                type: "GET",
                dataType: "json",
                data: {
                    'topic_id': topic_id
                },
                url: '/gallery/topic_file_download_link',
                beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                },
                success: function (data) {
                    window.location.href = data.url;
                },
            });
        }
    }
}

var currentPageTopic = 1;
var isSendingTopic = false;
let listTopicIds = $('.gallery__list-topics').attr('data-topic-ids');
let listTopic;
if(listTopicIds) {
    listTopic = listTopicIds.replace('[', '').replace(']', '').replace(/[' ]/gm, '').split(',');
}
const totalPageTopic = $('.gallery__list-topics').attr('data-total-page');
const pageGallery = $('.container.gallery-container').length ? true : false;
function autoLoadListTopic() {
    ajaxLoadMoreTopic(listTopic, totalPageTopic, pageGallery, currentPageTopic);
    function ajaxLoadMoreTopic(listTopic, totalPageTopic, pageGallery, currentPageTopic) {
        if (!listTopic || !totalPageTopic || isSendingTopic || currentPageTopic > totalPageTopic) {
            if (currentPageTopic > totalPageTopic) {
                autoScroll();
            }
            return
        }
        isSendingTopic = true;
        let list_topic_id = listTopic.slice(currentPageTopic * 3, currentPageTopic * 3 + 3);
        currentPageTopic += 1;
        let artist_profile_id = $('.profile').attr('data-user');
        let data = {
            'current_page': currentPageTopic,
            'list_topic_id': list_topic_id
        };
        let createURL = "/gallery/create_topic_form";

        if (artist_profile_id) {
            data.artist_id = artist_profile_id;
            createURL += '?artist_id=' + artist_profile_id;
        }

        $.ajax({
            type: "GET",
            dataType: "json",
            data: data,
            url: '/gallery/load_more_list_topics',
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            },
            success: function (data) {
                $('.gallery__list-topics .splide__list').append(data.html);

                let base_condition = totalPageTopic == currentPageTopic
                let condition1 =  base_condition && pageGallery && user_role === 'curator'
                let condition2 = base_condition && !pageGallery && $('.gallery__topics-container.editable').length
                if (condition1 || condition2) {
                    $('.gallery__list-topics .splide__list').append(`<a class="list-topics__button-add-topic splide__slide" href=${createURL}>
                        <div class="list-topics__button-add-topic__content">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M31.9997 5.33594C17.2797 5.33594 5.33301 17.2826 5.33301 32.0026C5.33301 46.7226 17.2797 58.6693 31.9997 58.6693C46.7197 58.6693 58.6663 46.7226 58.6663 32.0026C58.6663 17.2826 46.7197 5.33594 31.9997 5.33594ZM42.6663 34.6693H34.6663V42.6693C34.6663 44.1359 33.4663 45.3359 31.9997 45.3359C30.533 45.3359 29.333 44.1359 29.333 42.6693V34.6693H21.333C19.8663 34.6693 18.6663 33.4693 18.6663 32.0026C18.6663 30.5359 19.8663 29.3359 21.333 29.3359H29.333V21.3359C29.333 19.8693 30.533 18.6693 31.9997 18.6693C33.4663 18.6693 34.6663 19.8693 34.6663 21.3359V29.3359H42.6663C44.133 29.3359 45.333 30.5359 45.333 32.0026C45.333 33.4693 44.133 34.6693 42.6663 34.6693Z" fill="#F0F0F0"/>
                        </svg><p>トピックを追加</p>
                        </div>
                    </a>`);
                } else {
                    isSendingTopic = false;
                    ajaxLoadMoreTopic(listTopic, totalPageTopic, pageGallery, currentPageTopic);
                }
                $('#loading_animation').remove();
            },
            error: function () {
                currentPageTopic -= 1;
                isSendingTopic = false;

            },
        });
    }
}

function pauseAll() {
    $(document).find('audio, video').each(function(i, e) {
        e.pause()
        $(e).parents('.sample-audio-thumbnail, .exhibition-works__component-content, .list-circle__component, .list-new-works__media.gallery__item').removeClass('playing loading')
        if($(e).parents('.list-topics__content-bottom__list-circle, .section-content__list-media').length && $(this).parents('.list-circle__component').attr('process-status') === 'play') {
            $(this).parents('.list-circle__component').attr('process-status', 'pause');
            $(this).parents('.list-circle__component').css({'border': '1px solid #F0F0F0'});
            $(this).parents('.list-circle__component').attr('process-data', parseInt($(e).get(0).currentTime / $(e).get(0).duration * 100));
        }
    })
}

function autoScroll() {
    $('.splide__slide.splide__slide--clone').remove()
    let condition1 = pageGallery && user_role === 'curator'
    let condition2 = !pageGallery && $('.gallery__topics-container.editable').length
    let condition3 = $('.splide__track')[0].scrollWidth < $('.gallery__topics-container')[0].scrollWidth
    if(condition3 || condition1 || condition2) {
        if(current_splide) {
            current_splide.destroy()
            current_splide = new Splide('.splide', {
                type: 'slide',
                drag: 'free',
                autoScroll: {
                  speed: 0.5,
                  pauseOnHover: true,
                  pauseOnFocus: true
                },
                easing: 'linear',
                isNavigation: true,
                dragMinThreshold: {
                    mouse: 50,
                    touch: 50,
                },
                arrows: false,
                pagination: false,
            }).mount(window.splide.Extensions);
        }
        return
    } else {
        $('.gallery__list-topics').addClass('splide')
    }

    if(current_splide) {
        current_splide.destroy()
    }
    current_splide = new Splide('.splide', {
        type: 'loop',
        drag: 'free',
        isNavigation: true,
        easing: 'linear',
        dragMinThreshold: {
            mouse: 50,
            touch: 50,
        },
        perPage: '3',
        autoScroll: {
          speed: 0.5,
          pauseOnHover: true,
          pauseOnFocus: true
        },
        arrows: false,
        pagination: false,
    }).mount(window.splide.Extensions);
}


function showDetailTopic() {
    $(document).on('click tap', '.list-topics__topics-container', function (e) {
        e.preventDefault();
        let dataUrl = $(this).attr('data-url');
        if (!$('.order-step__option').length) {
            if ($(this).attr('data-can-edit') === 'True') {
                dataUrl += '&gallery=True';
            } else {
                dataUrl += '?gallery=True';
            }
        }
        window.location.href = dataUrl;
    });
}

function actionWithTopic() {
    $(document).on('click', '.gallery__list-topics .gallery__btn-delete', function (e) {
        e.stopPropagation();
        $('#modal-delete-topic').modal('show');
        $('#modal-delete-topic').attr('data-topic', $(this).parents('.list-topics__topics-container').attr('data-topic'));
    });


    $(document).on('click', '#modal-delete-topic .btn-popup-send', function (e) {
        let topic_id = $('#modal-delete-topic').attr('data-topic');

        $.ajax({
            method: 'POST',
            url: '/gallery/delete_topic',
            data: {
                'topic_id': topic_id,
            },
            success: function (data) {
                $('.list-topics__topics-container[data-topic=' + topic_id + ']').remove();
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-delete-topic').modal('hide');
            }
        });
    });
}

function dragableToppic() {
    $('.gallery__topics-container .gallery__list-topics .splide__list').sortable({
        axis: 'x',
        items: '.list-topics__topics-container.splide__slide',
        handle: 'span.gallery__btn-move',
        tolerance: 'pointer',
        helper: 'clone',
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
        },
        stop: function (event, ui) {
            let arr_order = [];

            $(ui.item).parents('.gallery__list-topics').find('.list-topics__topics-container').each(function (index, item) {
                arr_order.push(item.getAttribute('data-topic'));
            });

            // Merges both arrays and gets unique items
            arr_order = arrayUnique(arr_order.map(String).concat(listTopic));

            $.ajax({
                type: "POST",
                url: "/gallery/update_topic_index",
                data: {
                    'arr_order': arr_order
                },
                success: function (data) {
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            })
        }
    })
}
