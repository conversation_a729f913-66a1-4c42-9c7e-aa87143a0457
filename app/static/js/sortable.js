function sort_by_title_asc(a, b) {
    let a_title = $(a).find('.project-delivery-item-content').attr('data-title').toString().toLowerCase();
    let b_title = $(b).find('.project-delivery-item-content').attr('data-title').toString().toLowerCase();
    if (a_title === b_title) {
        return 0
    } else {
        return a_title < b_title ? -1 : 1;
    }
}

function sort_by_title_desc(a, b) {
    let a_title = $(a).find('.project-delivery-item-content').attr('data-title').toString().toLowerCase();
    let b_title = $(b).find('.project-delivery-item-content').attr('data-title').toString().toLowerCase();
    if (a_title === b_title) {
        return 0
    } else {
        return a_title > b_title ? -1 : 1;
    }
}

function parseDate(str_date) {
    let arr_date = str_date.split(' ');
    let date1 = arr_date[0].split('-');
    let newDate = date1[1] + '/' + date1[2] + '/' + date1[0] + ' ' + arr_date[1];
    let date = new Date(newDate);
    return date
}

function sort_by_modified_asc(a, b) {
    let a_modified = parseDate($(a).find('.project-delivery-item-content').attr('data-modified'));
    let b_modified = parseDate($(b).find('.project-delivery-item-content').attr('data-modified'));
    if (a_modified === b_modified) {
        return 0
    } else {
        return a_modified < b_modified ? -1 : 1;
    }
}

function sort_by_modified_desc(a, b) {
    let a_modified = parseDate($(a).find('.project-delivery-item-content').attr('data-modified'));
    let b_modified = parseDate($(b).find('.project-delivery-item-content').attr('data-modified'));
    if (a_modified === b_modified) {
        return 0
    } else {
        return a_modified > b_modified ? -1 : 1;
    }
}

function sort_by_priority_desc(a, b) {
    //high rating <=> low priority
    let a_rating = parseFloat($(a).find('.project-delivery-item-content').attr('data-rating'));
    let b_rating = parseFloat($(b).find('.project-delivery-item-content').attr('data-rating'));
    // if (a_rating === 0.0) {
    //     a_rating = 2.5;
    // }
    // if (b_rating === 0.0) {
    //     b_rating = 2.5;
    // }
    if (a_rating === b_rating) {
        return 0
    } else {
        return a_rating < b_rating ? -1 : 1;
    }
}

function sort_by_priority_asc(a, b) {
    //high rating <=> low priority
    let a_rating = parseFloat($(a).find('.project-delivery-item-content').attr('data-rating'));
    let b_rating = parseFloat($(b).find('.project-delivery-item-content').attr('data-rating'));
    // if (a_rating === 0.0) {
    //     a_rating = 2.5;
    // }
    // if (b_rating === 0.0) {
    //     b_rating = 2.5;
    // }
    if (a_rating === b_rating) {
        return 0
    } else {
        return a_rating > b_rating ? -1 : 1;
    }
}

function sort_scenes_with(ascending = true, parent) {
    let sort_type = parent.find('#video-orderby').val();
    let sort_method = sort_by_title_asc;
    switch (sort_type) {
        case "title":
            sort_method = ascending ? sort_by_title_asc : sort_by_title_desc;
            break;
        case "modified":
            sort_method = ascending ? sort_by_modified_asc : sort_by_modified_desc;
            break;
        case "priority":
            sort_method = ascending ? sort_by_priority_asc : sort_by_priority_desc;
            break;
        default:
            sort_method = sort_by_title_asc;
            break;

    }
    sort_switch_el = parent.find('#switch-chapter');

    if (!sort_switch_el.is(':checked')) {
        $('#id-sort-videos').html('');
        let chapter_video_items = parent.find('.pd-section--all-video').find('.pd-section__content').find('.cvideo');
        chapter_video_items.sort(sort_method);
        chapter_video_items.appendTo($(".pd-video-list").get(0));
    } else {
        parent.find('.pd-section--all-video').find('#id-sort-videos').html('');
        let chapters = parent.find('.pd-section--all-video').find('.pd-chapter ');
        chapters.each(function (index, chapter) {
            let chapter_video_items = $(chapter).find('.cvideo');
            let chapter_videos_el = $(chapter).find('.pd-chapter__list').get(0);
            chapter_video_items.sort(sort_method);
            chapter_video_items.appendTo(chapter_videos_el);
        });
    }
}

$(document).delegate(".project-chapter input.switch#id-sort-switch", "change", function () {
    let sort_switch_el = $(this);
    let sort_type_el = $(this).parents('.project-chapter').find("select#id-sort-type").first();
    let ascending_el = $(this).parents('.project-chapter').find(".messenger__order-right .sort-direction.active").first();
    sort_switch_el.parents('.project-chapter').find('.product-scene-list .project-chapter-videos#id-sort-videos').html('');
    sort_direction_check();
});

$(document).delegate(".project-chapter select#id-sort-type, #video-orderby", "change", function () {
    let sort_switch_el = $(this).parents('.project-chapter').find("input.switch#id-sort-switch").first();
    let sort_type_el = $(this);
    let ascending_el = $(this).parents('.project-chapter').find(".messenger__order-right .sort-direction.active").first();
    sort_switch_el.parents('.project-chapter').find('.product-scene-list .project-chapter-videos#id-sort-videos').html('');
    let parent = $(this).parents('.tab--video-all');
    sort_direction_check(parent);
});

$(document).delegate(".messenger__order-right .sort-direction.active", "click", function () {
    let sort_switch_el = $(this).parents('.project-chapter').find("input.switch#id-sort-switch").first();
    let sort_type_el = $(this).parents('.project-chapter').find("select#id-sort-type").first();
    $(this).parents('.project-chapter').find('.product-scene-list  .project-chapter-videos#id-sort-videos').html('');
    sort_direction_check();
    $(this).removeClass('active');
    $(this).siblings('.sort-direction').addClass('active');
});

function sort_direction_check(parent) {
    let direction = $('.video-order').find('.video-order-type.active');
    if (direction.hasClass('video-order__asc')) {
        sort_scenes_with(true, parent);
    } else {
        sort_scenes_with(false, parent);
    }
}
