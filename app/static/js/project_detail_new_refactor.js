$(document).ready(function () {
    $("#left-sidebar-open").click(function (e) {
        e.preventDefault();
        const $sidebar = $("#left-sidebar");

        if ($sidebar.hasClass("toggled")) {
            $sidebar.addClass("closing"); // アニメーション用のクラスを追加
            setTimeout(() => {
                $sidebar.removeClass("toggled closing boder-siderbar u-bg-background open-sidebar"); // アニメーション後にクラスを削除
            }, 200); // アニメーションの時間に合わせて調整
        } else {
            $sidebar.addClass("toggled boder-siderbar u-bg-background open-sidebar"); // サイドバーを開く
        }
    });
    $("#left-sidebar-close").click(function (e) {
        e.preventDefault();
        $("#left-sidebar").addClass("closing"); // アニメーション用のクラスを追加
        setTimeout(function() {
            $("#left-sidebar").removeClass("toggled closing");// アニメーション終了後にクラスを削除
        }, 200);
    });

    $(document).on('click', '.block-user-avatar', function (e) {
        e.preventDefault();
        let projectId;
        let $modal_manage = $('.modal-users-in-project');
        if ($(this).hasClass('sproject__user-btn')) {
            projectId = $(this).parents('.sprojects-item').attr('data-project');
        } else {
            projectId = $(this).parents('.project-item').attr('data-project-id');
        }
        $.ajax({
            type: "GET",
            data: {projectId: projectId},
            url: "/top/project_members",
            success: function (data) {
                $modal_manage.html(data.html);
                $modal_manage.attr('data-product', data.product_id);
                selectAction();
                inviteUser();
            },
            error: function () {
                $modal_manage.html('');
                $modal_manage.attr('data-product', '');
            },
            complete: function () {
                let modal_user_project = $('.modal-users-in-project');
                setTimeout(function () {
                    modal_user_project.removeClass('d-none-el');
                }, 100)
            }
        });
    });
})

document.addEventListener('click', function (event) {
    let schedulaCalendarModal = document.getElementById('schedulaCalendarModal');
    let schedulaCalendarContent = document.getElementsByClassName('sc-block');
    let staffModal = document.getElementById('modalUsersInProject');
    let staffModalContent = document.getElementsByClassName('block-users-in-project');
    let clickedElement = event.target;

     if (!$(schedulaCalendarModal).hasClass('d-none-schedule')){
        if (!schedulaCalendarContent[0].contains(clickedElement) && schedulaCalendarContent[0] !== clickedElement) {
            $(schedulaCalendarModal).addClass('d-none-schedule');
        }
    }
    if (!$(staffModal).hasClass('d-none-el')) {
        if (!staffModalContent[0].contains(clickedElement) && staffModalContent[0] !== clickedElement && !$('body').hasClass('modal-open')) {
            $(staffModal).addClass('d-none-el');
            $(staffModal).empty()
        }
    }
});

$(document).on('click', '.member-item__btn-change', function (e) {
    e.preventDefault();
    let $modal = $('.modal-users-in-project');
    
    // モーダルを非表示にする
    $modal.addClass('d-none-el');
    $modal.empty(); // モーダル内のコンテンツをクリア
});

$(document).on('click', '.member-item__btn-swap-order', function (e) {
    e.preventDefault();
    let $modal = $('.modal-users-in-project');
    
    // モーダルを非表示にする
    $modal.addClass('d-none-el');
    $modal.empty(); // モーダル内のコンテンツをクリア
});
