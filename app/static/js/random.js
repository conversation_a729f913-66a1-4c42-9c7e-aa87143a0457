function randomImageBackground(images) {
  function randomImage(max, min) {
    return Math.floor(Math.random() * (max - min + 1) + min);
  }
  $('.banner__img-pc, .banner__img').addClass(
    'img' + randomImage(images.length, 1)
  );
}

function removeNavbar() {
  $('header').addClass('hide')
}

function rezizeAuth() {
  let ratio = 1
  if ($(window).width() < 400) {
    ratio = $(window).width()/400;
    $('.auth__main').css('transform', 'scale('+ ratio +')');
  }

  if($(window).height() < $('.auth__main').height()*ratio) {
    $('.auth__main').css('transform', 'scale('+ $(window).height()/($('.auth__main').height()*ratio)*ratio*0.9 +')');
  }
}
