var $drpzoneInformation = [];
var $dropzoneUploadLogo = [];
var $dropzoneUpladThumbnail = [];
var real_name_information = '';
let credit_file;
let is_uploading_information = false;
let type_information = '';
let is_delete_file_information = false;
let is_delete_file_logo = false;
let is_delete_file_banner = false;
var modal_artist_init = false;
var modal_section_init = false;
var updating_artist = false;
var updating_section = false;
var modal_credit_init = false;
var delete_target = null;
var delete_progressing = false;
Dropzone.autoDiscover = false;

function actionBanner() {
    $('.project-list').on('click', '.action-notification',function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('.bdropdown').removeClass('open');
        let project_item = $(this).parents('.project-item');
        let product_id = project_item.attr('data-project-id');
        if (project_item.length < 1) {
            project_item = $(this).parents('.sprojects-item');
            product_id = project_item.attr('data-project');
        }
        let action_dom = project_item.find('.action-notification');

        $.ajax({
            type: "POST",
            data: {product_id: product_id},
            url: "/top/setting_notification_product_user",
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.status === 'success') {
                    if (response.notification === 'off') {
                        toastr.success("通知をOFFにしています。", "プロジェクトのお知らせ");
                        action_dom.html('ON Notification');
                        project_item.find('.pbanner, .sproject').addClass('no-notification')
                    } else {
                        toastr.success("通知をONにしています。", "プロジェクトのお知らせ");
                        action_dom.html('OFF Notification');
                        project_item.find('.pbanner, .sproject').removeClass('no-notification')
                    }
                } else {
                    toastr.error("エラーが発生しました", "プロジェクトのお知らせ");
                }
            },
            complete: function () {
                $('.pbanner__top .bdropdown').removeClass('open');
            }
        })
    });

    $('.project-list').on('click', '.max_scene-edit, .sproject-time__done-count:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let max_scene_el = $(this);
        let project_item = max_scene_el.parents('.project-item');
        let project_id = project_item.attr('data-project-id');
        let max_scene = $(this).attr('data-max-scene');
        let infor_scene = $(this).parents('.project-item').find('.pbanner__progress .progress');
        if (project_item.length < 1) {
            project_id = max_scene_el.parents('.sprojects-item').attr('data-project');
            max_scene = $(this).attr('data-max-scene');
            infor_scene = $(this).parents('.sprojects-item').find('.sproject__progressbar');
        }
        let current_scene = parseInt(infor_scene.attr('data-current-scene'));
        let done_scene = parseInt(infor_scene.attr('data-done-scene'));
        let type_page = infor_scene.attr('data-type');

        bootbox.prompt({
            title: "プロジェクトの総シーン数を設定してください。(現在のシーン合計: " + (current_scene) + ")",
            message: "<label> 総シーン数 * </label>",
            size: 'small',
            value: parseInt(max_scene),
            inputType: 'number',
            min: parseInt(current_scene),
            required: true,
            closeButton: false,
            buttons: {
                confirm: {
                    label: '更新',
                    className: 'btn btn--primary'
                },
                cancel: {
                    label: 'キャンセル',
                    className: 'btn btn--tertiary'
                }
            },
            callback: function (result) {
                let new_max_scene = result;
                $('.bdropdown').removeClass('open');

                if (new_max_scene) {
                    if (!project_id) {
                        toastr.error("入力した総シーン数が正しくありませんので、更新できませんでした。");
                    } else {
                        $.ajax({
                            url: '/top/update_max_scene',
                            method: 'POST',
                            data: {'project_id': project_id, "max_scene": new_max_scene},
                            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                max_scene_el.attr('data-max-scene', result);
                                project_item.find('.pbanner__progress').html(response.progress_bar);
                                project_item.find('.sproject-project-time').empty();
                                $(response.infor_project).appendTo(project_item.find('.sproject-date-time'));
                                project_item.find('.sproject-scene').empty();
                                project_item.find('.sproject-scene').append(response.infor_scene);
                                if (response.status === 'done') {
                                    project_item.find('.sproject-wishlist .icon').removeClass('icon--sicon-heart-o').addClass('icon--sicon-heart')
                                } else {
                                    project_item.find('.sproject-wishlist .icon').removeClass('icon--sicon-heart').addClass('icon--sicon-heart-o')
                                }
                            },
                            error: function (response) {
                                toastr.error('入力した総シーン数が正しくありませんので、更新できませんでした。');
                            }
                        });
                    }
                }
            }
        });
    });
}

function checkElement(element) {
    if ($(element).parents('.pbanner__user-list').length ||
        $(element).hasClass('icon--sicon-menu') ||
        $(element).hasClass("icon--sicon-union") ||
        $(element).hasClass('popup-container') ||
        $(element).parents('.pbanner__icon-expand-container').length ||
        $(element).hasClass('pbanner__icon-expand-container') ||
        $(element).hasClass('pbanner__detail-expand') ||
        $(element).parents('.pbanner__detail-expand').length ||
        $(element).parents('.bproject-setting').length ||
        $(element).hasClass('bproject-setting')) {
        return true;
    }
    if ($(element).parents(".bdropdown-menu").length || $(element).hasClass("bdropdown-menu")|| $(element).parents(".popup-container").length || $(element).hasClass("popup-container") || $(element).hasClass('pbanner__icon-expand-container')) {
        return true;
    }
    return false;
}

function animationPage() {
    $('.sprojects').on('click', '.project-item .pbanner__image', function(e) {
        e.preventDefault();
        if (checkElement(e.target)) {
            return;
        }
        $($('main')[1]).toggle('slide', {direction: 'left'}, 300);
        window.setTimeout(function(){
            $($('main')[0]).find('.container').show();
        }, 100)
        window.location = $(e.target).parents(".project-item-handle").find("a").attr('href');
    });

    $('.new-video-menu .project-item .pbanner__image').click(function(e) {
        e.preventDefault();
        if (checkElement(e.target)){
            return ;
        }
        $($('main')[1]).toggle('slide', {direction: 'right'}, 300);
        window.setTimeout(function(){
            $($('main')[0]).find('.container').show();
        }, 100);
        window.location = $('.new-video-menu .project-item a').attr('href');
    });
}

function showStaffInfo() {
    $('.project-list').on('click', '.button-show-staff:not(.btn--primary)', function(e) {
        let target = $(this).parents('.project-item');
        e.stopPropagation();
        e.preventDefault();

        // if(!target.is('.show-staff')) {
        //     target.addClass('show-staff');
            let project_id = target.attr('data-project-id');

            //show modal and loading icon
            $('#modal-staff-credit, #modal-staff-credit-artist, #modal-staff-credit-section, #modal-confirm-delete').remove();
            $('body').append(`
            <div class="modal popup-container" id="modal-staff-credit" role="dialog" style="z-index: 2000" data-project-id="${project_id}">
                <div class="modal-dialog">
                    <div class="modal-content popup-content u-col">
                        <div class="load-more-loading"></div>
                    </div>
                </div>
            </div>
            
            <div class="modal popup-container" id="modal-staff-credit-artist" role="dialog" style="z-index: 2001" data-project-id="${project_id}">
                <div class="modal-dialog">
                    <div class="modal-content popup-content u-col">
                        <div class="load-more-loading"></div>
                    </div>
                </div>
            </div>
            
            <div class="modal popup-container" id="modal-staff-credit-section" role="dialog" style="z-index: 2001" data-project-id="${project_id}">
                <div class="modal-dialog">
                    <div class="modal-content popup-content u-col">
                        <div class="load-more-loading"></div>
                    </div>
                </div>
            </div>
            
            <div class="modal popup-container" id="modal-confirm-delete" role="dialog" style="z-index: 2001">
                <div class="modal-dialog popup-dialog">
                    <div class="modal-content popup-content u-col u-w100">
                        <div class="popup-body bodytext-13 u-row">この操作は元に戻せません。本当に削除してもよろしいですか？</div>
                        <div class="popup-footer u-w100" style="text-align: right;">
                            <button type="button"
                                    class="btn btn--primary btn-confirm-delete-cancel">いいえ</button>
                            <button type="button"
                                    class="btn btn--tertiary btn-confirm-delete">はい</button>
                        </div>
                    </div>
                </div>
            </div>`)

            $('#modal-staff-credit').modal('show');
            renderStaffCredit();
        // }
    });

    $(document).on('hidden.bs.modal', '#modal-staff-credit-artist, #modal-staff-credit-section', function() {
        $('#modal-staff-credit').modal('show');
    })

    $(document).on('click', '#modal-staff-credit .modal-staff-credit-artist-add', function () {
        renderModalArtist('add');
    })

    $(document).on('click', '#modal-staff-credit .modal-staff-credit-section-add', function() {
        renderModalSection();
    })
}

function projectList() {
    $(document).on('click', '.member-ip__delete', function () {
        let ip_item = $(this).parents('.member-ip__item');
        if (ip_item.hasClass('new-ip')) {
            ip_item.remove();
        } else {
            let pu_id = $(this).parents('.modal-content').attr('data-product-user');
            let ip = ip_item.find('.member-ip__input input').attr('data-value');
            bootbox.confirm({
                message: gettext('Do you really want to delete this?'),
                buttons: {
                    confirm: {
                        label: 'はい',
                        className: 'btn btn-danger btn--tertiary btn-delete-message'
                    },
                    cancel: {
                        label: 'いいえ',
                        className: 'btn btn-info btn--primary btn-cancel-message'
                    }
                },
                callback: function (result) {
                    if (result) {
                        $.ajax({
                            type: "POST",
                            data: {
                                'pu_id': pu_id,
                                'ip': ip
                            },
                            url: "/top/delete_ip_user",
                            success: function (data) {
                                // toastr.success('IPアドレス制限を解除しました');
                                ip_item.remove();
                                let user_dom = $('.member-item[data-user^=' + data.user_id + ']');
                                user_dom.find('.detail-list-ip').html(data.str_list_ip);
                            }
                        });
                    }
                }
            });
        }
    });

    $(document).on('click', '.member-ip__new-btn', function () {
        var ip_new = $(this).parents('.member-ip__new').find('.member-ip__new-ip input').val();

        if (ip_new) {
            var ip_new_item = '<div class="member-ip__item new-ip">' +
                '<div class="member-ip__input">' +
                '<input class="sform-control sform-control--input" type="text" value="' + ip_new + '" data-value="' + ip_new + '">' +
                '</div><a class="member-ip__delete" href="#"><i class="icon icon--sicon-trash"></i></a>' +
                '</div>';
            $(this).closest('.member-ip__content').find('.member-ip__list').append(ip_new_item);
            $(this).parents('.member-ip__new').find('.member-ip__new-ip input').val('');
        } else {
            // Do something
            $(this).parents('.member-ip__new').find('.member-ip__new-ip input').focus();

            return;
        }
    });
}

function selectAction() {
    $('#modal-member-manage .sselect-wrapper').each(function () {
        var search = $(this).find('select').attr('data-search');
        console.log(search);
        var searchText = $(this).find('select').attr('data-search-text');

        $(this).find('select').SumoSelect({
            search: (search == 'true') ? true : false,
            searchText: searchText,
        });
        let is_only_view = $(this).find('select').attr('data-view');
        let is_access = 'edit';
        if (is_only_view === 'True') {
            $(this).find('select')[0].sumo.selectItem('view');
            is_access = 'view';
        } else {
            $(this).find('select')[0].sumo.selectItem('edit');
        }
        $(this).find('.select-user-access').on('change', function () {
            let x = (this).value;
            let user_dom = $(this).parents('.member-item');
            if (user_dom.length > 0) {
                let user_id = user_dom.attr('data-user');
                let product_id = user_dom.attr('data-pf');
                if (['edit', 'view'].includes(x) && is_access !== x) {
                    $.ajax({
                        type: "POST",
                        data: {
                            'user_id': user_id,
                            'product_id': product_id,
                            'access': x
                        },
                        url: "/top/update_access_view_user",
                        success: function (data) {
                            if (data.is_view_only !== is_only_view) {
                                // toastr.success('UPDATE ACCESS');
                            }
                        },
                        error: function () {
                            let user_dom = $('.member-item[data-user^=' + user_id + ']');
                            let is_only_view = user_dom.find('select').attr('data-view');
                            if (is_only_view === 'True') {
                                $(user_dom).find('select')[0].sumo.selectItem('view');
                            } else {
                                $(user_dom).find('select')[0].sumo.selectItem('edit');
                            }
                        }
                    });
                } else if (x === 'delete') {

                } else if (['resend', 'delete-inviting'].includes(x)) {
                    let jwt_token = user_dom.attr('data-jwt');
                    let is_only_view = user_dom.find('select').attr('data-view');
                    if (is_only_view === 'True') {
                        $(user_dom).find('select')[0].sumo.selectItem('view');
                    } else {
                        $(user_dom).find('select')[0].sumo.selectItem('edit');
                    }
                    if (x === 'resend') {
                        sendInviteEmailAgain(jwt_token, user_id);
                    } else if (x === 'delete-inviting') {
                        deleteUserInviting(jwt_token, user_id)
                    }
                }
            }

        });
    });

    $('.member-manage__content .member-item__btn-delete').each(function () {
        $(this).off('click').on('click', function () {
            let user_dom = $(this).parents('.member-item-container').find('.member-item');
            let user_id = user_dom.attr('data-user');
            let product_id = user_dom.attr('data-pf');

            bootbox.confirm({
                message: gettext('Would you like to leave the project?'),
                buttons: {
                    confirm: {
                        label: 'はい',
                        className: 'btn btn-success btn--tertiary btn-delete-message'
                    },
                    cancel: {
                        label: 'いいえ',
                        className: 'btn btn-danger btn--primary btn-cancel-message'
                    }
                },
                callback: function (result) {
                    if (result === true) {
                        $.ajax({
                            type: "POST",
                            url: "/top/update_access_view_user",
                            datatype: "json",
                            data: {
                                'user_id': user_id,
                                'product_id': product_id,
                                'access': 'delete'
                            },
                            success: function (data) {
                                // toastr.success('メンバーを退出させました');
                                let currentItemDom = $('.member-item[data-user^=' + user_id + ']').parents('.member-item-container');
                                if (currentItemDom.hasClass('can-self-edit-delete')) {
                                    location.reload();
                                }
                                currentItemDom.remove();
                                $('.project-item[data-project-id=' + product_id + ' .avatar[data-user^=' + user_id + ']').parent('.pbanner__user').remove()
                            },
                        });
                    }
                    user_dom.find('.member__ip-delete').addClass('hide')
                }
            });
        })
    });

    $('.member-item__ip-edit').each(function () {
        $(this).on('click', function () {
            let project_id = $(this).parents('.member-item').attr('data-pf');
            let user_id = $(this).parents('.member-item').attr('data-user');
            $.ajax({
                type: "GET",
                data: {
                    'project_id': project_id,
                    'user_id': user_id
                },
                url: "/top/member_list_ip",
                success: function (data) {
                    $('#modal-member-ip').find('.modal-dialog').html(data.html);
                    updateIPUser();
                }
            });

        })
    });
}

function manageMember() {
    $('.project-list').on('click', '.sproject__user-btn, .pbanner__user-list, .pbanner__admin-list', function (e) {
        e.preventDefault();
        let projectId;
        let $modal_manage = $('#modal-member-manage');
        if ($(this).hasClass('sproject__user-btn')) {
            projectId = $(this).parents('.sprojects-item').attr('data-project');
        } else {
            projectId = $(this).parents('.project-item').attr('data-project-id');
        }
        if ($(this).hasClass('pbanner__admin-list')) {
            $modal_manage.find('.member-manage__invite').addClass('hide');

            $.ajax({
                type: "GET",
                data: {projectId: projectId},
                url: "/top/project_admin_list",
                success: function (data) {
                    $modal_manage.find('.member-manage__content').html(data.html);
                    $modal_manage.find('.member-manage__content').attr('data-product', data.product_id);
                    draggableMember();
                },
                error: function () {
                    $modal_manage.find('.member-manage__content').html('');
                    $modal_manage.find('.member-manage__content').attr('data-product', '');
                }
            });
        } else {
            if ($(e.target).hasClass('pbanner__user-btn')) {
                return;
            } else {
                if ($(this).hasClass('pbanner__user-list') && $(this).find('.pbanner__user-btn').length) {
                    $modal_manage.find('.member-manage__invite').removeClass('hide');
                } else {
                    $modal_manage.find('.member-manage__invite').addClass('hide');
                }

                $.ajax({
                    type: "GET",
                    data: {projectId: projectId},
                    url: "/top/project_member_list",
                    success: function (data) {
                        $modal_manage.find('.member-manage__content').html(data.html);
                        $modal_manage.find('.member-manage__content').attr('data-product', data.product_id);
                        selectAction();
                        inviteUser();
                        draggableOwner();
                    },
                    error: function () {
                        $modal_manage.find('.member-manage__content').html('');
                        $modal_manage.find('.member-manage__content').attr('data-product', '');
                    }
                });
            }
        }
    });

    $(document).on('mouseenter mouseleave', '.member-manage__list-producer .member-item-container, .member-manage__list-director .member-item-container, .list-project-owner .member-item-container, .list-project-member .member-item-container', function (e) {
        if (!checkShowButtonProjectManage($(this))) {
            return
        }
        if (window.innerWidth > 992) {
            $(this).find('.member-item-action__button-container').toggleClass('show-button-action', e.type === 'mouseenter');
        }
    })

    $(document).on('click', '.member-manage__list-producer .member-item-container, .member-manage__list-director .member-item-container, .list-project-owner .member-item-container, .list-project-member .member-item-container', function (e) {
        if (!checkShowButtonProjectManage($(this))) {
            return
        }
        if($(this).find('.member-item-action__button-container').hasClass('show-button-action') && window.innerWidth < 992){
            $(this).find('.member-item-action__button-container').removeClass('show-button-action');
        } else if(!$(this).find('.member-item-action__button-container').hasClass('show-button-action') && window.innerWidth < 992) {
            $(this).find('.member-item-action__button-container').addClass('show-button-action');
        }
    })
}

function checkShowButtonProjectManage(item) {
    if (item.find('.member-item-action__button-container').parents('.manage-producer-director').length) {
        if ((item.find('.member-item-action__button-container').parents('.manage-producer-director').hasClass('project-done')
            && $('.manage-producer-director .member-item-container').index(item) === 0)
            || (item.find('.member-item-action__button-container').parents('.member-manage__list-producer').length
                && item.find('.member-item-action__button-container').parents('.member-manage__list-producer').find('.member-item-container').length < 2)) {
            return false
        }

        if (!$('.member-manage__group.member-manage').hasClass('can-edit-all')) {
            if (item.parents('.member-manage__list-producer').length && !item.hasClass('can-self-edit-delete')) {
                return false
            }
        }
    } else {
        if (item.find('.member-item-action__button-container').parents('.member-manage__group-owner').length
            && item.find('.member-item-action__button-container').parents('.member-manage__group-owner').find('.member-item-container').length < 2) {
            return false
        }
    }

    return true
}

function inviteUser() {
    $('.send-invite-email').on('click', function () {
        let email_dom = $('#email-member-inviting');
        let product_id = $('#modal-member-manage .member-manage__content').attr('data-product');
        if (!product_id) {
            product_id = $('#modalUsersInProject').attr('data-product')
        }
        $('.text-danger').remove();
        $('.error').removeClass('error');
        $('#invite_member_form').find('#product-member-inviting').val(product_id);
        if (!$(this).hasClass('button--disabled')) {
            $(this).addClass('button--disabled');
            const form = $('#invite_member_form');
            const emailMissingOrNotValid = form[0][2].checkValidity() || form[0][2].validity.valid;
            if (!emailMissingOrNotValid) {
                email_dom.addClass(('error'));
                email_dom.next().remove();
                email_dom.after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
                $(this).removeClass('button--disabled');
                return false;
            } else {
                form.submit();
            }
        }
    });

    $('#modal-invite-member').on('show.bs.modal', function () {
        $('#modal-member-manage').modal('hide')
    });

    $('#modal-invite-member').on('hidden.bs.modal', function () {
        let array_id = ['last-name-member-inviting', 'name-member-inviting', 'job-title-member-inviting', 'company-member-inviting'];
        let modal_invite = $('#modal-invite-member');
        for (i = 0; i < array_id.length; i++) {
            modal_invite.find('#' + array_id[i]).val('');
            modal_invite.find('#' + array_id[i]).attr('readonly', false);
        }
        $('.text-danger').remove();
        $('.error').removeClass('error');
        modal_invite.find('#email-member-inviting').val('');
        modal_invite.find('.send-invite-email').removeClass('button--disabled');
    });
}

function exportCSV() {
    $(document).on('click', '#export-acr, #export-comment', function (e) {
        let href = $(this).attr('export-url');
        let redirect_url = $(this).attr('update-project-url');
        let product_id = $('.project-item.active').attr('data-project-id');
        if (!redirect_url || !href || !product_id) {
            return
        }
        if ($(this).attr("id") == "export-acr") {
            // toastr.info('楽曲認識照合レポートを作成しています');
            $.ajax({
                url: '/product/acr_export/check_download_acr',
                data: {
                    'product_id': product_id
                },
                type: "GET",
                contentType: 'application/json',
                success: function (response, status, xhr) {
                    window.location = href
                },
                fail: function (e) {
                    console.log("fail: ", e);
                },
                error: function (data) {
                    let json_data = data.responseJSON;
                    if (json_data.status === 302) {
                        toastr.error(data.responseJSON.message);
                    } else if (json_data.status === 500) {
                        toastr.warning(data.responseJSON.message);
                    } else {
                        toastr.error(gettext('Something went wrong!'));
                    }
                }
            });
        } else {
            // toastr.info('コメントリストを作成中');
            window.location = href
        }
    });
}

function actionTab() {
    $(document).on('click', '.pbanner-info', function (e) {
        e.preventDefault();
    });
}

function showExpand() {

    // $(document).on('mouseenter mouseleave', '.pbanner__detail-expand .pbanner__detail-description', function (e) {
    //     $(this).find('.description-edit-pc').toggleClass('show-btn-edit', e.type === 'mouseenter' && window.innerWidth > 992);
    // });

    $(document).on('click', '.description-content__content:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if (!$(this).parents('.description-content').hasClass('cannot-check')) {
            $('#modal-description-edit').modal('show');
            type_information = 'description';
            getInfomationForModal('#modal-description-edit', $(this))
        }
    });

    $(document).on('mouseenter mouseleave', '.icon-container', function(e) {
        $(this).find('.pbanner__icon-expand-container').toggleClass('show-button-expand', e.type === 'mouseenter' && window.innerWidth > 992);
    });

    $(document).on('mouseenter mouseleave', '.pbanner__detail-expand .detail-expand-left .detail-code-name , .code-name-action', function (e) {
        $(this).parents('.pbanner__image').find('.code-name-edit-pc').toggleClass('show-btn-edit', e.type === 'mouseenter' && window.innerWidth > 992)
    });

    $(document).on('mouseenter mouseleave', '.pbanner__detail-expand .detail-expand-left .detail-time-range-container', function (e) {
        $(this).parents('.pbanner__image').find('.time-range-edit').toggleClass('show-btn-edit', e.type === 'mouseenter' && window.innerWidth > 992);
        // getInfomationForModal('#modal-schedule-edit', $(this))
    });

    $(document).on('mouseenter mouseleave', '.pbanner__detail-expand .pbanner__detail-expand-bottom', function (e) {
        $(this).parents('.pbanner__image').find('.owner-company-name-edit').toggleClass('show-btn-edit', e.type === 'mouseenter' && window.innerWidth > 992)
        type_information = 'client_name';
        getInfomationForModal('#modal-owner-company-name-edit', $(this))
    });

    $(document).on('mouseenter mouseleave', '.pbanner__detail-expand .credit-button-action', function (e) {
        $(this).parents('.pbanner__image').find('.credit-edit').toggleClass('show-btn-edit', e.type === 'mouseenter');
    });

    $(document).on('click', '.pbanner__icon-expand-container, .pbanner__icon-expand-container .icon--sicon-down', function (e){
        e.preventDefault();
        e.stopPropagation();
        let project_id = $(this).parents('.project-item').attr('data-project-id');
        $('.pbanner__image').find('.pbanner__icon-expand-container').removeClass('hide-button');
        $('.pbanner__image').find('.pbanner__detail-expand').css({'animation': 'slideout .25s forwards', 'pointer-events': 'none'});
        $(this).parents('.pbanner__image').find('.pbanner__detail-expand').css({'animation': 'slidein .25s forwards', 'pointer-events': 'all'});
        $(this).parents('.pbanner__image').find('.pbanner__icon-expand-container').addClass('hide-button');
        getProjectSettingDetail(project_id)
    });

    $(document).on('click', '.pbanner__icon-collapse-container, .pbanner__icon-collapse-container .icon--sicon-down', function (e){
        e.preventDefault();
        e.stopPropagation();
        $(this).parents('.pbanner__image').find('.pbanner__detail-expand').css({'animation': 'slideout .25s forwards', 'pointer-events': 'none'});
        var $this = $(this);
        setTimeout(function () {
            $this.parents('.pbanner__image').find('.pbanner__icon-expand-container').removeClass('hide-button');
        }, 200);
        $(this).find('.pbanner__icon-collapse-container').css({'animation': 'rotate-1 .25s forwards'});
    });

    $(document).on('click', '.description-edit-pc, .description-edit-sp', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('#modal-description-edit').modal('show');
        type_information = 'description';
        getInfomationForModal('#modal-description-edit', $(this))
    });

    $(document).on('click', '.pbanner__detail-owner-name:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('#modal-owner-company-name-edit').modal('show');
        type_information = 'company-name';
        // getInfomationForModal('.owner-company-name-edit', $(this))
    });

    $(document).on('click', '.detail-time-range:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('#modal-schedule-edit').modal('show');
        type_information = 'schedule';
        getInfomationForModal('#modal-schedule-edit', $(this))
    });

    dateRange();

    $(document).on('click', '.credit-edit', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let projectItem = $(this).parents('.project-item');
        if (projectItem.hasClass('show-staff')) {
            $(this).parents('.credit-button-action').find('.button-show-staff').trigger('click')
        }
        let project_id = projectItem.attr('data-project-id');
        $('#modal-drag-file-credit').attr('data-project-id', project_id);
        $('#modal-drag-file-credit').modal('show');
        type_information = 'information';
        let creditName = $(this).parents('.credit-button-action').find('.button-show-staff').attr('data-file-credit');
        if (creditName) {
            $('.mattach-info-file-information .file-name').text(creditName);
            $('.mattach-info-file-information').removeClass('hide');
        }
    });

    $(document).on('click', '.detail-code-name:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('#modal-code-name-edit').modal('show');
        type_information = 'code_name';
        getInfomationForModal('#modal-code-name-edit', $(this));
    });

    $(document).on('hidden.bs.modal', '#modal-description-edit, #modal-drag-file-credit, #modal-code-name-edit, #modal-owner-company-name-edit, #modal-schedule-edit', function () {
        $(this).attr('data-project-id', '');
        $('input[name="code_name"], textarea[name="description-project"], input[name="owner-company-name"]').val('');
        type_information = '';
        $('#modal-drag-file-credit .mattach-info-file .file-name').text('');
        $('#modal-drag-file-credit .mattach-info-file').addClass('hide');
        $dropzoneInformation.removeAllFiles();
        real_name_information = '';
        credit_file = '';
        is_uploading_information = false;
        is_delete_file_information = false;
    })
}

function dateRange() {
    // Range time
    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    if ($('#schedule_date').length) {
        $('#schedule_date').daterangepicker({
            startDate: new Date(),
            endDate: moment(new Date()).add(1, 'months'),
        })
    }
}

function getProjectSettingDetail(project_id) {
    if (project_id) {
        let projectDom = $('.project-item[data-project-id=' + project_id + ']');
        $.ajax({
            type: 'GET',
            datatype: 'json',
            url: '/top/get_project_setting_detail',
            data: {
                'project_id': project_id,
            },
            success: function (data) {
                projectDom.find('.pbanner__detail-expand').empty();
                projectDom.find('.pbanner__detail-expand').append(data.html);
                buttonCollapseAnimation(projectDom);
                checkDoneRate(projectDom);
            }
        });
    }
}

function buttonCollapseAnimation(dom) {
    dom.find('.pbanner__icon-collapse-container').css({'animation': 'rotate .25s forwards'});
}

function checkDoneRate(dom) {
    let percents = dom.find('.pbanner__detail-expand .sproject-time__current-heart-rate').text();
    let domIconExpand = dom.find('.pbanner__detail-expand .sproject-wishlist i');
    if(percents === "100%"){
        domIconExpand.removeClass('icon--sicon-heart-o').addClass('icon--sicon-heart');
    } else {
        domIconExpand.removeClass('icon--sicon-heart').addClass('icon--sicon-heart-o');
    }
}

function getInfomationForModal(modalEdit, element) {
    let projectItem = element.parents('.project-item');
    let project_id = projectItem.attr('data-project-id');
    $(modalEdit).attr('data-project-id', project_id);
    if (project_id && type_information) {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_project_detail_info",
            data: {
                'project_id': project_id,
                'type_information': type_information
            },
            success: function (data) {
                let currentValue = escapeHtml(data.current_value);
                if (type_information === 'description') {
                    $('textarea[name="description-project"]').val(currentValue)
                } else if (type_information === 'schedule') {
                    if(data.created_time) {
                        $('#schedule_date').val('');
                        let placeholderSchedule = formatDate(data.created_time) + ' - ' + formatDate(new Date());
                        $('#schedule_date').attr('placeholder',placeholderSchedule); //Set place holder for input date range picker
                    } else {
                        $('#schedule_date').attr('placeholder', 'yyyy/mm/dd - yyyy/mm/dd');
                        let start_Date = new Date(data.start_time);
                        let end_Date = new Date(data.end_time);
                        $('#schedule_date').daterangepicker({
                            startDate: start_Date,
                            endDate: end_Date,
                        });
                    }
                    allowEmptyDateRange();
                } else if(type_information === 'client_name'){
                    $('input[name="owner-company-name"]').val(currentValue);
                } else {
                    $('input[name="code_name"]').val(currentValue)
                }
            }
        });
    }
}

function allowEmptyDateRange() {
    let dateRangeVal = $('#schedule_date').val().trim();

    $('#schedule_date').on('input', function(e) {
        dateRangeVal = e.target.value;
    });

    $('#schedule_date').on('apply.daterangepicker', function(ev, picker) {
        $('#schedule_date').val(`${picker.startDate.format('YYYY/MM/DD')} - ${picker.endDate.format('YYYY/MM/DD')}`);
    });

    $('#schedule_date').on('hide.daterangepicker', function(ev, picker) {
        if(dateRangeVal === ''){
            $('#schedule_date').val('')
        }
    });
}

function updateProjectSetting() {
    $(document).on('click', '.btn-update-project-setting', function () {
        let modalDom = $(this).parents('.modal');
        let project_id = modalDom.attr('data-project-id');
        let projectDom = $('.project-item[data-project-id=' + project_id + ']');
        if (project_id && type_information) {
            if (is_uploading_information) {
                let timeoutUploadInformation = setTimeout(function () {
                    clearInterval(waiting_file_loading);
                    toastr.error(gettext('Something went wrong!'));
                }, 900000);
                let waitingFileLoading = setInterval(function () {
                    let progress = getProgressUploaded();
                    let progressDom = $('.upload-button-wrapper');
                    activeProgress();
                    progressDom.find('.fill .process').css('width', progress + '%');
                    if (!is_uploading_information) {
                        clearTimeout(timeoutUploadInformation);
                        clearInterval(waitingFileLoading);
                        ajaxUpdateProjectSetting(project_id, type_information, modalDom, projectDom)
                    }
                }, 100);
             } else {
                 ajaxUpdateProjectSetting(project_id, type_information, modalDom, projectDom)
             }

        }
    })
}

function ajaxUpdateProjectSetting(project_id, type_information, modalDom, projectDom) {
    let new_value = '';
    let inputDom;
    if (type_information === 'code_name') {
        inputDom = $('#code-name-edit input');
    } else if (type_information === 'description') {
        inputDom = $('#description-edit textarea');
    } else if (type_information === 'information') {

    } else if (type_information === 'schedule') {
        inputDom = $('#modal-schedule-edit input');
    } else if(type_information === 'client_name'){
        inputDom = $('#modal-owner-company-name-edit input');
    }

    if (inputDom) {
        new_value = inputDom.val().trim();
    }
    let dataForm = new FormData();
    dataForm.append('project_id', project_id);
    dataForm.append('type_information', type_information);
    dataForm.append('new_value', new_value);
    dataForm.append('real_name', real_name_information);
    dataForm.append('file', credit_file);
    dataForm.append('is_delete_file_information', is_delete_file_information);

    $.ajax({
        type: "POST",
        contentType: false,
        processData: false,
        cache: false,
        url: "/top/update_project_setting",
        data: dataForm,
        success: function (data) {
            let newValue = escapeHtml(data.new_value);
            if (type_information === 'description') {
                let newValue = data.new_value !== '' ? escapeHtml(data.new_value) : '';
                projectDom.find('.description-content .description-content__content').text(newValue);
                $('#id_description').val(newValue);
                projectDom.find('.description-content').toggleClass('none-description', newValue === '');
            } else if (type_information === 'code_name') {
                let newValue = data.new_value !== '' ? escapeHtml(data.new_value) : 'CODE NAME';
                projectDom.find('.detail-code-name .detail-code-name__content').text(newValue);
                $('#id_code_name').val(newValue);
                let title_page = data.title_page;
                changeTitlePage(title_page)
            } else if(type_information === 'client_name') {
                let newValue = data.new_value !== '' ? escapeHtml(data.new_value) : 'オーナー会社名';
                projectDom.find('.pbanner__detail-owner-name').text(newValue); //Check number of company name if more than 1 value is ''
            } else if(type_information === 'schedule') {
                let newValue = data.new_value !== '' ? escapeHtml(data.new_value) : 'YY/MM/DD - YY/MM/DD';
                projectDom.find('.detail-time-range').text(newValue);
            } else {
                let newValue = escapeHtml(data.real_name);
                projectDom.find('.button-show-staff').attr('data-file-credit', newValue);
                if (!newValue) {
                    projectDom.find('.button-show-staff').removeClass('btn--secondary').addClass('btn--primary disabled');
                } else {
                    projectDom.find('.button-show-staff').removeClass('btn--primary disabled').addClass('btn--secondary');
                }
            }
        },
        complete: function () {
            modalDom.modal('hide');
        }
    });
}


function changeTitlePage(title_page) {
    if ($('.pbanner-tab.pbanner-tab-all.active').length) {
        $('title').text(title_page + ' | HOME');
    } else if ($('.pbanner-tab.pbanner-tab--exchange.active').length) {
        $('title').text(title_page + ' | TALKROOM');
    } else if ($('.pbanner-tab.pbanner-tab-message.active').length) {
        $('title').text(title_page + ' | DM');
    }
}

function dragDropFileCredit() {
    let thisModal = $('#modal-drag-file-credit');
    if (thisModal.find('#uploadFileInformation').length) {
        Dropzone.autoDiscover = false;
        var previewNode = thisModal.find('.mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        try{

            $dropzoneInformation = new Dropzone('#uploadFileInformation', {
                maxFilesize: 4500,
                timeout: 900000,
                autoDiscover: false,
                acceptedFiles: '.html',
                previewsContainer: '#modal-drag-file-credit .mattach-previews-form',
                previewTemplate: previewTemplate,
                url: "/",
                autoProcessQueue: false,
                autoQueue: false,
                clickable: '#uploadFileInformation',
                maxFiles: 1,
                dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
            });
    
            $dropzoneInformation.on("maxfilesexceeded", function (file) {
                $dropzoneInformation.removeAllFiles();
                $dropzoneInformation.addFile(file);
            });
    
            $dropzoneInformation.on('removedfile', function (file) {
                real_name_information = '';
            });
    
            $dropzoneInformation.on('addedfile', function (file, e) {
                if ($dropzoneInformation.files && $dropzoneInformation.files[0] && $dropzoneInformation.files[0].name.match(/\.(html|HTML)$/)) {
                    let file_dom = $(file.previewElement);
                    real_name_information = file.name;
                    let file_preview = thisModal.find('.mattach-preview-container-form').find(".mcommment-file__name-form");
                    for (let i = 0; i < file_preview.length; i++) {
                        $(file_preview[i]).text('');
                        $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name_information);
                        break;
                    }
                    thisModal.find(".mattach-info-file").addClass("hide");
                    if (thisModal.find(".mattach-template").length == 2) {
                        $(thisModal.find(".mattach-template")[0]).remove();
                    }
                    file_name = this.files[0].name;
                    if (thisModal.find('.account__file').length) {
                        thisModal.find('.account__file').hide();
                        thisModal.find('.account__file').next().hide();
                    }
                    uploadFileS3Information(file, file_dom);
    
                } else if (!$dropzoneInformation.files) {
                    return false;
                } else {
                    alert('HTMLのみアップロードできます。');
                    $dropzoneInformation.removeAllFiles(true);
                }
            });
        }catch(e){
            console.log(e);
        }

        thisModal.find('.mattach-info-file .icon--sicon-close').on('click', function () {
            thisModal.find('.mattach-info-file').addClass('hide');
            is_delete_file_information = true;
        })
    }
}

function dragDropLogoSettingProject() {
    let thisModal = $('#modal-project-setting .project__upload-logo');
    if (thisModal.find('#dropzoneUpLoadLogo').length) {
        var previewNode = thisModal.find('.mattach-preview-container-form-upload-logo .mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        Dropzone.autoDiscover = false;

        try{

            $dropzoneUploadLogo = new Dropzone('#dropzoneUpLoadLogo', {
                maxFilesize: 4500,
                timeout: 900000,
                autoDiscover: false,
                acceptedFiles: '.png, jpg, .svg, .jpeg, .PNG, .JPG, .SVG, .JPEG',
                previewsContainer: '#modal-project-setting .mattach-preview-container-form-upload-logo .mattach-previews-form',
                previewTemplate: previewTemplate,
                url: "/",
                autoProcessQueue: false,
                autoQueue: false,
                clickable: '#dropzoneUpLoadLogo',
                maxFiles: 1,
                dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
            });
    
            $dropzoneUploadLogo.on("maxfilesexceeded", function (file) {
                $dropzoneUploadLogo.removeAllFiles();
                $dropzoneUploadLogo.addFile(file);
            });
    
            $dropzoneUploadLogo.on('removedfile', function (file) {
                real_name_information = '';
            });
    
            $dropzoneUploadLogo.on('addedfile', function (file, e) {
                if ($dropzoneUploadLogo.files.length > 1) {
                    $dropzoneUploadLogo.removeAllFiles(true);
                    $dropzoneUploadLogo.addFile(file);
                }
    
                if ($dropzoneUploadLogo.files && $dropzoneUploadLogo.files[0] && $dropzoneUploadLogo.files[0].name.match(/\.(png|PNG|jpg|JPG|jpeg|JPEG|svg|SVG)$/)) {
                    let file_dom = $(file.previewElement);
                    real_name_information = file.name;
                    let file_preview = thisModal.find('.mattach-preview-container-form-upload-logo').find(".mcommment-file__name-form");
                    for (let i = 0; i < file_preview.length; i++) {
                        $(file_preview[i]).text('');
                        $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name_information);
                        break;
                    }
                    file_name = this.files[0].name;
                    if (thisModal.find('.account__file').length) {
                        thisModal.find('.account__file').hide();
                    }
    
                    readDataUploadImage(file, 'logo', imageCropperLogo)
    
                } else if (!$dropzoneUploadLogo.files) {
                    return false;
                } else {
                    alert('PNG, JPG, SVG, JPEGのみアップロードできます。');
                    $dropzoneUploadLogo.removeAllFiles(true);
                }
            });
        }catch(e){
            console.log(e)
        }

        thisModal.find('.account__file .icon--sicon-close').on('click', function () {
            thisModal.find('.account__file').addClass('hide');
            is_delete_file_logo = true;
        })
    }
}

var imageCropperBanner = {
    viewMode: 1,
    rotatable: false,
    aspectRatio: 20 / 4,
    minCropBoxWidth: 200,
    minCropBoxHeight: 200,
    minContainerHeight: 400,
};

var imageCropperLogo = {
    viewMode: 1,
    rotatable: false,
    aspectRatio: 433 / 244,
    minCropBoxWidth: 200,
    minCropBoxHeight: 200,
    minContainerHeight: 400,
};

function readDataUploadImage(file, fileChange, crop) {
    let $image = $('#image-crop');
    let cropBoxData, canvasData;
    let fileReader = new FileReader();

    fileReader.onloadend = function (e) {
        $image.attr('src', e.target.result);
        $('#modalCropBanner').modal('show');
    };
    fileReader.readAsDataURL(file);

    $("#modalCropBanner").modal({
        show: false,
        backdrop: 'static'
    });

    $('#modalCropBanner').off().on('shown.bs.modal', function () {
        $image.cropper({
            viewMode: crop.viewMode,
            rotatable: crop.rotatable,
            aspectRatio: crop.aspectRatio,
            minCropBoxWidth: crop.minCropBoxWidth,
            minCropBoxHeight: crop.minCropBoxHeight,
            minContainerHeight: crop.minContainerHeight,
            ready: function () {
                $image.cropper('setCanvasData', canvasData);
                $image.cropper('setCropBoxData', cropBoxData);
            }
        });
        // $('.modal:not(#modalCropBanner)').hide();
    }).on('hidden.bs.modal', function () {
        cropBoxData = $image.cropper('getCropBoxData');
        canvasData = $image.cropper('getCanvasData');
        $('#modal-project-setting').show();
        $image.cropper('destroy');
    });

    $("#modalCropBanner .close").off('click'). on('click', function () {
        if (fileChange === 'logo') {
            $dropzoneUploadLogo.removeAllFiles()
        } else if (fileChange === 'banner') {
            $dropzoneUpladThumbnail.removeAllFiles()
        }
    });


    $('.js-crop-and-upload-project').unbind().bind("click", function () {
        var cropData = $image.cropper("getData");
        var uploadBanner = function () {
            $('#id_x').val(cropData['x']);
            $('#id_y').val(cropData['y']);
            $('#id_height').val(cropData['height']);
            $('#id_width').val(cropData['width']);
            $('#modalCropBanner').modal('hide');
        };

        var uploadlogo = function () {
            $('#id_x_logo').val(cropData['x']);
            $('#id_y_logo').val(cropData['y']);
            $('#id_height_logo').val(cropData['height']);
            $('#id_width_logo').val(cropData['width']);
            $('#modalCropBanner').modal('hide');
        };

        switch (fileChange) {
            case "banner":
                uploadBanner();
                break;
            case "logo":
                uploadlogo();
                break;
            default:
                break
        }
    });


    // Enable zoom in button
    $('.js-zoom-in').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').click(function () {
        $image.cropper('zoom', -0.1);
    });
}

function dragDropThumbnailSettingProject() {
    let thisModal = $('#modal-project-setting .project__upload_banner');
    if (thisModal.find('#dropzoneUpLoadThumbnail').length) {
        var previewNode = thisModal.find('.mattach-preview-container-form-upload-thumbnail .mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        Dropzone.autoDiscover = false;
        try{

            $dropzoneUpladThumbnail = new Dropzone('#dropzoneUpLoadThumbnail', {
                maxFilesize: 4500,
                timeout: 900000,
                autoDiscover: false,
                acceptedFiles: '.png, jpg, .svg, .jpeg, .PNG, .JPG, .SVG, .JPEG',
                previewsContainer: '#modal-project-setting .mattach-preview-container-form-upload-thumbnail .mattach-previews-form',
                previewTemplate: previewTemplate,
                url: "/",
                autoProcessQueue: false,
                autoQueue: false,
                clickable: '#dropzoneUpLoadThumbnail',
                maxFiles: 1,
                dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
            });
    
            $dropzoneUpladThumbnail.on("maxfilesexceeded", function (file) {
                $dropzoneUpladThumbnail.removeAllFiles();
                $dropzoneUpladThumbnail.addFile(file);
            });
    
            $dropzoneUpladThumbnail.on('removedfile', function (file) {
                real_name_information = '';
                // thisModal.find('.btn-upload-file-messenger-owner').addClass('disable');
            });
    
            $dropzoneUpladThumbnail.on('addedfile', function (file, e) {
                if ($dropzoneUpladThumbnail.files.length > 1) {
                    $dropzoneUpladThumbnail.removeAllFiles(true);
                    $dropzoneUpladThumbnail.addFile(file);
                }
                if ($dropzoneUpladThumbnail.files && $dropzoneUpladThumbnail.files[0] && $dropzoneUpladThumbnail.files[0].name.match(/\.(png|PNG|jpg|JPG|jpeg|JPEG|svg|SVG)$/)) {
                    let file_dom = $(file.previewElement);
                    real_name_information = file.name;
                    let file_preview = thisModal.find('.mattach-preview-container-form-upload-thumbnail').find(".mcommment-file__name-form");
                    for (let i = 0; i < file_preview.length; i++) {
                        $(file_preview[i]).text('');
                        $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name_information);
                        break;
                    }
    
                    file_name = this.files[0].name;
                    if (thisModal.find('.account__file').length) {
                        thisModal.find('.account__file').hide();
                    }
    
                    readDataUploadImage(file, 'banner', imageCropperBanner)
    
                } else if (!$dropzoneUpladThumbnail.files) {
                    return false;
                } else {
                    alert('PNG, JPG, SVG, JPEGのみアップロードできます。');
                    $dropzoneUpladThumbnail.removeAllFiles(true);
                }
            });
        }catch(e){
            console.log(e)
        }

        thisModal.find('.account__file .icon--sicon-close').on('click', function () {
            thisModal.find('.account__file').addClass('hide');
            is_delete_file_banner = true;
        })
    }
}

function uploadFileS3Information(file, file_dom) {
    file_dom.find('.determinate').css('width', '0%');
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            credit_file = data.presigned_post.fields["key"];
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr = new XMLHttpRequest();
            xhr.open('POST', url);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s');
                    file_dom.find('.determinate').css('width', percentComplete);
                    if ($('.upload-button-wrapper') && $('.upload-button-wrapper').hasClass("clicked")){
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        file_dom.find('.determinate').css('width', "100%");
                        is_uploading_information = false
                    }
                }
            };
            is_uploading_information = true;
            xhr.send(postData);
        }
    })
}

let current_x, current_y, current_height, current_width, current_x_logo, current_y_logo, current_height_logo,
    current_width_logo;

function projectSetting() {
    $(document).on('click', '.bproject-setting', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let projectId = $(this).parents('.project-item').attr('data-project-id');
        if (projectId) {
            is_delete_file_logo = false;
            is_delete_file_banner = false;
            let url = '/top/get_form_project_setting_modal/' + projectId +'/';
            $.ajax({
                type: 'GET',
                url: url,
                data: {},
                success: function (data) {
                    $('#modal-project-setting .popup-body-project-setting').empty();
                    $('#modal-project-setting .popup-body-project-setting').append(data);
                    $('#modal-project-setting').modal('show');
                    dragDropLogoSettingProject();
                    dragDropThumbnailSettingProject();
                    current_x = $('#id_x').val();
                    current_y = $('#id_y').val();
                    current_height = $('#id_height').val();
                    current_width = $('#id_width').val();
                    current_x_logo = $('#id_x_logo').val();
                    current_y_logo = $('#id_y_logo').val();
                    current_height_logo = $('#id_height_logo').val();
                    current_width_logo = $('#id_width_logo').val();
                },
                complete: function () {

                },

            });
        }
    });

    $(document).on('click', '.project-setting__block-list__butoon-add', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $('#modal-search-block-list').modal('show');
    });

    $(document).on('hidden.bs.modal', '#modal-search-block-list, #modalCropBanner', function () {
        $('body').addClass('modal-open');
    });

    $(document).on('mouseenter mouseleave', '.user-info__artist-infor .artist-infor__name', function(e) {
        $(this).find('.user-info__delete').toggleClass('show_action_delete', e.type === 'mouseenter');
    });

    $(document).on('click', '.user-info__artist-infor .user-info__delete', function (e) {
        let artistDom = $(this).parents('.project-setting__block-list-selected');
        bootbox.confirm({
            className: "modal-unlock-artist",
            message: gettext('Are you sure you want to unlock this artist?'),
            buttons: {
                cancel: {
                    label: 'キャンセル',
                    className: 'btn btn--primary'
                },
                confirm: {
                    label: 'はい',
                    className: 'btn btn--tertiary'
                }
            },
            callback: function (result) {
                if (result) {
                    artistDom.remove()
                }
            }
        });

        $('.bootbox-confirm .bootbox-close-button').remove();
        $('.bootbox-confirm').css({'z-index': '9999'})
        $('.bootbox-confirm .modal-footer').css({'border-top': 'none'});
        $('.modal-unlock-artist').css({'z-index': '9999'});
    });

}

function validateFormProjectSetting() {
    let countError = 0;
    $('.error-border').removeClass('error-border');
    $('.errorlist').remove();
    let textDom = $('#id_text_note');
    let textValue = textDom.val().trim();
    if (textValue !== '' && textValue.length > 255) {
        textDom.addClass('error-border');
        $('<ul class="errorlist">' +
            '<li>この値は 255 文字以下でなければなりません</li>' +
            '</ul>').insertAfter(textDom);
        countError++;
    }

    let acrHostDom = $('#id_acr_host');
    let acrAccessKeyDom = $('#id_acr_access_key');
    let acrAccessSecretDom = $('#id_acr_access_secret');
    let acrHostValue = acrHostDom.val().trim();
    let acrAccessKeyValue = acrAccessKeyDom.val().trim();
    let acrAccessSecretValue = acrAccessSecretDom.val().trim();

    if ((acrHostValue && acrAccessKeyValue && acrAccessSecretValue) || (!acrHostValue && !acrAccessKeyValue && !acrAccessSecretValue)) {
        return countError > 0
    } else {
        acrHostDom.addClass('error-border');
        acrAccessKeyDom.addClass('error-border');
        acrAccessSecretDom.addClass('error-border');
        $('<ul class="errorlist">' +
            '<li>認識できません！ 設定を確認して再試行してください！</li>' +
            '</ul>').insertAfter(acrHostDom);
        countError++;
    }

    return countError > 0
}

function submitProjectSetting() {
    $(document).on('click', '#submit-form-update', function (e) {
        e.preventDefault();
        let button_dom = $(this);
        if (!button_dom.hasClass('disable')) {
            if (validateFormProjectSetting()) {
                return
            }
            // submit form
            button_dom.addClass('disable');
            let form = $('#modal-form-project-settings');
            let url = form.attr('action');

            form.off().on('submit', function (e) {
                e.preventDefault();
                let formData = new FormData(form[0]);
                let listArtistId = getListIdArtistBlocked();
                if ($dropzoneUploadLogo.files.length) {
                    formData.append('id_logo', $dropzoneUploadLogo.files[0]);
                    formData.append('id_logo_name', $dropzoneUploadLogo.files[0].name);
                } else {
                    $('#id_x_logo').val(current_x_logo);
                    $('#id_y_logo').val(current_y_logo);
                    $('#id_height_logo').val(current_height_logo);
                    $('#id_width_logo').val(current_width_logo);
                }

                if ($dropzoneUpladThumbnail.files.length) {
                    formData.append('id_image', $dropzoneUpladThumbnail.files[0]);
                    formData.append('id_image_name', $dropzoneUpladThumbnail.files[0].name);
                } else {
                    $('#id_x').val(current_x);
                    $('#id_y').val(current_y);
                    $('#id_height').val(current_height);
                    $('#id_width').val(current_width);
                }

                formData.append('list_artist_block', listArtistId);
                formData.append('is_delete_file_banner', is_delete_file_banner);
                formData.append('is_delete_file_logo', is_delete_file_logo);
                $.ajax({
                    type: "POST",
                    url: url,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        let projectDom = $('.project-item[data-project-id=' + data.project_id + ']').find('.pbanner__image');
                        $('#modal-project-setting').modal('hide');
                        if (data.new_value === true) {
                            projectDom.css('background-image', 'url(/static/images/ProjectBanner_b.png)');
                        } else if (data.new_value) {
                            projectDom.css('background-image', 'url(' + data.new_value + ')');
                        }
                        projectDom.find('.project-text').empty();
                        projectDom.find('.project-text').html(data.text_note);
                    },
                    statusCode: {
                        400: function (data) {
                            let str = data.responseText;
                            str = str.replaceAll(')(', ', ').replaceAll(')', '').replaceAll('(', '');
                            let array = str.split(',');
                            var items = [];

                            for (i = 0; i <= array.length - 2; i += 2) {
                                items[array[i]] = array[i + 1]
                            }

                            for (const [key, value] of Object.entries(items)) {
                                let dom = $('#modal-form-project-settings #id_' + key.replaceAll(/'/g, '').trim());
                                let new_value = value.replace('[', '').replace(']', '').replaceAll(/'/g, '').trim();
                                $(`<ul class="errorlist"><li>${new_value}</li></ul>`).insertAfter(dom);
                                dom.addClass('error-border')
                            }
                        }
                    },
                    complete: function () {
                        button_dom.removeClass('disable');
                        $dropzoneUploadLogo.removeAllFiles();
                        $dropzoneUpladThumbnail.removeAllFiles();
                        is_delete_file_logo = false;
                        is_delete_file_banner = false;
                    }
                });
            });

            form.submit();
        }
        return
    })
}

function searchListArtistBlock() {
    $(document).on('keypress', function (e) {
        if (e.which == 13 && $('#id_block-list__search').is(":focus")) {
            e.preventDefault();
            let projectId = $('#modal-form-project-settings').attr('data-project-id');
            let listArtist = getListIdArtistBlocked();
            let keyWord = $('#id_block-list__search').val();
            $.ajax({
                type: 'GET',
                datatype: "json",
                url: '/top/search_artist_to_add_block_list',
                data: {
                    'project_id': projectId,
                    'list_artist': listArtist,
                    'key_word': keyWord
                },
                success: function (data) {
                    $('.block-list__results .block-list__results-search').empty();
                    $('.block-list__results .block-list__results-search').append(data.html)
                },
            });
        }
    });

    $(document).on('click', '.add-artist-into-block-list', function () {
        let artist_id = $(this).parents('.block-list__results-search-detail').attr('data-artist');
        let projectId = $('#modal-form-project-settings').attr('data-project-id');
        if (projectId && artist_id && !$(this).hasClass('artist-selected')) {
            $(this).addClass('artist-selected disabled');
            $(this).val('追加しました');

            $.ajax({
                type: 'POST',
                datatype: "json",
                url: '/top/add_artist_into_block_list',
                data: {
                    'project_id': projectId,
                    'artist': artist_id,
                },
                success: function (data) {
                    $('.project-setting__block-list-artist').append(data.html);
                },
                error: function (data) {
                    $(this).removeClass('artist-selected');
                }
            });
        }
    });

    $(document).on('hidden.bs.modal', '#modal-search-block-list', function () {
        $('.block-list__results .block-list__results-search').empty();
        $('#id_block-list__search').val('')
    })
}

function getListIdArtistBlocked() {
    let listArtistId = [];
    if ($('.project-setting__block-list-selected:not(.hide)').length) {
        $('.project-setting__block-list-selected:not(.hide)').each(function () {
            listArtistId.push($(this).attr('data-artist'))
        });
    }
    return listArtistId.join(',')
}

function addAdmin() {
    $(document).on('click', '.invite-the-admin-btn', function (){
        let project_id = $('#modal-member-manage .member-manage__content').attr('data-product');
        if (!project_id) {
            project_id = $('#modalUsersInProject').attr('data-product')
        }
        $('#modal-add-admin').modal('show');
        getAdminToAdd(project_id)
    });

    $(document).on('click', '#modal-add-admin .add-admin-into-list', function(){
        let buttonDom = $(this);
        let user_id = buttonDom.parents('.add-director__results-search-detail').attr('data-user');
        let project_id = $('#modal-member-manage .member-manage__content').attr('data-product');
        if (!project_id) {
            project_id = $('#modalUsersInProject').attr('data-product')
        }
        console.log('user_id: ', user_id)
        let new_ui = false;
        if (!project_id){
            project_id = $('#modalUsersInProject').attr('data-product')
            new_ui = true
        }
        if (!buttonDom.hasClass('disabled-btn-add')) {
            buttonDom.addClass('disabled-btn-add');
            addAdminToProject(user_id, project_id, buttonDom, new_ui);
        }
    })
}

function getAdminToAdd(project_id) {
    if (project_id) {
        $.ajax({
            type: "POST",
            url: "/top/get_admin_to_add_project",
            datatype: "json",
            async: false,
            data: {
                'project_id': project_id,
            },
            success: function (data) {
                $('#modal-add-admin .block-list__results-search').empty();
                $('#modal-add-admin .block-list__results-search').append(data.html);
            },
            error: function () {
                $('#modal-add-admin').modal('hide')
            },
            complete: function () {
            }
        });
    }
}

function addAdminToProject(user_id, project_id, buttonDom, new_ui=false) {
    let is_project_detail = $('.prdt').length > 0;
    $.ajax({
        type: "POST",
        url: "/top/add_this_admin_to_project",
        datatype: "json",
        async: false,
        data: {
            'project_id': project_id,
            'user_id': user_id,
            'is_project_detail': is_project_detail
        },
        success: function (data) {
            if (new_ui){
                $('.block-producers').append(data.html)
            }else {
                $('#modal-member-manage .member-manage__list.member-manage__list-producer').append(data.html);
            }

            
            $('.project-item[data-project-id=' + data.project_id + ']').find('.pbanner__admin-list').append(data.html_banner);
            let currentItem = $('#modal-member-manage .member-item[data-user=' + user_id + ']').parents('.member-item-container');
            sortIconMemberInBanner(currentItem, project_id, 'admin');
            let iconShow = $('.pbanner__admin-list .button--show-member');
            if (iconShow) {
                iconShow.parents('.pbanner__user').remove()
            }
            $('#modal-member-manage .member-manage-action').empty();
            $('#modal-member-manage .member-manage-action').append(data.html_button);
        },
        error: function () {
            buttonDom.removeClass('disabled-btn-add');
            $('.add-admin-into-list').removeClass('disabled-btn-add');
        },
        complete: function () {
            $('#modal-add-admin').modal('hide');
        }
    });
}

function addDirector() {
    $(document).on('click', '.invite-the-btn', function (){
        let project_id = $('#modal-member-manage .member-manage__content').attr('data-product');
        if (!project_id) {
            project_id = $('#modalUsersInProject').attr('data-product')
        }
        $('#modal-add-director').modal('show');
        let type_artist = $(this).hasClass('invite-the-producer-btn') ? 'producer': 'director';
        getArtistToAddDirector(project_id, type_artist)
    });

    $(document).on('click', '#modal-add-director .add-artist-into-list', function () {
        let buttonDom = $(this);
        let type_artist = buttonDom.hasClass('add-director-into-list') ? 'director' : 'producer';
        let user_id = buttonDom.parents('.add-director__results-search-detail').attr('data-user');
        let project_id = $('#modal-member-manage .member-manage__content').attr('data-product');
        if (!project_id) {
            project_id = $('#modalUsersInProject').attr('data-product')
        }
        if (!buttonDom.hasClass('disabled-btn-add')) {
            buttonDom.addClass('disabled-btn-add');
            addArtistToDirector(user_id, project_id, buttonDom, type_artist);
        }
    });

    $(document).on('click', '#modal-confirm-add-producer .button-accept-add-producer', function () {
        let buttonDom = $(this);
        let parentDom = buttonDom.parents('#modal-confirm-add-producer');
        let type_artist = 'producer';
        let user_id = parentDom.attr('data-user');
        let project_id = parentDom.attr('data-product');

        if (!buttonDom.hasClass('disabled-btn-add')) {
            buttonDom.addClass('disabled-btn-add');
            if (type_artist === 'producer') {
                $('#modal-add-director').modal('hide');
                $('#modal-confirm-add-producer').modal('hide');
            }
            ajaxAddArtistToDirector(user_id, project_id, buttonDom, type_artist)
        }
    });


    $(document).on('hidden.bs.modal', '#modal-confirm-add-producer, #modal-add-producer-error', function () {
        $('.add-producer-into-list').removeClass('disabled-btn-add');
    });

    $(document).ready(function() {
        // 初回ロード時およびプロデューサーリストが更新されたときにプロデューサー数を確認
        function checkProducerCount() {
            let producerCount = $('.member-manage__list-producer .member-item-container').length;
    
            if (producerCount <= 1) {
                // プロデューサーが1人以下の場合、その削除ボタンを非表示にする
                $('.member-manage__list-producer .member-item__btn-delete').css('display', 'none');
            } else {
                // プロデューサーが2人以上いる場合、削除ボタンを表示する
                $('.member-manage__list-producer .member-item__btn-delete').css('display', '');
            }
        }
    
        // ページ読み込み時にチェックを実行
        checkProducerCount();
    
        // 削除操作のイベントリスナー
        $(document).on('click', '.member-manage__list-director .member-item__btn-delete, .member-manage__list-producer .member-item__btn-delete', function (){
            let memberDom = $(this).parents('.member-item-container');
    
            if ($(this).parents('.member-manage__list-producer').length) {
                let producerCount = $('.member-manage__list-producer .member-item-container').length;
    
                if (producerCount <= 1) {
                    // プロデューサーが1人しかいない場合、削除操作は中止（ただしこのケースでは削除ボタンが非表示になっているため通常は発生しない）
                    return;
                }
            }
    
            let user_id = memberDom.find('.member-item').attr('data-user');
            let project_id = memberDom.find('.member-item').attr('data-pf');
    
            // 削除の確認ダイアログを表示
            bootbox.confirm({
                className: 'modal-delete-director',
                message: "本当に削除しますか？",
                buttons: {
                    confirm: {
                        label: 'はい',
                        className: 'btn btn--tertiary'
                    },
                    cancel: {
                        label: 'いいえ',
                        className: 'btn btn--primary'
                    }
                },
                callback: (result) => {
                    if (result) {
                        ajaxDeleteDirector(user_id, project_id);
                        // 削除後にプロデューサー数を再チェック
                        checkProducerCount();
                    }
                }
            });
        });
    
        // プロデューサーが追加または削除された際に数をチェックする関数を呼び出す
        $(document).on('DOMSubtreeModified', '.member-manage__list-producer', function() {
            checkProducerCount();
        });
    });
    
    $(document).on('hidden.bs.modal', '.modal-delete-director, .modal-upgrade-producer, .modal-unlock-artist', function (e) {
        $('body').addClass('modal-open');
    });
}

function ajaxDeleteDirector(user_id, project_id) {
    if (user_id && project_id) {
        $.ajax({
            type: "POST",
            url: "/top/remove_director_project",
            datatype: "json",
            async: false,
            data: {
                'user_id': user_id,
                'project_id': project_id,
            },
            success: function (data) {
                let currentItemDom = $('.member-item-container .member-item[data-user=' + user_id + ']').parents('.member-item-container');
                if (currentItemDom.hasClass('can-self-edit-delete')) {
                    location.reload();
                }
                currentItemDom.remove();

                if (data.remove_artist) {
                    $('.invite-the-director-btn').removeClass('disable');
                }
                if (data.html !== '') {
                    $('.member-manage__list.member-manage__list-director').prepend(data.html);
                    $('.invite-the-producer-btn').removeClass('disable');
                } else {
                    $('.project-item[data-project-id=' + project_id + '] .pbanner__user[data-user=' + user_id + ']').remove();
                    if (!$('.project-item[data-project-id=' + project_id + '] .pbanner__admin-list .pbanner__user').length) {
                        $('.project-item[data-project-id=' + project_id + '] .pbanner__admin-list').append($(`<div class="pbanner__user">
                      <div class="avatar avatar--image avatar--24 avatar--square background-avt">
                        <div class="avatar-image button--show-member button--show-member" style="background-image: url(/static/images/icon_user.svg)"></div>
                      </div>
                    </div>`))
                    }
                }

                $('#modal-member-manage .member-manage-action').empty();
                $('#modal-member-manage .member-manage-action').append(data.html_button);
            },
            error: function () {
            },
            complete: function () {
                checkNumberProducer()
            }
        });
    }
}

function getArtistToAddDirector(project_id, type_artist) {
    if (project_id) {
        $.ajax({
            type: "POST",
            url: "/top/get_artist_to_add_project",
            datatype: "json",
            async: false,
            data: {
                'project_id': project_id,
                'type_artist': type_artist
            },
            success: function (data) {
                $('#modal-add-director .block-list__results-search').empty();
                $('#modal-add-director .block-list__results-search').append(data.html);
                updateHeaderForModalInviteArtist(type_artist);
            },
            error: function () {
                $('#modal-add-director').modal('hide')
            },
            complete: function () {
            }
        });
    }
}

function addArtistToDirector(user_id, project_id, buttonDom, type_artist) {
    if (project_id) {
        if (type_artist === 'producer') {
            $.ajax({
                type: "POST",
                url: "/top/check_can_add_artist_to_master_producer",
                datatype: "json",
                async: false,
                data: {
                    'project_id': project_id,
                    'user_id': user_id,
                },
                success: function (data) {
                    if (data.has_offer) {
                        let modalConfirmDom = $('#modal-confirm-add-producer');
                        modalConfirmDom.modal('show');
                        modalConfirmDom.attr('data-product', project_id);
                        modalConfirmDom.attr('data-user', user_id);
                    } else if (data.over_budget) {
                        $('#modal-add-producer-error').modal('show');
                        $('#modal-add-producer-error .current-budget').html(data.current_budget);
                        $('#modal-add-producer-error .master-admin-offered').html(data.master_admin_offered);
                    } else {
                        ajaxAddArtistToDirector(user_id, project_id, buttonDom, type_artist)
                    }
                },
                error: function () {
                    $('#modal-add-director').modal('hide')
                },
            });
        } else {
            ajaxAddArtistToDirector(user_id, project_id, buttonDom, type_artist)
        }
    }
}

function ajaxAddArtistToDirector(user_id, project_id, buttonDom, type_artist) {
    let is_product_detail_page = $('.prdt').length > 0;
    $.ajax({
        type: "POST",
        url: "/top/add_artist_to_director_project",
        datatype: "json",
        async: false,
        data: {
            'project_id': project_id,
            'user_id': user_id,
            'type_artist': type_artist,
            'is_product_detail_page': is_product_detail_page
        },
        success: function (data) {
            if (type_artist === 'producer') {
                if (is_product_detail_page) {
                    $(`.block-producers .user-project-template-2[data-id=${user_id}]`).remove()
                    let listProducer = $(`.block-producers .user-project-template-1`)
                    if (listProducer.length) {
                        $(data.html).insertAfter(listProducer.last())
                    } else {
                        $(data.html).insertAfter($('.users-title-block.title-block-producers'))
                    }
                }else {
                    $(`#modal-member-manage .member-manage__list-director .member-item[data-user=' + ${user_id} + ']`).parents('.member-item-container').remove();
                    $('#modal-member-manage .member-manage__list.member-manage__list-producer').append(data.html);
                }

            } else {
                if (is_product_detail_page) {
                    $('.block-users-in-project .block-producers').append(data.html);

                } else {
                    $('#modal-member-manage .member-manage__list-director').append(data.html);
                }
            }
            $('.project-item[data-project-id=' + data.project_id + ']').find('.pbanner__admin-list').append(data.html_banner);
            let currentItem = $('#modal-member-manage .member-item[data-user=' + user_id + ']').parents('.member-item-container');
            sortIconMemberInBanner(currentItem, project_id, 'admin');
            let iconShow = $('.pbanner__admin-list .button--show-member');
            if (iconShow) {
                iconShow.parents('.pbanner__user').remove()
            }
            $('#modal-member-manage .member-manage-action').empty();
            $('#modal-member-manage .member-manage-action').append(data.html_button);
        },
        error: function () {
            buttonDom.removeClass('disabled-btn-add');
            $('.add-producer-into-list').removeClass('disabled-btn-add');
        },
        complete: function () {
            if (type_artist === 'producer') {
                $('#modal-add-director').modal('hide');
            }
            $('#modal-confirm-add-producer').modal('hide');
        }
    });
}

function updateHeaderForModalInviteArtist(type_artist) {
    if (type_artist === 'producer') {
        $('.header-modal-add-producer').removeClass('hide');
        $('.header-modal-add-director').addClass('hide');
    } else {
        $('.header-modal-add-producer').addClass('hide');
        $('.header-modal-add-director').removeClass('hide');
    }
}

function ajaxUpgrateDirectorToProducer(item, type_upgrate) {
    let user_id = item.find('.member-item').data('user');
    let project_id = item.find('.member-item').data('pf');
    let user_ids = [];
    $('.manage-producer-director .member-item-container').each(function () {
        let memberDom = $(this).find('.member-item');
        let user_id = memberDom.attr('data-user');
        if (user_id) {
            user_ids.push(user_id)
        }
    });
    $.ajax({
        type: "POST",
        url: "/top/upgrate_director_to_producer_project",
        datatype: "json",
        async: false,
        data: {
            'user_id': user_id,
            'project_id': project_id,
            'type_upgrate': type_upgrate,
            'user_ids': user_ids
        },
        success: function (data) {
            item.find('.member-item-action__button-container').removeClass('show-button-action');
            if (type_upgrate === 'downgrate_producer' && item.hasClass('can-self-edit-delete')) {
                $('.member-manage__list.member-manage__list-producer .member-item-action__button-container').addClass('hide');
                $('#modal-member-manage .member-manage-action').empty();
                $('#modal-member-manage .producer-manage-action').empty();
            }
            sortIconMemberInBanner(item, project_id, 'admin');
        },
        error: function () {
            let currentPosition = $(item).attr('data-current-position');
            backToOldPosition(item, currentPosition);
        },
        complete: function () {
            checkNumberProducer()
        }
    });
}

function draggableMember() {
    checkNumberProducer();

    $('#modal-member-manage .manage-producer-director .member-manage__list').sortable({
        axis: 'y',
        items: '> div, > span',
        handle: 'span.member-item__btn-move',
        scrollSensitivity: 100,
        tolerance: "pointer",
        connectWith: ".manage-producer-director .member-manage__list",
        cancel: ".sub-member-item, .member-item__btn-delete",
        start: function (event, ui) {
            let index = ui.item.index();
            $(ui.item).attr('data-start-index', index);
            if ($(ui.item).parents(".member-manage__list").length) {
                if ($(ui.item).parents(".member-manage__list.member-manage__list-producer").length) {
                    $(ui.item).attr('data-current-position', 'producer');
                } else {
                    $(ui.item).attr('data-current-position', 'director');
                }
            }
        },
        stop: function (event, ui) {
            let item = $(ui.item);
            if($(ui.item).parents(".member-manage__group").length){
                let currentPosition = $(ui.item).attr('data-current-position');

                if ($(ui.item).parents(".member-manage__list-producer").length && currentPosition === 'producer' || $(ui.item).parents(".member-manage__list-director").length && currentPosition === 'director') {
                    ajaxUpgrateDirectorToProducer(item, '');
                    return
                }
                let type_upgrate = '';
                if ($(ui.item).parents(".member-manage__list-producer").length) {
                    type_upgrate = 'upgrate_producer';
                } else {
                    type_upgrate = 'downgrate_producer';
                    if ($(ui.item).attr('data-role') === 'master_admin') {
                        backToOldPosition(item, 'producer');
                        return
                    }
                }
                ajaxUpgrateDirectorToProducer(item, type_upgrate)
            }
        }
    })
}


function backToOldPosition(item, currentPosition = '') {
    let current_item_html = item.get(0);
    let index = item.attr('data-start-index');
    item.remove();
    if (currentPosition === 'producer') {
        if ($($('.member-manage__list-producer .member-item-container')[index]).length) {
            $(current_item_html).insertBefore($($('.member-manage__list-producer .member-item-container')[index]));
        } else {
            $('.member-manage__list-producer').append(current_item_html)
        }
    } else {
        if ($($('.member-manage__list-director .member-item-container')[index]).length) {
            $(current_item_html).insertBefore($($('.member-manage__list-director .member-item-container')[index]));
        } else {
            $('.member-manage__list-director').append(current_item_html)
        }
    }

}


function sortIconMemberInBanner(currentItem, project_id, type) {
    let nextItem = currentItem.prev('.member-item-container');
    let bannerDom = $('.project-item[data-project-id=' + project_id + ']');
    if (type === 'member') {
        bannerDom = bannerDom.find('.pbanner__members .pbanner__user-list');
    } else {
        bannerDom = bannerDom.find('.pbanner__user-list.pbanner__admin-list');
    }
    let currentId = currentItem.find('.member-item').attr('data-user');

    let currentBanner = bannerDom.find('.pbanner__user[data-user=' + currentId + ']');

    if (nextItem.length) {
        let nextId = nextItem.find('.member-item').attr('data-user');
        let nextBanner = bannerDom.find('.pbanner__user[data-user=' + nextId + ']');
        currentBanner.insertAfter(nextBanner)
    } else {
        if (type === 'member') {
            if (currentItem.parents('.member-manage__group-member-invited').length) {
                let nextId = $('.member-manage__group-owner').find('.member-item-container').last().find('.member-item').attr('data-user');
                let nextBanner = bannerDom.find('.pbanner__user[data-user=' + nextId + ']');
                currentBanner.insertAfter(nextBanner)
            } else {
                bannerDom.find('div').first().prepend(currentBanner);
            }
        } else {
            if (currentItem.parents('.member-manage__list-director').length) {
                let nextId = $('.member-manage__list-producer').find('.member-item-container').last().find('.member-item').attr('data-user');
                let nextBanner = bannerDom.find('.pbanner__user[data-user=' + nextId + ']');
                currentBanner.insertAfter(nextBanner)
            } else {
                bannerDom.prepend(currentBanner);
            }
        }
    }
}


function draggableOwner () {
    checkNumberOwner();
    $('#modal-member-manage .member-manage-sortable .member-manage__list.can-drag').sortable({
        axis: 'y',
        items: '> div, > span',
        handle: '.member-item__btn-move',
        tolerance: "pointer",
        scrollSensitivity: 100,
        scroll: true,
        connectWith: ".member-manage-sortable .member-manage__list",
        containment: ".member-manage-sortable",
        cancel: ".cannot-drag, .member-item, .member-item__btn-delete",
        start: function (event, ui) {
            let index = ui.item.index();
            $(ui.item).attr('data-start-index', index);
            if ($(ui.item).parents(".member-manage__list").length) {
                if ($(ui.item).parents(".member-manage__list.list-project-owner").length) {
                    $(ui.item).attr('data-current-position', 'owner');
                } else {
                    $(ui.item).attr('data-current-position', 'client');
                }
            }
        },
        stop: function (e, ui) {
            if ($(ui.item).parents(".member-manage__group").length) {
                let currentPosition = $(ui.item).attr('data-current-position');
                if ($(ui.item).parents(".member-manage__group.member-manage__group-owner").length && currentPosition === 'owner' || $(ui.item).parents(".member-manage__group.member-manage__group-member-invited").length && currentPosition === 'client') {
                    ajaxUpgrateMember($(ui.item), '');
                    return
                }
                // Call ajax here
                let type_upgrate = '';
                if ($(ui.item).parents(".member-manage__group.member-manage__group-owner").length) {
                    type_upgrate = 'upgrate_owner';
                } else {
                    type_upgrate = 'downgrate_owner';
                }
                ajaxUpgrateMember($(ui.item), type_upgrate);
            }
        }
    })
}

function ajaxUpgrateMember(item, type_upgrate) {

    let user_id = item.find('.member-item').data('user');
    let project_id = item.find('.member-item').data('pf');
    let user_ids = [];
    $('.manage-owner-member-invited .member-item-container').each(function () {
        let memberDom = $(this).find('.member-item');
        let user_id = memberDom.attr('data-user');
        if (user_id) {
            user_ids.push(user_id)
        }
    });

    $.ajax({
        type: "POST",
        url: "/top/update_member_to_owner",
        datatype: "json",
        async: false,
        data: {
            'user_id': user_id,
            'project_id': project_id,
            'type_upgrate': type_upgrate,
            'user_ids': user_ids
        },
        success: function (data) {
            if (item.hasClass('can-self-edit-delete')) {
                item.find('.member-item-right').addClass('hide');
                item.find('.member-item-action .member-item__btn-move').addClass('hide');
                item.find('.member-item-action .member-item__btn-delete').addClass('hide');
            }else if (type_upgrate === 'upgrate_owner') {
                item.find('.member-item-right input[name="view-only"]').prop('checked', false);
                item.find('.member-item-right').addClass('hide');
                item.find('.member-item-action .member-item__btn-delete').addClass('hide');
            } else if (type_upgrate === 'downgrate_owner'){
                item.find('.member-item-right').removeClass('hide');
                item.find('.member-item-action .member-item__btn-delete').removeClass('hide');
            }
             sortIconMemberInBanner(item, project_id, 'member');
        },
        error: function () {
            let cuttent_item_html = item.get(0);
            let index = item.attr('data-start-index');
            item.remove();
            let parentDiv;
            if (item.attr('data-current-position') === 'member') {
                parentDiv = $('.list-project-member')
            } else {
                parentDiv = $('.list-project-owner')
            }
            if ($(parentDiv.find('.member-item-container')[index]).length) {
                $(cuttent_item_html).insertBefore($(parentDiv.find('.member-item-container')[index]));
            } else {
                parentDiv.append(cuttent_item_html)
            }
        },
        complete: function () {
            checkNumberOwner()
        }
    });

}

function checkDoneRateDetailProject() {
    if ($('.detail-wishlist').parents('.sproject-meta').find('.sproject-scene .sproject-time__current-heart-rate').text() === '100%') {
        $('.detail-wishlist .icon').removeClass('icon--sicon-heart-o').addClass('icon--sicon-heart')
    } else {
        $('.detail-wishlist .icon').removeClass('icon--sicon-heart').addClass('icon--sicon-heart-o')
    }
}

function checkNumberOwner () {
    let $modal_manage = $('#modal-member-manage');
    if($('.list-project-owner').find('.member-item-container').length === 1){
        $('.list-project-owner').find('.member-item-container').addClass('cannot-drag');
        return false
    } else {
        $('.list-project-owner').find('.member-item-container').removeClass('cannot-drag');
    }
    return true
}

function checkNumberProducer() {
    let $modal_manage = $('#modal-member-manage');
    let listProducer = $modal_manage.find('.member-manage__list.member-manage__list-producer');
    if (listProducer.find('.member-item-container').length < 2) {
        $('.member-manage__list-producer').find('.member-item-container').removeClass('show-button-action');
        listProducer.addClass('cannot-drag');
        return false
    } else {
        listProducer.removeClass('cannot-drag');
    }
    return true
}

$(document).ready(function () {
    actionBanner();
    showStaffInfo();
    manageMember();
    projectList();
    animationPage();
    exportCSV();
    actionTab();
    showExpand();
    updateProjectSetting();
    dragDropFileCredit();
    projectSetting();
    submitProjectSetting();
    searchListArtistBlock();
    addDirector();
    checkDoneRateDetailProject();
    renderCreditPDF();
    initModalArtist();
    addAdmin();
});

function initSortableSection() {
    $('#modal-staff-credit .modal-staff-credit-body').sortable({
        axis: 'y',
        items: '.modal-staff-credit-section-container',
        handle: '.modal-staff-credit-section--action-btn.drag-section .icon--sicon-equal',
        tolerance: "pointer",
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            let arr_order = [];
            let project_id = $('#modal-staff-credit').attr('data-project-id');

            $('#modal-staff-credit .modal-staff-credit-section-container').each(function (index, item) {
                arr_order.push(item.getAttribute('data-section-id'));
            });

            // TODO: call ajax to update order
            $.ajax({
                type: "POST",
                url: "/credit/update_index_section_staff_credit",
                data: {
                    'project_id': project_id,
                    'arr_order': arr_order,
                },
                success: function() {
                    // toastr.success('並び替えました');
                },
                fail: function (data) {
                    toastr.error('エラーが発生しました');
                }
            })
        },
    })
}

function initSortableArtist() {
    initSortableArtistEach($('#modal-staff-credit .modal-staff-credit-body .modal-staff-credit-section-container .all-artist-item-container'));
}
 var is_sorting = false;
function initSortableArtistEach(element) {
    // element.sortable({
    //     axis: 'y',
    //     items: '.modal-staff-credit-section-artist',
    //     handle: '.modal-staff-credit-section--action-btn.drag-artist .icon--sicon-equal',
    //     tolerance: "pointer",
    //     connectWith: '.modal-staff-credit-section-container .all-artist-item-container',
    //     cursor: "move",
    //     scroll: true,

    //     start: function (event, ui) {
    //         ui.placeholder.height(ui.helper.outerHeight());
    //         let sectionId =  $(ui.item).parents('.modal-staff-credit-section-container').attr('data-section-id');
    //         $(ui.item).attr('data-current-section', sectionId);
    //     },
    //     change: function (event, ui) {
    //     },
    //     update: function (event, ui) {
    //         let newSection = $(ui.item).parents('.modal-staff-credit-section-container');
    //         let arr_order = [];
    //         let project_id = $('#modal-staff-credit').attr('data-project-id');
    //         let newSectionId = newSection.attr('data-section-id');
    //         let sectionId = $(ui.item).attr('data-current-section');
    //         let new_section_id;
    //         if (sectionId !== newSectionId) {
    //             new_section_id = newSectionId;
    //         }

    //         newSection.find('.modal-staff-credit-section-artist').each(function (index, item) {
    //             arr_order.push(item.getAttribute('data-item-artist-id'));
    //         });
    //         if (!is_sorting) {
    //             is_sorting = true;
    //             $.ajax({
    //                 type: "POST",
    //                 url: "/credit/update_index_section_staff_credit",
    //                 data: {
    //                     'project_id': project_id,
    //                     'arr_order': arr_order,
    //                     'type_order': 'item_artist',
    //                     'new_section_id': new_section_id
    //                 },
    //                 success: function (data) {
    //                     toastr.success('並び替えました');
    //                     is_sorting = false
    //                 },
    //                 fail: function (data) {
    //                     is_sorting = false;
    //                     toastr.error('エラーが発生しました');
    //                 }
    //             })
    //         }

    //     },
    // })
}

function initModalArtist() {

    $(document).on('click', '.modal-staff-credit-artist-header-item:not(.active)', function () {
        $(this).parents('.modal-staff-credit-artist-header').find('.active').removeClass('active');
        $(this).addClass('active');
        fillInfoToInput($(this));
        checkSubmitButtonArtist();
    });

    $(document).on('click', '.modal-staff-credit-artist-header-item.active', function () {
        $(this).parents('.modal-staff-credit-artist-header').find('.active').removeClass('active');
        let inputs = $('#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]');
        inputs.eq(0).val('');
        inputs.eq(1).val('');
        inputs.eq(2).val('');
        inputs.eq(3).val('');
        $('#switch-show-profile-link').prop('checked', false);
        checkSubmitButtonArtist();
    });

    $(document).on('keyup', '#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]', function() {
        checkSubmitButtonArtist();
    });

    $(document).on('click', '#modal-staff-credit-artist .btn-staff-credit-artist-submit:not(.btn--disabled)', function() {
        if(!updating_artist) {
            // updating_artist = true;

            let inputs = $('#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]');
            //TODO: call ajax to update artist
            let item_id = $('.item-edit-artist').attr('data-item-artist-id');
            $.ajax({
                type: "POST",
                url: "/credit/add_update_item_artist_section_credit",
                data: {
                    'project_id': $('#modal-staff-credit').attr('data-project-id'),
                    'link_profile': $('#switch-show-profile-link')[0].checked,
                    'title': inputs.eq(0).val().trim(),
                    'title-en': inputs.eq(1).val().trim(),
                    'artist-name': inputs.eq(2).val().trim(),
                    'artist-name-en': inputs.eq(3).val().trim(),
                    'artist_id': $('#modal-staff-credit-artist .modal-staff-credit-artist-header-item.active').attr('data-artist'),
                    'language': $('.modal-staff-credit-header-lan_jp.active').length ? 'jp' : 'en',
                    'item_id': item_id,
                },
                success: function(data) {
                    // toastr.success('クレジットを更新しました');
                    //TODO: update modal credit: render same html as load staff credit
                    $('#modal-staff-credit-artist').modal('hide');
                    updating_artist = false;
                    let itemDom = $('.modal-staff-credit-section-container .modal-staff-credit-section-artist[data-item-artist-id=' + item_id + ']');

                    if (!itemDom.length) {
                        $(data.html).appendTo('.modal-staff-credit-body');
                        initSortableSection();
                        initSortableArtist();
                        initModalCredit();
                    } else {
                        $(data.html).insertBefore(itemDom);
                        itemDom.remove();
                    }
                },
                fail: function (data) {
                    toastr.error('エラーが発生しました');
                    updating_artist = false;
                },
                complete: function() {
                    updating_artist = false;
                }
            })
        }
    })

    $(document).on('click', '#modal-staff-credit-artist .btn-staff-credit-artist-back', function() {
        $('#modal-staff-credit-artist').modal('hide');
    });

    $(document).on('change', '#modal-staff-credit-artist #switch-show-profile-link', function () {
        if (!$('.modal-staff-credit-artist-header-item.active').attr('data-artist') || !$('.modal-staff-credit-artist-header-item.active').length) {
            $('#switch-show-profile-link')[0].checked = false
        }
        checkSubmitButtonArtist();
    });

    checkSubmitButtonArtist();
}

function fillInfoToInput(target) {
    let inputs = $('#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]');
    inputs.eq(0).val(target.attr('data-title'));
    inputs.eq(1).val(target.attr('data-title-en'));
    inputs.eq(2).val(target.attr('data-artist-name'));
    inputs.eq(3).val(target.attr('data-artist-name-en'));
    if (target.attr('data-link-creator') === 'False') {
        $('#switch-show-profile-link')[0].checked = false;
    } else {
        $('#switch-show-profile-link')[0].checked = true;
    }
}

function checkSubmitButtonArtist() {
    $('#modal-staff-credit-artist button.btn-staff-credit-artist-submit').removeClass('btn--disabled');
    $('#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]').each(function (i, e) {
        if ($(e).val().trim().length < 1) {
            $('#modal-staff-credit-artist button.btn-staff-credit-artist-submit').addClass('btn--disabled');
            return false
        }
    });

    if ($('.item-edit-artist.active').length) {
        $('#modal-staff-credit-artist button.btn-staff-credit-artist-submit').addClass('btn--disabled');
        if (checkButtonEditArtist()) {
            $('#modal-staff-credit-artist button.btn-staff-credit-artist-submit').removeClass('btn--disabled');
        }
    }
}


function checkButtonEditArtist() {
    let target = $('.item-edit-artist.active');
    let currentValue = target.attr('data-link-creator') === 'True' ? true : false;
    if (currentValue !== $('#switch-show-profile-link')[0].checked) {
        $('#modal-staff-credit-artist button.btn-staff-credit-artist-submit').removeClass('btn--disabled');
        return true
    }
    let inputs = $('#modal-staff-credit-artist .modal-staff-credit-artist-body input[type=text]');
    if (inputs.eq(0).val().trim() !== target.attr('data-title')) {
        return true
    }
    if (inputs.eq(1).val().trim() !== target.attr('data-title-en')) {
        return true
    }
    if (inputs.eq(2).val().trim() !== target.attr('data-artist-name')) {
        return true
    }
    if (inputs.eq(3).val().trim() !== target.attr('data-artist-name-en')) {
        return true
    }
    return false
}

function initModalSection(action) {

    $(document).on('keyup', '#modal-staff-credit-section input[type=text]', function() {
        checkSubmitButtonSection();
    });

    $(document).on('click', '#modal-staff-credit-section .btn-staff-credit-section-submit:not(.btn--disabled)', function() {
        if(!updating_section) {
            updating_section = true;

            let inputs = $('#modal-staff-credit-section input[type=text]');
            //TODO: call ajax to update section info
            let section_id = $('#modal-staff-credit-section').attr('data-section-id');
            let section_name = inputs.eq(0).val().trim();
            let section_name_en = inputs.eq(1).val().trim();
            let explanation = inputs.eq(2).val().trim();
            let explanation_en = inputs.eq(3).val().trim();
            $.ajax({
                type: "POST",
                url: "/credit/add_update_section_credit",
                data: {
                    'project_id': $('#modal-staff-credit').attr('data-project-id'),
                    'section_name': inputs.eq(0).val().trim(),
                    'section_name_en': inputs.eq(1).val().trim(),
                    'explanation': inputs.eq(2).val().trim(),
                    'explanation_en': inputs.eq(3).val().trim(),
                    'section_id': section_id,
                    'action': action,
                    'language': $('.modal-staff-credit-header-lan_jp.active').length ? 'jp' : 'en'
                },
                success: function(data) {
                    // toastr.success('クレジットを更新しました');
                    //TODO: update modal credit: render same html as load staff credit
                    if (!section_id && data.html) {
                        $('#modal-staff-credit .modal-staff-credit-body').append(data.html);
                    } else {
                        let sectionDom = $('.modal-staff-credit-section-container[data-section-id=' + section_id + ']');
                        if (sectionDom) {
                            sectionDom.find('.modal-staff-credit-section-header').attr('data-section', section_name);
                            sectionDom.find('.modal-staff-credit-section-header').attr('data-section-en', section_name_en);
                            sectionDom.find('.modal-staff-credit-section-header').attr('data-desc-en', explanation_en);
                            sectionDom.find('.modal-staff-credit-section-header').attr('data-desc', explanation);
                            let header = section_name_en;
                            let desc = explanation_en;
                            if ($('.modal-staff-credit-header-lan_jp.active').length) {
                                header = section_name;
                                desc = explanation;
                            }
                            sectionDom.find('.modal-staff-credit-section-header .modal-staff-credit-section-header--title').text(header);
                            sectionDom.find('.modal-staff-credit-section-header .modal-staff-credit-section-header--desc').text(desc);
                        }
                    }
                    $('#modal-staff-credit-section').modal('hide');
                    initSortableSection();
                    initSortableArtist();
                    initModalCredit();
                },
                fail: function (data) {
                    toastr.error('エラーが発生しました');
                },
                complete: function() {
                    updating_section = false;
                }
            })
        }
    })

    $(document).on('click', '#modal-staff-credit-section .btn-staff-credit-section-back', function() {
        $('#modal-staff-credit-section').modal('hide');
    });

    checkSubmitButtonSection();
}

function checkSubmitButtonSection() {
    // $('#modal-staff-credit-section button.btn-staff-credit-section-submit').removeClass('btn--disabled');
    // if ($('#modal-staff-credit-section input[type=text]#id_section_name').val().trim().length < 1 && $('#modal-staff-credit-section input[type=text]#id_section_name_en').val().trim().length < 1) {
    //     $('#modal-staff-credit-section button.btn-staff-credit-section-submit').addClass('btn--disabled');
    //     return false
    // }
}

function initModalCredit() {
    actionToggleLinkProfile();
    if(modal_credit_init) {
        checkSubmitButtonSection();
        return
    } else {
        modal_credit_init = true;
    }

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-header .modal-staff-credit-section--action-btn .icon--sicon-pencil', function () {
        let section = $(this).parents('.modal-staff-credit-section-header');
        let data = {
            'section': section.attr('data-section'),
            'section-en': section.attr('data-section-en'),
            'desc': section.attr('data-desc'),
            'desc-en': section.attr('data-desc-en'),
            'section-id': section.attr('data-section-id'),
        }
        renderModalSection('edit', data);
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-header .modal-staff-credit-section--action-btn .icon--sicon-trash', function () {
        showModalConfirmDelete($(this));
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-header .modal-staff-credit-section-header--title', function () {
        let section = $(this).parents('.modal-staff-credit-section-header');
        let data = {
            'section': section.attr('data-section'),
            'section-en': section.attr('data-section-en'),
            'desc': section.attr('data-desc'),
            'desc-en': section.attr('data-desc-en'),
            'section-id': section.attr('data-section-id'),
        }
        renderModalSection('edit', data);
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-header .modal-staff-credit-section-header--desc', function () {
        let section = $(this).parents('.modal-staff-credit-section-header');
        let data = {
            'section': section.attr('data-section'),
            'section-en': section.attr('data-section-en'),
            'desc': section.attr('data-desc'),
            'desc-en': section.attr('data-desc-en'),
            'section-id': section.attr('data-section-id'),
        }
        renderModalSection('edit', data);
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-artist .modal-staff-credit-section--action-btn .icon--sicon-pencil', function () {
        let section_id = $(this).parents('.modal-staff-credit-section-container').attr('data-section-id');
        let item_artist_id = $(this).parents('.modal-staff-credit-section-artist').attr('data-item-artist-id');
        if (!section_id || !item_artist_id) {
            return
        }
        renderModalArtist('edit', section_id, item_artist_id)

    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-artist .modal-staff-credit-section--action-btn .icon--sicon-trash', function () {
        showModalConfirmDelete($(this));
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-artist .modal-staff-credit-section-artist--title', function () {
        let section_id = $(this).parents('.modal-staff-credit-section-container').attr('data-section-id');
        let item_artist_id = $(this).parents('.modal-staff-credit-section-artist').attr('data-item-artist-id');
        if (!section_id || !item_artist_id) {
            return
        }
        renderModalArtist('edit', section_id, item_artist_id)
    })

    $(document).on('click', '#modal-staff-credit .editable .modal-staff-credit-section-artist .modal-staff-credit-section-artist--name', function () {
        let section_id = $(this).parents('.modal-staff-credit-section-container').attr('data-section-id');
        let item_artist_id = $(this).parents('.modal-staff-credit-section-artist').attr('data-item-artist-id');
        if (!section_id || !item_artist_id) {
            return
        }
        renderModalArtist('edit', section_id, item_artist_id)
    })

    $(document).on('click', '#modal-confirm-delete .btn-confirm-delete-cancel', function () {
        $('#modal-confirm-delete').modal('hide');
        $('#modal-staff-credit').modal('show');
    })

    $(document).on('click', '#modal-confirm-delete .btn-confirm-delete', function () {
        if(!delete_target) {
            $('#modal-confirm-delete').modal('hide');
            $('#modal-staff-credit').modal('show');
            return
        }

        if(delete_progressing) {
            return
        }

        delete_progressing = true;
        let target = delete_target

        // if(delete_target.parents('.modal-staff-credit-section-artist').length) {
        //     let item_artist_id = delete_target.parents('.modal-staff-credit-section-artist').attr('data-item-artist-id');

        //     delete artist;
        //     $.ajax({
        //         type: "POST",
        //         url: "/credit/delete_section_item_artist",
        //         data: {
        //             'project_id': $('#modal-staff-credit').attr('data-project-id'),
        //             'item_artist_id': item_artist_id
        //         },
        //         success: function() {
        //             toastr.success('スタッフクレジットを更新しました');
        //             target.parents('.modal-staff-credit-section-artist').remove();
        //             $('#modal-confirm-delete').modal('hide');
        //             $('#modal-staff-credit').modal('show');
        //             delete_target = null;

        //         },
        //         fail: function (data) {
        //             toastr.error('エラーが発生しました');
        //         },
        //         complete: function() {
        //             delete_progressing = false;
        //         }
        //     })
        // } else {
            let section_id = delete_target.parents('.modal-staff-credit-section-header').attr('data-section-id');

            delete section;
            $.ajax({
                type: "POST",
                url: "/credit/delete_section_item_artist",
                data: {
                    'project_id': $('#modal-staff-credit').attr('data-project-id'),
                    'section_id': section_id
                },
                success: function() {
                    // toastr.success('クレジットを更新しました');
                    target.parents('.modal-staff-credit-section-container').remove()
                    $('#modal-confirm-delete').modal('hide');
                    $('#modal-staff-credit').modal('show');
                    delete_target = null;
                },
                fail: function (data) {
                    toastr.error('エラーが発生しました');
                },
                complete: function() {
                    delete_progressing = false;
                }
            })
        // }
    })

    $(document).on('click', '.modal-staff-credit-body:not(.editable) .link-to-profile', function() {
        let url = $(this).attr('data-href');
        if(url) {
            window.open(url, '_blank');
        }
    })

    $(document).on('click', '#modal-staff-credit .btn-staff-credit-back', function () {
        $('#modal-staff-credit').modal('hide')
    })

    $(document).on('click', '#modal-staff-credit .modal-staff-credit-header-lan_jp:not(.active)', function () {
        renderStaffCredit('jp')
    })

    $(document).on('click', '#modal-staff-credit .modal-staff-credit-header-lan_en:not(.active)', function () {
        $('#modal-staff-credit .modal-staff-credit-header-lan.active').removeClass('active');
        $(this).addClass('active');
        renderStaffCredit('en');
    })
}

function renderModalSection(action='add', data={}, section='', section_en='', desc='', desc_en='') {
    let show_section = '', show_desc = '';

     if ($('.popup-container-modal-credit').hasClass('cannot-edit-credit')) {
         return
     }
    if (action === 'edit') {
        section = data['section'];
        section_en = data['section-en'];
        desc = data['desc'];
        desc_en = data['desc-en'];
        $('#modal-staff-credit-section').attr('data-section-id', data['section-id'])
    } else {
        $('#modal-staff-credit-section').attr('data-section-id', '')
    }

    $('#modal-staff-credit-section .popup-content').empty();
    $('#modal-staff-credit-section .popup-content').append(`
     <div class="u-w100" style="display: flex; flex-direction: column; padding: 0 0 24px; margin-bottom: 8px;">
        <div class="form-group col-sm-12 ${show_section}" style="padding: 0;  margin-bottom: 24px;">
            <span class="bodytext--13 u-row">セクション名<span class="account__jp-astarisk-op" style="margin-left: 8px;">[任意]</span></span>
            <div class="col-sm-12" style="padding: 0;">
                <div class="col-sm-6" style="padding: 0 0 0 8px; margin: 0 8px 0 -8px">
                    <input type="text" name="section_name_jp" placeholder="セクション" class="form-control" id="id_section_name" value="${section}" maxlength="255">
                    
                </div>
                <div class="col-sm-6" style="padding: 0 8px 0 0; margin: 0 -8px 0 8px">
                    <input type="text" name="section_name_en" placeholder="Section" class="form-control" id="id_section_name_en" value="${section_en}" maxlength="255">
                </div>
            </div>
        </div>
        
        <div class="form-group col-sm-12 ${show_desc}" style="padding: 0;  margin: 0;">
            <span class="bodytext--13 u-row">説明<span class="account__jp-astarisk-op" style="margin-left: 8px;">[任意]</span></span>
            <div class="col-sm-12" style="padding: 0;">
                <div class="col-sm-12" style="padding: 0; margin: 0 0 8px">
                    <input type="text" name="section_desc_jp" placeholder="ここにセクションの補足が入ります。" class="form-control" id="id_section_desc" value="${desc}" maxlength="500">
                </div>
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <input type="text" name="section_desc_en" placeholder="Description" class="form-control" id="id_section_desc_en" value="${desc_en}" maxlength="500">
                </div>
            </div>
        </div>
    </div>
    <div class="popup-footer u-w100" style="text-align: right;">
        <button type="button"
                class="btn btn--tertiary btn-staff-credit-section-back">キャンセル</button>
        <button type="button"
                class="btn btn--primary btn-staff-credit-section-submit">OK</button>
    </div>`);
    $('#modal-staff-credit').modal('hide');
    $('#modal-staff-credit-section').modal('show');
    initModalSection(action);
}

function showModalConfirmDelete(target) {
    $('#modal-staff-credit').modal('hide');
    $('#modal-confirm-delete').modal('show');
    delete_target = target;

}

function renderModalArtist(action = 'new', item_artist_id='') {
    let target = $('#modal-staff-credit');
    if (!target.is('.show-artist') || !target.parents('.popup-container-modal-credit').hasClass('cannot-edit-credit')) {
        target.addClass('show-artist');
        let project_id = target.attr('data-project-id');

        //show modal and loading icon
        $('#modal-staff-credit-artist .popup-content').empty();
        $('#modal-staff-credit-artist .popup-content').append(`<div class="load-more-loading"></div>`);
        $('#modal-staff-credit').modal('hide');
        $('#modal-staff-credit-artist').modal('show');

        //get modal HTML
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/credit/get_all_artist_in_project",
            data: {
                'project_id': project_id,
                'action': action,
                'item_artist_id': item_artist_id
            },
            success: function (data) {
                target.removeClass('show-artist');
                $('#modal-staff-credit-artist .modal-content').empty();
                $('#modal-staff-credit-artist .modal-content').append(data.html);
                activeItemArtistEdit();
            },
            complete: function () {
                target.removeClass('show-artist');
            },
            error: function () {
                target.removeClass('show-artist');
                $('#modal-staff-credit-artist').modal('hide');
            }
        });
    }
}

function activeItemArtistEdit() {
    $('.modal-staff-credit-artist-header-item.item-edit-artist').trigger('click');
    let currentPosition = $('.modal-staff-credit-artist-header-item.item-edit-artist');
    let top_length = currentPosition.offset().left - $('.modal-staff-credit-artist-header-item').first().offset().left;
    $('.modal-staff-credit-artist-header').animate({
        scrollLeft: top_length
    }, top_length / 3);
}

function renderStaffCredit(lan='jp') {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/credit/get_staff_credit_for_project",
        data: {
            'project_id': $('#modal-staff-credit').attr('data-project-id'),
            'language': lan
        },
        success: function (data) {
            $('#modal-staff-credit .modal-content').empty();
            $('#modal-staff-credit .modal-content').append(data.html);
            initSortableSection();
            initSortableArtist();
            initModalCredit();
        },
        complete: function() {
            $('#modal-staff-credit').removeClass('show-staff');
        },
        error: function() {}
    });
}


function renderCreditPDF() {
    $(document).on('click', '.btn-staff-credit-download-pdf', function () {
        let project_id = $('#modal-staff-credit').attr('data-project-id');
        if (!project_id) {
            return
        }
        window.location = '/credit/download_staff_credit_pdf?project_id=' + $('#modal-staff-credit').attr('data-project-id');
    })
}


function actionToggleLinkProfile() {
    $(document).on('change', '#switch-show-profile-link', function () {
        if (!$('.modal-staff-credit-artist-header-item.active').attr('data-artist') || !$('.modal-staff-credit-artist-header-item.active').length ) {
            $('#switch-show-profile-link')[0].checked = false
        }
    })
}
