const MESSAGE_PER_LOAD = 30;
let is_loading_product_comment = false;
let current_load_product_comment = 0;
let total_product_comment;

function scrollProductComment() {
    $('.project-tab.project-tab-product-comment.active .pd-product-comment .mscrollbar--bottom').scroll(function () {
            if ($(this).scrollTop() < 500 && !is_loading_product_comment) {
                ajaxLoadMoreProductComment()
            }
        }
    )
}

function ajaxLoadMoreProductComment() {
    if (current_load_product_comment < total_product_comment && !is_loading_product_comment) {
        current_load_product_comment++;
        let last_message_id = $('.load-lasted-message').attr('data-message-id');
        is_loading_product_comment = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_load_more_comment",
            data: {
                'project_id': project_id,
                'type': 'project',
                'offset': current_load_product_comment,
                'last_message_id': last_message_id
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.project-tab-product-comment .mmessage-list').prepend(`<div class="load-more-loading"></div>`);
            },
            success: function (data) {
                is_loading_product_comment = false;
                let target = $('.project-tab.project-tab-product-comment .mmessage-list');
                target.prepend(data.html);
                if (target.scrollTop() === 0 && current_load_product_comment < total_product_comment) {
                   target.animate({scrollTop: 200});
                }
                actionShowIconResolved();
                $('.load-more-loading').remove();
                autoLoadMore();
            },
            complete: function () {
                calcPositionDropdownComment2();
                calcMoreActionComment($('.prdt .mmessage'));
                hoverDropdownMessage();
                hoverBtnActionMessage()
                clickBtnActionMessage()
            }
        });
    }
}

function autoLoadMore() {
    let countCurrentProductComment = current_load_product_comment === 0 ? MESSAGE_PER_LOAD : current_load_product_comment * MESSAGE_PER_LOAD;
    if ($('.mmessage').length - $('.mmessage .mmessage-resolved').length < countCurrentProductComment && $('.project-tab.project-tab-product-comment').hasClass('active')) {
        if (current_load_product_comment < total_product_comment && !is_loading_product_comment) {
            ajaxLoadMoreProductComment();
        }
    }
}


let is_loading_offer_message = false;
let current_load_offer_message = 0;
let total_offer_message;

function scrollOfferMessage() {
    $('.project-tab.project-tab-messenger.active .tab--messenger-artist .mmessage-list.mscrollbar').scroll(function () {
            if ($(this).scrollTop() < 500 && !is_loading_offer_message) {
                ajaxLoadMoreOfferMessage()
            }
        }
    )
}

function ajaxLoadMoreOfferMessage() {
    let offer_id = $('.mitem.mactive').attr('data-offer');
    if (!offer_id) {
        return
    }
    if (current_load_offer_message < total_offer_message && !is_loading_offer_message) {
        current_load_offer_message++;
        let last_message_id = $('.load-lasted-message').attr('data-message-id');
        is_loading_offer_message = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/messenger/load_more_offer_message",
            data: {
                'offer_id': offer_id,
                'offset': current_load_offer_message,
                'last_message_id': last_message_id
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.project-tab.project-tab-messenger .mcolumn.mcolumn--main .messenger-detail .mmessage-list').prepend(`<div class="load-more-loading"></div>`);
            },
            success: function (data) {
                if ($('.mitem.mactive').attr('data-offer') == offer_id) {
                    let target = $('.project-tab.project-tab-messenger .mcolumn.mcolumn--main .messenger-detail .mmessage-list');
                    target.prepend(data.html);
                    if (target.scrollTop() === 0 && current_load_offer_message < total_offer_message) {
                        target.animate({scrollTop: 200});
                    }
                    autoLoadMoreOfferMessage();
                }
                $('.load-more-loading').remove();
                is_loading_offer_message = false;
            },
            complete: function () {
                calcPositionDropdownComment2();
                calcMoreActionComment($('.prdt .mmessage'));
            }
        });
    }
}

function autoLoadMoreOfferMessage() {
    let countCurrentOfferMessage = current_load_offer_message === 0 ? MESSAGE_PER_LOAD : current_load_offer_message * MESSAGE_PER_LOAD;
    if ($('.mmessage').length - $('.mmessage .mmessage-resolved').length < countCurrentOfferMessage && $('.project-tab.project-tab-messenger').hasClass('active')) {
        if (current_load_offer_message < total_offer_message && !is_loading_offer_message) {
            ajaxLoadMoreOfferMessage();
        }
    }
}
