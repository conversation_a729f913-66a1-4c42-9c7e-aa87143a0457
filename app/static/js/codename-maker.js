// step1 コードネームの自動生成
// 各カテゴリーの単語リスト
export const codeNames = {
  地名: [
    "Acapulco",
    "Alexandria",
    "Alhambra",
    "Amalfi",
    "Amazon",
    "Andorra",
    "Angkor",
    "Ankara",
    "Antarctica",
    "Ararat",
    "Arequipa",
    "Aswan",
    "Aspen",
    "Athens",
    "Auckland",
    "Avalon",
    "Baghdad",
    "Bagan",
    "Bali",
    "Banff",
    "Belize",
    "Barcelona",
    "Bergen",
    "Bhutan",
    "BoraBora",
    "Bordeaux",
    "Borneo",
    "Brussels",
    "Budapest",
    "Brasilia",
    "Cairo",
    "Capri",
    "Casablanca",
    "Catalonia",
    "Cusco",
    "Dakar",
    "Damascus",
    "Edinburgh",
    "Fez",
    "Fiji",
    "Galapagos",
    "Gibraltar",
    "Hanoi",
    "Havana",
    "Himalayas",
    "Istanbul",
    "Jakarta",
    "Kathmandu",
    "Kyoto",
    "Lalibela",
    "Lhasa",
    "MachuPicchu",
    "Madagascar",
    "Marrakech",
    "Maui",
    "Milan",
    "Mombasa",
    "MonteCarlo",
    "Nairobi",
    "Naples",
    "Olympia",
    "Oslo",
    "Patagonia",
    "Phuket",
    "Prague",
    "Quebec",
    "Quito",
    "Rajasthan",
    "Reykjavik",
    "Rome",
    "Samarkand",
    "Santorini",
    "Savannah",
    "Shanghai",
    "Siberia",
    "Sicily",
    "Timbuktu",
    "Tokyo",
    "Transylvania",
    "Ulaanbaatar",
    "Valencia",
    "Venice",
    "Vienna",
    "Xanadu",
    "Yangon",
    "Zanzibar",
    "Cappadocia",
    "Caracas",
    "Cartagena",
    "Chengdu",
    "ChichenItza",
    "Colombo",
    "Corsica",
    "Crete",
    "Delhi",
    "Dubai",
    "Dublin",
    "Durban",
    "Elba",
    "Ellora",
    "Featherdale",
    "Giza",
    "Goa",
    "Granada",
    "Greenland",
    "Guangzhou",
    "Hamburg",
    "Hiroshima",
    "Hobart",
    "Ibiza",
    "InleLake",
    "Jerusalem",
    "Kamchatka",
    "Kilimanjaro",
    "Kingston",
    "Kolkata",
    "Krakow",
    "LaPaz",
    "Lauterbrunnen",
    "Leh",
    "Lijiang",
    "Lima",
    "Lisbon",
    "Liverpool",
    "Ljubljana",
    "Lofoten",
    "Luxor",
    "Lyon",
  ],
  デザート: [
    "Alfajor",
    "Baklava",
    "BanoffeePie",
    "Blancmange",
    "Brownie",
    "Cannoli",
    "Charlotte",
    "Cheesecake",
    "Churros",
    "Clafoutis",
    "Cobbler",
    "CremeBrulee",
    "Cupcake",
    "Dacquoise",
    "DobosTorte",
    "Eclair",
    "Financier",
    "Flan",
    "Fondant",
    "FruitTart",
    "Galette",
    "Gelato",
    "Genoise",
    "Halva",
    "Honeycake",
    "Jalebi",
    "Kanafeh",
    "KeyLimePie",
    "Kulfi",
    "Lamington",
    "LinzerTorte",
    "Macaron",
    "Madeleine",
    "Meringue",
    "MilleFeuille",
    "Mochi",
    "Napoleon",
    "OperaCake",
    "PannaCotta",
    "Pavlova",
    "PecanPie",
    "PetitFour",
    "PoundCake",
    "Profiterole",
    "Pudding",
    "PumpkinPie",
    "RedVelvetCake",
    "RumBaba",
    "SacherTorte",
    "Semifreddo",
    "Shortcake",
    "Souffle",
    "Spumoni",
    "StickyToffeePudding",
    "Strudel",
    "Tiramisu",
    "Torte",
    "Trifle",
    "Truffle",
    "UpsideDownCake",
    "Vacherin",
    "WhoopiePie",
    "Zabaglione",
    "Zeppole",
  ],
  天体: [
    "Andromeda",
    "Antares",
    "Aquarius",
    "Aquila",
    "Aries",
    "Aurora",
    "Betelgeuse",
    "Callisto",
    "CanisMajor",
    "Capricorn",
    "Cassiopeia",
    "Centauri",
    "Cepheus",
    "Ceres",
    "Charon",
    "Comet",
    "Corona",
    "Cygnus",
    "Deimos",
    "Draco",
    "Electra",
    "Eridanus",
    "Europa",
    "Fomalhaut",
    "Galatea",
    "Gemini",
    "Halley",
    "Helix",
    "Hercules",
    "Hyperion",
    "Io",
    "Jupiter",
    "Kale",
    "Leo",
    "Libra",
    "Lyra",
    "Maia",
    "Mars",
    "Mercury",
    "Meteor",
    "MilkyWay",
    "Miranda",
    "Nebula",
    "Neptune",
    "Nova",
    "Orion",
    "Pegasus",
    "Perseus",
    "Phobos",
    "Pisces",
    "Pleiades",
    "Pluto",
    "Pollux",
    "Proxima",
    "Rigel",
    "Saturn",
    "Scorpius",
    "Sirius",
    "Sol",
    "Sputnik",
    "StarDust",
    "Taurus",
    "Titania",
    "Triton",
    "Uranus",
    "Vega",
    "Venus",
    "Vesta",
    "Virgo",
    "Vulcan",
  ],
  神話: [
    "Achilles",
    "Adonis",
    "Aeneas",
    "Andromeda",
    "Apollo",
    "Arachne",
    "Ares",
    "Artemis",
    "Athena",
    "Atlas",
    "Baldur",
    "Bellerophon",
    "Bragi",
    "Calypso",
    "Cerberus",
    "Cupid",
    "Daedalus",
    "Daphne",
    "Demeter",
    "Diana",
    "Dionysus",
    "Echo",
    "Eos",
    "Eris",
    "Eros",
    "Freyja",
    "Freyr",
    "Gaia",
    "Galatea",
    "Ganymede",
    "Hades",
    "Hecate",
    "Helios",
    "Hera",
    "Hercules",
    "Hermes",
    "Hestia",
    "Hippolyta",
    "Hyperion",
    "Icarus",
    "Iris",
    "Isis",
    "Janus",
    "Juno",
    "Jupiter",
    "Krishna",
    "Loki",
    "Maia",
    "Mars",
    "Medusa",
    "Mercury",
    "Minerva",
    "Morpheus",
    "Narcissus",
    "Nemesis",
    "Neptune",
    "Nike",
    "Odin",
    "Orion",
    "Osiris",
    "Pan",
    "Pandora",
    "Persephone",
    "Phoebe",
    "Pluto",
    "Poseidon",
    "Prometheus",
    "Rhea",
    "Selene",
    "Thor",
    "Venus",
    "Vesta",
    "Vulcan",
    "Zeus",
  ],
  動物: [
    "Albatross",
    "Antelope",
    "Armadillo",
    "BarnOwl",
    "Bison",
    "BlackPanther",
    "Cheetah",
    "Chimpanzee",
    "Cobra",
    "Condor",
    "Dolphin",
    "Dragonfly",
    "Eagle",
    "Elephant",
    "Falcon",
    "Flamingo",
    "Fox",
    "Gazelle",
    "Giraffe",
    "Hawk",
    "Hippopotamus",
    "Ibis",
    "Jaguar",
    "Kangaroo",
    "Kingfisher",
    "Koala",
    "Leopard",
    "Lynx",
    "Macaw",
    "Manatee",
    "Narwhal",
    "Octopus",
    "Orangutan",
    "Osprey",
    "Otter",
    "Panda",
    "Peacock",
    "Pelican",
    "Penguin",
    "Phoenix",
    "PolarBear",
    "Porcupine",
    "Quetzal",
    "Raccoon",
    "RedPanda",
    "Rhinoceros",
    "Salamander",
    "SeaTurtle",
    "SnowLeopard",
    "Sparrow",
    "Sphinx",
    "Stingray",
    "Swan",
    "Tiger",
    "Toucan",
    "Umbrellabird",
    "Vulture",
    "Walrus",
    "Warthog",
    "Wildebeest",
    "Wolf",
    "Wombat",
    "Woodpecker",
    "Yak",
    "Zebra",
    "Zeppelin",
    "Zorilla",
  ],
  色: [
    "Auburn",
    "Azure",
    "Beige",
    "Bisque",
    "Blush",
    "Bronze",
    "Burgundy",
    "Carmine",
    "Cerulean",
    "Charcoal",
    "Chartreuse",
    "Chestnut",
    "Citrine",
    "Clay",
    "Cobalt",
    "Copper",
    "Coral",
    "Crimson",
    "Cyan",
    "Denim",
    "Emerald",
    "Fuchsia",
    "Garnet",
    "Gold",
    "Harlequin",
    "Indigo",
    "Ivory",
    "Jade",
    "Jet",
    "Khaki",
    "Lavender",
    "Lemon",
    "Lilac",
    "Lime",
    "Magenta",
    "Mahogany",
    "Maroon",
    "Mauve",
    "Moss",
    "Mustard",
    "Navy",
    "Ochre",
    "Olive",
    "Onyx",
    "Opal",
    "Peach",
    "Pearl",
    "Periwinkle",
    "Plum",
    "Puce",
    "Raspberry",
    "Ruby",
    "Saffron",
    "Sage",
    "Salmon",
    "Sapphire",
    "Scarlet",
    "Sienna",
    "Silver",
    "Taupe",
    "Teal",
    "Thistle",
    "Turquoise",
    "Umber",
    "Vermilion",
    "Violet",
    "Viridian",
    "Wheat",
    "Wisteria",
    "Zinnia",
  ],
  宝石: [
    "Agate",
    "Alexandrite",
    "Amber",
    "Amethyst",
    "Apatite",
    "Aquamarine",
    "Aventurine",
    "Azurite",
    "Beryl",
    "Bloodstone",
    "Carnelian",
    "Chalcedony",
    "Charoite",
    "Chrysocolla",
    "Chrysoprase",
    "Citrine",
    "Coral",
    "Diamond",
    "Diopside",
    "Emerald",
    "Fluorite",
    "Garnet",
    "Goshenite",
    "Heliodor",
    "Hematite",
    "Iolite",
    "Jade",
    "Jasper",
    "Kunzite",
    "Kyanite",
    "Labradorite",
    "LapisLazuli",
    "Larimar",
    "Malachite",
    "Moonstone",
    "Morganite",
    "Onyx",
    "Opal",
    "Peridot",
    "Pyrite",
    "Quartz",
    "Rhodochrosite",
    "Rhodonite",
    "Ruby",
    "Sapphire",
    "Sardonyx",
    "Serpentine",
    "Spinel",
    "Sunstone",
    "Tanzanite",
    "Topaz",
    "Tourmaline",
    "Turquoise",
    "Unakite",
    "Variscite",
    "Zircon",
    "Amazonite",
    "Andalusite",
    "Benitoite",
    "Chrysoberyl",
    "Danburite",
    "Epidote",
    "Garnierite",
    "Howlite",
    "Jadeite",
    "Jet",
    "Kornerupine",
    "Lazulite",
  ],
};

// 英数字を自動生成する関数
export const generateAlphanumeric = () => {
  // アルファベット（大文字のみ、'O'を除く）と数字（'0'を除く）のリスト
  const alphabets = "ABCDEFGHIJKLMNPQRSTUVWXYZ";
  const numbers = "123456789";

  // 1文字目は必ずアルファベット
  let result = alphabets[Math.floor(Math.random() * alphabets.length)];

  // 2文字目と3文字目は英数字
  for (let i = 0; i < 2; i++) {
    const chars = alphabets + numbers;
    result += chars[Math.floor(Math.random() * chars.length)];
  }

  return result;
};
