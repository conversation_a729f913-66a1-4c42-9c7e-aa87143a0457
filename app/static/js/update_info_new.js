function updateInfoNew() {
    infoCreate();
    infoEdit();
    infoDelete();
    dragItem("popup-container-add");
}
function infoToggleCollapse() {
    $(document).on("click", ".collapse-checkbox", function () {
        const checked = $(this).is(':checked');
        if (checked) {
            $(this).parents('.updateinfo__tree-container').addClass("active");
            $(this).parents('.collapse-checkbox').attr('checked', 'checked');
        } else {
            $(this).parents('.updateinfo__tree-container').removeClass("active");
            $(this).parents('.collapse-checkbox').removeAttr('checked');
        }
    });
    $(".collapse-checkbox").first().trigger('click');
}

function onChangeItem(){
    $(".popup-wbody-file").on("change", function(){
        let parent = $(this).parents(".popup-wbody-item");
        let text = parent.find("textarea");
        if (!text.val() && !$(this).val()){
            parent.addClass("blank_item");
        }else{
            parent.removeClass("blank_item");
        }
    })

    $(".popup-wbody-content textarea").on("keyup", function(){
        let parent = $(this).parents(".popup-wbody-item");
        let file = parent.find(".popup-wbody-file");
        if (!$(this).val() && !file.val()){
            parent.addClass("blank_item");
        }else{
            parent.removeClass("blank_item");
        }
    })
}


// Insert img
function readURL(input, $input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
            $input.parents('.popup-wbody-img').find('.popup-wbody-img-insert').find('.popup-wbody-img-show').attr('src', e.target.result);
            $("<div class=\"popup-wbody-delete-img\">" +
                "<span class=\"delete-img icon icon--sicon-trash\"></span>" +
                "</div>").insertAfter($input.parents('.popup-wbody-img').find('.popup-wbody-img-insert'));
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// var es = document.forms[0].elements;
//     es[1].onclick = function(){
//     clearInputFile(es[0]);
// };

function clearInputFile(f){
    if(f.value){
        try{
            f.value = '';
        }catch(err){
        }
        if(f.value) { 
            var form = document.createElement('.popup-wbody-delete-img'), ref = f.nextSibling;
            form.appendChild(f);
            form.reset();
            ref.parentNode.insertBefore(f,ref);
        }
    }
}

// Drag item
function dragItem(containment) {
    $(".popup-wbody-list").sortable({
        containment: $(`.${containment}`),
        items: "> div:not('.blank_item'), > span",
        connectWith: '.popup-wbody-list',
        handle: 'span.drag-row',
        tolerance: 'pointer',
        cursor: "move",
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
            let index = ui.item.index();
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            if (ui.sender) {
                let item_id = ui.item.find('.popup-wbody-list').attr('data-item-id');
                let post_id = ui.item.parents('.updateinfo__popup-container').attr('data-post-id');
            }
            if(containment == "popup-container-add"){
            reorderItems("add");
            }else{
                reorderItems("edit");
            }
        },
    }).disableSelection();
}

function check_blank_item(container_comp){
    let list_items = container_comp.find(".popup-wbody-item:not('.hide')");
    if(!list_items.length){
        return true;
    }
    var result = true;
    list_items.each(function(i, item){
            let value = $(item).find("textarea").val();
            let file = $(item).find(".popup-wbody-file").val();
            if (!value && !file){
                result =  false;
                return;
            }
        })
    return result;
}

function infoCreate() {
    // Insert img
    $(document).on('change', ".popup-wbody-file", function(){
        readURL(this, $(this));
    });

    // Range time
    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    let startDate = new Date();
    let endDate = new Date().addDays(5);
    $('.js-daterangepicker').daterangepicker({
        startDate: startDate,
        endDate: endDate,
    });

    // Delete img
    $(document).on('click', ".delete-img", function () {
        $(this).parents('.popup-wbody-img').find('.popup-wbody-img-insert').find('.popup-wbody-img-show').attr('src', '');
        $(this).parents('.popup-wbody-img').find('.popup-wbody-file').val('');
        $(this).parents(".popup-wbody-delete-img").remove();
    });

    // Add row
    $("#add_new .popup-wbody-add__icon").click(function(){
        let parent = $(this).parents(".popup-wbody")
        let check = check_blank_item(parent);
        if(!check){
            return;
        }
        parent.find(".new-popup-wbody-item").removeClass("new-popup-wbody-item");
        dragItem("popup-container-add");
        let item_before = $("#add_new").find('.popup-wbody-item').length;
        let id_image = 'imgInpNew' + item_before;
        let order = $("#add_new .popup-wbody-list").children().length + 1;
        var add_new =  '<div class="popup-wbody-item blank_item" data-order="'+ order +'">' +
            '<div class="popup-wbody-img">'+
            '<label for="' + id_image +'" class="popup-wbody-img-insert">' +
            '<img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="" />' +
            '</label>' +
            '<input type="file" class="popup-wbody-file" accept="image/*" name="image" id="' + id_image +'">' +
            '</div>' +
            '<div class="popup-wbody-content">' +
            '<textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>' +
            '</div>' +
            '<div class="popup-wbody-action">' +
            '<span class="delete-row icon icon--sicon-trash"></span>' +
            '<span class="drag-row icon icon--sicon-drag-indicator"></span>' +
            '</div>' +
            '</div>';
        $("#add_new .popup-wbody-list").append(add_new);
        onChangeItem();
    });

    $("#add_update .popup-wbody-add__icon").click(function(){
        let parent = $(this).parents(".popup-wbody")
        let check = check_blank_item(parent);
        if(!check){
            return;
        }
        parent.find(".new-popup-wbody-item").removeClass("new-popup-wbody-item");
        dragItem("popup-container-add");
        let item_before = $("#add_update").find('.popup-wbody-item').length;
        let id_image = 'imgInpUpdate' + item_before;
        let order = $("#add_update .popup-wbody-list").children().length + 1;
        var add_update = '<div class="popup-wbody-item blank_item" data-order="'+ order +'">' +
            '<div class="popup-wbody-img">'+
            '<label for="' + id_image +'" class="popup-wbody-img-insert">' +
            '<img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="" />' +
            '</label>' +
            '<input type="file" class="popup-wbody-file" accept="image/*" name="image" id="' + id_image +'">' +
            '</div>' +
            '<div class="popup-wbody-content">' +
            '<textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>' +
            '</div>' +
            '<div class="popup-wbody-action">' +
            '<span class="delete-row icon icon--sicon-trash"></span>' +
            '<span class="drag-row icon icon--sicon-drag-indicator"></span>' +
            '</div>' +
            '</div>';
        $("#add_update .popup-wbody-list").append(add_update);
        onChangeItem();
    });
}

function onchangeNewItem(){
    $("textarea").change(function(){
        console.log("111");
    })
}

function infoEdit() {
    // Add row
    $("#editInfo").on('click', '#edit_new .popup-wbody-add__icon', function(){
        let parent = $(this).parents(".popup-wbody")
        let check = check_blank_item(parent);
        if(!check){
            return;
        }
        parent.find(".new-popup-wbody-item").removeClass("new-popup-wbody-item");
        dragItem("popup-container-edit");
        let item_before = $("#edit_new").find('.popup-wbody-item').length;
        let id_image = 'imgInpEditNew' + item_before;
        let order = $("#edit_new .popup-wbody-list").children().length + 1;
        var edit_new = '<div class="popup-wbody-item blank_item" new data-order="'+ order +'">' +
            '<div class="popup-wbody-img">'+
            '<label for="'+id_image+'" class="popup-wbody-img-insert">' +
            '<img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="" />' +
            '</label>' +
            '<input type="file" class="popup-wbody-file" accept="image/*" name="image" id="'+id_image+'">' +
            '</div>' +
            '<div class="popup-wbody-content">' +
            '<textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>' +
            '</div>' +
            '<div class="popup-wbody-action">' +
            '<span class="delete-row icon icon--sicon-trash"></span>' +
            '<span class="drag-row icon icon--sicon-drag-indicator"></span>' +
            '</div>' +
            '</div>';
        $("#edit_new .popup-wbody-list").append(edit_new);
        onChangeItem();
    });

    $("#editInfo").on('click', '#edit_update .popup-wbody-add__icon', function(){
        let parent = $(this).parents(".popup-wbody")
        let check = check_blank_item(parent);
        if(!check){
            return;
        }
        parent.find(".new-popup-wbody-item").removeClass("new-popup-wbody-item");
        dragItem("popup-container-edit");
        let item_before = $("#edit_update").find('.popup-wbody-item').length;
        let id_image = 'imgInpEditUpdate' + item_before;
        let order = $("#edit_update .popup-wbody-list").children().length + 1;
        var edit_update = '<div class="popup-wbody-item blank_item" new data-order="'+ order +'">' +
            '<div class="popup-wbody-img">'+
            '<label for="'+id_image+'" class="popup-wbody-img-insert">' +
            '<img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="" />' +
            '</label>' +
            '<input type="file" class="popup-wbody-file" accept="image/*" name="image" id="'+id_image+'">' +
            '</div>' +
            '<div class="popup-wbody-content">' +
            '<textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>' +
            '</div>' +
            '<div class="popup-wbody-action">' +
            '<span class="delete-row icon icon--sicon-trash"></span>' +
            '<span class="drag-row icon icon--sicon-drag-indicator"></span>' +
            '</div>' +
            '</div>';
        $("#edit_update .popup-wbody-list").append(edit_update);
        onChangeItem();
    });

    // Delete row
    $(document).on('click', ".delete-row", function () {
        let parent = $(this).parents('.popup-wbody-item');
        if (parent.hasClass('new')) {
            $(this).closest(".popup-wrap .popup-wbody-item").remove();
        } else {
            parent.addClass('hide');
        }
        if($(this).parents("#popup-container-add").length){
            reorderItems("add");
        }else{
            reorderItems("edit");
        }
    });
}

function infoDelete() {
    $(document).on('click', '.updateinfo__tree-action .btn--delete', function () {
        $('#deleteInfo').attr('data-post-id', $(this).parents('.updateinfo__tree-container').attr('data-post-id'));
    });
    $('#deleteInfo').find('.btn-popup-delete').off('click').on('click', function () {
        let post_id = $('#deleteInfo').attr('data-post-id');
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/post/delete_post",
            data: {
                'post_id': post_id
            },
            success: function () {
                $('#deleteInfo').find('.btn-popup-close').click();
                $('.updateinfo__tree-container[data-post-id^=' + post_id + ']').remove();
            }
        })
    })

    $(document).on('click', '.updateinfo__tree-action .btn--edit', function () {
        $('#editInfo').attr('data-post-id', $(this).parents('.updateinfo__tree-container').attr('data-post-id'));
        let post_id = $('#editInfo').attr('data-post-id');
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/post/get_post_items",
            data: {
                'post_id': post_id
            },
            success: function (data) {
                $('#editInfo').find('form').empty();
                $('#editInfo').find('form').append(data.html);
                // Range time
                let startDate = new Date(data.start_time);
                let endDate = new Date(data.end_time);
                $('#deadline-date-update').daterangepicker({
                    startDate: startDate,
                    endDate: endDate,
                });
                // Delete img
                $(".delete-img").click(function(){
                    $(this).parents('.popup-wbody-img').find('.popup-wbody-img-insert').find('.popup-wbody-img-show').attr('src', '');
                    $(this).parents('.popup-wbody-img').find('.popup-wbody-file').val('');
                    $(this).parents(".popup-wbody-delete-img").remove();
                });
                dragItem("popup-container-edit");
                preventEnterDateInput();
            }
        })
    });
}

let formData = {
    "post_id": "",
    "range_date": "",
    "post_items": [
        {
            "id": "",
            "content": "",
            "type": "new", // new/update
            "status": "new", // new, edited
            "file": ""
        }
    ],
    "list_deleted": [],
    "type": "new"
};
let form;

function checkButtonSubmit() {
    formData = {
        "post_id": "",
        "range_date": "",
        "post_items": [],
        "list_deleted": [],
        "type": "new"
    };
    let type = 'new';
    let list_item;
    let post_index;
    let range_date;
    form = new FormData();
    $('.btn-submit-profile').removeClass('hide').addClass('disable');
    let post_dom = $('#editInfo');
    let post_id;
    if ($('#edit_new').length && post_dom.attr('data-post-id')) {
        post_id = post_dom.attr('data-post-id');
        formData.post_id = post_id;
        post_index = $('.updateinfo__tree-container[data-post-id^=' + post_id + ']').find('.updateinfo__tree-collapse').attr('for').split('_')[1];
        type = 'update';
        formData.type = 'update';
        list_item = post_dom.find('.popup-wbody-item');
        range_date = $('#deadline-date-update').val();

    } else {
        formData.type = 'new';
        list_item = $('#addInfo').find('.popup-wbody-item');
        if ($('.updateinfo__tree-collapse').length > 0) {
            post_index = (parseInt($('.updateinfo__tree-collapse').first().attr('for').split('_')[1]) + 1).toString();
        } else {
            post_index = ''
        }
        range_date = $('#deadline-date').val();
    }
    formData.post_index = post_index;
    formData.range_date = range_date;

    list_item.each(function (i, e) {
        let item_id = $(this).attr('data-item-id');
        let status = 'edited';

        if (!item_id) {
            item_id = 'new_' + i;
            status = 'new'
        } else if ($(this).hasClass('hide')) {
            status = 'deleted'
        }
        formData.post_items.push(postItemObject(item_id, $(this), status));
    });
    return true
}

function postItemObject(id, e, status) {
    let object = {
        "id": "",
        "content": "",
        "type": "new",
        "status": "new",
        "file": "",
        "delete_file": "",
        "order": ""
    };
    let content = $(e).find('textarea').val().trim();
    // let file = $(e).find('.popup-wbody-file').val();
    let file = $(e).find('img').attr('src');
    let delete_file = false; // check delete file?
    let order = $(e).data("order");
    object.status = status;
    object.id = id;
    object.order = order;
    if (status !== 'deleted') {
        object.delete_file = delete_file;
        let input_file = $(e).find('.popup-wbody-file');
        if (input_file.length && input_file[0].files.length) {
            form.append(id, input_file[0].files[0]);
        }
        object.content = content;
        object.file = file;
        if(!object.file) {
            delete_file = true;
            object.delete_file = delete_file;
        }
        
        let parent_dom = e.parents('.popup-wrap');
        if (parent_dom.is('#add_new') || parent_dom.is('#edit_new')) {
            object.type = 'new'
        } else {
            object.type = 'update'
        }
    }
    return object;
}

function submitPost() {
    $('.btn-popup-save').off().on('click', function (e) {
        let button_dom = $(this);
        if (!button_dom.hasClass('disable')) {
            if (checkButtonSubmit()) {
                button_dom.addClass('disable');
                form.append('json', JSON.stringify(formData));
                $.ajax({
                    type: "POST",
                    url: "/post/add_update_post_info",
                    data: form,
                    contentType: false,
                    processData: false,
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        console.log('ok');
                        $('.updateinfo__popup-container').modal('hide');
                        let post_id = response.post_id;
                        if (post_id) {
                            let post_dom = $('.updateinfo__tree-container[data-post-id^=' + post_id + ']');
                            $(response.post_html).insertAfter(post_dom);
                            post_dom.remove();
                            $('.updateinfo__tree-container[data-post-id^=' + post_id + ']').find((".collapse-checkbox")).trigger('click')
                            
                        } else {
                            $('.updateinfo__tree').prepend(response.post_html);
                            $(".collapse-checkbox").first().trigger('click');
                        }
                    },
                    error: function () {

                    },
                    complete: function () {
                        button_dom.removeClass('disable');
                    }
                })
            }
        }
    });
}

function resetMOdal() {
    $('#addInfo').on('hidden.bs.modal', function (e) {
        $('#addInfo').find('.popup-wbody-list').empty();

        let new_item = '<div class="popup-wbody-item " data-order="1">' +
            '<div class="popup-wbody-img">' +
            '<label for="imgInpNew" class="popup-wbody-img-insert">' +
            '<img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="">' +
            '</label>' +
            '<input type="file" class="popup-wbody-file" accept="image/*" name="image" id="imgInpNew">' +
            '</div>' +
            '<div class="popup-wbody-content">\n' +
            '<textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>' +
            '</div>' +
            '<div class="popup-wbody-action">' +
            '<span class="delete-row icon icon--sicon-trash"></span>' +
            '<span class="drag-row icon icon--sicon-drag-indicator"></span>' +
            '</div>' +
            '</div>'

        $('#addInfo #add_new').find('.popup-wbody-list').append(new_item);

        let update_item = '<div class="popup-wbody-item " data-order="1">\n' +
            '                                    <div class="popup-wbody-img">\n' +
            '                                        <label for="imgInpUpdate" class="popup-wbody-img-insert">\n' +
            '                                            <img class="icon icon--sicon-insert-photo popup-wbody-img-show" id="" src="#" alt="">\n' +
            '                                        </label>\n' +
            '                                        <input type="file" class="popup-wbody-file" accept="image/*" name="image" id="imgInpUpdate">\n' +
            '                                    </div>\n' +
            '\n' +
            '                                    <div class="popup-wbody-content">\n' +
            '                                        <textarea name="content" id="content" placeholder="バグを修正し、使い勝手を高めました" rows="2" cols="10"></textarea>\n' +
            '                                    </div>\n' +
            '                                    <div class="popup-wbody-action">\n' +
            '                                       <span class="delete-row icon icon--sicon-trash"></span>\n' +
        '                                           <span class="drag-row icon icon--sicon-drag-indicator"></span>\n' +
            '                                   </div>\n' +
            '                                </div>'

        $('#addInfo #add_update').find('.popup-wbody-list').append(update_item);
    })

    $('#editInfo').on('hidden.bs.modal', function (e) {
        $('#editInfo').find('form').empty();
    })
}

function reorderItems(type){
    let new_items = $(`#${type}_new .popup-wbody-list`).children();
    let update_items = $(`#${type}_update .popup-wbody-list`).children();
    if(new_items.length){
        new_items.each(function(item){
            $(this).attr("data-order", item+1);
        });
    }
    if(update_items.length){
        update_items.each(function(item){
            $(this).attr("data-order", item+1);
        });
    }
}

function preventEnterDateInput(){
    $(".js-daterangepicker").keydown(function(event){
        if(event.keyCode == 13) {
          event.preventDefault();
          return false;
        }
    });
}

$(document).ready(function () {
    infoToggleCollapse();
    updateInfoNew();
    submitPost();
    resetMOdal();
    onChangeItem();
    preventEnterDateInput();
});

