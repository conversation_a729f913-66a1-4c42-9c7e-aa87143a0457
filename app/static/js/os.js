// gsap animation
gsap.registerPlugin(ScrollTrigger);

// ヒーローイメージのアニメーション

window.onload = () => {
  document.body.style.visibility = "visible";
  gsap.fromTo('.os-heroimage-container', 
    {
      autoAlpha: 0, // 透明度を0から開始
      scale: 1.3 // スケールを0.5から開始
    }, 
    {
      autoAlpha: 1, // 透明度を1までアニメーション
      scale: 1, // スケールを1までアニメーション
      duration: 3, // アニメーションの継続時間
      ease: 'power2.out', // イージング関数
    }
  );
}

// 画面上でフェードアウトさせるアニメーション
// document.querySelectorAll('.panel').forEach(panel => {
//     gsap.fromTo(panel, 
//         { opacity: 1, scale: 1 },
//         {
//             opacity: 0,
//             scale: 0.809,
//             scrollTrigger: {
//                 trigger: panel,
//                 start: "bottom 19.1%",
//                 end: "bottom top",
//                 scrub: true
//             }
//         }
//     );
// });

document.querySelectorAll('.slideup').forEach(slideup => {
    gsap.fromTo(slideup, 
        { y: 16, opacity: 0 },
        {
            y: 0,
            opacity: 1,
            scrollTrigger: {
                trigger: slideup,
                start: "top 90.45%",
                end: "bottom 80.9%",
                scrub: true
            }
        }
    );
});

document.querySelectorAll('.slideup-center').forEach(slideup => {
  gsap.fromTo(slideup, 
      { y: 64, opacity: 0 },
      {
          y: 0,
          opacity: 1,
          scrollTrigger: {
              trigger: slideup,
              start: "top 80.9%",
              end: "top 61.8%",
              scrub: true
          }
      }
  );
});

// フェードイン
gsap.utils.toArray('.fade-in').forEach(item => {
  gsap.fromTo(item, 
    { opacity: 0 },
    {
      opacity: 1,
      scrollTrigger: {
        trigger: item,
        start: "center bottom", // 要素の下がビューポートの下端に達したとき
        end: "bottom top", // 要素の下端がビューポートの上端に達したとき
        toggleActions: "play none none none",
        once: true // 一度だけアニメーションを実行
      }
    }
  );
});



// tooltipの表示・非表示
const links = document.querySelectorAll("a");

links.forEach((link) => {
  link.addEventListener("mouseenter", () => {
    const id = link.id;
    const tooltip = document.querySelector(`[anchor=${id}]`);
    if (tooltip == null) {
      return;
    }
    if (!tooltip.matches(":popover-open")) {
      tooltip.showPopover();
    }
  });
  link.addEventListener("mouseleave", () => {
    const id = link.id;
    const tooltip = document.querySelector(`[anchor=${id}]`);
    if (tooltip == null) {
      return;
    }
    if (tooltip.matches(":popover-open")) {
      tooltip.hidePopover();
    }
  });
});



/* 動画をホバー再生 */
document.querySelectorAll('.c-movie').forEach(function(movie) {
  movie.addEventListener('mouseenter', function() {
    this.play();
  });

  movie.addEventListener('mouseleave', function() {
    this.pause();
  });
});