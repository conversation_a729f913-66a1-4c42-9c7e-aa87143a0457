    function deleteUserInvited(jwt_token) {
        bootbox.confirm({
            message: gettext('Do you really want to delete this?'),
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if (result === true) {
                    $.ajax({
                        type: 'DELETE',
                        url: '/top/invite_user?jwt=' + jwt_token,
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            if (response.status === '200') {
                                var signature = jwt_token.split('.')[2];
                                $('.project-member-invited-' + signature).remove();
                            }
                        },
                        error: function (response) {
                            toastr.error("このアクションは実行できません！");
                        }
                    })
                }
            }
        })
    }

    function updateUserInvited(signature) {
        let member_dom = $(document).find(".project-member-invited-" + signature);
        product_id = member_dom.parents('.project-member-setting.active').attr('data-project');
        let user_id = member_dom.attr('data-user');
        let member_content = member_dom.find('.project-setting-member__setting');
        if (product_id !== undefined && user_id !== undefined && !member_dom.is('.editing')) {
            member_dom.addClass('editing');
            let set_view_only = member_content.find('.project-setting-member__permission');
            member_content.find('.project-setting-member__ip').addClass('hide');
            let list_ip = [];
            member_content.find('.project-setting-member__ip').each(function (i) {
                list_ip[i] = $(this).html().trim()
            });
            let list_ip_content = list_ip.join(', ');
            member_content.append('<div class="setting-member"><div class="form-group">\n' +
                '                    <label for="member-ip">IP制限</label>\n' +
                '                    <input id="set-ip" class="form-control form-control" type="text" name="member-ip" value="">\n' +
                '                </div>' +
                '<div class="form-group">\n' +
                '                    <div class="checkbox input-checkbox">\n' +
                '                        <input type="checkbox" name="member-permit-view-only" id="set-view">\n' +
                '                        <label for="member-checkbox"><a></a>閲覧のみ\n' +
                '                        </label>\n' +
                '                    </div>\n' +
                '                    <div class="project-setting-member_field-desc">※検収・コメント記載するには、チェックを外してください。</div>\n' +
                '                </div><div class="member-submit"></div></div>');
            member_content.find('#set-ip').attr('value', list_ip_content);
            if (set_view_only.length) {
                set_view_only.addClass('hide');
                member_content.find('#set-view').prop('checked', true);
            }
            member_content.find('.member-submit').append(`<div class="video-comment-action">
            <a class="button button--text button--text-primary member-edit-save" style="margin-right: 35px"
               href="javascript:void(0)" role="button">更新</a>
            <a class="button button--text button--text-primary member-edit-cancel"
               href="javascript:void(0)" role="button">キャンセル</a>
            </div>`);
            member_dom.find('.member-edit-cancel').off().on('click', function () {
                member_content.find('.project-setting-member__ip').removeClass('hide');
                member_content.find('.project-setting-member__permission').removeClass('hide');
                member_content.find('.setting-member').remove();
                member_dom.removeClass('editing');
            });
            member_dom.find('.member-edit-save').off().on('click', function () {
                let new_list_ip = member_content.find('#set-ip').val();
                let new_set_view = member_content.find('#set-view').is(":checked");
                member_dom.removeClass('editing');
                values = {
                    'user_id': user_id,
                    'product_id': product_id,
                    'list_ip': new_list_ip,
                    'view_only': new_set_view
                };
                $.ajax({
                    type: "POST",
                    url: "/top/update_project_member",
                    data: values,
                    dataType: 'json',
                    success: function (data) {
                        if (data.status === 'success') {
                            // toastr.success('編集を保存しました。', 'コメントの編集');
                            member_content.find('.project-setting-member__ip').remove();
                            if (data.view_only === 'true') {
                                member_content.append('<div class="project-setting-member__permission">閲覧のみ</div>')
                            } else {
                                member_content.find('.project-setting-member__permission').addClass('hide')
                            }
                            $.each(data.list_ip, function (index, value) {
                                member_content.append('<div class="project-setting-member__ip"></div>');
                            });
                            member_content.find('.project-setting-member__ip').each(function (i) {
                                $(this).html(data.list_ip[i])
                            });
                            member_content.find('.setting-member').remove();
                        } else if (data.status === failed) {
                            toastr.error('エラーが発生しました', 'コメントの編集');
                            member_content.find('.project-setting-member__ip').removeClass('hide');
                            member_content.find('.project-setting-member__permission').removeClass('hide');
                            member_content.find('.setting-member').remove();
                        }
                    },
                });
            });
        }
    }

    function deleteUserInviting(jwt_token, user_id) {
        bootbox.confirm({
            message: "本当に招待を取り消しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn btn-success btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn btn-danger btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result === true) {
                    $.ajax({
                        type: 'DELETE',
                        url: '/top/invite_user?jwt=' + jwt_token,
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            if (response.status == '200') {
                                var signature = jwt_token.split('.')[2];
                                let memberDom = $('.member-item[data-user^=' + user_id + ']');
                                if (memberDom.parents('.member-manage__group').find('.member-item').length === 1) {
                                    memberDom.parents('.member-manage__group').remove();
                                    let listMemberBannerDom = $('.project-item[data-project-id=' + response.project_id + ']').find('.pbanner__user-list:not(.pbanner__admin-list)');
                                    if (listMemberBannerDom.find('.button--show-member.button--show-member').length) {
                                        listMemberBannerDom.find('.pbanner__user').remove()
                                    }
                                } else {
                                    memberDom.remove();
                                    $('.project-member-inviting-' + signature).remove();
                                }
                            }
                        },
                        error: function (response) {
                            toastr.error("このアクションは実行できません！");
                        }
                    })
                }
            }
        })
    }

    function sendInviteEmailAgain(jwt_token, user_id) {
        $.ajax({
            type: 'PUT',
            url: '/top/invite_user?jwt=' + jwt_token,
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                const signature = jwt_token.split('.')[2];
                let memberDom = $('.project-member-inviting-' + signature);
                if (memberDom.length < 1) {
                    memberDom = $('.member-item[data-user^=' + user_id + ']');
                }
                const response_status_good = response.status === '200';
                const response_status_bad = response.status === '404';
                if (response_status_good) {
                    memberDom.after('<small class="text-danger">招待メールを送りました。</small> <br>');
                }
                if (response_status_bad) {
                    toastr.error("このアクションは実行できません！");
                    memberDom.after('<small class="text-danger">A Token is not out of date</small>');
                }
            },
            error: function (response) {
                toastr.error("このアクションは実行できません！");
            }
        })
    }

    function checkEmailExistInviteUser() {
        let email_user = $('#email-member-inviting').val();
        let product_id = $('#modal-member-manage').find('.member-manage__content').attr('data-product');
        if (!product_id){
            product_id = $('.modal-users-in-project').attr('data-product');
        }
        let email_dom = $('#email-member-inviting');
        if ($('#email-member-inviting').length < 1) {
            email_dom = $('input[name="member-email"]')
            email_user = $('input[name="member-email"]').val();
            product_id = $('input[name="member-product-id"]').val();
        }
        $.ajax({
            type: 'POST',
            url: '/top/check_email_exist_invite_user',
            data: {
                'email_user': email_user,
                'product_id': product_id,
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                let array_id = ['last-name-member-inviting', 'name-member-inviting', 'job-title-member-inviting', 'company-member-inviting'];
                let modal_invite = $('#modal-invite-member');
                for (i = 0; i < array_id.length; i++) {
                    modal_invite.find('#' + array_id[i]).val('');
                    modal_invite.find('#' + array_id[i]).attr('readonly', false);
                }
                $('.text-danger').remove();
                $('.error').removeClass('error');
                if (response.existed === true) {
                    if (response.accept_role === false) {
                        email_dom.addClass('error');
                        email_dom.next().remove();
                        email_dom.after('<small class="text-danger">' + 'User role in not accept' + '</small>');
                        $('.send-invite-email').addClass('button--disabled')
                    } else {
                        if (response.in_project === true) {
                            email_dom.addClass('error');
                            email_dom.next().remove();
                            email_dom.after('<small class="text-danger">' + 'このメールアドレスは、すでに本プロジェクトに参加しています' + '</small>');
                             $('.send-invite-email').addClass('button--disabled')
                        } else {
                            $('.send-invite-email').removeClass('button--disabled');
                            let first_name = response.first_name;
                            let last_name = response.last_name;
                            let position = response.position ? response.position : '';
                            let enterprise = response.enterprise ? response.enterprise : '';
                            let array_value = [last_name, first_name, position, enterprise];
                            for (i = 0; i < array_id.length; i++) {
                                modal_invite.find('#' + array_id[i]).val(array_value[i]);
                                modal_invite.find('#' + array_id[i]).attr('readonly', true);
                            }
                        }
                    }
                } else {
                    $('.send-invite-email').removeClass('button--disabled');
                }
            }
        });
    }

    function updateIPUser() {

        $('.member-ip__send').off('click').on('click', function () {
            $('.member-ip__new-btn').trigger('click');
            let pu_id = $(this).parents('.modal-content').attr('data-product-user');
            let list_ip = [];
            $('.member-ip__input').each(function () {
                let input_ip = $(this).find('input');
                if (input_ip.val()) {
                    list_ip.push(input_ip.val());
                }
            });

            $('.member-ip__new-ip').each(function () {
                let input_ip = $(this).find('input');
                if (input_ip.val()) {
                    list_ip.push(input_ip.val().trim());
                }
            });

            if (list_ip.length < 1 && $('.member-ip__item').length < 1) {
                $('.member-ip__new-ip').each(function () {
                    let input_ip = $(this).find('input');
                    input_ip.addClass('error-border')
                });
                $('.member-ip__new-ip input').keyup(function () {
                    let val = $(this).val().trim();
                    if (val != '') {
                        $(this).removeClass('error-border')
                    } else {
                        $(this).addClass('error-border')
                    }
                })

            } else {
                $.ajax({
                    type: "POST",
                    data: {
                        'pu_id': pu_id,
                        'list_ip': list_ip
                    },
                    url: "/top/update_ip_user",
                    success: function (data) {
                        // toastr.success('IPアドレスを設定しました');
                        $('.error-border').removeClass('error-border');
                        $('.member-ip__item').removeClass('new-ip');
                        let user_dom = $('.member-item[data-user^=' + data.user_id + ']');
                        user_dom.find('.detail-list-ip').html(data.str_list_ip);
                    }
                });
            }

        })
    }

    function changeViewOnlyStatus(target){
        let checked = target.checked;
        let parents = $(target).parents(".member-item");
        let user_id = parents.data("user");
        let product_id = parents.data("pf");
        $.ajax({
            type: "POST",
            data: {'product_id': product_id, 'user_id':user_id, 'checked': checked},
            url: "/top/update_view_only_product",
            success:function(){
                // toastr.success('閲覧権限を更新しました。');
            }
        });
    }

$(document).ready(function () {
    $(document).on('click', '.project-item__member', function (e) {
            e.preventDefault();
            let $project_item = $(this).parents('.project-item');
            let projectId = $project_item[0].dataset.projectId;
            $.ajax({
                type: "POST",
                data: {projectId: projectId},
                url: "/top/project_member_list",
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    let target = $('#project-member-setting');
                    target.empty();
                    target.html(response.html);
                    $('.member-email').keyup(function () {
                        $('.member-email').css('border', '1px solid #53565a')
                        $(this).next().remove();
                    });
                    $('.member-first-name').keyup(function () {
                        $('.member-first-name').css('border', '1px solid #53565a')
                        $(this).next().remove();
                    });
                    $('.member-last-name').keyup(function () {
                        $('.member-last-name').css('border', '1px solid #53565a')
                        $(this).next().remove();
                    });
                    $('.create-user-invite-js').on('click', function () {
                        const form = $('#invite_user_form');
                        const emailMissingOrNotValid = form[0][1].validity.valueMissing || !form[0][1].validity.valid;
                        const firstNameMissingOrNotValid = form[0][3].validity.valueMissing || !form[0][3].validity.valid;
                        const lastNameMissingOrNotValid = form[0][4].validity.valueMissing || !form[0][4].validity.valid;
                        if (emailMissingOrNotValid || firstNameMissingOrNotValid || lastNameMissingOrNotValid) {
                            if (emailMissingOrNotValid) {
                                $('.member-email').css('border', '1px solid red');
                                $('.member-email').next().remove();
                                $('.member-email').after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
                            }
                            if (firstNameMissingOrNotValid) {
                                $('.member-first-name').css('border', '1px solid red');
                                $('.member-first-name').next().remove();
                                $('.member-first-name').after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
                            }
                            if (lastNameMissingOrNotValid) {
                                $('.member-last-name').css('border', '1px solid red');
                                $('.member-last-name').next().remove();
                                $('.member-last-name').after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
                            }
                            return false;
                        } else {
                            form.submit();
                        }
                    });
                    $('a[name="display-area-create-user-invite"]').off().on('click', function () {
                        $('.area-show-user-invite').hide();
                        $('.area-create-user-invite').show();
                    });
                    $('a[name="display-area-show-user-invite"]').off().on('click', function () {
                        $('.area-show-user-invite').show();
                        $('.area-create-user-invite').hide();
                    });
                    target.modal();
                }
            })
        });
});
