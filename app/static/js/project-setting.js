// スクロール感知ヘッダーの実装
//
let lastScrollTop = 0;
const grobalHeader = document.getElementById("grobal-header");
const projectBanner = document.getElementById("project-banner");
const topAppBar = document.getElementById("top-app-bar");
// const bottomAppBar = document.getElementById("bottom-app-bar");
var $dropzoneUpladThumbnail;
var real_name_information = "";
var block_popup;
var crop_banner_popup;
var is_delete_file_banner = false;
var initialSetting = false;
var lastInterval = false;

var imageCropperBanner = {
  viewMode: 1,
  rotatable: false,
  aspectRatio: 20 / 4,
  minCropBoxWidth: 200,
  minCropBoxHeight: 200,
  minContainerHeight: 400,
};

//project-settingの実装
//
// チェックボックスの変更を監視し、対応するセクションを表示する
document.querySelectorAll('input[name="item"]').forEach((elem) => {
  elem.addEventListener("change", (event) => {
    // 全てのセクションを非表示にする
    document.querySelectorAll(".p-right-column > article").forEach((elem) => {
      elem.style.display = "none";
    });

    // 選択されたチェックボックスに対応するセクションを表示する
    const articleId = "article" + event.target.id.replace("item", "");
    document.getElementById(articleId).style.display = "block";
  });
});

const leftColumn = document.querySelector(".p-left-column");
const resizeHandle = document.querySelector(".resize-handle");
const rightColumn = document.querySelector(".p-right-column");
const navigateBefore = document.querySelector(".c-navigate-before");

// 初回ロード時のウィンドウ幅チェックと要素の表示切り替え
window.addEventListener("DOMContentLoaded", (event) => {
  if (window.innerWidth < 640) {
    rightColumn.style.display = "none";
    resizeHandle.style.display = "none";
    navigateBefore.style.display = "none";
    // bottomAppBar.style.display = "block";
  }
});

// name="item"要素がクリックされたときの表示切り替え
document.querySelectorAll('input[name="item"]').forEach((elem) => {
  elem.addEventListener("click", (event) => {
    if (window.innerWidth < 640) {
      leftColumn.style.display = "none";
      rightColumn.style.display = "block";
      navigateBefore.style.display = "flex";
      // bottomAppBar.style.display = "none";
    }
  });
});

// c-navigate-beforeがクリックされたときの表示切り替え
document.querySelectorAll(".c-navigate-before").forEach((elem) => {
  elem.addEventListener("click", (event) => {
    if (window.innerWidth < 640) {
      navigateBefore.style.display = "none";
      rightColumn.style.display = "none";
      // bottomAppBar.style.display = "block";
      leftColumn.style.display = "block";
    }
  });
});

// コードネーム
// ------------------------------------------------------------
import { codeNames } from "./codename-maker.js";
import { generateAlphanumeric } from "./codename-maker.js";

// フォームのinput要素を取得
// const input = document.querySelector('input[name="code_name"]');

// id="code_name" の input 要素を取得
const codeNameInput = document.getElementById("code_name");

// id="codeNameDisplay" の要素を取得
const codeNameDisplay = document.getElementById("code-name-display");

// 各カテゴリー名にクリックイベントハンドラを設定
$(".c-keyword").each((index, element) => {
  element.addEventListener("click", function () {
    // クリックされたカテゴリー名を取得
    const category = this.textContent;

    let word;
    if (category === "英数字") {
      // 英数字の場合は自動生成する
      word = generateAlphanumeric();
    } else {
      // それ以外の場合はリストからランダムに選択
      word =
        codeNames[category][
          Math.floor(Math.random() * codeNames[category].length)
        ];
    }

    // フォームに表示
    codeNameInput.value = word.toUpperCase();
    codeNameDisplay.textContent = word.toUpperCase();
    document.title = `Setting | ${this.value}`;
    validateInput();
  });
});

// code_name の値が変更されたときに codeNameDisplay を更新
codeNameInput.addEventListener("input", function () {
  codeNameDisplay.textContent = this.value;
  document.title = `Setting | ${this.value}`;
  validateInput();
});

//バナー（フォントとカラー）の設定
// ------------------------------------------------------------
//project-banner-tabのラジオボタンの変更を監視し、対応するセクションを表示する
document
  .querySelectorAll('input[name="project-banner-tab"]')
  .forEach((elem) => {
    elem.addEventListener("change", (event) => {
      // 全てのセクションを非表示にする
      document
        .querySelectorAll('[class^="c-tab1"], [class^="c-tab2"]')
        .forEach((elem) => {
          elem.style.display = "none";
        });

      // 選択されたラジオボタン（project-banner-tab）に対応するクラス（c-tab1,c-tab2）を表示する
      const selectedTab = "c-tab" + event.target.id.replace("tab", "");
      document.querySelectorAll(`.${selectedTab}`).forEach((elem) => {
        elem.style.display = "block";
      });
    });
  });

// 全体スケジュール
// ------------------------------------------------------------
// 全ての u-block-wrapper 要素を取得
const blockWrappers = document.querySelectorAll(".milestone-field");

// 各要素に対してイベントハンドラを設定
blockWrappers.forEach((wrapper) => {
  wrapper.addEventListener("mouseover", function () {
    // 次の要素（hover-menu）を取得
    const hoverMenu = this.querySelector(".hover-menu");

    // hover-menu の visibility を visible に設定
    if (hoverMenu && hoverMenu.classList.contains("hover-menu")) {
      hoverMenu.style.visibility = "visible";
    }
  });

  wrapper.addEventListener("mouseout", function () {
    // 次の要素（hover-menu）を取得
    const hoverMenu = this.querySelector(".hover-menu");

    // hover-menu の visibility を hidden に設定
    if (hoverMenu && hoverMenu.classList.contains("hover-menu")) {
      hoverMenu.style.visibility = "hidden";
    }
  });
});
// 概要
// ------------------------------------------------------------
// 文字数をカウントする
const overview = document.getElementById("project-overview");
const overviewCount = document.getElementById("overview-count");

overview.addEventListener("input", () => {
  const count = overview.value.length;
  overviewCount.textContent = count;
});

document
  .getElementById("dropzoneUpLoadThumbnail")
  .addEventListener("mouseover", function () {
    // 次の要素（hover-menu）を取得
    const imageElement = $(".image-banner");

    // hover-menu の visibility を visible に設定
    if (!imageElement.hasClass("hover")) {
      imageElement.addClass("hover");
    }
  });

document
  .getElementById("dropzoneUpLoadThumbnail")
  .addEventListener("mouseout", function () {
    // 次の要素（hover-menu）を取得
    const imageElement = $(".image-banner");

    // hover-menu の visibility を hidden に設定
    if (imageElement.hasClass("hover")) {
      imageElement.removeClass("hover");
    }
  });

$(document).ready(function () {
  const count = overview.value.length;
  overviewCount.textContent = count;
  validateInput();
  calculateProgressRate();
  bannerSetting();

  $("#product-name").on("input", function () {
    validateInput();
  });

  $("#checkpoints").on("input", function () {
    validateInput();
    calculateProgressRate();
    if (!validateMaxSceneInput()) {
      $(this).addClass("error");
      $(".error-input").css("visibility", "visible");
    } else {
      $(this).removeClass("error");
      $(".error-input").css("visibility", "hidden");
    }
  });

  block_popup = $("#modal-search-block-list").get(0);
  crop_banner_popup = $("#modal-crop-banner").get(0);
  let data_start_time = $("#daterangePicker").attr("data-start-time");
  let data_end_time = $("#daterangePicker").attr("data-end-time");
  var defaultDate = [new Date(), moment(new Date()).add(1, "months").toDate()];
  if (data_start_time && data_end_time) {
    defaultDate = [
      moment(data_start_time).toDate(),
      moment(data_end_time).toDate(),
    ];
  }
  flatpickr("#daterangePicker", {
    mode: "range",
    dateFormat: "y/n/j",
    defaultDate: defaultDate,
    showMonths: 1,
    onOpen: function (selectedDates, dateStr, instance) {
      $(instance.element).next(".c-icon-date-range").addClass("is-icon-active");
    },
    onClose: function (selectedDates, dateStr, instance) {
      $(instance.element)
        .next(".c-icon-date-range")
        .removeClass("is-icon-active");
    },
    onChange: function (selectedDates, dateStr, instance) {
      let startDate = selectedDates[0];
      let endDate = selectedDates[1];
      $(instance.element).attr(
        "data-start-time",
        moment(startDate).format("YYYY-MM-DD HH:mm:ss")
      );
      if (endDate)
        $(instance.element).attr(
        "data-end-time",
        moment(endDate).format("YYYY-MM-DD HH:mm:ss")
      );
      submitForm();
    },
    // minDate: "today",
    // maxDate: new Date().fp_incr(120),
  });
  submitForm();

  $(".c-icon-date-range").on("click", function () {
    $(this).prev("#daterangePicker").focus();
  });

  $(".milestone-field")
    .find(".datePicker")
    .each(function (index, element) {
      $(element).flatpickr({
        mode: "single",
        dateFormat: "y/n/j",
        defaultDate: moment($(element).attr("data-select-date")).toDate(),
        showMonths: 1,
        onOpen: function (selectedDates, dateStr, instance) {
          $(instance.element).next(".c-icon-event").addClass("is-icon-active");
        },
        onClose: function (selectedDates, dateStr, instance) {
          $(instance.element)
            .next(".c-icon-event")
            .removeClass("is-icon-active");
        },
        onChange: function (selectedDates, dateStr, instance) {
          let selectedDate = selectedDates[0];
          $(instance.element).attr(
            "data-select-date",
            moment(selectedDate).format("YYYY-MM-DD HH:mm:ss")
          );
          submitForm();
        },
      });
    });

  $(".c-icon-event").on("click", function () {
    $(this).prev(".datePicker").focus();
  });

  $("#add-milestone").on("click", function () {
    var defaultdate;
    var selectedDatesArray = $(".datePicker")
      .map(function () {
        return moment($(this).attr("data-select-date")).toDate();
      })
      .get();
    if (selectedDatesArray.length === 0) {
      defaultdate = new Date().fp_incr(14);
    } else {
      var highestDate = new Date(
        Math.max(
          ...selectedDatesArray.map(function (element) {
            return element.getTime();
          })
        )
      );
      let today = new Date();
      today.setHours(0);
      today.setMinutes(0);
      today.setSeconds(0);
      if (highestDate.getTime() < today.getTime()) {
        defaultdate = today.fp_incr(1);
      } else {
        defaultdate = highestDate.fp_incr(1);
      }
    }
    const htmlString = `
        <div class="u-block-wrapper milestone-field">
            <div class="u-row u-gap8 u-relative">
                 <input class="datePicker" type="text" name="milestone-date" data-select-date="${moment(
                   defaultdate
                 ).format("YYYY-MM-DD HH:mm:ss")}" style="width: 128px;">
                        <span class="material-symbols-rounded c-icon-event">
                                event
                        </span>
            </div>
            <input type="text" id="milestone-name" name="milestone-name" value="">
            <div class="hover-menu">
                 <span class="material-symbols-rounded delete-milestone" id="delete-milestone">
                                delete
                 </span>
            </div>
        </div>
        `;

    let htmlElement = $(htmlString);
    htmlElement.on("mouseover", function () {
      // 次の要素（hover-menu）を取得
      const hoverMenu = this.querySelector(".hover-menu");

      // hover-menu の visibility を visible に設定
      if (hoverMenu && hoverMenu.classList.contains("hover-menu")) {
        hoverMenu.style.visibility = "visible";
      }
    });

    htmlElement.on("mouseout", function () {
      const hoverMenu = this.querySelector(".hover-menu");

      if (hoverMenu && hoverMenu.classList.contains("hover-menu")) {
        hoverMenu.style.visibility = "hidden";
      }
    });

    htmlElement.find(".datePicker").flatpickr({
      mode: "single",
      dateFormat: "y/n/j",
      defaultDate: defaultdate,
      showMonths: 1,
      onOpen: function (selectedDates, dateStr, instance) {
        $(instance.element).next(".c-icon-event").addClass("is-icon-active");
      },
      onClose: function (selectedDates, dateStr, instance) {
        $(instance.element).next(".c-icon-event").removeClass("is-icon-active");
      },
      onChange: function (selectedDates, dateStr, instance) {
        let selectedDate = selectedDates[0];
        $(instance.element).attr(
          "data-select-date",
          moment(selectedDate).format("YYYY-MM-DD HH:mm:ss")
        );
        submitForm();
      },
    });

    htmlElement.find(".c-icon-event").on("click", function () {
      $(this).prev(".datePicker").focus();
    });

    htmlElement.find(".delete-milestone").on("click", function () {
      $(this).parent().parent().remove();
    });
    $(this).prev().append(htmlElement);
    submitForm();
  });

  $(".delete-milestone").on("click", function () {
    $(this).parent().parent().remove();
    submitForm();
  });

  // $(document).on('hidden.bs.modal', '#modal-search-block-list, #modalCropBanner', function () {
  //   $('body').addClass('modal-open');
  //   $('.block-list__results .block-list__results-search').empty();
  //   $('#id_block-list__search').val('')
  // });

  $(document).on("click", "#add-block", function (e) {
    e.preventDefault();
    e.stopPropagation();
    block_popup.showModal();
  });

  $(document).on("click", ".smodal-close", function (e) {
    block_popup.close();
    $(".block-list__results .block-list__results-search").empty();
  });

  block_popup.addEventListener("close", function () {
    $(".block-list__results .block-list__results-search").empty();
    $("#id_block-list__search").val("");
  });

  $(document).click(function (event) {
    if ($(event.target).is(block_popup) && $(block_popup).prop("open")) {
      block_popup.close();
    }
  });

  $("#notice-confirm-button").on("click", submitForm);

  $("a.link-nav-item").on("click", function () {
    let code_name_value = $("#code_name").val();
    let product_name_value = $("#product-name").val();
    if (code_name_value && product_name_value && validateMaxSceneInput()) {
      if (is_create_new) {
        var dialog = $("#creation-notice").get(0);
        dialog.showModal();
      } else {
        submitForm();
      }
    }
  });

  searchListArtistBlock();
  dragDropThumbnailSettingProject();
});

//use this function to get current setting
function getInitializeSetting(){
  let code_name = $("#code_name").val();
  let product_name = $("#product-name").val();
  let banner_font_id = $("[name='code-font']:checked").attr("data-id");
  let banner_color_id = $("[name='banner-color']:checked").attr("data-id");
  let dateRangePicker = $("#daterangePicker");
  let startDate = dateRangePicker.attr("data-start-time");
  let endDate = dateRangePicker.attr("data-end-time");
  let milestoneObj = [];
  let milestonesField = $(".milestone-field");
  milestonesField.each(function (index, element) {
    let milestoneTime = $(element).find(".datePicker").attr("data-select-date");
    let milestoneName = $(element).find("#milestone-name").val();
    if (milestoneTime) {
      milestoneObj.push({
        milestoneTime: milestoneTime,
        milestoneName: milestoneName || "",
      });
    }
  });
  let description = $("#project-overview").val();
  let maxScene = $("#checkpoints").val();
  let clientName = $("#end-client").val();
  let arcHost = $("#acr_host").val();
  let arcAccessKey = $("#acr_access_key").val();
  let arcAccessSecret = $("#acr_access_secret").val();
  let x = $("#id_x").val();
  let y = $("#id_y").val();
  let width = $("#id_width").val();
  let height = $("#id_height").val();

  return {
    code_name,
    product_name,
    banner_font_id,
    banner_color_id,
    startDate,
    endDate,
    milestoneObj,
    description,
    maxScene,
    clientName,
    arcHost,
    arcAccessKey,
    arcAccessSecret,
    x,
    y,
    width,
    height
  }
}

function submitForm() {
  if (!validateMilestonesInput()) {
    return;
  }
  let code_name = $("#code_name").val();
  let product_name = $("#product-name").val();
  let banner_font_id = $("[name='code-font']:checked").attr("data-id");
  let banner_color_id = $("[name='banner-color']:checked").attr("data-id");
  let dateRangePicker = $("#daterangePicker");
  let startDate = dateRangePicker.attr("data-start-time") ? dateRangePicker.attr("data-start-time")  : moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
  let endDate = dateRangePicker.attr("data-end-time") ? dateRangePicker.attr("data-end-time") : moment(new Date()).add(1, "months").format('YYYY-MM-DD HH:mm:ss'); 
  let milestoneObj = [];
  let milestonesField = $(".milestone-field");
  milestonesField.each(function (index, element) {
    let milestoneTime = $(element).find(".datePicker").attr("data-select-date");
    let milestoneName = $(element).find("#milestone-name").val();
    if (milestoneTime) {
      milestoneObj.push({
        milestoneTime: milestoneTime,
        milestoneName: milestoneName || "",
      });
    }
  });
  let description = $("#project-overview").val();
  let maxScene = $("#checkpoints").val();
  let clientName = $("#end-client").val();
  let arcHost = $("#acr_host").val();
  let arcAccessKey = $("#acr_access_key").val();
  let arcAccessSecret = $("#acr_access_secret").val();
  let x = $("#id_x").val();
  let y = $("#id_y").val();
  let width = $("#id_width").val();
  let height = $("#id_height").val();

  let formData = new FormData();
  formData.append("code_name", code_name);
  formData.append("name", product_name);
  if (banner_font_id) {
    formData.append("banner_font_id", parseInt(banner_font_id));
  }
  if (banner_color_id) {
    formData.append("banner_color_id", parseInt(banner_color_id));
  }

  if (startDate) {
    formData.append("start_date", startDate);
  }
  if (endDate) {
    formData.append("end_date", endDate);
  }
  formData.append("description", description);
  formData.append("max_scene", parseInt(maxScene));
  formData.append("client_name", clientName);
  formData.append("acr_host", arcHost);
  formData.append("acr_access_key", arcAccessKey);
  formData.append("acr_access_secret", arcAccessSecret);
  formData.append("list_artist_block", getListIdArtistBlocked());
  formData.append("milestones", JSON.stringify(milestoneObj));
  if ($dropzoneUpladThumbnail && $dropzoneUpladThumbnail.files.length > 0) {
    formData.append("id_image", $dropzoneUpladThumbnail.files[0]);
    formData.append("id_image_name", $dropzoneUpladThumbnail.files[0].name);
  }
  formData.append("x", x);
  formData.append("y", y);
  formData.append("width", width);
  formData.append("height", height);
  formData.append("is_delete_file_banner", is_delete_file_banner);

  $.ajax({
    type: "POST",
    url: window.location.href,
    contentType: false,
    processData: false,
    cache: false,
    data: formData,
    beforeSend: function (xhr, settings) {
      xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
    },
    success: function (response) {
      // const origin_url = window.location.origin;
      // const path_product_detail = "/top/project/";
      // window.location.href =
      //   origin_url + path_product_detail + response.product_id;
    },
    error: function () {
      toastr.error("何かエラーを発生しました。");
    },
  });
}

window.addEventListener('load', function() {
  lastInterval = setInterval(() => {
    if (!initialSetting){
      initialSetting = getInitializeSetting();
    }else{
      //compare current setting and initialsetting to check if it changed or not
      if (JSON.stringify(initialSetting) === JSON.stringify(getInitializeSetting()) ){
        //no change
      }else{
        initialSetting = getInitializeSetting();
        //changed
        //trigger save form
        // submitForm();
      }
    }
  }, 500)
})

const inputs = ['acr_host', 'acr_access_key', 'acr_access_secret', 'end-client', 'project-overview'];

// Function to add event listeners to all inputs
inputs.forEach(inputId => {
  document.getElementById(inputId).addEventListener('input', function(event) {
    submitForm(); // Call submitForm when any input changes
  });
});

function searchListArtistBlock() {
  $(document).on("keypress", function (e) {
    if (e.which == 13 && $("#id_block-list__search").is(":focus")) {
      e.preventDefault();
      let projectId = $("main").attr("data-product-id");
      let listArtist = getListIdArtistBlocked();
      let keyWord = $("#id_block-list__search").val();
      $.ajax({
        type: "GET",
        datatype: "json",
        url: "/top/search_artist_to_add_block_list",
        data: {
          project_id: projectId,
          list_artist: listArtist,
          key_word: keyWord,
        },
        success: function (data) {
          $(".block-list__results .block-list__results-search").empty();
          $(".block-list__results .block-list__results-search").append(
            data.html
          );
        },
      });
    }
  });

  $(document).on("click", ".add-artist-into-block-list", function () {
    let artist_id = $(this)
      .parents(".block-list__results-search-detail")
      .attr("data-artist");
    let projectId = $("main#project-setting").attr("data-product-id");
    if (projectId && artist_id && !$(this).hasClass("artist-selected")) {
      $(this).addClass("artist-selected disabled");
      $(this).val("追加しました");

      $.ajax({
        type: "POST",
        datatype: "json",
        url: "/top/add_block_artist_into_project_setting",
        data: {
          project_id: projectId,
          artist: artist_id,
        },
        beforeSend: function (xhr, settings) {
          xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
        },
        success: function (data) {
          $(".project-setting__block-list-artist").append(data.html);
        },
        error: function (data) {
          $(this).removeClass("artist-selected");
        },
      });
    }
  });

  $(document).on(
    "mouseenter mouseleave",
    ".block-artist-selected",
    function (e) {
      if (e.type === "mouseenter") {
        $(this).find(".hover-menu").css("visibility", "visible");
      } else {
        $(this).find(".hover-menu").css("visibility", "hidden");
      }
    }
  );

  $(document).on("click", ".delete-block-artist", function (e) {
    $(this).closest(".block-artist-selected").remove();
  });
}

function getListIdArtistBlocked() {
  let listArtistId = [];
  if ($(".block-artist-selected").length) {
    $(".block-artist-selected").each(function () {
      listArtistId.push($(this).attr("data-artist"));
    });
  }
  return listArtistId.join(",");
}

function dragDropThumbnailSettingProject() {
  let thisModal = $(".project__upload_banner");
  var previewNode = thisModal.find(".mcomment-attached .account__file");
  var previewTemplate = previewNode.parent().html();
  previewNode.parent().empty();
  Dropzone.autoDiscover = false;

  $dropzoneUpladThumbnail = new Dropzone("#dropzoneUpLoadThumbnail", {
    maxFilesize: 4500,
    timeout: 900000,
    autoDiscover: false,
    acceptedFiles: ".png, jpg, .svg, .jpeg, .PNG, .JPG, .SVG, .JPEG",
    previewsContainer: ".mcomment-attached",
    previewTemplate: previewTemplate,
    url: "/",
    autoProcessQueue: false,
    autoQueue: false,
    clickable: "#dropzoneUpLoadThumbnail",
    maxFiles: 1,
    // dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
  });

  $dropzoneUpladThumbnail.on("maxfilesexceeded", function (file) {
    $dropzoneUpladThumbnail.removeAllFiles();
    $dropzoneUpladThumbnail.addFile(file);
  });

  $dropzoneUpladThumbnail.on("removedfile", function (file) {
    real_name_information = "";
    // thisModal.find('.btn-upload-file-messenger-owner').addClass('disable');
  });

  $dropzoneUpladThumbnail.on("addedfile", function (file, e) {
    if ($dropzoneUpladThumbnail.files.length > 1) {
      $dropzoneUpladThumbnail.removeAllFiles(true);
      $dropzoneUpladThumbnail.addFile(file);
    }
    if (
      $dropzoneUpladThumbnail.files &&
      $dropzoneUpladThumbnail.files[0] &&
      $dropzoneUpladThumbnail.files[0].name.match(
        /\.(png|PNG|jpg|JPG|jpeg|JPEG|svg|SVG)$/
      )
    ) {
      let file_dom = $(file.previewElement);
      real_name_information = file.name;
      let file_preview = thisModal
        .find(".mcomment-attached")
        .find(".account__file");
      for (let i = 0; i < file_preview.length; i++) {
        $(file_preview[i]).append(
          '<a><div class="account__file-name" style="margin: 0">' +
            real_name_information +
            '</div></a><div class="icon--close"><span class="material-symbols-rounded">close</span></div>'
        );
        $(file_preview[i])
          .find(".icon--close")
          .on("click", function () {
            $dropzoneUpladThumbnail.removeFile(file);
            $(".image-banner").attr("src", "");
            file_dom.remove();
          });
      }
      if (thisModal.find(".account__file").length) {
        thisModal.find(".account__file.uploaded-file").hide();
      }

      readDataUploadImage(file, imageCropperBanner);
    } else if (!$dropzoneUpladThumbnail.files) {
      return false;
    } else {
      alert("PNG, JPG, SVG, JPEGのみアップロードできます。");
      $dropzoneUpladThumbnail.removeAllFiles(true);
    }
  });

  thisModal
    .find(".account__file.uploaded-file .icon--close")
    .on("click", function () {
      thisModal.find(".account__file.uploaded-file").hide();
      is_delete_file_banner = true;
      $(".image-banner").attr("src", "");
    });
}

function readDataUploadImage(file, crop) {
  let $image = $("#image-crop");
  let cropBoxData, canvasData;
  let fileReader = new FileReader();

  crop_banner_popup.addEventListener("close", function () {
    cropBoxData = $image.cropper("getCropBoxData");
    canvasData = $image.cropper("getCanvasData");
    $image.cropper("destroy");
  });

  $("#modal-crop-banner .close")
    .off("click")
    .on("click", function () {
      $dropzoneUpladThumbnail.removeAllFiles();
      $(".image-banner").attr("src", "");
      crop_banner_popup.close();
      setTimeout(() => {
        $(".cropper-container").remove();
      }, 300);
    });

  fileReader.onloadend = function (e) {
    $image.attr("src", e.target.result);
    $image.cropper({
      viewMode: crop.viewMode,
      rotatable: crop.rotatable,
      aspectRatio: crop.aspectRatio,
      minCropBoxWidth: crop.minCropBoxWidth,
      minCropBoxHeight: crop.minCropBoxHeight,
      minContainerHeight: crop.minContainerHeight,
      ready: function () {
        $image.cropper("setCanvasData", canvasData);
        $image.cropper("setCropBoxData", cropBoxData);
      },
    });
    crop_banner_popup.showModal();
  };

  $(".js-crop-and-upload-project")
    .unbind()
    .bind("click", function () {
      var cropData = $image.cropper("getData");
      var uploadBanner = function () {
        var croppedImageData = $image
          .cropper("getCroppedCanvas")
          .toDataURL("image/jpeg");
        $(".image-banner").attr("src", croppedImageData);
        $("#id_x").val(cropData["x"]);
        $("#id_y").val(cropData["y"]);
        $("#id_height").val(cropData["height"]);
        $("#id_width").val(cropData["width"]);
        crop_banner_popup.close();
      };
      uploadBanner();
      submitForm();
      setTimeout(() => {
        $(".cropper-container").remove();
      }, 300);
    });

  fileReader.readAsDataURL(file);

  // Enable zoom in button
  $(".js-zoom-in").click(function () {
    $image.cropper("zoom", 0.1);
  });

  // Enable zoom out button
  $(".js-zoom-out").click(function () {
    $image.cropper("zoom", -0.1);
  });
}

function validateInput() {
  let code_name_value = $("#code_name").val();
  let product_name_value = $("#product-name").val();
  let input_scene_value = $("#checkpoints").val();
  $("a.link-nav-item").addClass("tabBottom");
  if (!code_name_value || !product_name_value || !validateMaxSceneInput()) {
    $("a.link-nav-item .nav-bar-item")
      .addClass("disabled")
      .on("click", function (event) {
        event.preventDefault();
      });
  } else {
    $("a.link-nav-item .nav-bar-item")
      .removeClass("disabled")
      .off("click");
  }
}

function validateMaxSceneInput() {
  let input_scene_element = $("#checkpoints");
  let minScene = Number(input_scene_element.attr("min"));
  let maxScene = Number(input_scene_element.attr("max"));
  return (
    input_scene_element.val() >= minScene &&
    input_scene_element.val() <= maxScene
  );
}

function calculateProgressRate() {
  let maxSceneValue = parseInt($("#checkpoints").val());

  const completionRateElement = document.getElementById("completion-rate");
  const completionRateParent = completionRateElement.parentElement;
  if (!maxSceneValue) {
    completionRateParent.style.display = "none";
    document.getElementById("completion-rate-meter").style.display = "none";
    return;
  }

  // 入力された値を取得し、100で割る
  const rate = 100 / maxSceneValue;
  // 小数点第一位までの値を表示
  const roundedRate = rate.toFixed(1);
  // 結果がInfinityの場合、メーターを非表示に
  if (roundedRate === "Infinity") {
    completionRateParent.style.display = "none";
    document.getElementById("completion-rate-meter").style.display = "none";
  } else {
    // 結果をid="completion-rate"の要素に表示
    completionRateParent.style.display = "";
    completionRateElement.textContent = roundedRate;
    document.getElementById("completion-rate-meter").style.display = "block";
    document.getElementById("completion-rate-meter").value = roundedRate;

    // infographicsの追加
    const infographicCheckpoints = document.getElementById(
      "infographic-checkpoints"
    );
    infographicCheckpoints.innerHTML = ""; // 既存の内容をクリア

    const maxCheckpoints = Math.min(maxSceneValue, 256); // 最大200まで

    for (let i = 0; i < maxCheckpoints; i++) {
      const span = document.createElement("span");
      span.className = "material-symbols-rounded u-text-border";
      span.textContent = "check_circle";
      if (i === 0) {
        span.className = "material-symbols-rounded u-text-blue"; // 最初の要素を青に
      }
      infographicCheckpoints.appendChild(span);
    }

    if (maxSceneValue > 256) {
      const ellipsis = document.createElement("span");
      ellipsis.className = "u-text-light-gray";
      ellipsis.textContent = "…";
      infographicCheckpoints.appendChild(ellipsis);
    }
  }
}

function validateMilestonesInput() {
  let milestonesField = $(".milestone-field");
  milestonesField.each(function (index, element) {
    let milestoneTime = $(element).find(".datePicker").attr("data-select-date");
    let milestoneName = $(element).find("#milestone-name").val();
    if (!milestoneTime && milestoneName) {
      toastr.error("aaaaa", "bbbb");
      return false;
    }
  });
  return true;
}

function bannerSetting() {
  // コードフォントのradio 要素を取得
  const codeFontRadioButtons = document.querySelectorAll(
    'input[name="code-font"]'
  );

  // 各要素に対してイベントハンドラを設定
  codeFontRadioButtons.forEach((radioButton) => {
    radioButton.addEventListener("change", function () {
      // code-name-display 要素を取得
      const displayName = document.getElementById("code-name-display");
      submitForm();
      // 既存の code-font* クラスを削除
      displayName.className = displayName.className.replace(/code-font\d/g, "");

      // 選択した radio button の id に基づくクラスを追加
      if (this.checked) {
        displayName.classList.add(this.id);
      }
    });
  });

  // banner-color の全ての radio 要素を取得
  const bannerColorRadioButtons = document.querySelectorAll(
    'input[name="banner-color"]'
  );

  // 各要素に対してイベントハンドラを設定
  bannerColorRadioButtons.forEach((radioButton) => {
    radioButton.addEventListener("change", function () {
      // project-banner 要素を取得
      const projectBanner = document.getElementById("project-banner");

      // 既存の banner-color* クラスを削除
      projectBanner.className = projectBanner.className.replace(
        /banner-color\d+/g,
        ""
      );
      submitForm();
      // 選択した radio button の id に基づくクラスを追加
      if (this.checked) {
        projectBanner.classList.add(this.id);
      }
    });
  });

  if ($('input[name="code-font"]:checked').length === 0) {
    let firstFontElement = $('input[name="code-font"]').first();
    firstFontElement.prop("checked", true);
    $("#project-banner").addClass(firstFontElement.attr("id"));
  }

  if ($('input[name="banner-color"]:checked').length === 0) {
    let firstColorElement = $('input[name="banner-color"]').first();
    firstColorElement.prop("checked", true);
    $("#project-banner").addClass(firstColorElement.attr("id"));
  }
}
