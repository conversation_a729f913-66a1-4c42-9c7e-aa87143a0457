//Array of images which you want to show: Use path you want.
let images = ['img1', 'img2', 'img3', 'img4', 'img5', 'img6', 'img7'];
randomImageBackground(images);
function validateConfirmPassWord() {
  newPass = $('input[name="new_password1"]').val().trim();
  passConfirm = $('input[name="new_password2"]').val().trim();
  let checkNewPass = newPassFnc(newPass, $('input[name="new_password1"]'));
  let checkConfirmNewPass = confirmPassFnc(
    passConfirm,
    newPass,
    $('input[name="new_password2"]')
  );

  button_disable_toggle = checkNewPass && checkConfirmNewPass;
  $('.btn-confirm-password').toggleClass(
    'disabled',
    !button_disable_toggle
  );
}

$(document).ready(function () {
  if (window.location.pathname.includes('/invite/password/change')) {
    $('.auth__form-title').text(gettext('Set password confirm title'));
  } else {
    $('.auth__form-note').css('display', 'none');
  }

  if (window.screen.availWidth < 600) {
    let ratio = window.screen.availWidth / 500;
    $('.auth__main').css('transform', 'scale(' + ratio + ')');
  }
  validateConfirmPassWord();

  $(document).on('input', 'input[name="new_password1"]', () => {
    $('.errorlist').remove();
    validateConfirmPassWord();
  });

  $(document).on('input', 'input[name="new_password2"]', () => {
    $('.errorlist').remove();
    validateConfirmPassWord();
  });
});
