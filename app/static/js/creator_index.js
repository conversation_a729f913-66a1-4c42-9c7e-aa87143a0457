let add_count = 0;
updateSVG(0);

$(document).ready(function () {
    setTimeout(() => {
        removeLoadAnimation()
    }, 1500)
  //load list creators
    window.onpageshow = function(event) {
        if (event.persisted) {
            window.location.reload()
        }
    };
    $.ajax({
        method: 'GET',
        url: '/gallery/get_creator_lists',
        before: function () {
            ajax_calling = true;
            $('.container.gallery-container').append(`<div class="load-more-loading load-list"></div>`)
        },
        success: function (data) {
            if (data.html) {
                $('main .container.gallery-container').append(data.html);
                total_page = data.total_page;
                list_id = data.list_creator_list;
                initAction();
                initListCreator();
                setTimeout(function () {
                    $(window).scroll(function () {
                        loaMoreListCreator();
                    });
                }, 1000)
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', 'テーマ一覧')
        },
        complete: function () {
            ajax_calling = false;
        }
    });

    playNewWorks();
    CRUDNewWork();
    dragableNewWorkThemes();
    showAlbumPreview();
    hoverPlayVideo();
    redirectProfile()
})

let current_page = 0;
let ajax_calling = false;
let total_page = 1;
let list_id = [];

function loaMoreListCreator() {
    console.log($(window).scrollTop() > $(document).height() - $(window).height() - 700);
    if ($(window).scrollTop() > $(document).height() - $(window).height() - 700) {
        if (current_page < total_page && !ajax_calling) {
            current_page++;
            ajax_calling = true;
            let list_creator_id = list_id.slice(current_page*4, current_page*4 +4);
            let current_page_str = current_page.toString();
            $.ajax({
                type: "GET",
                data: {
                    'page': current_page,
                    'list_creator_id': list_creator_id
                },
                url: "/gallery/load_more_creator_lists",
                beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    $('.container').append(`<div class="load-more-loading" data-load='${current_page_str}'></div>`)
                },
                success: function (data) {
                    $('main .container .gallery-list').append(data.html);
                    if (data.is_curator && current_page === total_page) {
                        $('main .container .gallery-list').append($(`  <div class="gallery-new">
                                                          <a class="gallery-new__btn" data-toggle="modal" data-target="#composer-new-modal">
                                                          <span class="srm-icon-wrap">
                                                          <div class="srm-icon srm-add"></div>
                                                          </span>
                                                          </a>
                                                          <div class="gallery-new__desc">新しいテーマを追加</div>
                                                          </div>`))
                    }
                    initListCreator();
                },
                error: function () {
                    current_page--;
                },
                complete: function () {
                    ajax_calling = false;
                    $('.load-more-loading[data-load=' + current_page_str + ']').remove();
                }
            });
        }
    }
}


function update_creator_in_list() {
    $('.list-composer__list.is_curator').sortable({
        helper: 'clone',
        items: '.user-item.can-sort',
        forcePlaceholderSize: true,
        tolerance: 'pointer',
        cursor: 'move',
        start: function (event, ui) {

        },
        change: function (event, ui) {

        },
        update: function (event, ui) {
            let arr_order = [];
            let list_id = $(this).parents('.list-composer').attr('data-list-id');

            $(this).find('.user-item').each(function (index, item) {
                arr_order.push(item.getAttribute('data-creator-id'));
            });

            $.ajax({
                type: "POST",
                url: "/accounts/creators/update_creator_index",
                data: {
                    'list_id': list_id,
                    'arr_order': arr_order
                },
                success: function (data) {
                    // toastr.success('並び替えました')
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            })
        }
    });

    $('.list-composer__list.is_curator').sortable('disable');
    let t;
    $(document).on('mousedown touchstart', '.list-composer__list .user-item', function (event) {
        var self = this;
        clearTimeout(t);
        if ($(self).hasClass('dragging')) {
            return;
        }
        t = setTimeout(function (e) {
            event.preventDefault();
            $('.list-composer__list.is_curator').sortable('enable');
            $(self).addClass('dragging');
            console.log('start drag');
            $(self).trigger(event)
        }, 300);
    });

    $('.user-item').on("mouseup touchend", function () {
        clearTimeout(t);
        $('.list-composer__list.is_curator').sortable('disable');
        $('.dragging').removeClass('dragging')
    });
}

function initListCreator() {
  $('.gallery-list.is_curator').sortable({
    start: function (event, ui) {

    },
    change: function (event, ui) {
    },
    update: function (event, ui) {
      let arr_order = [];

      $(this).find('.list-composer').each(function (index, item) {
            arr_order.push(item.getAttribute('data-list-id'));
      });

      // Merges both arrays and gets unique items
      arr_order = arrayUnique(arr_order.map(Number).concat(list_id));

      $.ajax({
        type: "POST",
        url: "/accounts/creators/update_list_creator_index",
        data: {
          'arr_order': arr_order
        },
        success: function (data) {
          // toastr.success('並び替えました')
        },
        error: function (data) {
          toastr.error('エラーが発生しました');
        }
      })
    }
  });

  $('.gallery-list.is_curator').sortable('disable');

  let t;
    $(document).on('mousedown touchstart', '.list-composer', function (event) {
        var self = this;
        clearTimeout(t);
        if ($(self).hasClass('dragging')) {
            return;
        }
        t = setTimeout(function (e) {
            event.preventDefault();
            $('.gallery-list.is_curator').sortable('enable');
            $(self).addClass('dragging');
            console.log('start drag');
            $(self).trigger(event)
        }, 300);
    });

    $(document).on("mouseup touchend", function () {
        clearTimeout(t);
        $('.gallery-list.is_curator').sortable('disable');
        $('.dragging').removeClass('dragging')
    });

  update_creator_in_list();
}


function initAction() {
    $('.gallery-list').on('click', '.list-composer__add-btn', function () {
        let add_btn = $(this);
        let list_composer = add_btn.parent().parent();
        let list_id = add_btn.parents('.list-composer').attr('data-list-id');

        let $search_form = $('.composer-modal__form');
        $.ajax({
            url: '/accounts/find_creators',
            data: {'list_id': list_id},
            success: function (data) {
                $('.composer-modal__list').html('');
                $search_form.find('.composer-modal__not-found').removeClass('active');
                $search_form.find('.composer-modal__existed').removeClass('active');
                if (data.status === 'ok') {
                    $('.composer-modal__list').append(data.creators_html);
                } else if (data.status === 'existed') {
                    $search_form.find('.composer-modal__existed').addClass('active');
                } else {
                    $search_form.find('.composer-modal__not-found').addClass('active');
                }
            },
        });


        $search_form.find('.composer-modal__input').on('keyup', function () {
            let name = $(this).val();
            if (name !== '') {
                name = name.toLowerCase();
                $('.composer-modal__list').find('.user-item').addClass('hide');
                $('.composer-modal__list').find('.user-item').removeClass('search-found');
                $('.composer-modal__list').find('.user-item[data-name*="' + name + '"]').removeClass('hide');
                $('.composer-modal__list').find('.user-item[data-name*="' + name + '"]').addClass('search-found');
                if (!$('.search-found').length) {
                    $search_form.find('.composer-modal__not-found').addClass('active');
                } else {
                    $search_form.find('.composer-modal__not-found').removeClass('active');
                }
            } else {
                $('.composer-modal__list').find('.user-item').removeClass('hide');
                $('.composer-modal__list').find('.user-item').removeClass('search-found');
                $search_form.find('.composer-modal__not-found').removeClass('active');
            }
        });

        $('.composer-modal__form').off().on('click', '.user-item', function () {

            $(this).toggleClass('selected');

            if ($(this).hasClass('selected')) {
                add_count = add_count + 1;
            } else {
                add_count = add_count - 1;
            }

            if (add_count > 0) {
                $search_form.parents('.composer-modal').find('.btn-add-composer').removeClass('disabled');
            } else {
                $search_form.parents('.composer-modal').find('.btn-add-composer').addClass('disabled');
            }
        })

        $search_form.find('.btn-add-composer').off().on('click', function (e) {
            e.preventDefault();
            let $newItems = $('.user-item.selected', $search_form);
            let list_id = list_composer.parents(".list-composer").data("list-id");
            let list_composer_id = [];
            for (let i = 0; i < $newItems.length; i++) {
                let id = $($newItems[i]).data("id");
                list_composer_id.push(id);
            }
            $.ajax({
                method: 'POST',
                url: '/accounts/creators/add_composer_to_list',
                data: {'list_composer_id': list_composer_id, 'list_id': list_id},
                success: function (data) {
                    for (let i = 0; i < $newItems.length; i++) {
                        let url_creator = $($newItems[i]).find('.user-item__name').attr('href');
                        let user_avatar = $($newItems[i]).find('.user-item__avatar');
                        let wrapper = document.createElement('a');
                        $(wrapper).attr('href', url_creator);
                        $(wrapper).attr('target', '_blank');
                        user_avatar.remove();
                        user_avatar.appendTo($(wrapper));
                        $(wrapper).prependTo($($newItems[i]));
                        $($newItems[i]).attr('data-creator_list-creator', data.creator_list_creators[i]);
                    }
                    list_composer.find('.list-composer__add').after($newItems);
                    update_creator_in_list();
                    // toastr.success('テーマに追加しました。');
                },
                error: function () {
                    toastr.error('エラーが発生しました');
                }
            })
            $('#composer-modal').modal('hide');
        });
    });

    $('.gallery-list').on('click', '.user-item__delete', function (e) {
        e.preventDefault();
        let creator = $(this).parents(".user-item");
        let list_id = $(creator).parents(".list-composer").data("list-id");
        let creator_id = $(creator).data("creator-id");
        if (!creator_id) {
            creator_id = $(creator).data("id");
        }
        let item_delete = this;
        $.ajax({
            method: 'POST',
            url: 'creators/delete_creator_from_list',
            data: {'list_id': list_id, 'creator_id': creator_id},
            success: function (data) {
                if (data.status == "success") {
                    $(item_delete).closest('.user-item').remove();
                    // toastr.success('テーマから消しました。')
                } else {
                    toastr.error('エラーが発生しました')
                }
            },
            error: function () {
                toastr.error('エラーが発生しました')
            }
        })
    });

    $('.gallery-list.is_curator').on('click', '.delete-creator-list', function () {
        let list_id = $(this).parents('.list-composer').attr('data-list-id');
        if (list_id) {
            bootbox.confirm({
                title: "テーマを削除",
                message: "本当に削除したいでしょうか?",
                buttons: {
                    confirm: {
                        label: 'はい',
                        className: 'btn--tertiary btn-primary'
                    },
                    cancel: {
                        label: 'いいえ',
                        className: 'btn--primary btn-light'
                    }
                },
                callback: function (result) {
                    if (result) {
                        $.ajax({
                            type: "POST",
                            url: "/accounts/creators/delete_list_creator/",
                            data: {
                                'list_id': list_id
                            },
                            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                $('.list-composer[data-list-id=' + response.list_id + ']').remove();
                                // toastr.success('テーマが削除しました。', 'テーマ削除');
                            },
                            error: function () {
                                toastr.error('エラーが発生しました', 'テーマ削除');
                            }
                        })
                    }
                }
            });
        }
    });

    $('.gallery-list.is_curator').on('click', '.edit-creator-list', function () {
        let list_id = $(this).parents('.list-composer').attr('data-list-id');
        let title_content = $(this).parents('.list-composer').find('.list-composer__title').text();
        let description_content = $(this).parents('.list-composer').find('.list-composer__sub-title').text();
        if (list_id) {
            $('#composer-update-modal').off().on('shown.bs.modal', function () {
                $('#composer-update-name').val(title_content);
                $('#composer-update-desc').val(description_content);
                $('#composer-update-modal').find('#composer-update-name').on('keyup', function () {
                    var name = $(this).val().trim();
                    var desc = $('#composer-update-desc').val().trim();

                    if (desc.length > 0 && name.length > 0) {
                        $('#composer-update-modal').find('.btn-new-composer').removeClass('disabled');
                    } else {
                        $('#composer-update-modal').find('.btn-new-composer').addClass('disabled');
                    }
                });

                $('#composer-update-modal').find('#composer-update-desc').on('keyup', function () {
                    var desc = $(this).val().trim();
                    var name = $('#composer-update-name').val().trim();

                    if (desc.length > 0 && name.length > 0) {
                        $('#composer-update-modal').find('.btn-new-composer').removeClass('disabled');
                    } else {
                        $('#composer-update-modal').find('.btn-new-composer').addClass('disabled');
                    }
                });

                $('#composer-update-modal').find('.btn-new-composer').off('click').on('click', function (e) {
                    e.preventDefault();
                    var name = $('#composer-update-name').val().trim();
                    var desc = $('#composer-update-desc').val().trim();
                    $.ajax({
                        method: 'POST',
                        url: 'accounts/creators/update_list_creators',
                        data: {"list_id": list_id, "name": name, "desc": desc},
                        success: function (data) {
                            // toastr.success('テーマが更新しました。');
                            $('#composer-update-modal').modal('hide');
                            $('.list-composer[data-list-id=' + data.list_id.toString() + ']').find('.list-composer__title').html(data.new_title);
                            $('.list-composer[data-list-id=' + data.list_id.toString() + ']').find('.list-composer__sub-title').html(data.new_description)
                        },
                        error: function () {
                            $('#composer-update-modal').find('.btn-new-composer').addClass('disabled');
                            $('#composer-update-modal').modal('hide');
                            toastr.error('エラーが発生しました')
                        }
                    })
                });
            })
        }
    });

    $('#composer-modal').on('hidden.bs.modal', function () {
        $('.composer-modal__form').find('.composer-modal__list').removeClass('searching');
        $('.composer-modal__form').find('.composer-modal__not-found').removeClass('active');
        $('.composer-modal__form').find('.user-item').removeClass('selected')
        $('.composer-modal__form').parents('.composer-modal').find('.btn-add-composer').addClass('disabled');
        add_count = 0;
    });

    $('.composer-new__form').each(function () {
        var $this = $(this);
        $(this).find('#composer-name').on('keyup', function () {
            var name = $(this).val().trim();
            var desc = $('#composer-desc').val().trim();


            if (desc.length > 0 && name.length > 0) {
                $this.parents('.composer-new').find('.btn-new-composer').removeClass('disabled');
            } else {
                $this.parents('.composer-new').find('.btn-new-composer').addClass('disabled');
            }
        });

        $(this).find('#composer-desc').on('keyup', function () {
            var desc = $(this).val().trim();
            var name = $('#composer-name').val().trim();

            if (desc.length > 0 && name.length > 0) {
                $this.parents('.composer-new').find('.btn-new-composer').removeClass('disabled');
            } else {
                $this.parents('.composer-new').find('.btn-new-composer').addClass('disabled');
            }
        });

        $this.parents('.composer-new').find('.btn-new-composer').off('click').on('click', function (e) {
            e.preventDefault();
            var name = $('#composer-name').val().trim();
            var desc = $('#composer-desc').val().trim();
            var last_child = $(".gallery-list .list-composer:last-child");
            var order = 1;
            if (last_child.length > 0) {
                order = parseInt($(last_child).data("order")) + 1;
            }
            $.ajax({
                method: 'POST',
                url: '/creators/create_list_creators',
                data: {"name": name, "desc": desc, "order": order},
                success: function (data) {
                    let id = data.id.toString();
                    var new_data = '<div class="list-composer" data-order="' + order + '" data-list-id="' + id + '">' +
                        '<div class="creator-list_header">' +
                        '<div class="list-composer__title ui-sortable-handle">' + name + '</div>' +
                        '<div class="list-composer__sub-title">' + desc + '</div>' +
                        '<div class="creator-list-chapter-title-edit comment-edit-button-group">\n' +
                        '<button class="edit-creator-list" data-toggle="modal" data-target="#composer-update-modal">' +
                        '<img src="/static/images/edit.svg" alt="">' +
                        '</button>' +
                        '<button class="delete-creator-list">' +
                        '<img src="/static/images/delete.svg" alt="">' +
                        '</button>' +
                        '</div>' +
                        '</div>' +
                        '<div class="list-composer__list is_curator">' +
                        '<div class="list-composer__add">' +
                        '<a class="list-composer__add-btn" data-toggle="modal" data-target="#composer-modal">' +
                        '<span class="srm-icon-wrap">' +
                        '<div class="srm-icon srm-add"></div>' +
                        '</span>' +
                        '</a>' +
                        '<div class="list-composer__add-desc">アーティストを選択</div>' +
                        '</div>' +
                        '</div>' +
                        '</div>';
                    $('.gallery-list').prepend(new_data);
                    $('#composer-new-modal').find('.btn-new-composer').addClass('disabled');
                    $('#composer-new-modal').modal('hide');
                    // toastr.success('テーマに追加しました。');
                },
                error: function () {
                    $('#composer-new-modal').find('.btn-new-composer').addClass('disabled');
                    $('#composer-new-modal').modal('hide');
                    toastr.error('エラーが発生しました')
                }
            })
        });
    });

    $("#composer-new-modal").on("hidden.bs.modal", function () {
        $('#composer-name').val("");
        $('#composer-desc').val("");
        $("#composer-modal").find(".btn-new-composer").addClass("disabled");
    });

    $("#composer-modal").on("hidden.bs.modal", function () {
        $('#composer-modal .composer-modal_search').val("");
        $('.btn-add-composer').addClass('disabled');
    });
}

function playNewWorks() {
    $(document).on('click', `.list-new-works__sub-item .gallery__item:not(.loading, .playing) .list-search__item-playpause:not(.btn-preview-album)`, function () {
        const audio = $(this).parent().find('audio')
        audio.off('canplay')
        audio.each(function (i, e) {
            e.pause()
        })
        playPauseOnHoverNewWork('play', $(this))
        playPauseInNavigation('', 'pause');
    });

    $(document).on('click', `.gallery__list-new-works .gallery__item:not(.playing-navi) .list-search__item-playpause:not(.btn-preview-album)`, function () {
        const thumbnail = $(this).parent();
        const audio = thumbnail.find('audio');
        let type = thumbnail.data('type');
        let music = false, voice = false, sound = false;
        if(type=='music') {
            music = true
        } else if(type=='voice') {
            voice = true
        } else if(type=='sound_effect') {
            sound = true
        }

        $(this).parents('.playing, .loading').removeClass('playing, loading')

        checkPlayPauseConditionForTypeNewWork(thumbnail);
        playPauseInNavigation('', 'play', audio);
        let played_id = []
        let current_album = thumbnail.find('audio').attr('data-album')
        $('.playing-navi').each(function(i, e) {
            let a = $(e).find('audio');
            let album_id = a.attr('data-album')
            if(current_album == album_id) {
                return
            }
            if(!played_id.includes(album_id)) {
                played_id.push(album_id)
                let type = $(e).data('type');
                if(type=='music') {
                    music = true
                } else if(type=='voice') {
                    voice = true
                } else if(type=='sound_effect') {
                    sound = true
                }
                let currentTime = a.attr('currentTime');
                if(currentTime) {
                    a[0].currentTime = currentTime;
                    a[0].play();
                }
            }
        })
      showToastPlayMix(music, voice, sound)
   });

    $(document).on('click', `.list-new-works__sub-item .gallery__item.playing .list-search__item-playpause:not(.btn-preview-album),
    .list-new-works__sub-item .gallery__item.loading .list-search__item-playpause:not(.btn-preview-album)`, function () {
        const audio = $(this).parent().find('audio')
        playPauseOnHoverNewWork('pause', $(this))
        playPauseInNavigation('', 'pause');
    });

    $(document).on('click', `.gallery__list-new-works .playing-navi .list-search__item-playpause:not(.btn-preview-album)`, function () {
        const audio = $(this).parent().find('audio')
        playPauseInNavigation('', 'pause', audio);
    });
}

// action drag sale content in list work
function dragableNewWorks() {
    $('.gallery__list-new-works').sortable({
        axis: 'x',
        items: '> div, > a',
        handle: 'a.gallery__btn-move',
        containment: 'document',
        tolerance: 'pointer',
        connectWith: ".gallery__list-new-works",
        cancel: ".list-new-works__button-add-work",
        start: function (event, ui) {
        },
        stop: function (event, ui) {
            $(ui.item).css({'left': 'unset', 'top': 'unset'});
            let parentDom = $(ui.item).parents('.gallery__new-works__sub-component');
            let listId = parentDom.attr('data-list-id');
            let arr_order = [];

            parentDom.find('.list-new-works__item-container').each(function (index, item) {
                arr_order.push(item.getAttribute('data-id'));
            });

            $.ajax({
                type: "POST",
                url: "/gallery/update_sale_content_index",
                data: {
                    'list_id': listId,
                    'arr_order': arr_order
                },
                success: function (data) {
                    // toastr.success('並び替えました')
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            })
        }
    }).disableSelection();
}

// Action drag/drop list new works
function dragableNewWorkThemes() {

    $('.gallery__new-works-container:not(.cannot-check)').sortable({
        axis: 'y',
        items: '> div',
        handle: 'div.gallery__title-new-works__container',
        containment: 'document',
        tolerance: 'pointer',
        connectWith: ".gallery__new-works-container",
        // cancel: ".list-new-works__button-add-work",
        start: function (event, ui) {

        },
        stop: function (event, ui) {
            $(ui.item).css({'left': 'unset', 'top': 'unset'});
            let arr_order = [];

            $('.gallery__new-works__sub-component').each(function (index, item) {
                arr_order.push(item.getAttribute('data-list-id'));
            });
            // Merges both arrays and gets unique items
            arr_order = arrayUnique(arr_order.map(Number).concat(listNewWork));

            $.ajax({
                type: "POST",
                url: "/gallery/update_list_work_index",
                data: {
                    'arr_order': arr_order
                },
                success: function (data) {
                    // toastr.success('並び替えました')
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            })
        }
    }).disableSelection();
}

function CRUDNewWork() {

    $(document).on('click', '.list-new-works__button-add-work-theme', function(e) {
        $('#modal-add-new-work-theme').modal('show');
        $('#modal-add-new-work-theme').removeClass('modal-edit-list-work');
        validateFormNewWorkTheme();
    });

    $(document).on('click', '.btn-edit-work-theme', function(e){
        $('#modal-add-new-work-theme input[name="title-new-work"]').val($(this).parents('.gallery__new-works__sub-component').find('.gallery__title-new-works').text());
        $('#modal-add-new-work-theme input[name="description-new-work"]').val($(this).parents('.gallery__new-works__sub-component').find('.gallery__sub-title-new-works').text());
        $('#modal-add-new-work-theme').modal('show');
        $('#modal-add-new-work-theme').attr('data-list-id' ,$(this).parents('.gallery__new-works__sub-component').attr('data-list-id'));
        $('#modal-add-new-work-theme').addClass('modal-edit-list-work');
    });

    $(document).on('click', '.btn-delete-work-theme', function(e){
        e.preventDefault();
        $('#modal-delete-work-theme').modal('show');
        $('#modal-delete-work-theme').find('.modal-content').attr('data-list-id', $(this).parents('.gallery__new-works__sub-component').attr('data-list-id'));
    });

    $('#modal-delete-work-theme').on('click', '.btn-popup-send', function (e) {
        let list_id = $(this).parents('.modal-content').attr('data-list-id');
        $.ajax({
            method: 'POST',
            url: '/gallery/delete_list_work',
            data: {
                'list_id': list_id,
            },
            success: function (data) {
                $('.gallery__new-works__sub-component[data-list-id=' + list_id + ']').remove();
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-delete-work-theme').modal('hide');
            }
        });
    });

    // Action add list work

    $('#modal-add-new-work-theme').on('input', 'input[name="title-new-work"], input[name="description-new-work"]', function(e){
        validateFormNewWorkTheme();
    });
    validateFormNewWorkTheme();

    function validateFormNewWorkTheme() {
        let title = $('#modal-add-new-work-theme input[name="title-new-work"]').val().trim();
        let description = $('#modal-add-new-work-theme input[name="description-new-work"]').val().trim();
        let btnSubmit = $('#modal-add-new-work-theme .btn-popup-send');

        if (title !== '' && description !== '') {
            btnSubmit.removeClass('disabled');
        } else {
            btnSubmit.addClass('disabled');
        }
    }

    $('#modal-add-new-work-theme').on('click', '.btn-popup-send', function(e){
        let buttonDom = $(this);
        if (!buttonDom.hasClass('disabled')) {
            buttonDom.addClass('disabled');
            let title = $('#modal-add-new-work-theme input[name="title-new-work"]').val().trim();
            let description = $('#modal-add-new-work-theme input[name="description-new-work"]').val().trim();
            if (title === '' || description === '') {
                return
            }
            if ($('#modal-add-new-work-theme').hasClass('modal-edit-list-work')) {
                let list_id = $('#modal-add-new-work-theme').attr('data-list-id');
                if (!list_id) {
                    return
                }
                ajaxUpdateListWork(list_id, title, description, buttonDom)
            } else {
                ajaxCreateListWork(title, description, buttonDom)
            }
        }
    });


    function ajaxCreateListWork(title, description, buttomDom) {
        $.ajax({
            method: 'POST',
            url: '/gallery/create_list_work',
            data: {
                'title': title,
                'description': description
            },
            success: function (data) {
                $('.gallery__new-works-container').prepend(data.html);
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-add-new-work-theme').modal('hide');
                buttomDom.removeClass('disabled');
            }
        })
    }

    function ajaxUpdateListWork(list_id, title, description, buttomDom) {
        $.ajax({
            method: 'POST',
            url: '/gallery/edit_list_work_name',
            data: {
                'list_id': list_id,
                'title': title,
                'description': description
            },
            success: function (data) {
                if (data.new_name) {
                    let listDom = $('.gallery__new-works-container .gallery__new-works__sub-component[data-list-id=' + list_id + ']');
                    listDom.find('.gallery__title-new-works').html(escapeHtml(data.new_name));
                    listDom.find('.gallery__sub-title-new-works').html(escapeHtml(data.new_description));
                }
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-add-new-work-theme').modal('hide');
                buttomDom.removeClass('disabled');
            }
        })
    }

    // Reset Form
    $(document).on('hidden.bs.modal', '#modal-add-new-work-theme', function () {
        $('#modal-add-new-work-theme input[name="title-new-work"]').val('');
        $('#modal-add-new-work-theme input[name="description-new-work"]').val('');
        $('#modal-add-new-work-theme .btn-popup-send').addClass('disabled');
    });

    // Action delete list work
    $(document).on('click', '.gallery__list-new-works .gallery__btn-delete', function() {
        $('#modal-delete-sub-work').modal('show');
        $('#modal-delete-sub-work').find('.modal-content').attr('data-list-id', $(this).parents('.gallery__new-works__sub-component').attr('data-list-id'));
         $('#modal-delete-sub-work').find('.modal-content').attr('data-sale-id', $(this).parents('.list-new-works__item-container').attr('data-id'));
    });

    $('#modal-delete-sub-work').on('click', '.btn-popup-send', function (e) {
        let list_id = $(this).parents('.modal-content').attr('data-list-id');
        let sale_id = $(this).parents('.modal-content').attr('data-sale-id');
        $.ajax({
            method: 'POST',
            url: '/gallery/delete_sale_content_in_list_work',
            data: {
                'list_id': list_id,
                'sale_id': sale_id
            },
            success: function (data) {
                $('.list-new-works__item-container[data-id=' + sale_id + ']').remove();
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-delete-sub-work').modal('hide');
            }
        });
    });

    // Add sale content into list work

    $(document).on('click', '.gallery__list-new-works .list-new-works__button-add-work', function () {
        $('#modal-add-sub-work').modal('show');
        let listId = $(this).parents('.gallery__new-works__sub-component').attr('data-list-id');
        $('#modal-add-sub-work').attr('data-list-id', listId);
    });

    // search artist
    $(document).on('keypress', '#new-work__add-work', function (e) {
        if (e.which === 13) {
            let listId = $('#modal-add-sub-work').attr('data-list-id');
            let keyWord = $(this).val().trim();
            if (listId) {
                ajaxGetSaleContentToAdd(listId, keyWord)
            }
        }
    });


    $(document).on('hidden.bs.modal', '#modal-add-sub-work', function () {
        $('#modal-add-sub-work').attr('data-list-id', '');
        resetModalSearchSale();
    });

    function ajaxGetSaleContentToAdd(listId, keyWord) {
        $.ajax({
            method: 'POST',
            url: '/gallery/get_sale_content_to_add_list_work',
            data: {
                'list_id': listId,
                'key_word': keyWord
            },
            success: function (data) {
                $('#modal-add-sub-work .list-search-work.mscrollbar').empty();
                $('#modal-add-sub-work .list-search-work.mscrollbar').prepend(data.html);
                dragableNewWorks()
            },
            error: function () {
                $('#modal-add-sub-work .list-search-work.mscrollbar').empty();
                toastr.error('エラーが発生しました');
            },
            complete: function () {
                 $('#modal-add-sub-work .btn-popup-send').addClass('disabled')
            }
        })
    }

    $(document).on('click', '#modal-add-sub-work .btn-popup-send', function () {
        let buttonDom = $(this);
        if (buttonDom.hasClass('disabled')) {
            return
        }
        buttonDom.addClass('disabled');
        let listId = $('#modal-add-sub-work').attr('data-list-id');
        let arr_order = [];

        $('#modal-add-sub-work .list-new-works__item-container').each(function (index, item) {
            if ($(this).find('.check-to-add-into-list').is(':checked')) {
                arr_order.push(item.getAttribute('data-sale-content-id'));
            }
        });
        ajaxAddSaleContentToList(listId, arr_order, buttonDom)
    });

    $(document).on('change', '.check-to-add-into-list', function () {
        if ($('input.check-to-add-into-list:checked').length) {
            $('#modal-add-sub-work .btn-popup-send').removeClass('disabled');
            $('#new-work__add-work').attr('disabled', true);
        } else {
            $('#modal-add-sub-work .btn-popup-send').addClass('disabled');
            $('#new-work__add-work').attr('disabled', false);
        }
    });

    function ajaxAddSaleContentToList(listId, arr, buttonDom) {
        $.ajax({
            method: 'POST',
            url: '/gallery/add_sale_content_into_list_work',
            data: {
                'list_id': listId,
                'arr': arr
            },
            success: function (data) {
                $('.gallery__new-works__sub-component[data-list-id=' + listId + '] .gallery__list-new-works.mscrollbar').prepend(data.html);
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-add-sub-work').modal('hide');
                buttonDom.removeClass('disabled');
            }
        })
    }


    $(document).on('input', '#new-work__add-work', function (e) {
        if (e.target.value.trim() !== '') {
            $(this).parents('.sform-group__append-before').find('.search-delete').css({"display": "block"});
        }
    });

    $('.search-delete').on('click', function () {
        resetModalSearchSale()
    });

    $('#modal-add-sub-work').on('click', '.list-new-works__media', function() {
        let checkbox = $(this).find('#list-search-work__checkbox');
        checkbox.prop('checked', !checkbox.prop('checked'));
    });

    function resetModalSearchSale() {
        $('#new-work__add-work').val('');
        $('#new-work__add-work').attr('disabled', false);
        $(this).css({"display": "none"});
        $('#modal-add-sub-work .list-search-work.mscrollbar').empty();
        $('#modal-add-sub-work .btn-popup-send').addClass('disabled');
    }

}

let hovering = false;
let hovering_id = 0;
let hover_zooming = false;
let close_full_screen = false;
function hoverPlayVideo() {
    $(document).on('mouseenter mouseleave', '.list-new-works__media.gallery__item', function(e) {
        if($('.audio-navi').is('.showing')) {
            return
        }
        if(e.type === 'mouseenter') {
            if ($(this).attr('data-file-type') === 'audio') {
                if($(this).is('.playing-navi')) {
                    return;
                }
                let target = $(this)
                let timeout = 200;
                if(hover_zooming) {
                    timeout = 400;
                }
                let current_hover_id = hovering_id;
                hovering = true;
                setTimeout(() => {
                    if(hovering && current_hover_id == hovering_id && !$('.audio-navi.showing').length) {
                        playPauseOnHoverNewWork('play', target.find('.list-search__item-playpause'))
                    }
                }, timeout)
            } else if (!hover_zooming && $(this).attr('data-file-type') === 'movie' && $(this).find('video').length) {
                let current_hover_id = hovering_id;
                let target = $(this)
                hovering = true;
                setTimeout(() => {
                    if(!hovering || current_hover_id != hovering_id || $('.audio-navi.showing').length) {
                        return
                    }
                    $('video, audio').each(function(i, e) {
                        e.pause()
                        $(e).parents('.sample-audio-thumbnail, .exhibition-works__component-content, .list-circle__component, .list-new-works__media.gallery__item').removeClass('playing loading')
                        if($(e).parents('.list-topics__content-bottom__list-circle, .section-content__list-media').length && $(this).parents('.list-circle__component').attr('process-status') === 'play') {
                            $(this).parents('.list-circle__component').attr('process-status', 'pause');
                            $(this).parents('.list-circle__component').css({'border': '1px solid #F0F0F0'});
                            $(this).parents('.list-circle__component').attr('process-data', parseInt($(e).get(0).currentTime / $(e).get(0).duration * 100));
                        }
                    })
                    $(this).find('video').get(0).play();
                    $(this).find('video').css('display', 'block');
                    let counter = hovering_id;
                    let video = $(this).find('video').get(0);
                    let viewportOffset = video.getBoundingClientRect();
                    let top = viewportOffset.top
                    let left = viewportOffset.left
                    let width = video.clientWidth
                    let height = video.clientHeigth

                    hovering = true;
                    close_full_screen = false;
                    setTimeout(function() {
                        console.log(hovering, hovering_id, counter)
                        if(hovering && counter === hovering_id) {
                            target.css({'position': 'fixed', 'top': top, 'left': left, 'max-width': width, 'height': height});
                            hover_zooming = true;
                            target.find('.list-new-works__content_hover').removeClass('hide');
                            target.parent().addClass('video-newwork-placeholder');
                            target.addClass('hover-zoom-netflix-prepare');
                            setTimeout(()=>{
                                target.addClass('hover-zoom-netflix');
                            }, 100);

                            setTimeout(() => {
                                target.addClass('hover-zoom-netflix-center');
                            }, 600);

                            $('body').append(`<div class='video-netflix-overlay'></div>`)
                        }
                    }, 10000)
                }, 200);
            }
        }

        if(e.type === 'mouseleave') {
            hovering_id++;
            if ($(this).attr('data-file-type') === 'audio') {
                if($(this).is('.playing-navi')) {
                    return;
                }
                $(this).removeClass('playing loading');
                playPauseOnHoverNewWork('pause', $(this).find('.list-search__item-playpause'))
            } else if (!hover_zooming && $(this).attr('data-file-type') === 'movie' && $(this).find('video').length && !close_full_screen) {
                $(this).find('video').get(0).pause();
                hovering = false;
                $(this).css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
                $(this).parents('.video-newwork-placeholder').removeClass('video-newwork-placeholder');
                $(this).removeClass('hover-zoom-netflix hover-zoom-netflix-center hover-zoom-netflix-prepare')
                $(this).find('.list-new-works__content_hover').addClass('hide');
            }
        }
    });

    $(document).on('click', '.video-netflix-overlay:not(.topic-netflix-overlay)', function() {
        // $('.hover-zoom-netflix video').get(0).pause();
        $('.hover-zoom-netflix').removeClass('hover-zoom-netflix-center');
        $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')
        setTimeout(() => {
            $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
            $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
            $('.hover-zoom-netflix-prepare').find('.list-new-works__content_hover').addClass('hide');

            $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
            $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
        }, 200)

        setTimeout(() => {
            $('.hover-zoom-out-netflix-prepare-2').css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
            $('.hover-zoom-out-netflix-prepare-2').parents('.video-newwork-placeholder').removeClass('video-newwork-placeholder');
            $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
        }, 600)

        hover_zooming = false;
        hovering_id++;
        hovering = false;
        close_full_screen = true;
        $('body .video-netflix-overlay').remove();
    })

    $(document).on('click', '.list-new-works__media.gallery__item video', function() {
        $(this).parent().find('.btn-preview-album').trigger('click');
        close_full_screen = true;
        hovering_id++;
    })
}

function redirectProfile() {
    $(document).on('click', '.list-new-works__sub-title', function(e) {
        e.stopPropagation();
        window.open($(this).attr('data-url'), '_blank');
    })
}

function playPauseOnHoverNewWork(play_type='pause', target) {
    let id = $('.audio-navi').attr('data-id')
    let audio_id = target.parents('.list-new-works__item-container').attr('data-sale-id')

    if(id != audio_id) {
        if(play_type == 'play') {
            $('.playing-navi').each(function(i, e) {
                let a = $(e).find('audio');
                $(e).removeClass('playing-navi')
            })

            $('video, audio').each(function(i, e) {
                e.pause()
                $(e).parents('.sample-audio-thumbnail, .exhibition-works__component-content, .list-circle__component, .list-new-works__media.gallery__item').removeClass('playing loading')
                if($(e).parents('.list-topics__content-bottom__list-circle, .section-content__list-media').length && $(this).parents('.list-circle__component').attr('process-status') === 'play') {
                    $(this).parents('.list-circle__component').attr('process-status', 'pause');
                    $(this).parents('.list-circle__component').css({'border': '1px solid #F0F0F0'});
                    $(this).parents('.list-circle__component').attr('process-data', parseInt($(e).get(0).currentTime / $(e).get(0).duration * 100));
                }
            })
            const thumbnail = target.parent();
            const audio = thumbnail.find('audio');
            let type = thumbnail.data('type');

            checkPlayPauseConditionForType(thumbnail);

            $('.gallery__item[data-type=' + type + ']').each(function () {
              $(this).removeClass('playing loading');
            });

            audio.on('canplay', function () {
              audio.off('canplay');
              if (thumbnail.is('.loading')) {
                thumbnail.removeClass('loading');
                thumbnail.addClass('playing')
              }
            });

            audio[0].play()
            audio[0].muted = false
            audio[0].loop = true

            if (audio[0].readyState > 1) {
              thumbnail.removeClass('loading')
              thumbnail.addClass('playing')
            } else {
              thumbnail.removeClass('playing')
              thumbnail.addClass('loading')
            }
        } else {
            const audio = target.parent().find('audio');
            audio.off('canplay');
            audio.each(function (i, e) {
                e.pause()
            })
            target.parents('.gallery__item').removeClass('playing loading')
            audio.parents('.playing-navi').removeClass('playing-navi')
        }
    } else {
        if(play_type == 'play' && !wavesurfer.isPlaying()) {
            playPauseInNavigation('', 'play');
          }
    }
}

function checkPlayPauseConditionForTypeNewWork(sample) {
    switch (sample.attr('data-type')) {
        case 'music':
            $('.playing[data-type="music"] audio, .playing[data-type="2mix"] audio, .loading[data-type="music"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                if($(e).parents('.playing')[0] == sample[0]) {
                    return;
                }
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            $('.playing-navi').each(function (i, e) {
                if(e == sample.parent()[0]) {
                  return
                }
                let curr_type = $(e).attr('data-type');
                if(curr_type === 'music' || curr_type === '2mix') {
                  $(e).removeClass('playing-navi')
                  $(e).find('audio')[0].pause();
                } else {
                  let audio =  $(e).find('audio')
                  audio.attr('currentTime', audio[0].currentTime)
                }
            });
            break;
        case 'voice':
            $('.playing[data-type="voice"] audio, .playing[data-type="2mix"] audio, .loading[data-type="voice"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                if($(e).parents('.playing')[0] == sample[0]) {
                    return;
                }
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            $('.playing-navi').each(function (i, e) {
                if(e == sample.parent()[0]) {
                  return
                }
                let curr_type = $(e).attr('data-type');
                if(curr_type === 'voice' || curr_type === '2mix') {
                  $(e).removeClass('playing-navi')
                  $(e).find('audio')[0].pause();
                } else {
                  let audio =  $(e).find('audio')
                  audio.attr('currentTime', audio[0].currentTime)
                }
            });
            break;
        case 'sound_effect':
            $('.playing[data-type="sound_effect"] audio, .playing[data-type="2mix"] audio, .loading[data-type="sound_effect"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
                if($(e).parents('.playing')[0] == sample[0]) {
                    return;
                }
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            $('.playing-navi').each(function (i, e) {
                if(e == sample.parent()[0]) {
                  return
                }
                let curr_type = $(e).attr('data-type');
                if(curr_type === 'sound_effect' || curr_type === '2mix') {
                  $(e).removeClass('playing-navi')
                  $(e).find('audio')[0].pause();
                } else {
                  let audio =  $(e).find('audio')
                  audio.attr('currentTime', audio[0].currentTime)
                }
            });
            break;
        default:
            $('.playing audio, .loading audio').each(function (i, e) {
                if($(e).parents('.playing')[0] == sample[0]) {
                    return;
                }
                $(e).parent().find('.list-search__item-playpause').trigger('click');
                e.pause();
            });
            $('.playing-navi').each(function (i, e) {
                if(e == sample.parent()[0]) {
                  return
                }
                $(e).removeClass('playing-navi')
                $(e).find('audio')[0].pause();
            });
            break;
    }
}
