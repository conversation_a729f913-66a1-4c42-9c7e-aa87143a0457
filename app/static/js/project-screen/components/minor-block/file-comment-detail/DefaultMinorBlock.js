import UserDownloaded from "../file-info/UserDownloaded.js";

const DefaultMinorBlock = ({
  fileInfo,
  file,
  version,
  is_pc_device
}) => {
  let dataFileInfo = fileInfo;
  let parsed = false;

  if (dataFileInfo && Object.keys(dataFileInfo).length > 0){
    parsed = true;
  }else{
    try {
      dataFileInfo = JSON.parse(file?.file_info);
      parsed = true;
    }catch(e){
      //data is not JSON
    }
  }

  if (version == 2){
    return `
      <div class="comment-file-content ">
        <p style="word-break: break-all;" class="file-name">
          ${file?.real_name?.length > 25 ? file?.real_name?.substring(0,25) : file?.real_name}
        </p>
        <div class="btn-download-file-cmt">
          <a href="javascript:void(0);" data-scene-title-id="${ file?.scene?.scene_id}" data-file-id="${ file?.file_id }"
          class="btn-download-file ${file?.scene?.production_file ? 'tfile-producttion-file' : ''} block-download-file">
            <span class="material-symbols-rounded scene-file-download">
						download
					</span>
          </a>
        </div>
      </div>
      <div class="info-message-file">
        <div class="size-file-message">
          ${parsed && dataFileInfo?.size_converted ? `<span>${ dataFileInfo?.size_converted}</span>` : ''}
        </div>
        <div class="user-download-file">
          <div class="has_user_downloaded">
            ${UserDownloaded({
              file,
              type_comment: 'messenger',
              is_pc_device
            })}
          </div>
        </div>
      </div>`;
  }else{
    return `
      <div class="size-file-message">
        ${parsed ? `
        <span>${ dataFileInfo.size_converted }</span>` : ''}
      </div>`;
  }

};

export default DefaultMinorBlock;
