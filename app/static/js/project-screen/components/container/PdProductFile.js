import { dayOfWeek, getWeekNumber } from "../../common/utils.js";
import FileItemBlock from "../block/FileItemBlock.js";
import FileInfoBlock from "../minor-block/file-info/FileInfoBlock.js";
import FolderInfoBlock from "../minor-block/file-info/FolderInfoBlock.js";

const PdProductFile = ({comments, projectPk, currentUserId, is_pc_device, comment_files, folders, current_user_role}) => {
	//we will need to join both files and folders and order by modified at
	
	return `
		<div class="pd-section-file file-hide max-height-pd-section-file-up active hide-file-tab" id="pd-section-file">
      		<div class="pd-file-heading hide">
        		<i class="icon icon--sicon-storage"></i>
				<div class="pd-file-toggle active">
					<i class="icon icon--sicon-next"></i>
				</div>
      		</div>
			<div class="pd-file-content mscrollbar active">
				${FileItemBlock({ comments: comments, fileList: comment_files, folderList: folders, is_pc_device})}
			</div>
    	</div>
	`;
}

export default PdProductFile;
