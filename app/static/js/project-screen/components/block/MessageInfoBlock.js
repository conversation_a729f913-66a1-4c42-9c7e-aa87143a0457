import UserSeenMessage from "./UserSeenMessage.js";
import CountUserSeen from "./CountUserSeen.js";
import { dayOfWeek } from "../../common/utils.js";

const MessageInfoBlock = ({comment, typeInfo, is_pc_device, currentUserId}) => {
  if (false){
    return `
{% if new_layout is None %}
    {% if type_comment == 'messenger' and message.user_id != user.pk or type_comment == 'messenger_owner' and message.user_id != user.pk %}
    {% else %}
        {% if type_infor == 'received' %}
            <div class="{% if offer and type_comment != 'messenger' %} dp-none {% endif %} dropdown dropdown-comment-new dropdown-comment actions-received dropdown-comment-received">
                <div class="dropdown-toggle show-more-comment-received show-more-action-message"
                     id="dropdownMenuButton2" data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                    <img src="{% static 'images/scene-detail/icon-more-horiz.svg' %}" class="more-action-hoz"
                         alt="more horiz comment">
                </div>
                <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenuButton2">
                    {% if offer is None %}
                        <li class="li-resolve-message">
                            <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                               href="javascript:void(0);">
                                <span class="txt-item-comment">{% if message.resolved %}進行中に戻す{% else %}
                                    解決済みにする{% endif %}</span>
                                <div class="img-resolve-comment"></div>
                            </a>
                        </li>

                        <li class="last-action-comment">
                            <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                                <span class="txt-item-comment txt-reply-comment">返信</span>
                                <div class="img-reply-comment"></div>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>

        {% else %}
            <div class="dropdown dropdown-comment-new dropdown-comment">
                <div class="dropdown-toggle show-more-comment show-more-action-message" id="dropdownMenuButton2"
                     data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                    <img src="{% static 'images/scene-detail/icon-more-horiz.svg' %}" class="more-action-hoz"
                         alt="more horiz comment">
                </div>
                <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenuButton2">
                    {% if offer is None %}
                        <li class="li-resolve-message">
                            <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                               href="javascript:void(0);">
                                <span class="txt-item-comment">{% if message.resolved %}進行中に戻す{% else %}
                                    解決済みにする{% endif %}</span>
                                <div class="img-resolve-comment"></div>
                            </a>
                        </li>

                        <li class="li-reply-message">
                            <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                                <span class="txt-item-comment txt-reply-comment">返信</span>
                                <div class="img-reply-comment"></div>
                            </a>
                        </li>
                    {% endif %}
                    {% if message.user.role == user.role and message.user.pk == user.pk %}
                        <li class="li-edit-message">
                            <a class="dropdown-item mmessage-edit" href="javascript:void(0);">
                                <span class="txt-item-comment txt-edit-comment">コメントを編集</span>
                                <div class="img-edit-comment"></div>
                            </a>
                        </li>
                        <li class="last-action-comment last-action-comment">
                            <a class="dropdown-item mmessage-delete" href="javascript:void(0);">
                                <span class="txt-item-comment txt-green">削除</span>
                                <img src="{% static 'images/scene-detail/icon-delete.svg' %}" alt="delete comment">
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        {% endif %}
    {% endif %}

{% endif %}`
  }else{
    let maxLoop = is_pc_device ? 7 : 4;
    let type_comment = 'messenger';
    let time = "";
    const nowDate = new Date();
    const dateCreated = new Date(comment.created);
    
    if( new Date(comment.created).getDate() === nowDate.getDate() && new Date(comment.created).getMonth() === nowDate.getMonth() && new Date(comment.created).getFullYear() === nowDate.getFullYear() ){
        time = `${dateCreated.getHours().toString().padStart(2, '0')}:${dateCreated.getMinutes().toString().padStart(2, '0')}`
    } else if ((nowDate - dateCreated)/(24*3600*1000) < 7){
        time = dayOfWeek[dateCreated.getDay()];
    } else {
        time = `${dateCreated.getMonth() + 1}/${dateCreated.getDate()}`
    }
    
    return `
      <div class="message-info-container ${typeInfo == 'received' ? 'actions-received' : ''}
       ${(type_comment == 'messenger' && comment.user_id != currentUserId) || (type_comment == 'messenger_owner' && comment.user_id === currentUserId) ? 'height-init' : ''}">
        <div class="mmessage-status">
            <div class="mmessage-time">${time}</div>
            <div class="mmessage-user">
                ${comment?.list_user_seen?.length ? typeInfo !== "received" ? CountUserSeen({
                    user_count: comment?.list_user_seen?.length - maxLoop
                }) : '': ""}
                ${comment?.list_user_seen?.length > 0 ? comment?.list_user_seen?.map((user,indexUser) => {
                    if (indexUser < (maxLoop - 1)){
                        return UserSeenMessage({
                            user
                        })
                    }else{
                        return ``;
                    }
                }).join(""): ""}
                ${comment?.list_user_seen?.length ? typeInfo === "received" ? CountUserSeen({
                    user_count: comment?.list_user_seen?.length - maxLoop
                }) : '' : ""}
            </div>
        </div>
    </div>`
  }
};
export default MessageInfoBlock;
