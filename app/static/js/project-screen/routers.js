
import { dict_chatsocket } from "./common/utils.js";
import <PERSON>Room from "./pages/TalkRoom.js";

const TabList = {
  //"progress": 1,//first screen
  "product-comment": TalkRoom,//talkroom
  //"messenger": 3,//dm
};

export const router = async (ev) => {
  let currentLocation = new URL(ev?.destination?.url ?? window.location);
  if (ev?.destination){
    if (currentLocation?.href?.includes("/top/project/")){
      //parse to get project id
      let projectPK = currentLocation.pathname?.split("/")[3];
  
      let searchParams = new URLSearchParams(currentLocation?.search);
      let tab = searchParams?.get('tab');
     
      if (typeof TabList[tab] !== undefined && projectPK && projectPK != ''){
        let screen = TabList[tab];
        //const main = document.getElementById("main-container");
        //main.innerHTML = 
        if (typeof screen?.render == 'function'){
          await screen.render(projectPK);
        } else {
          let listScript3 = document.getElementsByTagName('script');
          let matchRegex3 = /initSocket\(([0-9]+[\D]*)\)/gm;
          let currentId3 = 0;
          for (let i = 0; i < listScript3.length; i++){
              let scriptString = listScript3[i];
              let match = scriptString.innerText.match(matchRegex3);
              if (match){
                  currentId3 = match[0].replace("initSocket(","").replace(")","");
              }
          }
          try{
            let current_chatsocket = dict_chatsocket.get(Number(currentId3.split(",")[0]));
            if(current_chatsocket){
              current_chatsocket.close();
              dict_chatsocket = new Map();
            }
          } catch(e) {}
        }
        if (typeof screen?.after_render == 'function') await screen.after_render(projectPK);
      }else{
        //do nothing
      }
    }else{
      //wrong location
      //do nothing
    }
  }else{
    let currentLocation = window.location;
    if (currentLocation?.href?.includes("/top/project/")){
      //parse to get project id
      let projectPK = currentLocation.pathname?.split("/")[3];
  
      let searchParams = new URLSearchParams(currentLocation?.search);
      let tab = searchParams?.get('tab');
      if (typeof TabList[tab] !== undefined && projectPK && projectPK != ''){
        let screen = TabList[tab];
        //const main = document.getElementById("main-container");
        //main.innerHTML = 
        if (typeof screen?.render == 'function'){
          await screen.render(projectPK);
        }
        if (typeof screen?.after_render == 'function') await screen.after_render(projectPK);
      }else{
        let listScript3 = document.getElementsByTagName('script');
        let matchRegex3 = /initSocket\(([0-9]+[\D]*)\)/gm;
        let currentId3 = 0;
        for (let i = 0; i < listScript3.length; i++){
            let scriptString = listScript3[i];
            let match = scriptString.innerText.match(matchRegex3);
            if (match){
                currentId3 = match[0].replace("initSocket(","").replace(")","");
            }
        }
        try{
          let current_chatsocket = dict_chatsocket.get(Number(currentId3.split(",")[0]));
          if(current_chatsocket){
            current_chatsocket.close();
            dict_chatsocket = new Map();
          }
        } catch(e) {}
        //do nothing
      }
    }else{
      //wrong location
      //do nothing
    }
  }
};


window.addEventListener("load", router);
window.addEventListener("hashchange", router);
window.navigation.addEventListener("navigate", async (event) => {
  await router(event)
});
