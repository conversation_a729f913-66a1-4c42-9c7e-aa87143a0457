let current_x;
let current_y;
let current_height;
let current_width;
let current_img;
let current_file;
var $myDropZone;
var key_file = '';
var real_file_name = '';
let list_file_id = [];
let list_file_remove = [];
var is_delete_file = false;
Dropzone.autoDiscover = false;

// Scroll tabs
function onScroll(event) {
    var difference = document.documentElement.scrollHeight - window.innerHeight;
    var scrollPos = $(document).scrollTop();


    if (difference - scrollPos <= 160 && $('.user-info__tabs-list li a[href="#tab_4"]').length) {
        $('.user-info__tabs-list li a').removeClass('active');
        $('.user-info__tabs-list li a[href="#tab_4"]').addClass('active')
    } else {
         $('.user-info__tabs-list li a').removeClass('active');
         $('.user-info__tabs-list li a').each(function () {
            var currLink = $(this);
            var refElement = $(currLink.attr("href"));
            if (refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                $('.user-info__tabs-list li a').removeClass("active");
                currLink.addClass("active");
            } else {
                currLink.removeClass("active");
            }
        });
    }
}

// Datepicker
if ($('.mcalendar').length > 0) {
    $('.mcalendar').each(function () {
        var date = new Date();
        date.setDate(date.getDate());
        let current_date = $("#id_dob").val();
        flatpickr("#id_dob", {
          mode: "single",
          dateFormat: "Y/m/d",
          defaultDate: formatDate(current_date),
          showMonths: 1,
          onOpen: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .addClass("is-icon-active");
          },
          onClose: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .removeClass("is-icon-active");
          },
            onChange: function (selectedDates, dateStr, instance) {
              let startDate = selectedDates[0];
              let endDate = selectedDates[1];
              $(instance.element).attr(
                "data-start-time",
                moment(startDate).format("yyyy-MM-DD HH:mm:ss")
              );
              if (endDate)
                $(instance.element).attr(
                  "data-end-time",
                  moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                );
            },
          // minDate: "today",
          // maxDate: new Date().fp_incr(120),
        });
    })
}

let is_deleting = false;

function deleteAccount() {
    $('#deleteAccount').find('.btn-popup-delete').off('click').on('click', function () {
        let user_id = $('#acc-info__form').attr('data-user');
        if (!user_id || !is_deleting) {
            $.ajax({
                type: 'POST',
                datatype: 'json',
                url: '/accounts/remove_current_user',
                data: {'user_id': user_id},
                beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    is_deleting = true;
                },
                success: function (data) {
                    $('#deleteAccount').modal('hide');
                    if (data.is_current_user) {
                        window.location.href = '/'
                    } else if (data.user_role === 'curator') {
                        window.location.href = '/accounts/curator/curator_setting'
                    } else {
                        window.location.href = '/accounts/list'
                    }
                },
                complete: function () {
                    is_deleting = false;
                }
            })
        }
    })
}

function updateAccount() {
    // $(document).on('change', ".user-info__upload-file", function () {
    //     readURL(this, $(this));
    // });
}


function getZipCode(id_get_zip_code) {
    $(id_get_zip_code).on('click', function () {
        let post_number = $('#id_post_number');
        let province = $('#id_province');
        let city = $('#id_city');

        if (id_get_zip_code === '#get_zip_code2') {
            post_number = $('#id_post_number2');
            province = $('#id_province2');
            city = $('#id_city2');
        }
        let zip_code = post_number.val();
        if (zip_code) {
            zip_code = zip_code.replace(/[^0-9]/gm, '');
            let url = 'https://apis.postcode-jp.com/api/v4/postcodes/' + zip_code + '?apiKey=ng0RDOveBSGo11VtLNlaV2gdviVzBb3e6hUvFhn';
            $.ajax({
                url: url,
                dataType: 'jsonp',
                beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    $('.loading-icon').removeClass('hide');
                },
                success: function (data) {
                    $('.loading-icon').addClass('hide');
                    post_number.val(zip_code);
                    post_number.data('value', zip_code);
                    if (data[0]) {
                        province.val(data[0].pref);
                        city.val(data[0].city);
                        province.parent().find('p.form-error').remove();
                        province[0].removeAttribute("style");
                        city.parent().find('p.form-error').remove();
                        city[0].removeAttribute("style");
                    }
                },
                complete: function () {
                    $('.loading-icon').addClass('hide');
                }
            })
        }
    });
}


function resetPassword() {
    let array = ['#new_pass', '#confirm_pass', '#current_pass'];
    $('#resetPassword').off('click', '.btn-popup-save').on('click', '.btn-popup-save', function (e) {
        let button = $(this);
        e.preventDefault();
        $('.error-pass').remove();
        $('.error-input-pass').removeClass('error-border');
        const form = $('#resetPassword form');
        let user_id = $('#acc-info__form').attr('data-user');
        if (button.hasClass('disable') || !user_id) {
            return
        } else {
            let new_pass = $('#new_pass').val().trim();
            let confirm_pass = $('#confirm_pass').val().trim();
            let current_pass = $('#current_pass').val().trim();
            array.map((x) => {
                if ($(x).val().trim() === '') {
                    $(x).addClass('error-border');
                } else {
                    $(x).removeClass('error-border');
                }
            });

            if (new_pass === '' || confirm_pass === '' || current_pass === '') {
                return
            }

            if (new_pass !== confirm_pass) {
                $('#confirm_pass').addClass('error-border');
                $('<ul class="errorlist error-pass">' +
                    '<li>新しいのパスワードと確認パスワードが同じじゃありません。</li>' +
                    '</ul>').insertAfter($('#confirm_pass'));
                return
            } else if (!new_pass.match(/^(?=.*[A-Za-z])[A-Za-z\d.@+-^]{8,20}$/)){
                $('#confirm_pass').addClass('error-border');
                $('#new_pass').addClass('error-border');
                $('<ul class="errorlist error-pass">' +
                    '<li>パスワードは最低 8ー２0 文字以上必要です。</li>' +
                    '<li>数字だけのパスワードにはできません。</li>' +
                    '</ul>').insertAfter($('#new_pass'));
                return
            }
            else  {
                $('#confirm_pass').removeClass('error-border');
                $('#remove_pass').removeClass('error-border');
                $('.error-pass').remove()
            }

            button.addClass('disable');
            form.off('submit').on('submit', function (e) {
                e.preventDefault();
                var url = form.attr('action');
                var formData = new FormData(form[0]);
                formData.append('user_id', user_id);
                $.ajax({
                    type: "POST",
                    url: url,
                    processData: false,
                    contentType: false,
                    data: formData,
                    beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    },
                    success: function (data) {
                        console.log('ok');
                        let status = data.status;
                        // toastr.success(status);
                        $('#resetPassword').modal('hide')
                    },
                    statusCode: {
                        400: function (data) {
                            let str = data.responseText;
                            str = str.replaceAll(')(', ', ').replaceAll(')', '').replaceAll('(', '');
                            let array = str.split(',');
                            var items = [];

                            for (i = 0; i <= array.length - 2; i += 2) {
                                items[array[i]] = array[i + 1]
                            }

                            for (const [key, value] of Object.entries(items)) {
                                console.log(key);
                                let dom = $('input[name=' + key.replaceAll(/'/g, '').trim() + ']');
                                let new_value = value.replace('[', '').replace(']', '').replaceAll(/'/g, '').trim();
                                $(`<ul class="errorlist error-pass"><li>${new_value}</li></ul>`).insertAfter(dom);
                                dom.addClass('error-border error-input-pass')
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        if (xhr.responseJSON) {
                            toastr.error(xhr.responseJSON.status)
                        }
                    },
                    complete: function () {
                        button.removeClass('disable');
                    }
                });
            });
            form.submit()
        }
    });

    $('#resetPassword').on('hidden.bs.modal', function () {
        array.map((x) => {
            $(x).val('');
            $(x).removeClass('error-border')
        });
        $('#resetPassword .errorlist').remove();
        $('#resetPassword .btn-popup-save').removeClass('disable')
    })
}

function checkAddress() {
    if ($('#id_only_address').is(':checked')) {
        $('.address2').addClass('disable');
        handleFillInputForm2Value();
    }

    $('#id_only_address').on('change', function () {
        if ($(this).is(':checked')) {
            $('.address2').addClass('disable');
            handleFillInputForm2Value();
        } else {
            $('.address2').removeClass('disable');
            handleRemoveInputForm2Value();
        }
    })

    $('input[name="post_number"], input[name="province"], input[name="city"], input[name="mansion"]').on('input', function() {
        if($('#id_only_address').is(':checked')){
            handleFillInputForm2Value();
        }
    })

}

function cancelAvatar() {
    $(document).on('click', '.user-info__cancel-img .user-info__cancel', function () {
        $('#id_avatar').val('');
        $('#id_x').val(current_x);
        $('#id_y').val(current_y);
        $('#id_height').val(current_height);
        $('#id_width').val(current_width);
        $('.user-info__images img').attr('src', current_img);
        $('.user-info__cancel-img').remove()
    })
}

function uploadFileS3(file, file_dom) {
    file_dom.find('.determinate').css('width', '0%');
    $.ajax({
        type: 'GET',
        datatype: 'json',
        url: '/get_presigned_url',
        data: {
            'file_name': file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            let key_file = data.presigned_post.fields["key"];
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            let postData = new FormData();
            for(key in data.presigned_post.fields){
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);

            xhr.upload.addEventListener('progress', function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 100 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s');
                    file_dom.find('.determinate').css('width', percentComplete);
                    if (file_dom.length) {
                        if (!file_dom.attr('data-total')) {
                            file_dom.attr('data-total', evt.total);
                        }
                        file_dom.attr('data-loaded', evt.loaded);
                    }
                }
            }, false);

            xhr.onreadystatechange = function() {
                if(xhr.readyState === 4){
                    if(xhr.status === 200 || xhr.status === 204){
                        let data = new FormData();
                        data.append('key_file', key_file);
                        data.append('file_name', file.name);
                        data.append('user_id', $('#acc-info__form').attr('data-user'));

                        $.ajax({
                            type: "POST",
                            data: data,
                            contentType: false,
                            processData: false,
                            url: '/accounts/curator/artist_create_files',
                            success: function (data) {
                                Object.assign(list_file_id, data);
                                file_dom.find('.determinate').css('width', '100%');
                            },
                        });
                    }
                    else{
                        alert("Could not upload file.");
                    }
                }
            };

            xhr.send(postData);
        }
    })
}

var sending_file = false;
function creatorUpload() {
    if ($('#myId').length) {
        $('#id_user_file').attr({accept: 'application/pdf, .jpg, .png'});
        var previewNode = $('.mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        $myDropZone = new Dropzone('#myId', {
            url: "/accounts/uploadFIle",
            autoDiscover: false,
            previewsContainer: '.mattach-previews-form',
            previewTemplate: previewTemplate,
            acceptedFiles: 'application/pdf, .jpg, .png',
            maxFilesize: 4500,
            timeout: 900000,
            clickable: '#myId',
            autoProcessQueue: false,
            autoQueue: false,
            dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
        });

        $myDropZone.on('addedfile', function (file, e) {
            let file_dom = $(file.previewElement);
            if ($('.account__status.account__status-normal').length){
                $('.account__status.account__status-normal').show();
            }else{
                $('.label-status').find('.account__jp-astarisk-op').after(`
                    <span class='account__status account__status-normal'>審査中</span>`)
            }
            file_dom.find('.determinate').css('width', '0%');

            if (file && file.name.match(/\.(pdf|PDF|png|PNG|jpg|JPG)$/)) {
                // $('#user_file-clear_id').prop('checked', false);
                file_name = file.name;
                uploadFileS3(file, file_dom);
                // if($('.account__file').length){
                //     $('.account__file').hide();
                //     $('.account__file').next().hide();
                // }
            } else {
                alert('PDF, PNG, JPGのみアップロードできます。');
                $myDropZone.removeAllFiles(true);
            }
        });

        $myDropZone.on('removedfile', function (file) {
            key_file = '';
            real_file_name = '';
            $('.account__status.account__status-normal').hide();
            if (!is_delete_file && $(".account__file").length){
                $('.account__file').show();
                $('.account__file').next().show();
                $('.account__status.account__status-normal').show();
            }
        });

        $('.account_upload-file #myId').contents().filter(function () {
            return this.nodeType === Node.TEXT_NODE || this.tagName === 'A';
        }).remove();
        $(".account_upload-file").find('label[for=user_file-clear_id]').hide();
        $(".account_upload-file").find('#user_file-clear_id').hide();

        $(document).on('click', '.account__file .icon--sicon-close', function () {
            $(this).parents('.account__file').remove();
            $('.account__status.account__status-normal').hide();
            $('.account__field-hint.text--file').remove();
            let file_id = $(this).parents('.account__current-file').attr('data-file-id');
            list_file_remove.push(file_id);
            $(this).parents('.mattach-template').remove();
        });
    }
}

function disableBtnSubmit() {
    var new_pass = $('#new_pass').val().trim();
    var confirm_pass = $('#confirm_pass').val().trim();
    var current_pass = $('#current_pass').val().trim();

    if(new_pass === '' || confirm_pass === '' || current_pass === '') {
        $('.btn-popup-save').addClass('disable');
    }

    $('#new_pass, #confirm_pass, #current_pass').on('input', function (e) {
        new_pass = $('#new_pass').val().trim();
        confirm_pass = $('#confirm_pass').val().trim();
        current_pass = $('#current_pass').val().trim();

        if (new_pass !== '' && confirm_pass !== '' && current_pass !== '') {
            $('.btn-popup-save').removeClass('disable');
        }
        else {
            $('.btn-popup-save').addClass('disable');
        }
    });
}

function submitForm(){
    const form = $('#acc-info__form');
    const arrayField = ['#id_email', '#id_fullname'];

    $('.errorlist').remove();
    $('.error-border').removeClass('error-border');

    if (checkValidateBlank(arrayField)) {
        return
    }

    // check email
    if (!validateEmail() || !validatePhone()) {
        return false
    }

    //check post number
    var post_number = $('#id_post_number').val().trim();
    var post_number2 = $('#id_post_number2').val().trim();
    if (post_number) {
        $('#id_post_number').val(post_number.replace(common_not_number_regex, ''));
    }
    if (post_number2) {
        $('#id_post_number2').val(post_number2.replace(common_not_number_regex, ''));
    }

    if (!sending_file) {
        form.submit();
    }
}

function showAgreement() {
    $(document).on('click', '.btn-nondisclosure-agreement', function(e) {
        let dataType = $(this).attr('data-type');
        let dataSource = $(this).attr('data-source');
        let dataName = $(this).attr('data-name');
        let dataID = $(this).attr('data-id');

        if (dataType === 'document') {
            $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
            setTimeout(() => {
                $('#modal-document-popup').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(dataSource) + '#zoom=page-width');
                $('#modal-document-popup').find('.file-name_modal').html(dataName);
                $('#modal-document-popup').attr('data-file-id', dataID);
                $('#modal-document-popup').modal('show');
                $('#modal-document-popup .modal-dialog').css({'width': '558px'})
            }, 100)
        } else if (dataType === 'image') {
           $('#modal-image-popup').find('img').attr('src', dataSource);
           $('#modal-image-popup').find('.image-popup__title').html(dataName);
           $('#modal-image-popup').attr('data-file-id', dataID);
           $('#modal-image-popup').modal('show');
        } else if (dataType === 'video') {
           $('#modal-video-popup').find('video').attr('src', dataSource);
           $('#modal-video-popup').find('.video-popup__title').html(dataName);
           $('#modal-video-popup').attr('data-file-id', dataID);
           $('#modal-video-popup').modal('show');
        }
        else {
            let user_id = $('#acc-info__form').attr('data-user');
            curator_get_link_download(user_id)
        }

        $(document).on('click', '.smodal-download', function() {
            let user_id = $('#acc-info__form').attr('data-user');
            curator_get_link_download(user_id)
        })
    });
}

function curator_get_link_download(user_id) {
    if (user_id) {
        $.ajax({
            type: "POST",
            data: {
                'user_id': user_id
            },
            url: '/accounts/curator/curator_file_download_link',
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            },
            success: function (data) {
                window.location.href = data.url;
            },
        });
    }
}

$(document).ready(function () {
    removeOverLayModal();
    $('.errorlist').each(function () {
        $(this).prev().addClass('error-border')
    });
    current_x = $('#id_x').val();
    current_y = $('#id_y').val();
    current_height = $('#id_height').val();
    current_width = $('#id_width').val();
    current_img = $('.user-info__images img').attr('src');
    current_file = $('#id_avatar').attr('value');
    let file_name;
    $('#id_avatar').attr({accept: 'image/*'});
    $('#id_avatar').on('change', function () {
        var image_dom = $('.user-info__images img');
        if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
            file_name = this.files[0].name;
            let reader = new FileReader();
            reader.onload = function (e) {
                $('#image').attr('src', e.target.result);
                $('#modalCrop').modal('show');
            };
            reader.readAsDataURL(this.files[0]);
        } else if (this.files.length == 0) {
            return false;
        } else {
            alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
            $(this).val('').clone(true);
        }
    })
    $("#modalCrop").modal({
        show: false,
        backdrop: 'static'
    });

    var $image = $('#image');
    var cropBoxData;
    var canvasData;
    $('#modalCrop').on('shown.bs.modal', function () {
        $image.cropper({
            viewMode: 1,
            aspectRatio: 1 / 1,
            minCropBoxWidth: 200,
            minCropBoxHeight: 200,
            minContainerHeight: 300,
            ready: function () {
                $image.cropper('setCanvasData', canvasData);
                $image.cropper('setCropBoxData', cropBoxData);
            }
        });
        $(document).keypress(function (e) {
            var code = e.which; // recommended to use e.which, it's normalized across browsers
            if (code == 13) {
                e.preventDefault();
                $('.js-crop-and-upload').click();
                return false;
            }
        });
        $('#modalCrop').find('.modal-header button').on('click', function () {
            $('#id_avatar').val('');
            $('#id_x').val(current_x);
            $('#id_y').val(current_y);
            $('#id_height').val(current_height);
            $('#id_width').val(current_width);
            $('.user-info__images img').attr('src', current_img);
            $('.user-info__cancel-img').remove()
        })
    }).on('hidden.bs.modal', function () {
        cropBoxData = $image.cropper('getCropBoxData');
        canvasData = $image.cropper('getCanvasData');
        if ($('.user-info__images img')[0].src.match('/default-avt.png')
            || $('.user-info__images img')[0].src.match('/default-avatar-client.png')
            || $('.user-info__images img')[0].src.match('/default-avatar-admin.png')
            || $('.user-info__images img')[0].src.match('/default-avatar-master-admin.png')
            || $('.user-info__images img')[0].src.match('/default-avatar-creator.png')) {
            $('#id_avatar').val('').clone(true);
        }
        $image.cropper('destroy');
    });

    // Enable zoom in button
    $('.js-zoom-in').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').click(function () {
        $image.cropper('zoom', -0.1);
    });

    $('.js-crop-and-upload').click(function () {
        var cropData = $image.cropper("getData", {fillColor: '#fff'});
        var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
        var image_dom = $('.user-info__images img');
        image_dom.attr('src', croppedImageDataURL);

        $('#id_x').val(cropData['x']);
        $('#id_y').val(cropData['y']);
        $('#id_height').val(cropData['height']);
        $('#id_width').val(cropData['width']);
        $image[0].height = cropData['height'];
        $image[0].width = cropData['width'];
        console.log($image[0].height, $image[0].width, $image[0].x, $image[0].y);
        $('#modalCrop').modal('hide');
        if ($('.user-info__cancel-img').length < 1) {
            $("<div class=\"user-info__cancel-img\">" +
                "<span class=\"user-info__name-file\">" + file_name + "</span>" +
                "<input type=\"button\" class=\"user-info__cancel\" value=\"Cancel\" />" +
                "</div>").insertAfter($('.user-info__images'));
        } else {
            $('.user-info__cancel-img').find('.user-info__name-file').text(file_name)
        }
    });

    $('#id_phone').on('input', function(e){
        var phone = $('#id_phone').val().trim();
        if (this.value.length > 30) {
            this.value = this.value.slice(0, 30);
        }
        $('#id_phone').val(phone.replace(common_not_number_regex, ''));
    })

    $('#acc-info__form').off('click', '#btn__ok').on('click', '#btn__ok', function (e) {
        e.preventDefault();
        if ($myDropZone && ($myDropZone.files.length || list_file_remove.length)) {
            let file_loaded = Object.values(list_file_id);
            let file_loading = $myDropZone.files.length - file_loaded.length;
            if (file_loading > 0) {
                var waiting_file_loading = setInterval(function () {
                    let current_file_loaded = Object.values(list_file_id);
                    let current_file_loading = $myDropZone.files.length - current_file_loaded.length;
                    if (current_file_loading < 1) {
                        clearInterval(waiting_file_loading);
                        submitFormWithFile(e)
                    }
                }, 100);

            } else {
                submitFormWithFile(e)
            }
        } else {
            submitForm();
        }
    });

    changePlaceholder();

    $('#id_fullname').on('input', function () {
        changePlaceholder();
    });

    // Clear input, button
    $('#resetPassword').on('shown.bs.modal', function (e) {
        $(this).find("input").val('').end();
        $('.btn-popup-save').addClass('disable');
    })

    onScroll();
    $(document).on("scroll", onScroll);

    updateAccount();
    getZipCode('#get_zip_code');
    getZipCode('#get_zip_code2');
    resetPassword();
    deleteAccount();
    checkAddress();
    cancelAvatar();
    creatorUpload();
    disableBtnSubmit();
    showAgreement();
});

function submitFormWithFile(e) {
    let data = new FormData();
    data.append('list_file_id', Object.keys(list_file_id));
    data.append('list_file_remove', list_file_remove);
    data.append('user_id', $('#acc-info__form').attr('data-user'));
    $.ajax({
        type: "POST",
        data: data,
        contentType: false,
        processData: false,
        url: '/accounts/uploadFile',
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            sending_file = true;
        },
        success: function (data) {
            sending_file = false;
            e.preventDefault();
            submitForm();
        },
    });
}


function changePlaceholder() {
    // Placeholder display_name
    let default_placeholder = $('#id_stage_name').length ? 'ジョン・レノン' : 'ぽっちんぷりん';
    let placeholder = $('#id_fullname').val() !== '' ? $('#id_fullname').val() : default_placeholder;
    $('#id_display_name').attr('placeholder', placeholder);
    $('#id_stage_name').attr('placeholder', placeholder);
    $('#id_stage_name_en').attr('placeholder', placeholder);
    if($('#id_stage_name_en').attr('placeholder') === 'ジョン・レノン') {
        $('#id_stage_name_en').attr('placeholder', 'John Lennon');
    }
    $('#id_fullname').on('input', function () {
        let placeholder = $('#id_fullname').val() ? $('#id_fullname').val() : default_placeholder;
        $('#id_display_name').attr('placeholder', placeholder);
        $('#id_stage_name').attr('placeholder', placeholder);
        $('#id_stage_name_en').attr('placeholder', placeholder);
    })
}


function handleFillInputForm2Value() {
    $('input[name="post_number2"]').val($('input[name="post_number"]').val())
    $('input[name="province2"]').val($('input[name="province"]').val())
    $('input[name="city2"]').val($('input[name="city"]').val())
    $('input[name="mansion2"]').val($('input[name="mansion"]').val())
}

function handleRemoveInputForm2Value() {
    $('input[name="post_number2"]').val('')
    $('input[name="province2"]').val('')
    $('input[name="city2"]').val('')
    $('input[name="mansion2"]').val('')
}
