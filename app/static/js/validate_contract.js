var max_length_phone = 30;
var max_length_message = 1000;

function validateFormContact() {
    $('.errorlist').remove();
    $('.error-border').removeClass('error-border');
    let emailDom = $('#id_email');
    let emailConfirmDom = $('#id_email_confirm');
    let fullnameDom = $('#id_fullname');
    let urlDom = $('#id_company_url');

    let array_fields = ['#id_fullname', '#id_email', '#id_email_confirm'];
    if ($('#id_message').length) {
        array_fields = ['#id_fullname', '#id_email', '#id_email_confirm', '#id_message'];
    }
    let countError = 0;
    array_fields.map((x) => {
        $(x).val($(x).val().trim());
        if ($(x).val() === '') {
            countError++;
            if (countError === 1) {
                $(x).focus();
            }
            $(x).addClass('error-border');
            $('<ul class="errorlist">' +
                '<li>このフィールドは必須です。</li>' +
                '</ul>').insertAfter($(x));
        } else {
            $(x).removeClass('error-border');
        }
    });
    let email = emailDom.val();
    let email_confirm = emailConfirmDom.val();
    let fullname = fullnameDom.val();
    let url = urlDom.val().trim();
    if ($('#id_message').length) {
        let message = $('#id_message').val().trim();

        if (email === '' || email_confirm === '' || fullname === '' || message === '') {
            return false
        }
    } else {
        if (email === '' || email_confirm === '' || fullname === '') {
            return false
        }
    }


    // validate email
    if (!validateEmail() || !validatePhone()) {
        return false
    }

    // validate email confirm
    if (email_confirm !== email) {
        emailConfirmDom.focus();
        emailConfirmDom.addClass('error-border');
        $('<ul class="errorlist">' +
            '<li>' + gettext('Please enter a valid email address.') + '</li>' +
            '</ul>').insertAfter(emailConfirmDom);
        return false
    }

    //Validate url company
    if (url !== '') {
        if (!isUrl(url)) {
            urlDom.focus();
            urlDom.addClass('error-border');
            $('<ul class="errorlist">' +
                '<li>' + gettext('The URL format is incorrect. ') +
                '</ul>').insertAfter(urlDom);
            return false;
        }
    }

    let messageDom = $('#id_message');
    if (messageDom.length) {
        let isChrome = window.chrome;
        let messageValue = messageDom.val();
        if (isChrome) {
            messageValue = messageDom.val().replace(/(\r\n|\n|\r)/g, "  ");
        }

        if (messageValue.length > 1000) {
            messageDom.val(messageValue.slice(0, max_length_message))
        }
    }

    return true

}

function isUrl(urlWebDom) {
    if(urlWebDom === '')
        return true;

    return (urlWebDom.match(/(http(s)?:\/\/.)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z0-9]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g) !== null);
}

function validateEmail() {
    let emailDom = $('#id_email');
    let email = emailDom.val();
    if (email) {
        if (!email.match(common_email_regex)) {
            emailDom.focus();
            emailDom.addClass('error-border');
            $('<ul class="errorlist">' +
                '<li>' + gettext('Please enter a valid email address.') + '</li>' +
                '</ul>').insertAfter(emailDom);
            return false
        }
    }
    return true
}


function validatePhone() {
    var phone = $('#id_phone').val();
    var phonefomat = /^([0-9]+-{0,1})+\d{1,}?$/;
    if (phone) {
        phone = phone.trim();
        if (!phone.match(phonefomat)) {
            $('#id_phone').focus();
            if ($('.errorlist').length < 1) {
                $('#id_phone').addClass('error-border');
                $('<ul class="errorlist">' +
                    '<li>' + gettext('Please enter a valid phone number.') +
                    '</ul>').insertAfter($('#id_phone'));
                return false
            }
        } else {
            $('#id_phone').removeClass('error-border');
            $('.errorlist').remove();
        }
    }

    if (phone.length > max_length_phone) {
        $('#id_phone').val(phone.slice(0, max_length_phone));
    }
    return true
}
