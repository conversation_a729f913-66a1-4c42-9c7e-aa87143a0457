$(document).ready(function () {
    const YOUTUBE_API_KEY = 'AIzaSyDCMgNHV7qoz3qTtwgSBdih8nniwORpqMM';
    const SRC_API = `https://www.youtube.com/iframe_api?key=${YOUTUBE_API_KEY}`;
    let sale_youtube_videos = $('.sale-youtube-video');

    function getYouTubeVideoId(url) {
        // Regular expression to extract video ID from various YouTube URL formats
        const regExp = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
        const match = url.match(regExp);

        // If there's a match, return the video ID, otherwise return null
        return match ? match[1] : null;
    }

    function onYouTubeIframeAPIReady(idElement, idVideo) {
        let player = new YT.Player(idElement, {
            height: '144',
            width: '256',
            videoId: idVideo,
            playerVars: {
                'autoplay': 0,
                'controls': 0,
                'showinfo': 0,
                'autohide': 1,
                'rel': 0,
            },
            events: {
                'onReady': function (event) {
                    onPlayerReady(event, idElement);
                }
            }
        });
    }

    function onPlayerReady(event, idElement) {
        let thumbnail = document.getElementById(idElement);
        let playButton = document.createElement('div');
        playButton.id = 'play-button';
        thumbnail.appendChild(playButton);

        thumbnail.addEventListener('mouseenter', function () {
            event.target.playVideo();
        });

        thumbnail.addEventListener('mouseleave', function () {
            event.target.pauseVideo();
        });
    }

    if (sale_youtube_videos.length > 0) {
        // Sử dụng cả hai dòng sau để đảm bảo rằng iframe_api đã tải trước khi gọi onYouTubeIframeAPIReady
        let tag = document.createElement('script');
        tag.src = SRC_API;
        let firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

        // Đợi sự kiện tải xong trước khi gọi onYouTubeIframeAPIReady
        tag.onload = function () {
            for (let item of sale_youtube_videos) {
                let idElement = $(item).attr('data-id-element');
                let videoURL = $(item).attr('data-video-url');
                let idVideo = getYouTubeVideoId(videoURL);
                onYouTubeIframeAPIReady(idElement, idVideo);
            }
        };
    }
});
