// グローバル変数
//
// メイン商品のオブジェクトを作成
const plans = {
    plan1: {
        image: "images/mgk-plan1.jpg",
        name: "STANDARD",
        description: "ツールのみ",
        price: 100000,
        value: "standard"
    },
    plan2: {
        image: "images/mgk-plan2.jpg",
        name: "PRO",
        description: "ツール　＆　ヒューマンリサーチ",
        price: null,
        value: "pro"
    },
};

// オプションのオブジェクトを作成
const options = {
    option1: {
        image: "images/mgk-option1.jpg",
        name: "カスタムコンテンツ",
        description:
            "お客様だけのデータベースを構築し、まだリリースされていない未発表音源などのツール内照合を可能にします。",
        price: 50000,
        value: 'custom_content'
    },
    option2: {
        image: "images/mgk-option2.jpg",
        name: "ヒューマンリサーチ",
        description:
            "楽曲知識に精通したスペシャリストによる複眼的リサーチ。シリーズ作品、似た世界観、競合コンテンツなど、影響をうけうる元ネタを探ります。",
        price: null,
        value: 'human_research'
    },
    option3: {
        image: "images/mgk-option3.jpg",
        name: "詳細レポート",
        description:
            "期間内にアップロード頂いた全ファイルの詳細情報をリスト化。懸念事項を総評にまとめ、レポートとして提出します。",
        price: null,
        value: 'detailed_report'
    },
};


const displayComfirm = () => {
// フォーム入力値を取得
    let plan = document.querySelector('input[type="radio"][name="plan"]:checked').value;
    let option1 = document.getElementById('option1').checked;
    let option2 = document.getElementById('option2').checked;
    let option3 = document.getElementById('option3').checked;

    let fullname = document.getElementById('fullname').value;
    let email = document.getElementById('email').value;
    let position = document.getElementById('position').value;
    let company = document.getElementById('company').value;
    let phone = document.getElementById('phone').value;

// 選択されたラジオボタンを取得
    let selectedContact = document.querySelector('input[name="contact"]:checked').parentNode.textContent;
    let message = document.getElementById('message').value;

// 確認画面に表示
    document.getElementById('planConfirm').textContent = plans[plan].name;
    if (option1) {
        document.getElementById('option1Confirm').textContent = options['option1'].name;
    } else {
        document.getElementById('option1Confirm').textContent = '';
    }
    if (option2) {
        document.getElementById('option2Confirm').textContent = options['option2'].name;
    } else {
        document.getElementById('option2Confirm').textContent = '';
    }

    if (option3) {
        document.getElementById('option3Confirm').textContent = options['option3'].name;
    } else {
        document.getElementById('option3Confirm').textContent = '';
    }

    document.getElementById('fullnameConfirm').textContent = fullname;
    document.getElementById('emailConfirm').textContent = email;
    document.getElementById('positionConfirm').textContent = position;
    document.getElementById('companyConfirm').textContent = company;
    document.getElementById('phoneConfirm').textContent = phone;
    document.getElementById('contactConfirm').textContent = selectedContact;
    document.getElementById('messageConfirm').textContent = message;
};


// スクロール感知ヘッダーの実装
//
let lastScrollTop = 0;
const nav = document.querySelector("nav");
const footer = document.querySelector("footer");

// ページが読み込まれたときは、フッターのみ隠した状態にする
window.onload = () => {
    nav.style.top = "0px"; // ヘッダーの高さに応じて調整
    footer.style.bottom = "-96px";
};
// スクロールするたびに実行されるイベントリスナー
document.querySelector("body").addEventListener(
    `scroll`,
    () => {
        let currentScroll =
            document.body.scrollTop || document.documentElement.scrollTop;

        if (currentScroll > lastScrollTop) {
            // 下にスクロール
            nav.style.top = "-112px";
            footer.style.bottom = "0px";
        } else {
            // 上にスクロール
            nav.style.top = "0px";
            // footer.style.bottom = "-96px";
        }
        lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
    },
    false
);

// 日本の通貨形式で数値をフォーマットする関数
const formatAsJapaneseCurrency = (num) => {
    return new Intl.NumberFormat("ja-JP", {
        style: "decimal",
        maximumFractionDigits: 0, // 小数点以下を切り捨てる
    }).format(num);
};

// 価格取得と合計金額を計算する関数
const handleInputChange = () => {
    // ラジオボタン選択中の商品の価格を取得
    const selectedplan = document.querySelector(
        'input[type="radio"][name="plan"]:checked'
    );
    let selectedplanPrice = plans[selectedplan.value].price;

    // チェックボックス選択中のオプションの価格を取得
    const selectedOptions = document.querySelectorAll(
        'input[type="checkbox"][name="option"]:checked'
    );
    // オプション価格を合計。
    let optionsTotal = 0;
    let hasNullOption = false;
    selectedOptions.forEach((option) => {
        if (options[option.value].price === null) {
            hasNullOption = true;
        } else {
            optionsTotal += options[option.value].price;
        }
    });

    // 商品またはオプションの価格がnullである場合、#totalPriceを"個別見積"に設定
    if (selectedplanPrice === null || hasNullOption) {
        document.querySelector("#totalPrice").textContent = "個別にお見積りします";
    } else {
        // 課税対象額を計算
        const taxableTotal = selectedplanPrice + optionsTotal;
        // #taxableTotalに代入
        // document.querySelector("#taxableTotal").textContent =
        //   formatAsJapaneseCurrency(taxableTotal);

        // 消費税を計算
        const tax_amount = Math.floor(taxableTotal * 0.1);
        // #tax_amountに代入
        // document.querySelector("#tax_amount").textContent =
        //   formatAsJapaneseCurrency(tax_amount);

        // 合計金額を計算
        const totalPrice = taxableTotal + tax_amount;
        // totalPriceに代入
        document.querySelector("#totalPrice").innerHTML =
            "合計:" + formatAsJapaneseCurrency(totalPrice) + "<a>円（税込）/月</a>";
    }
};

// プラン（ラジオボタン）とオプション（チェックボックス）の要素を選択
const inputElements = document.querySelectorAll(
    'input[type="checkbox"], input[type="radio"]'
);

// プラン（ラジオボタン）とオプション（チェックボックス）の数だけループを回す
inputElements.forEach((input) => {
    // チェックボックスまたはラジオボタンのchangeイベントにリスナーを追加
    input.addEventListener("change", handleInputChange);
});

// プラン（ラジオボタン）とオプションを取得
const plansDisplay = document.querySelectorAll(
    'input[type="radio"][name="plan"]'
);
const optionsDisplay = document.querySelectorAll(".options .u-wrapper-reading");

// プラン（ラジオボタン）に応じてオプション表示を更新する関数
const updateOptions = () => {
    // デフォルトではすべてのオプションとh3タグを非表示に
    optionsDisplay.forEach((option) => (option.style.display = "none"));
    document.querySelector(".options h3").style.display = "none";

    // plan1（STANDARD）が選択されている場合はoption1を表示
    if (document.querySelector("#plan1").checked) {
        optionsDisplay[0].style.display = "flex";
        gsap.from(".p-option", {
            opacity: 0,
            x: -128,
            duration: 0.5,
            stagger: 0.2,
        });

        //optionsセクション内のh3タグを表示
        document.querySelector(".options h3").style.display = "block";

        //option2とoption3のチェックを外す
        document.querySelector("#option2").checked = false;
        document.querySelector("#option3").checked = false;

        handleInputChange();
    }

    // plan2（PRO）が選択されている場合はすべてのオプションを表示
    if (document.querySelector("#plan2").checked) {
        optionsDisplay.forEach((option) => (option.style.display = "flex"));
        gsap.from(".p-option", {
            opacity: 0,
            x: -128,
            duration: 0.5,
            stagger: 0.2,
        });

        //optionsセクション内のh3タグを表示にする
        document.querySelector(".options h3").style.display = "block";
    }
};

// ページが読み込まれたときにupdateOptionsを呼び出す
window.addEventListener("load", updateOptions);

// プラン（ラジオボタン）が変更されたときにupdateOptionsを呼び出す
plansDisplay.forEach((radio) =>
    radio.addEventListener("change", updateOptions)
);

// ステップフォームの実装
//
// ページが読み込まれたときにstep1を表示する
document.addEventListener("DOMContentLoaded", () => {
    // キャッチコピーをアニメーションさせる;
    gsap.from(".catch", {
        opacity: 0,
        scale: 1.5,
        y: 128,
        duration: 1.5,
    });

    // "navigate-step1"ボタンがクリックされたときと同じ処理を行う：
    document.querySelector("#btn-step1").click();
});

const navigateToStep = (stepId) => {
    // その他のstep要素を非表示
    document.querySelectorAll('[id^="step"]').forEach((step) => {
        step.classList.remove("is-active");
        step.classList.remove("is-active-flex");
        step.classList.add("is-hidden");
    });

    // 指定されたstepとそのfooterを表示
    document.querySelector(`#${stepId}`).classList.add("is-active");
    document.querySelector(`#${stepId}-footer`).classList.add("is-active-flex");
};

document
    .querySelector("#btn-step1")
    .addEventListener("click", () => navigateToStep("step1"));
document.querySelector("#btn-step2").addEventListener("click", () => {
    navigateToStep("step2");
    document.querySelector("body").scrollTop = 0;
    gsap.from(".plans", {
        opacity: 0,
        x: -128,
        duration: 0.5,
        stagger: 0.2,
    });
});
document.querySelector("#btn-back2").addEventListener("click", () => {
    navigateToStep("step2");
    document.querySelector("body").scrollTop = 0;
    gsap.from(".plans", {
        opacity: 0,
        x: -128,
        duration: 0.5,
        stagger: 0.2,
    });
});
document.querySelector("#btn-step3").addEventListener("click", () => {
    navigateToStep("step3");
    document.querySelector("body").scrollTop = 0;
});
document.querySelector("#btn-step4").addEventListener("click", () => {
    displayComfirm();
    navigateToStep("step4");
    document.querySelector("body").scrollTop = 0;
});
document.querySelector("#btn-back3").addEventListener("click", () => {
    navigateToStep("step3");
    document.querySelector("body").scrollTop = 0;
});


document.querySelector("#btn-submit").addEventListener("click", () => {
    let plan = document.querySelector('input[type="radio"][name="plan"]:checked').value;
    let option1 = document.getElementById('option1').checked;
    let option2 = document.getElementById('option2').checked;
    let option3 = document.getElementById('option3').checked;

    let fullname = document.getElementById('fullname').value;
    let email = document.getElementById('email').value;
    let position = document.getElementById('position').value;
    let company = document.getElementById('company').value;
    let phone = document.getElementById('phone').value;

// 選択されたラジオボタンを取得
    let selectedContact = document.querySelector('input[name="contact"]:checked').value;
    let message = document.getElementById('message').value;

    let optionSelected = [];
    if (option1) {
        optionSelected.push(options['option1'].name)
    }
    if (option2) {
        optionSelected.push(options['option2'].name)
    }
    if (option3) {
        optionSelected.push(options['option3'].name)
    }
    let data = new FormData();
    data.append("plan", plans[plan].value);
    data.append("option", optionSelected.join(', '))
    data.append("fullname", fullname)
    data.append("email", email)
    data.append("email_confirm", email)
    data.append("job_type", position)
    data.append("enterprise", company)
    data.append("phone", phone)
    data.append("contact_channel", selectedContact)
    data.append("message", message)
    data.append("csrfmiddlewaretoken", window.CSRF_TOKEN)
    data.append("sign", sign)

    $.ajax({
        type: "POST",
        url: '/mgk',
        data: data,
        processData: false,
        contentType: false,
        success: function (data) {
            if (data.status === 'success') {
                document.querySelector("dialog").showModal();
                dataLayer.push({'event': 'conversion_event_submit_lead_form_1'})
            }
        }
    })
});



// バリデーションの実装
document.addEventListener('DOMContentLoaded', function () {
    const nameInput = document.getElementById('fullname');
    const emailInput = document.getElementById('email');
    const emailConfirmInput = document.getElementById('email_confirm');
    const step2Button = document.getElementById('btn-step2');
    const step3Button = document.getElementById('btn-step3');
    const step4Button = document.getElementById('btn-step4');
    const plan1Radio = document.getElementById('plan1'); // 追加
    const plan2Radio = document.getElementById('plan2'); // 追加

    const validateForm = () => {
        const isPlanSelected = plan1Radio.checked || plan2Radio.checked; // 追加
        const isEmailMatch = emailInput.value === emailConfirmInput.value && emailInput.value.trim() !== '';
        const isNotEmpty = nameInput.value.trim() !== '';

        step3Button.disabled = !isPlanSelected; // 追加
        step4Button.disabled = !(isEmailMatch && isNotEmpty);
    };

    // 各入力フィールドの変更を監視
    step2Button.addEventListener('click', validateForm);
    step3Button.addEventListener('click', validateForm);
    nameInput.addEventListener('input', validateForm);
    emailInput.addEventListener('input', validateForm);
    emailConfirmInput.addEventListener('input', validateForm);
    plan1Radio.addEventListener('change', validateForm); // 追加
    plan2Radio.addEventListener('change', validateForm); // 追加

  });
  

