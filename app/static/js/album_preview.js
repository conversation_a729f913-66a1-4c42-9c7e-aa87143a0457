let last_played_file = false;
function showAlbumPreview() {
    $('#modal-document-popup, #modal-image-popup, #modal-video-popup').on('hidden.bs.modal', function () {
        if(last_played_file) {
           last_played_file.removeClass('playing');
           if(last_played_file.parent().is('.loading, .playing')) {
                last_played_file.parent().removeClass('loading playing')
           }
        }
    });
    $(document).on('click', '.btn-preview-album', function(e) {
        let navi = $('.playing-navi')
        if(navi.length) {
            playPauseInNavigation('', 'pause')
        }
        navi.each(function(i,e) {
            $(e).find('audio')[0].pause()
            $(e).removeClass("playing-navi")
        })
        let srcDom = $(this).siblings('audio');
        if(!srcDom.length) {
            srcDom = $(this).find('audio');
        }
        let dataType = srcDom.attr('data-file-type');
        let dataSource = srcDom.attr('src');
        let dataName = srcDom.attr('data-name');
        if(srcDom.parents('.sample-audio-thumbnail').length) {
            dataName = srcDom.attr('data-album-name');
        }
        let dataID = srcDom.attr('data-album');
        last_played_file = $(this)
        $(this).addClass('playing');
        if (dataType === 'pdf') {
            $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
            setTimeout(() => {
                $('#modal-document-popup').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(dataSource) + '#zoom=page-width');
                $('#modal-document-popup').find('.file-name_modal').html(dataName);
                $('#modal-document-popup').attr('data-file-id', dataID);
                $('#modal-document-popup').modal('show');
                $('#modal-document-popup .modal-dialog').css({'width': '558px'})
            }, 100)
        } else if (dataType === 'image') {
            $('#modal-image-popup').find('img').attr('src', dataSource);
            $('#modal-image-popup').find('.image-popup__title').html(dataName);
            $('#modal-image-popup').attr('data-file-id', dataID);
            $('#modal-image-popup').modal('show');
        } else if (dataType === 'movie') {
            // $('#modal-video-popup').find('video').attr('src', dataSource);
            // $('#modal-video-popup').find('.video-popup__title').html(dataName);
            // $('#modal-video-popup').attr('data-file-id', dataID);
            // $('#modal-video-popup').modal('show');
            $('audio, video').each(function(i, e) {
                e.pause();
                $(e).parents('.sample-audio-thumbnail, .exhibition-works__component-content, .list-circle__component, .list-new-works__media.gallery__item').removeClass('playing loading')
                if($(e).parents('.list-topics__content-bottom__list-circle, .section-content__list-media').length && $(this).parents('.list-circle__component').attr('process-status') === 'play') {
                    $(this).parents('.list-circle__component').attr('process-status', 'pause');
                    $(this).parents('.list-circle__component').css({'border': '1px solid #F0F0F0'});
                    $(this).parents('.list-circle__component').attr('process-data', parseInt($(e).get(0).currentTime / $(e).get(0).duration * 100));
                }
            })
            let video = $(this).siblings('video').get(0);
            if(!video) {
                if($(this).find('video').length) {
                    video = $(this).find('video').get(0);
                } else {
                    let src = $(this).find('audio').attr('src')
                    if(!src) {
                        src = $(this).parent().find('audio').attr('src')
                    }
                    $(this).append(`<video preload="none" style="width: 100%; height: 100%; max-height: 100%; background: white; display: block;">
                        <source src="${src}">
                    </video> `)
                    video = $(this).find('video').get(0);
                }
            }
             
            video.play()
            $(video).css('display', 'block');
            openFullscreen(video)
        }
        else {
            download_sale_content_file(dataID);
            $(this).removeClass('playing');
        }
        
        if($(this).parents('.list-topics__content-bottom__list-circle').length) {
            let selected = $(this).parents('.list-topics__content-bottom__list-circle').find('.last-selected');
            $('.list-circle__component.last-selected').removeClass('last-selected');
            let selection = $(this).attr('data-selection');
            selected.each(function(i, e) {
                if($(e).attr('data-selection') !== selection && $(e).is('.playing, .loading')) {
                    $(e).addClass('last-selected');
                }
            })
            $(this).addClass('last-selected');
        } else if($(this).parents('.section-content__list-media').length) {
            $(this).parents('.section-content__list-media').find('.list-circle__component.last-selected').removeClass('last-selected');
            $(this).addClass('last-selected');
        }

        $(document).off('click', '.smodal-download').on('click', '.smodal-download', function() {
            let dataID = $(this).parents('#modal-document-popup, #modal-video-popup, #modal-image-popup').attr('data-file-id');
            if (dataID) {
                download_sale_content_file(dataID)
            }
        })
    });
}

function download_sale_content_file(dataID) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_sale_content_download_link",
        data: {
            'id': dataID,
        },
        success: function(response) {
            window.location.href = response.url;
            // toastr.success('ダウンロードしました。');
        }
    })
}

function openFullscreen(e) {
    if (e.requestFullscreen) {
        e.requestFullscreen();
    } else if (e.webkitRequestFullscreen) { /* Safari */
        e.webkitRequestFullscreen();
    } else if (e.msRequestFullscreen) { /* IE11 */
        e.msRequestFullscreen();
    }
}
