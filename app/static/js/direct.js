var names = [];
$(document).ready(function () {
    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };

    $('.form-datepicker').datepicker({
        format: 'yyyy/mm/dd',
        startDate: (new Date()).addDays(7)
    });

    let to_date = $('#to-date').val();
    if($('#to-date').length && !to_date.length) {
        $('#to-date').datepicker('update', (new Date()).addDays(7));
    }

    var list_contacts = $('.messenger__item');

    $('.messenger__list').mCustomScrollbar({
        theme: 'minimal-dark',
        mouseWheelPixels: 50,
        scrollEasing: 'linear',
        scrollInertia: 0,
        advanced: {
            autoScrollOnFocus: false
        }
    });

    //load message
    list_contacts.on('click', function () {
        if($('.carosel-pdf').is('.hide')) {
            $(".messenger__column-right").html('');
            $('.messenger__item--selected').removeClass('messenger__item--selected');
            $(this).addClass('messenger__item--selected');
            let offer_ids = $(this).data('offers');

            let url = new URL(window.location);
            url.searchParams.set("offer",  offer_ids[0] );
            let refresh = url.toString();

            window.history.pushState({ path: refresh }, '', refresh);
            loadMessage(offer_ids);
            let top_length = $(this).get(0).offsetTop;
            $('.messenger__list').mCustomScrollbar('scrollTo', top_length);
        } else {
            show_chatbox_list();
            $('.messenger__item--selected').removeClass('messenger__item--selected');
        }
    });

    //select_messenger_box
    let all_box = list_contacts.length;
    let new_box = $('.messenger__item.messenger__item--new').length;

    url_string = window.location.href;
    var url = new URL(url_string);
    var offer_active = url.searchParams.get("offer");
    let auto_open = url.searchParams.get("open");

    if (offer_active) {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/direct/check_offer_status",
            async: false,
            data: {
                'offer_id': offer_active,
            },
            success: function (data) {
                if (data.is_done === 'done' && url_string.includes('is_done=1') || data.is_done === 'not_done' && !url_string.includes('is_done=1')) {
                    $('.messenger__item').each(function () {
                        let offer_ids = $(this).data('offers');
                        if (offer_ids.includes(parseInt(offer_active))) {
                            $(this).click();
                            return false;
                        }
                    })
                } else {
                    window.location.href = data.url;
                    $('.messenger__item').each(function () {
                        let offer_ids = $(this).data('offers');
                        if (offer_ids.includes(parseInt(offer_active))) {
                            $(this).click();
                            return false;
                        }
                    })
                }
            }
        });
    }

    $('.messenger-popup').off().on('click', function () {
        $('body').addClass('modal-messenger-popup-overlay');
    });

    if($('.messenger__item1').hasClass('offers-none')) {
        $('#messenger-popup').modal()
    }

    $(document).on('show.bs.modal', '#messenger-popup', function () {
        $(this).find('.messenger-popup__content').addClass('modal-slide-up').removeClass('modal-slide-down');
        console.log('up');
    });

    $('#messenger-popup').on('hidden.bs.modal', function (e) {
        $(this).find('.messenger-popup__content').removeClass('modal-slide-up').addClass('modal-slide-down');
        $('#contract-confirm').prop('checked', false);
        $('.input-description').addClass('hide');
        $('#description').val('');
        $('.messenger-popup__file-content .messenger-popup__file-input').addClass('hide');
        $('.messenger-popup__file-content .messenger-popup__file-btn').addClass('hide');
        $('#messenger-popup').find('.input-date').addClass('hide');
        $('#start').removeClass('hide');
        $('#submit').addClass('hide');
        $('#submit').removeClass('button--disabled');
        $('#start').addClass('button--disabled');
        $('#file-create').val('');
        $('.messenger-form__confirm.align-center').removeClass('hide');
        $('#messenger-popup').find('.messenger-popup__form-text').removeClass('hide');
        $('.input-budget').addClass('hide');
        $('#budget').val('');
        names = [];
        console.log('down');
        $('.file-list').remove();
        $('.text-danger').remove()
    });

    $('.messenger-popup__action-button .button--text-gray').off().on('click', function () {
        $('.messenger-popup__content').removeClass('modal-slide-up').addClass('modal-slide-down');
    });

    $('#contract-confirm').off().on('click', function () {
        if ($(this).is(':checked')) {
            $('#start').removeClass('button--disabled');
        } else {
            $('#start').addClass('button--disabled');
        }
    });

    $('#start').on('click', function () {
        $('.messenger-form__confirm.align-center').addClass('hide');
        $('.input-description').removeClass('hide');
        $('.input-budget').removeClass('hide');
        $('.messenger-popup__file-content .messenger-popup__file-input').removeClass('hide');
        $('.messenger-popup__file-content .messenger-popup__file-btn').removeClass('hide');
        $('.input-date').removeClass('hide');
        $('#submit').removeClass('hide');
        $('#start').addClass('hide button--disabled');
        $('#messenger-popup').find('.messenger-popup__form-text').addClass('hide');
    });

    if ($('#messenger-popup').find('.messenger-popup__file').length > 0) {
        $('#messenger-popup').find('.messenger-popup__file').each(function () {
            $this = $(this);

            $this.find('.messenger-popup__file-btn').on('click', function (e) {
                e.preventDefault();
                $this.find('.messenger-popup__file-input:hidden').trigger('click');
            });

            $this.find('.messenger-popup__file-input').on('change', function (e) {
                names = [];
                for (var i = 0; i < $(this).get(0).files.length; ++i) {
                    names.push($(this).get(0).files[i].name);
                }

                var file_list = '<ul class="file-list">';
                for (var i = 0; i < names.length; ++i) {
                    file_list += '<li class="messenger-popup__file-title">' + names[i] + '<span class="file-delete" data-value="' + names[i] + '">+</span></li>';
                }
                file_list += '</ul>';

                $this.find('.messenger-popup__file-content').append(file_list);

                $this.find('.file-delete').off().on('click', function () {
                    var value = $(this).attr('data-value');
                    const index = names.indexOf(value);

                    if (index > -1) {
                        names.splice(index, 1);
                    }
                    $(this).parents('.messenger-popup__file-title').remove();
                });
            });
        });
    }

    // create inbox
    $('#submit').off().on('click', function (e) {
        e.preventDefault();
        const form = $('#create_inbox');
        let content = form[0][2].value;
        let to_date, budget;
        to_date = form[0][5].value;
        budget = form[0][6].value;
        budget = parseInt(budget);
        if (!content || !to_date || (!Number.isNaN(budget) && budget <=0)) {
            if (!content) {
                $('#description').css('border', '1px solid red');
                $('#description').next().remove();
                $('#description').after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
            }
            if (!to_date) {
                $('#to_date').css('border', '1px solid red');
                $('#to_date').next().remove();
                $('#to_date').after('<small class="text-danger">' + 'このフィールドは必須項目です。' + '</small>');
            }
            if (!Number.isNaN(budget) && budget <=0) {
                $('#budget').css('border', '1px solid red');
                $('#budget').next().remove();
                $('#budget').after('<small class="text-danger">' + '正の数を入力してください。' + '</small>');
            }
            return false;
        } else {
            $('#submit').addClass('button--disabled');
            form.off().on('submit', function (e) {
                e.preventDefault();
                var url = form.attr('action');
                var formData = new FormData(form[0]);
                $.ajax({
                    type: "POST",
                    url: url,
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function(data){
                        // toastr.info('アップロード中');
                    },
                    success: function (data) {
                        if (data.status === 'success') {
                            $('#messenger-popup').find('.close-modal').trigger('click');
                            $('#create-offer-popup').modal();
                            $('#create-offer-popup').on('hidden.bs.modal', function (e) {
                                window.location.href = data.url;
                            });
                        }else if(data.status === 'failed') {
                            $('#submit').removeClass('button--disabled');
                            toastr.warning('予算目安のところ、正の数を入力してください。');
                        }else {
                            $('#submit').removeClass('button--disabled');
                            toastr.warning('エラーが発生しました');
                        }
                    },
                    error: function (data) {
                        $('#messenger-popup').find('.close-modal').trigger('click');
                        toastr.error('エラーが発生しました');
                    }
                });
            });
            form.submit()
        }
    });

    // hidden popup upload plan
    $('#messenger-popup-plan').on('hidden.bs.modal', function (e) {
        $('.messenger-popup__form-input .messenger-input-text').val('');
        $('.messenger-popup__file-content').val('');
        $('.selected_file_plan').html('');
        $('#upload_plan_product').val('');
        $('.text-danger').remove();
        $('#submit-plan').addClass('button--disabled');
    });

    $('#messenger-popup-product').on('hidden.bs.modal', function (e) {
        $('#project-name').val('');
        $('.text-danger').remove()
    });

    $('#upload_plan_product').on('change', function () {
        if ($(this)[0].files.length) {
            for (i = 0; i < $(this)[0].files.length; i++) {
                let node = document.createElement("div");
                let textnode = document.createTextNode($(this)[0].files[i].name);
                node.append(textnode);
                $('.selected_file_plan').append(node);
            }
            $('#submit-plan').removeClass('button--disabled');
        } else {
            $('.selected_file_plan').html('');
            $('#submit-plan').addClass('button--disabled');
        }
    });

    // create product
    $('#submit-create-product').off().on('click', function (e) {
        e.preventDefault();
        const form = $('#create_product');
        let name = form[0][2].value;
        if (!name) {
            $('#project-name').css('border', '1px solid red');
            $('#project-name').next().remove();
            $('#project-name').after('<div class="text-danger">' + 'このフィールドは必須項目です。' + '</div>');
            return false;
        } else {
            $('#submit-create-product').addClass('button--disabled');
            $('#messenger-popup-product').find('.close-modal').trigger('click');
            form.off().on('submit', function (e) {
                e.preventDefault();
                var url = form.attr('action');
                var data = new FormData();
                let project_id;
                let product_name;
                let old_project = $('#choice-project').is(':checked');
                let offer_active = form[0][1].value;
                if (old_project) {
                    project_id = $('#old_products option:selected').data('value');
                    data.append('project_id', project_id);
                } else {
                    product_name = $('#project-name').val().trim();
                    data.append('product_name', product_name);
                }
                data.append('offer_active', offer_active);
                data.append('old_project', old_project);

                $.ajax({
                    type: "POST",
                    url: url,
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data.status === 'success') {
                            window.location.href = data.url_success;
                        }else if(data.status === 'failed') {
                            $('#submit-create-product').removeClass('button--disabled');
                            toastr.warning('予算目安のところ、正の数を入力してください。');
                        }
                    },
                    error: function (data) {
                        $('#messenger-popup-product').find('close-modal').trigger('click');
                        toastr.error('エラーが発生しました');
                    }
                });
            });
            form.submit();
            return true;
        }
    });

    $('#choice-project').off().on('click', function () {
        if($(this).is(':checked')) {
            $('.create-new-project').addClass('hide');
            $('.select-old-project').removeClass('hide')
        }else{
            $('.create-new-project').removeClass('hide');
            $('.select-old-project').addClass('hide')
        }
    });


    $('#messenger-popup-contact').on('hidden.bs.modal', function (e) {
        $('#upload_contact').val('');
        $('#submit-upload-contact').addClass('button--disabled');
        $('#messenger-popup-contact').find('.selected_file').html('');
    });

    $('.offer-variation-choosed .messenger-carousel-item__type').off().on('click', function () {
        handleClickVersion($(this));
    });

    // click to active offer
    active_offer();

    active_carousel();

    $(".messenger__column-right").on("click", ".video-pin-time", function (event) {
        let variation_id = $(this).data('variation');
        let type = $(this).data('type');
        let carousel_active = $('.messenger-carousel-item[data-variation^=' + variation_id + ']');
        let index = carousel_active.index();
        $('.messenger-carousel-for').slick('slickGoTo', index);
        carousel_active.find('.messenger-carousel-file').addClass('hide');
        carousel_active.find('.messenger-carousel-file[data-type^=' + type + ']').removeClass('hide');
        if (!carousel_active.find('.messenger-carousel-file').not('.hide').length) {
            carousel_active.find('.messenger-carousel-file[data-type^=2]').removeClass('hide');
        }
    });

    $(".carosel-pdf").on("click", ".download__file-pdf", function (event) {
        let variation_id;
        let file_active;
        $('.messenger-carousel-item').each(function () {
            if ($(this).attr('aria-hidden') === 'false') {
                variation_id = $(this).data('variation');
                item_active = $(this).find('.messenger-carousel-file').not('.hide');
                file_active = item_active.data('type');
                return false
            }
        });

        $.ajax({
            type: "GET",
            url: "/top/get_file_download_link",
            data: {
                "variation_id": variation_id,
                "type": file_active
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                window.location.href = response.url;
            },
            fail: function (response) {
                toastr.error('エラーが発生しました', 'ファイルをダウンロード');
            }
        })
    });

    $(".offer-close-btn, .offer-done-btn").on('click', function(e) {
        e.stopPropagation();
        let status;
        if ($(this).is('.offer-close-btn')) {
            status = '7';
        } else if ($(this).is('.offer-done-btn')){
            status = '8';
        }
        let offer_id = $(this).parents('.messenger__item-menu').data('offer-id');

        if (offer_id && status) {
            $.ajax({
                type: "POST",
                url: '/messenger/update_offer/',
                data: {
                    'status': status,
                    'offer_id': offer_id
                },
                success: function (data) {
                    // toastr.success('完了いたしました!');
                    window.location.href = window.location.pathname + '?is_done=1';
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                }
            });
        }
    });

    if (auto_open === 'true') {
        $('.btn-create-offer a').click()
    }
});


function next_to_carousel(to_file) {
    if ($('.' + to_file).length > 0) {
        $('.carousel-file').addClass('hide');
        $('.' + to_file).removeClass('hide');
        $('.' + to_file).find('.messenger-carousel').each(function () {
            $(this).find('.messenger-carousel-for').slick("getSlick").refresh();
        });
    }
}


function get_offer_file(offer_id, active_offer, file_active, scroll=true) {
    $.ajax({
        type: "POST",
        datatype: 'json',
        url: "/direct/load_file_offer",
        data: {
            'offer_id': offer_id
        },
        success: function (data) {
            hide_other_chatbox();
            initCarousel(data.carosel_html);
            let active_offer = $('#offer-' + offer_id);
            if(scroll) {
                let top_length = active_offer[0].offsetTop;
                $('.all-message').mCustomScrollbar('scrollTo', top_length)
            }

            $('.offer-variation-choosed .messenger-carousel-item__type').off().on('click', function () {
                handleClickVersion($(this));
            });
            active_carousel();
            if (file_active === 'active-plan') {
                $('.next-to-plan').trigger('click')
            } else if (file_active === 'active-contract') {
                $('.next-to-contract').trigger('click')
            } else if (file_active === 'active-bill') {
                $('.next-to-bill').trigger('click')
            }

            // choose plan
            choose_plan();
            accept_contract();
            payment_bill();
            upload_contract();
            upload_plan();
            upload_bill();
        }
    })
}


function loadMessage(offer_ids) {
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/top/load_message_product",
        async: false,
        data: {
            'offer_ids': offer_ids,
        },
        success: function (data) {
            let len_offers = offer_ids.length;
            let user_id = data.user_id.toString();
            hide_other_chatbox();
            initCarousel(data.carosel_html);
            $(".messenger__column-right").append("<div class='all-message'></div>");
            let top_carousel = 0;

            for (i = 0; i < len_offers; i++) {
                $(".all-message").append("<div class='offer-" + offer_ids[i] + "' id='offer-" + offer_ids[i] + "' data-user=${user_id}><div class=\"messenger-detail\" ></div></div>");
                let active_offer = $('.offer-' + offer_ids[i]);
                active_offer.find(' .messenger-detail').html(data.htmls[i]);
                initCustomScrollbar();
                if (is_pc) {
                    if (!active_offer.find('.go-to-project').length) {
                        top_carousel = $('.carosel-pdf').get(0).offsetTop;
                        $('.all-message').css('margin-top', top_carousel);
                    } else {
                        top_carousel = $('.carosel-pdf').get(0).offsetTop;
                        $('.all-message').find('.messenger-detail-content').css('margin-top', top_carousel / 3);
                    }
                    let max_height = parseInt($('.messenger-detail-content').css('max-height'));
                    $('.messenger-detail-content').css('max-height', max_height - top_carousel);
                }

                //send_button
                active_offer.find(' .messenger-detail__input textarea').on('keyup', function (e) {
                    e.stopPropagation();
                    $(this).height('inherit');
                    var height = parseInt(this.scrollHeight)
                        + parseInt($(this).css('border-top-width'))
                        - parseInt($(this).css('padding-top'))
                        + parseInt($(this).css('border-bottom-width'))
                        - parseInt($(this).css('padding-bottom'));
                    if ($(this).val().length) {
                        if (height < 38) {
                            height += 20
                        }
                        $(this).parent().find('.messenger-detail__button-send .button--disabled').removeClass('button--disabled');
                        $(this).parents('.messenger-detail__input').height(height + 'px');
                        $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom');

                    } else {
                        $(this).parent().find('.button.button--text.button--text-primary').addClass('button--disabled');
                        $(this).parents('.messenger-detail__input').height('38px');
                    }
                });

                //create_message
                active_offer.find('.messenger-detail__button-send').off().on('click', function (e) {
                    e.stopPropagation();
                    let messageContent = active_offer.find('.messenger-detail__input textarea').val();
                    if (messageContent !== '') {
                        let offer_id = $(this).parents('.messenger-director__list').data('offer');
                        let pin_active = active_offer.find('.video-comment-input-pin').hasClass('active');
                        let variation_id, item_active, file_active;
                        if (pin_active === true) {
                            $('.messenger-carousel-item').each(function () {
                                if ($(this).attr('aria-hidden') === 'false') {
                                    variation_id = $(this).data('variation');
                                    item_active = $(this).find('.messenger-carousel-file').not('.hide');
                                    file_active = item_active.data('type');
                                    return false
                                }
                            })
                        }

                        let upload_final_elements = active_offer.find('#upload_final_product-' + offer_id);
                        if (upload_final_elements.length > 0 && upload_final_elements[0].files.length) {
                            data = new FormData();
                            data.append('message', messageContent);
                            data.append('offer_id', offer_id);
                            data.append('file', active_offer.find('#upload_final_product-' + offer_id)[0].files[0]);
                            $.ajax({
                                type: "POST",
                                async: false,
                                contentType: false,
                                processData: false,
                                cache: false,
                                data: data,
                                url: '/product_message/create',
                                success: function (data) {
                                    active_offer.find('.messenger-detail__input textarea').val('');
                                    active_offer.find('.messenger-detail__button-send a.button--text-primary').not('.button--disabled').addClass('button--disabled');
                                    active_offer.find('.selected_file').html('');
                                    active_offer.find('#upload_final_product-' + offer_id).val('');
                                }
                            });
                        } else {
                            $.ajax({
                                type: "POST",
                                data: {
                                    'message': messageContent,
                                    'offer_id': offer_id,
                                    'variation_id': variation_id,
                                    'type': file_active
                                },
                                url: '/product_message/create',
                                success: function (data) {
                                    active_offer.find('.messenger-detail__input textarea').val('');
                                    active_offer.find('.messenger-detail__button-send a.button--text-primary').not('.button--disabled').addClass('button--disabled');
                                }
                            });
                        }
                    }
                });


                active_offer.find('#upload_final_product-' + offer_ids[i]).on('change', function () {
                    if ($(this)[0].files.length) {
                        for (i = 0; i < $(this)[0].files.length; i++) {
                            let node = document.createElement("div");
                            let textnode = document.createTextNode($(this)[0].files[i].name);
                            node.append(textnode);
                            active_offer.find('.selected_file').append(node)
                        }
                    } else {
                        active_offer.find('.selected_file').html('');
                    }
                });

                // upload plan
                let message = active_offer.find('.messenger-director__item-mess');
                $.each(message, function (i, v) {
                    if (!$(v).is('.align-center')) {
                        let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                        v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                    }
                });

                active_offer.find('.upload--plan').off().on('click', function () {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $('#messenger-popup-plan').attr('data-offer', offer_id);
                    $('#offer-active').attr('value', offer_id);
                    $('#messenger-popup-plan').modal()
                });

                //create project
                active_offer.find('.create--product').off().on('click', function () {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $('#messenger-popup-product').attr('data-offer', offer_id);
                    $('#offer-id').attr('value', offer_id);
                    $('#messenger-popup-product').modal()
                });

                //upload bill
                active_offer.find('.upload--bill').off().on('click', function () {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $('#messenger-popup-bill').attr('data-offer', offer_id);
                    $('#messenger-popup-bill').find('.offer-active').attr('value', offer_id);
                    $('#messenger-popup-bill').modal()
                });

                active_offer.find('.show-upload-contact').off().on('click', function () {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $('#messenger-popup-contact').attr('data-offer', offer_id);
                    $('#offer').attr('value', offer_id);
                    $('#messenger-popup-contact').modal()
                });

                // pin variation
                initPinTimeButton();

                // upload contract
                $('#upload_contact-'+ offer_ids[i]).off().on('change', function (e) {
                    e.preventDefault();
                    if ($(this)[0].files.length) {
                        active_offer.find('.selected_file').html($(this)[0].files[0].name);
                        active_offer.find('.upload-contact').removeClass('button--disabled hide');
                        active_offer.find('.upload-contact').off().on('click', function (e) {
                            e.preventDefault();
                            active_offer.find('.upload-contact').addClass('button--disabled hide');
                            var form = active_offer.find('.form-upload_contact');
                            form.off().on('submit', function (e) {
                                e.preventDefault();
                                var url = form.attr('action');
                                var formData = new FormData(form[0]);
                                $.ajax({
                                    type: "POST",
                                    url: url,
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                                        // toastr.info('アップロード中‥');
                                    },
                                    success: function (data) {
                                        if (data.status === 'success') {
                                            console.log('upload successly contact');
                                            // toastr.success('アップロードいたしました。');
                                            active_offer.find('.infor-contact').remove();
                                            active_offer.find('.selected_file').html('');
                                            active_offer.find('.upload-contact').addClass('button--disabled hide');
                                            active_offer.find('.show--contract').removeClass('hide');
                                            get_offer_file(data.offer_id, active_offer, 'active-contract');
                                        }
                                    },
                                });
                            });
                            form.submit();
                        })
                    } else {
                        active_offer.find('.selected_file').html('');
                        active_offer.find('.upload-contact').addClass('button--disabled hide');
                    }
                });

                // submit plan
                active_offer.find('.choose-file').off().on('click', function (e) {
                    e.stopPropagation();
                    $('#upload_plan-' + offer_ids[i]).val();
                    active_offer.find('.selected_file_plan').html('');
                    active_offer.find('.submit-plan').addClass('button--disabled hide');
                });

                $('#upload_plan-'+ offer_ids[i]).off().on('change', function (e) {
                    if ($(this)[0].files.length) {
                        for (i = 0; i < $(this)[0].files.length; i++) {
                            let node = document.createElement("div");
                            let textnode = document.createTextNode($(this)[0].files[i].name);
                            node.append(textnode);
                            active_offer.find('.selected_file_plan').append(node);
                        }
                        active_offer.find('.submit-plan').removeClass('button--disabled hide');
                        active_offer.find('.submit-plan').off().on('click', function (e) {
                            e.preventDefault();
                            active_offer.find('.submit-plan').addClass('button--disabled hide');
                            var form = active_offer.find('.create_plan');
                            form.off().on('submit', function (e) {
                                e.preventDefault();
                                var url = form.attr('action');
                                var formData = new FormData(form[0]);
                                $.ajax({
                                    type: "POST",
                                    url: url,
                                    data: formData,
                                    processData: false,
                                    contentType: false,
                                    beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                                        // toastr.info('アップロード中‥');
                                    },
                                    success: function (data) {
                                        if (data.status === 'success') {
                                            console.log('upload successly contact');
                                            // toastr.success('アップロードいたしました。');
                                            if (data.is_ready === 'ready') {
                                                active_offer.find('.selected_file_plan').html('');
                                                get_offer_file(data.offer_id, active_offer, 'active-plan');
                                            }
                                            else {
                                                setTimeout(function () {
                                                    window.location.href = data.url;
                                                }, 2000)
                                            }
                                        }
                                    }
                                });
                            });
                            form.submit();
                        })
                    } else {
                        active_offer.find('.selected_file_plan').html('');
                        active_offer.find('.submit-plan').addClass('button--disabled hide');
                    }
                });
            }

            //check seen message
            $('.messenger-detail__input textarea').on('click', function () {
                if ($(this).parents('.messenger-director__list').hasClass('not-seen')) {
                    let offer_id = $(this).parents('.messenger-director__list').data('offer');
                    $.ajax({
                        type: "POST",
                        datatype: "json",
                        url: "/product_message/update_seen",
                        data: {
                            'offer_id': offer_id,
                        },
                        success: function () {
                            console.log('update seen successful');
                        }
                    })
                }
            });

            // download file
            $('.messenger-director__item-info').find('.fa-download').off().on('click', function (e) {
                e.stopPropagation();
                let offer_id = $(this).attr('data-offer');
                $.ajax({
                    type: "GET",
                    url: "/top/get_file_download_link",
                    data: {
                        "comment_id": offer_id,
                        "type": 'offer_product'
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        window.location.href = response.url;
                    },
                    fail: function (response) {
                        toastr.error('エラーが発生しました', 'ファイルをダウンロード');
                    }
                })
            });

            $('.offer-variation-choosed .messenger-carousel-item__type').off().on('click', function () {
                handleClickVersion($(this));
            });

            $('.go-to-project, .go-to-setting-project').each(function () {
                $(this).off().on('click', function (e) {
                    e.stopPropagation();
                })
            });

            // choose plan
            choose_plan();

            // accept contract
            accept_contract();
            payment_bill();

            // upload pdf
             upload_contract();
             upload_plan();
             upload_bill();
            //
            active_offer();

            active_carousel();

            url_string = window.location.href;
            var url = new URL(url_string);
            var offer_active = url.searchParams.get("offer");
            if (offer_active) {
                $("#offer-" + offer_active).ready(function () {
                    let active_offer = $("#offer-" + offer_active);
                    get_offer_file(offer_active, active_offer, '');
                })
            }

            if(!is_pc) {
                if ($('.carosel-pdf .messenger-director__item_carousel').children().length) {
                    $('.all-message .messenger-detail .messenger-detail-content')[0].style.height = '175px';
                } else {
                    $('.all-message .messenger-detail .messenger-detail-content')[0].style.height = 'calc(100vh - 200px)';
                }
            }
        }
    })
}


function initCustomScrollbar() {
    $('.custom-scrollbar').mCustomScrollbar({
        theme: 'minimal-dark',
        mouseWheelPixels: 50,
        scrollEasing: 'linear',
        scrollInertia: 0
    });

    $('.custom-scrollbar-horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });

    $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom', {
        scrollEasing: 'ease'
    });
}


function choose_plan() {
    $('.choose--plan').off().on('click', function () {
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        let variation_active = $(this).parents('.messenger-carousel-item').data('variation');
        let active_offer = $('#offer-' + offer_id);
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/direct/update_plan_status",
            data: {
                'offer_id': offer_id,
                'variation_id': variation_active
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.status === 'success') {
                    $('#choose-plan-popup').modal();
                    $('#choose-plan-popup').on('hidden.bs.modal', function (e) {
                        get_offer_file(offer_id, active_offer, '')
                    });
                }
            }
        })
    });
}


function accept_contract() {
    $('.accept-contact').off().on('click', function () {
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        let active_offer = $('#offer-' + offer_id);
        data = {
            'offer_id': offer_id
        };
        $.ajax({
            type: "POST",
            url: '/direct/accept_contract',
            data: data,
            success: function (data) {
                if (data.status === 'success') {
                    $("#accept-contact-popup").modal();
                    $('#accept-contact-popup').on('hidden.bs.modal', function (e) {
                        get_offer_file(offer_id, active_offer, 'active-contract')
                    });
                }
            }
        })
    });
}


function payment_bill() {
    $('.payment--bill').off().on('click', function (e) {
        e.preventDefault();
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/direct/check_payment",
            data: {
                'offer_id': offer_id,
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.status === 'success') {
                    if (response.card === 'no_card') {
                        $('#modal-add-card').find('.link-add--card').attr('href', response.url);
                        $('#modal-add-card').modal()
                    }
                    if (response.card === 'has_card') {

                        $('#modal-choose--card').find('.total-bill').html(response.total + ' 円');
                        $('#modal-choose--card').find('.card-select-container').remove();
                        $('#modal-choose--card').find('.messenger-popup__form').prepend(response.cards_html);
                        $('#modal-choose--card').find('.messenger-popup__form .InputGroup input[type=radio]').on('change', function () {
                            if ($(this).is(':checked')) {
                                $(this).parents('.messenger-popup__form').find('button#payment_submit').removeClass('button--disabled');
                            }
                        });
                        $('#modal-choose--card').on('hidden.bs.modal', function () {
                            $(this).find('button#payment_submit').addClass('button--disabled');
                        });
                        $('#modal-choose--card').on('click', 'button#payment_submit', function () {
                            let offer_id = response.offer_id;
                            let card_id = $(this).parents('#modal-choose--card').find('.InputGroup input[type=radio]:checked').get(0).value;

                            $.ajax({
                                url: "/accounts/payment/charge",
                                type: "POST",
                                data: {
                                    "card_id": card_id,
                                    "offer_id": offer_id
                                },
                                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                    console.log(response);
                                    if (response.code === 302) {
                                        window.location.replace(response.url_redirect);
                                    } else if (response.code === 500) {
                                        toastr.error(response.message);
                                    } else {
                                        // toastr.success("Charge success!");
                                        $('#modal-choose--card').modal('hide');
                                        $('#payment-success-popup').modal();
                                        $('#payment-success-popup').on('hidden.bs.modal', function (e) {
                                            window.location.href = response.url_project;
                                        });
                                        window.open(response.receipt_url, '_blank');
                                    }
                                },
                                error: function (response) {
                                    toastr.warning("Charge failed!");
                                }
                            });
                        });
                        $('#modal-choose--card').modal()
                    }
                }
            }
        })
    });
}


function active_carousel() {
    $('.button-show').each(function () {
        $(this).off().on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            let offer_id = $(this).parents('.messenger-director__list').data('offer');
            let active_offer = $('#offer-' + offer_id);
            let file_active = '';
            if ($(this).hasClass('show-plan')) {
                file_active = 'active-plan';
            } else if ($(this).hasClass('show-contract')) {
                file_active = 'active-contract';
            } else if ($(this).hasClass('show-bill')) {
                file_active = 'active-bill';
            }
            get_offer_file(offer_id, active_offer, file_active, false)
        })
    });
}


function go_to_next_file() {
    $('.next-to-contract').on('click', function () {
        if ($('.carousel--contract').length > 0) {
            next_to_carousel('carousel--contract');
        } else {
            next_to_carousel('carousel--plan');
        }
    });

    $('.next-to-bill').on('click', function () {
        if ($('.carousel--bill').length > 0) {
            next_to_carousel('carousel--bill');
        } else {
            next_to_carousel('carousel--contract');
        }
    });

    $('.next-to-plan').on('click', function () {
        next_to_carousel('carousel--plan');
    });

}

function initCarousel(html) {
    $('.carosel-pdf').removeClass('hide');
    $('.carosel-pdf').html(html);
    $('.carosel-pdf').find('.messenger-carousel').each(function () {
        $(this).find('.messenger-carousel-for').slick({
            dots: true,
            infinite: false,
            arrows: false,
            speed: 300,
            slidesToShow: 1,
            slidesToScroll: 1,
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        infinite: true,
                        dots: true
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });
    });
}


function upload_contract() {
    $('.upload__contract').off().on('click', function () {
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        bootbox.confirm({
            title: "契約書を送る",
            message: "<input type='file' class='upload-offer-contract' data-offer='" + offer_id + "' accept=\"application/pdf\"/>",
            buttons: {
                confirm: {
                    label: 'アップロード',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'キャンセル',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if (result) {
                    let input = $('input.upload-offer-contract');
                    if (input.length && input[0].files.length) {
                        let data = new FormData();
                        data.append('files', input[0].files[0]);
                        data.append('offer-active', offer_id);
                        $.ajax({
                            type: "POST",
                            url: "/direct/upload_contact",
                            data: data,
                            cache: false,
                            processData: false,
                            contentType: false,
                            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                                // toastr.info('アップロード中‥');
                            },
                            success: function (data) {
                                if (data.status === 'success') {
                                    console.log('upload successly contact');
                                    let active_offer = $('.offer-' + data.offer_id);
                                    // toastr.success('アップロードいたしました。');
                                    get_offer_file(data.offer_id, active_offer, 'active-contract');
                                }
                            },
                            error: function () {
                                toastr.error('エラーが発生しました', '契約書を送る');
                            }
                        })
                    } else {
                        toastr.error('ファイルを選択してください。', '契約書を送る');
                    }
                }
            }
        }).find('.modal-content').css({
            'margin-top': function () {
                var w = $(window).height();
                var b = $(".modal-dialog").height();
                var h = (w - b) / 3;
                return h + "px";
            }
        });
    })
}


function upload_plan() {
    $('.upload__plan').off().on('click', function () {
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        let type = '';
        type = $(this).data('plan');
        let message = "<input type='file' class='upload-offer-plan' data-offer='" + offer_id + "' multiple accept=\"application/pdf\"/>";
        if (type === 'one'){
            message = "<input type='file' class='upload-offer-plan' data-offer='" + offer_id + "' accept=\"application/pdf\"/>"
        }
        bootbox.confirm({
            title: "お見積もりを送る",
            message: message,
            buttons: {
                confirm: {
                    label: 'アップロード',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'キャンセル',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if (result) {
                    let input = $('input.upload-offer-plan');
                    if (input.length && input[0].files.length) {
                        let data = new FormData();
                        $.each(input[0].files, function (i, file) {
                            data.append('files[]', file);
                        });
                        data.append('offer-active', offer_id);
                        data.append('type', type);
                        $.ajax({
                            type: "POST",
                            url: "/direct/upload_plan",
                            data: data,
                            cache: false,
                            processData: false,
                            contentType: false,
                            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                                // toastr.info('アップロード中‥');
                            },
                            success: function (data) {
                                if (data.status === 'success') {
                                    console.log('upload successly contact');
                                    toastr.success('アップロードいたしました。');
                                    let active_offer = $('.offer-' + data.offer_id);
                                    get_offer_file(data.offer_id, active_offer, 'active-plan');
                                }
                            },
                            error: function () {
                                toastr.error('エラーが発生しました', 'お見積もりを送る');
                            }
                        })
                    } else {
                        toastr.error('ファイルを選択してください。', 'お見積もりを送る');
                    }
                }
            }
        }).find('.modal-content').css({
            'margin-top': function () {
                var w = $(window).height();
                var b = $(".modal-dialog").height();
                var h = (w - b) / 3;
                return h + "px";
            }
        });
    })
}


function upload_bill() {
    $('.upload__bill').off().on('click', function () {
        let offer_id = $(this).parents('.messenger-carousel').data('offer');
        $('#messenger-popup-bill').attr('data-offer', offer_id);
        $('#messenger-popup-bill').find('.offer-active').attr('value', offer_id);
        $('#messenger-popup-bill').modal()
    });

    $('#messenger-popup-bill').on('hidden.bs.modal', function (e) {
        $('#upload-bill-file').val('');
        $('#project-amount').val('');
        $('#submit-upload-bill').addClass('button--disabled');
        $('#messenger-popup-bill').find('.bill-file').html('');
    });

    $('#upload-bill-file').off().on('change', function () {
        if ($(this)[0].files.length) {
            $('#upload_bill').find('.bill-file').html($(this)[0].files[0].name);
            $('#submit-upload-bill').removeClass('button--disabled');

        } else {
            $('#upload_bill').find('.bill-file').html('');
            $('#submit-upload-bill').addClass('button--disabled');
        }
    });

    $('#submit-upload-bill').off().on('click', function (e) {
        const form = $('#upload_bill');
        if (form.valid()) {
            $('#submit-upload-bill').addClass('button--disabled');
            form.off().on('submit', function (e) {
                e.preventDefault();
                var url = form.attr('action');
                var formData = new FormData(form[0]);
                $.ajax({
                    type: "POST",
                    url: url,
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        // toastr.info('アップロード中‥');
                    },
                    success: function (data) {
                        if (data.status === 'success') {
                            console.log('upload successly contact');
                            let active_offer = $('.offer-' + data.offer_id);
                            toastr.success('アップロードいたしました。');
                            $('#messenger-popup-bill').find('.close-modal').click();
                            get_offer_file(data.offer_id, active_offer, 'active-bill');
                        }
                    },
                });
            });
            form.submit();
            return true;
        }
    });
}

function active_offer() {
    $('.messenger-detail').each(function () {
        $(this).off().on('click', function (e) {
            let offer_id = $(this).find('.messenger-director__list').data('offer');
            let offer_carousel = $('.messenger-director__item_carousel').data('offer');
            if (offer_id !== offer_carousel) {
                let active_offer = $('#offer-' + offer_id);
                let file_active = '';
                get_offer_file(offer_id, active_offer, file_active);
            }
        })
    });
}

function hide_other_chatbox() {
    let left_column = $('.col-md-5.col-sm-5.messenger__column-left');
    left_column.find('.messenger__item:not(.messenger__item--selected), .btn-create-offer').addClass('hide');
    left_column.find('.messenger__list').css('height', 'auto');
    $('.col-md-7.col-sm-7.messenger__column-right').removeClass('hide');
    $('.project-progress-action, .show_done').addClass('hide');
}

function show_chatbox_list() {
    $('.carosel-pdf').addClass('hide');
    let left_column = $('.col-md-5.col-sm-5.messenger__column-left');
    left_column.find('.messenger__item:not(.messenger__item--selected), .btn-create-offer').removeClass('hide');
    left_column.find('.messenger__list')[0].style.height = 'calc(100vh - 75px)';

    $('.col-md-7.col-sm-7.messenger__column-right').addClass('hide');
    $('.project-progress-action, .show_done').removeClass('hide');
}


function handleClickVersion(target) {
    let file_item_list = target.parents('.messenger-carousel-item').find('.variation---choosed');
    let num_of_video_item = file_item_list.length;
    if (num_of_video_item > 1) {
        let self_index = target.parents('.variation---choosed').index();
        target.parents('.variation---choosed').addClass('hide');
        let next_index = (self_index + 1) % num_of_video_item;
        let next_file_item_component = target.parents('.messenger-carousel-item').find('.variation---choosed').eq(next_index);
        next_file_item_component.removeClass('hide');
    }
}


function initPinTimeButton() {
    $('.video-comment-input-pin').off().on('click', function (e) {
        $(this).toggleClass('active')
    });
}
