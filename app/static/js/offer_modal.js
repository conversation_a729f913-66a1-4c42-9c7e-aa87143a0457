var myDropzone;
var key_file = "";
var real_name = "";
var added_files= [];
var is_delete_file = false;
var is_uploading = false;
var svg_upload = `<svg class="icon_upload_svg" width="65" height="64" viewBox="0 0 65 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.233 26.6666H24.473V39.9999C24.473 41.4666 25.673 42.6666 27.1397 42.6666H37.8063C39.273 42.6666 40.473 41.4666 40.473 39.9999V26.6666H44.713C47.0863 26.6666 48.2863 23.7866 46.6063 22.1066L34.3663 9.86661C33.3263 8.82661 31.6463 8.82661 30.6063 9.86661L18.3663 22.1066C16.6863 23.7866 17.8597 26.6666 20.233 26.6666ZM13.833 50.6666C13.833 52.1333 15.033 53.3333 16.4997 53.3333H48.4997C49.9663 53.3333 51.1663 52.1333 51.1663 50.6666C51.1663 49.1999 49.9663 47.9999 48.4997 47.9999H16.4997C15.033 47.9999 13.833 49.1999 13.833 50.6666Z" fill="#F0F0F0"/>
</svg>`

function uploadFileS3Offer(file, file_dom, file_data) {
    file_dom.find('.determinate').css('width', '0%');
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            file_data.key = data.presigned_post.fields["key"];
            added_files.push(file_data)
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s');
                    file_dom.find('.determinate').css('width', percentComplete);
                    if ($('.upload-button-wrapper') && $('.upload-button-wrapper').hasClass("clicked")){
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        file_dom.find('.determinate').css('width', "100%");
                        is_uploading = false
                    }
                }
            };
            is_uploading = true;
            xhr.send(postData);
        }
    })
}

function dragDropSearch() {
    if ($('#create-offer-upload-form').length > 0) {
        var zdrop = [];
        Dropzone.autoDiscover = false;
        if (csrf) {
            $("#create-offer-upload-form").append(csrf);
        }

        var previewNode = $('.modal-create-edit-offer .account_upload-file .mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();

        myDropzone = new Dropzone("#create-offer-upload-form", {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            previewsContainer: '.modal-create-edit-offer .mattach-previews-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#create-offer-upload-form',
            dictDefaultMessage: svg_upload + '\n' + '<p>ファイルを選択</p>'
        });

        myDropzone.on("maxfilesexceeded", function (file) {
            myDropzone.removeAllFiles();
            myDropzone.addFile(file);
        });

        $(window).on('dragover', function (e) {
            var dt = e.originalEvent.dataTransfer;
            if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                $('.mupload-drop').show();
            }
        });

        myDropzone.on('drop', function (e) {
            $('.mupload-drop').hide();
        });

        $(window).on('drop', function (e) {
            $('.mupload-drop').hide();
        });

        myDropzone.on('removedfile', function (file) {
            added_files = added_files.filter(function(e) {
                return e.uuid != file.upload.uuid;
            })
        });

        myDropzone.on('addedfile', function (file, e) {
            $('.file_offer').remove();
            let file_dom = $(file.previewElement);
            let fileData = {'real_name': file.name, 'uuid': file.upload.uuid}
            let file_preview = $('#modal-create-edit-offer .mattach-preview-container-form').find(".mcommment-file__name-form");
            for (let i = 0; i < file_preview.length; i++) {
                $(file_preview[i]).text('');
                $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name);
                break;
            }
            $("#modal-create-edit-offer .mattach-info-file").addClass("hide");
            if ($("#modal-create-edit-offer .mattach-template").length == 2) {
                $($("#modal-create-edit-offer .mattach-template")[0]).remove();
            }
            uploadFileS3Offer(file, file_dom, fileData);
        });

        $('#modal-create-offer').on('hidden.bs.modal', function () {
            myDropzone.removeAllFiles();
        });

        $('#modal-edit-offer').on('hidden.bs.modal', function () {
            myDropzone.removeAllFiles();
        });

        $(".mattach-info-file .icon--sicon-close").on("click", function() {
            $(".mattach-info-file").addClass("hide");
            is_delete_file = true;
        })
    }
};

function resetForm(modal_create) {
    modal_create.on("hidden.bs.modal", function () {
        $('.dz-preview').remove();
        $('.upload_file_container').remove();
        $('.error').removeClass('error');
        $('#input-scenes').val('');
        $('#input-quantity').val('');
        $('#id_data_format').val('');
        $('#budget').val('');
        $('#id_tax_amount').html('0');
        $('#id_total_amount').html('0');
        $('#input-message').val('');
        myDropzone.files = [];
        $('#id_contract').attr('disabled', false);
        let array_values = ['input-quantity', 'id_data_format', 'input-message'];
        for (i = 0; i < array_values.length; i++) {
            $('#' + array_values[i]).attr('readonly', false);
        }
        
        let endDate = new Date().addDays(5);
        $("#deadline-date").val(moment(endDate).format("YYYY/M/D"));
        // $('#deadline-date').datepicker('setDate',  moment(endDate).format('YYYY/M/D'));
        $('.select-deadline_time').val('10:00');
        $('#create-offer-upload').css('pointer-events', 'all');

        $('.file_offer').remove();

        $('.accordion-heading').attr('data-toggle', 'collapse');
        $('.mupload-drop').hide();
        $('.mattach-info-file .file-name').text('');
        $('.mattach-info-file').addClass('hide');
        $('#modal-edit-offer .account_upload-file.mattach, .mattach-info-file .icon--sicon-close').removeClass('hide')

    });
   modal_create.find('.modal-content').on('click', function () {
       $('.mupload-drop').hide();
   });

    $('.close-modal-over').on('click', function () {
        $('#over-budget').modal('hide');
        $('#modal-reward').modal('hide');
    })

}

function actionForm(modal_create) {
    var typingTimer;                //timer identifier
    var doneTypingInterval = 300;

    modal_create.find('#budget').on('keyup', function () {
        let tmp_reward = parseFloat(this.value);
        if (tmp_reward < 0 || isNaN(tmp_reward)) {
            $(this).val('');
            tmp_reward = 0;
        }
        let tax_amount = Math.round(tmp_reward * 0.1);
        let total_amount = Math.round(tmp_reward * 1.1);
        modal_create.find('#id_tax_amount').html((tax_amount).toLocaleString(undefined));
        modal_create.find('#id_total_amount').html((total_amount).toLocaleString(undefined));
        clearTimeout(typingTimer);
        typingTimer = setTimeout(doneTyping, doneTypingInterval);
    });
    
    modal_create.find('#budget').on('keydown', function () {
        clearTimeout(typingTimer);
    });

    function doneTyping () {
        let reward = $('#id_total_amount').text();
        let project_id = modal_create.attr('data-project');
        let data_form = new FormData();
        data_form.append('project_id', project_id);
        data_form.append('reward', reward);
        
        $.ajax({
            type: "POST",
            contentType: false,
            processData: false,
            cache: false,
            url: "/check_offer_creator",
            data: data_form,
            success: function (data) {
                if (data.budget === 'over' && modal_create.find('#budget').val() !== '') {
                    // buttom_dom.addClass('disabled');
                    // $("#over-budget").modal();
                    $(document).find('#budget').addClass('error');
                    if($(document).find('.error-budget')){
                        $(document).find('.error-budget').remove()
                        $(document).find('#budget').parent().addClass('error-input')
                        $(`<span class="error-input error-budget">${data.maxBudget <=0 ? '予算がありません。' : `予算オーバーです。${data.maxBudget}円以内に設定してください 。`}</span>`).insertAfter('#budget');
                    }
                } else {
                    //TODO check validate here
                    $(document).find('#budget').parent().removeClass('error-input')
                    $(document).find('#budget').removeClass('error');
                    $(document).find('.error-budget').remove();
                }
            },
            fail: function (data) {
                toastr.error(gettext('Something went wrong!'));
            },
            error: function(xhr, status, error) {
                var err = eval("(" + xhr.responseText + ")")
                alert(err.message);
            },
            complete: function () {
            }
        });
    }

    modal_create.find('#budget').on('focusin focusout', function(e){
        let val = $(this).val();
        if(e.type === 'focusin') {
            val = val.replaceAll(',','');
        } else {
            val = parseFloat(val.replaceAll(',',''));
            if(isNaN(val)){
                val = ''
            } else {
                val = val.toLocaleString(undefined);
            }
        }
        
        $(this).val(val);
    })

    modal_create.find('#budget').on('change', function () {
        $(this).trigger('keyup')
    });

    modal_create.find('#id_contract').on('change', function () {
        let contract = $('#id_contract option').filter(':selected').val();
        let type_contract;
        switch (contract) {
            case '4':
                type_contract = 'サウンドデザイナー';
                break;
            case '5':
                type_contract = 'オーディオエンジニア';
                break;
            case '6':
                type_contract = '声優・ナレーター';
                break;
            case '7':
                type_contract = 'ボーカリスト';
                break;
            case '8':
                type_contract = '演奏家';
                break;
            case '9':
                type_contract = 'ディレクター';
                break;
            default:
                type_contract = 'コンポーザー';
        }
        $('#id_type_contract').attr('value', type_contract);
    });

    $('#deadline-date').on('change', function () {
       let endDate = moment(new Date($('#deadline-date').val())).format('YYYY/MM/DD');
        let dates = $("#id_period_offer").val().split(" から ");
        let startDateString = dates[0];
        let endDateString = dates[1];
        if (moment(endDate) < moment(endDateString)) {
          $("#id_period_offer").val(
            `${startDateString} から ${startDateString} `
          );
        } else {
          $("#id_period_offer").val(`${startDateString} から ${endDate} `);
        }
    //    $('#id_period_offer').data('daterangepicker').setEndDate(endDate);
    });

}

function validateBudget() {
    $(document).on('input', '.modal-create-edit-offer #budget', function () {
        let valueBudget = this.value;
        this.value = valueBudget.replace(/\D/g, '')
    })
}

function addDataList(listDom, dataList) {
    let optionsField = '';
    for (i = 0; i < dataList.length; i++) {
        optionsField += '<option data-name="' + escapeHtml(dataList[i]) + '">' + escapeHtml(dataList[i]) + '</option>';
    }
    listDom.empty();
    listDom.append(optionsField);
}

function customSwitchPublic() {
    setTimeout(() => {
        let toggleDom = $(document).find('.modal-create-edit-offer .switch-checkbox-public #allow_subcontracting')
        let toggleStatus = toggleDom.is(':checked');
        // console.log("check toggle", toggleDom);
        if(toggleStatus) {
            toggleDom.parents('.switch-checkbox-public').addClass('checked');
        } else {
            toggleDom.parents('.switch-checkbox-public').removeClass('checked');
        }
    }, 1000);

    $(document).on('change', '.modal-create-edit-offer .switch-checkbox-public #allow_subcontracting', function(e){
        if($(this).is(':checked')) {
            $(this).parents('.switch-checkbox-public').addClass('checked');
        } else {
            $(this).parents('.switch-checkbox-public').removeClass('checked');
        }
    })

    setTimeout(() => {
        let toggleDom = $(document).find('#modal-upload-contract-plan .switch-checkbox-public #id_public')
        let toggleStatus = toggleDom.is(':checked');
        // console.log("check toggle", toggleDom);
        if(toggleStatus) {
            toggleDom.parents('.switch-checkbox-public').addClass('checked');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_active_share').removeClass('hide');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_inactive_share').addClass('hide');
        } else {
            toggleDom.parents('.switch-checkbox-public').removeClass('checked');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_active_share').addClass('hide');
            toggleDom.parents('.switch-checkbox-public').find('svg.svg_inactive_share').removeClass('hide');
        }
    }, 1000);

    $(document).on('change', '#modal-upload-contract .switch-checkbox-public #id_public', function(e){
        if($(this).is(':checked')) {
            $(this).parents('.switch-checkbox-public').addClass('checked');
            $(this).parents('.switch-checkbox-public').find('svg.svg_active_share').removeClass('hide');
            $(this).parents('.switch-checkbox-public').find('svg.svg_inactive_share').addClass('hide');
        } else {
            $(this).parents('.switch-checkbox-public').removeClass('checked');
            $(this).parents('.switch-checkbox-public').find('svg.svg_active_share').addClass('hide');
            $(this).parents('.switch-checkbox-public').find('svg.svg_inactive_share').removeClass('hide');
        }
    })
}

$(document).ready(function () {
    validateBudget();
    customSwitchPublic();
});
