const MESSAGE_PER_LOAD = 30;
let is_loading_scene_comment = false;
let current_load_scene_comment = 0;
let total_scene_comment;

$(document).ready(function () {
    total_scene_comment = $('.pd-section--detail.pd-scene-title-detail').attr('data-total');
    $('.mmessage').last().addClass('load-lasted-message');
    scrollSceneComment();
    autoLoadMoreSceneComment();
});

function scrollSceneComment() {
    $('.pd-section--detail.pd-scene-title-detail .mmessage-list.mscrollbar').scroll(function () {
            if ($(this).scrollTop() < 500 && !is_loading_scene_comment) {
                ajaxLoadMoreSceneComment()
            }
        }
    )
}

function ajaxLoadMoreSceneComment() {
    let scene_id = $('.cscene__variation').first().attr('data-scene-id');
    if (current_load_scene_comment < total_scene_comment && !is_loading_scene_comment && scene_id) {
        current_load_scene_comment++;
        let last_message_id = $('.load-lasted-message').attr('data-message-id');
        is_loading_scene_comment = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/load_more_scene_comment",
            data: {
                'scene_id': scene_id,
                'offset': current_load_scene_comment,
                'last_message_id': last_message_id
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.pd-section--detail.pd-scene-title-detail .mmessage-list.mscrollbar').prepend(`<div class="load-more-loading"></div>`);
            },
            success: function (data) {
                is_loading_scene_comment = false;
                let target = $('.pd-section--detail.pd-scene-title-detail .mmessage-list.mscrollbar');
                target.prepend(data.html);
                $('.load-more-loading').remove();
                if (target.scrollTop() === 0 && current_load_scene_comment < total_scene_comment) {
                    target.animate({scrollTop: 200});
                }
                actionShowIconResolved();
                autoLoadMoreSceneComment();
            }
        });
    }
}

function autoLoadMoreSceneComment() {
    let countCurrentSceneComment = current_load_scene_comment === 0 ? MESSAGE_PER_LOAD : current_load_scene_comment * MESSAGE_PER_LOAD;
    if ($('.mmessage').length - $('.mmessage .mmessage-resolved').length < countCurrentSceneComment && $('.pd-section--detail.pd-scene-title-detail .mmessage-list.mscrollbar').length) {
        if (current_load_scene_comment < total_scene_comment && !is_loading_scene_comment) {
            ajaxLoadMoreSceneComment();
        }
    }
}
