var wavesurfer_arr = [];
var player = [];
$(document).ready(function () {
    $('#id_from_date').val()
    $('input#id_project').val(project_id);

    $('.form-datepicker .form-control').datepicker({
        format: 'yyyy/mm/dd',
        todayHighlight: true,
    });

    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    }

    let from_date = $('.form-datepicker.from_date_datepicker .form-control').val();
    if(! from_date.length){
        $('.form-datepicker.from_date_datepicker .form-control').datepicker('update', new Date());
    }

    let to_date = $('.form-datepicker.to_date_datepicker .form-control').val();
    if(!to_date.length){
        $('.form-datepicker.to_date_datepicker .form-control').datepicker('update', (new Date()).addDays(5));
    }

    $('.form-timepicker .form-control').datetimepicker({
        format: 'HH:mm',
        debug: false,
        stepping: 30
    });

    $('.messenger-form__confirm input[type="checkbox"]').on('click', function () {
        if ($(this).is(':checked')) {
            $('.messenger-form__action .button').removeClass('disabled');
        } else {
            $('.messenger-form__action .button').addClass('disabled');
        }
    });

    $('.messenger__audio-item, .messenger__creator-item').click(function () {
        let creator_info = $(this).find('.creator-info');
        if (creator_info.length) {
            $('.messenger__avatar img.messenger__avatar-img').removeClass('creator');
            let trading = creator_info.data('trading');
            $('.selected-creator').removeClass('selected-creator');
            $(this).addClass('selected-creator');
            $('.selected_creator').removeClass('hide');
            $('.messenger__info .messenger__name').html(creator_info.data('creator-name'));
            $('.messenger__info .messenger__work').html(creator_info.data('creator-role'));
            $('.messenger__info .messenger__info-url').attr('href', creator_info.data('detail-url'));
            $('.messenger__avatar img.messenger__avatar-img').attr('src', creator_info.data('avatar-url'));
            if (creator_info.data('avatar-url') === '/static/images/default-avatar-creator.png') {
                $('.messenger__avatar img.messenger__avatar-img').addClass('creator')
            }
            $(' textarea').val(creator_info.data('policy'));
            $('input#id_creator').val(creator_info.data('user-id'));
            $('.account__trade-item').removeClass('active');
            $('.account__trade-item[data-value=' + trading.toString() + ']').addClass('active')
        }
    });

    $('#id_contract').on('change', function () {
        let contract = $('#id_contract option').filter(':selected').val();
        let type_contract;
        switch (contract) {
            case '4':
                type_contract = 'サウンドデザイナー';
                break;
            case '5':
                type_contract = 'オーディオエンジニア';
                break;
            case '6':
                type_contract = '声優・ナレーター';
                break;
            case '7':
                type_contract = 'ボーカリスト';
                break;
            case '8':
                type_contract = '演奏家';
                break;
            default:
                type_contract = 'コンポーザー';
        }
        $('#id_type_contract').attr('value', type_contract)
    });

    $('.btn-offer-creator').on('click', function () {
        let from_date = $('input#id_from_date').val();
        let to_date = $('input#id_to_date').val();
        let time = $('input#start-time').val();
        let target = $('input#id_date');
        target.val(from_date + " - " + to_date + " " + time);
        target.parent().parent().find('input#id_time').val(time);
        target.parent().find('input#id_start_time').val(from_date);
        target.parent().find('input#id_deadline').val(to_date + " " + time);
    });

    if ($('.audio-player-waveform').length > 0) {
        wavesurfer_arr = [];
        player = [];

        $('.messenger__audio-item').each(function (i, item) {
            if (!$(this).is('.hide')) {
                initPlayer($(this), i);
            }
        });

        // Format time
        var formatTime = function (time) {
            return [
                Math.floor((time % 3600) / 60), // minutes
                ('00' + Math.floor(time % 60)).slice(-2) // seconds
            ].join(':');
        };
    }

    $('.messenger__audio-button').on('click', function () {
        if($(this).children().is('.show-more-creator')) {
            $('.messenger__creator-item.hide:lt(3)').each(function (i, e) {
                $(e).removeClass('hide');
            });
            if (!$('.messenger__creator-item.hide').length) {
                $(this).addClass('hide');
            }
        } else if ($(this).children().is('.show-more-audio')) {
            $('.messenger__audio-item.hide:lt(2)').each(function (i, e) {
                $(e).removeClass('hide');
                initPlayer($(e), player.length - 1);
            });
            if (!$('.messenger__audio-item.hide').length) {
                $(this).addClass('hide');
            }
        }
    });

    function initPlayer(target, i) {
        var $player = target.find('.audio-player-component');
        let item = target.find('.audio-player-waveform')[0];
        var wavesurfer = WaveSurfer.create({
            container: item,
            waveColor: '#a7a8a9',
            progressColor: '#36aac4',
            cursorColor: 'rgba(0,157,196,0.29)',
            barWidth: 3,
            barRadius: 3,
            cursorWidth: 3,
            barGap: 3,
            mediaControls: false,
            height: 80,
            responsive: true,
            hideScrollbar: true,
            partialRender: true,
            backend: 'MediaElement'
        });

        wavesurfer_arr[i] = wavesurfer;
        player[i] = $player;

        var link = $player.find('.audio-player-data').data('link');
        var title = $player.find('.audio-player-data').data('title');

        wavesurfer.load(link);
        $player.find('.audio-player-title').text(title);

        // Play on audio load
        wavesurfer.on('ready', function () {
            $player.find('.audio-player-current').text('00:00');
            $player.find('.audio-player-duration').text(formatTime(wavesurfer.getDuration()));
            // wavesurfer.play(); // Autoplay
            $player.find('.audio-player-control-playpause').off().on('click', function () {
                wavesurfer.playPause();
                // Update player
                $.each(player, function (index, item) {
                    item.removeClass('active');
                });
                $player.addClass('active');
                // Pause all other instances
                $.each(wavesurfer_arr, function (index, item) {
                    if (item !== wavesurfer) {
                        if (item.isPlaying()) {
                            item.pause();
                        }
                    }
                });
            });
        });

        // Check Playpause button
        wavesurfer.on('pause', function () {
            $player.find('.audio-player-control-playpause').removeClass('active');
        });

        wavesurfer.on('play', function () {
            $player.find('.audio-player-control-playpause').addClass('active');
        });

        // Display player time
        wavesurfer.on('audioprocess', function () {
            $player.find('.audio-player-current').text(formatTime(wavesurfer.getCurrentTime()));
        });

        wavesurfer.on('error', function (e) {
            console.warn(e);
        });

        wavesurfer.on('finish', function () {
            // Go to the next track on finish
        });
    }

    $('#id_file').on('change', function() {
        if($(this)[0].files.length) {
            $('.selected_file').html($(this)[0].files[0].name);
        } else {
            $('.selected_file').html('');
        }
    })

    $('#id_file').on('invalid', function() {
        $('.selected_file').html('ファイルを選択してください。');
    })

    $('#offerModal form input#id_tmp_reward').on('change',function () {
        let tmp_reward = parseFloat(this.value);
        if (tmp_reward < 0 || isNaN(tmp_reward)){
            $(this).val('');
        }
        let tax_amount = Math.round(parseFloat(this.value) * 0.1);
        let total_amount = Math.round(parseFloat(this.value) * 1.1);
        $(this).parents('form').find('.tax-amount span.tax-amount-amount#id_tax_amount').html((tax_amount).toLocaleString(undefined));
        $(this).parents('form').find('.tax-amount span.tax-amount-amount#id_total_amount').html((total_amount).toLocaleString(undefined));
        $(this).parents('form').find('input#id_reward').val(parseInt(total_amount));
    });

    $('#offerModal form input#id_tmp_reward').keyup(function () {
        $(this).trigger('change');
    });

    $("#offerModal").ajaxForm({
        beforeSend: function (data) {
            // toastr.info('アップロード中…');
            $('.upload-button-wrapper').css('display', 'flex');
            $('.upload-button-wrapper').addClass('clicked');
            $('.upload-button-wrapper .fill .process').css('width', '2%');
        },
        xhr: function () {
            let xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70;
                    $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function (data) {
            $('.upload-button-wrapper .fill .process').css('width', '100%');
            setTimeout(function () {
                // toastr.success('オファーしました。');
                $('.upload-button-wrapper').removeClass('clicked').addClass('success')
            }, 1000);
            setTimeout(function () {
                $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                $('.upload-button-wrapper .fill .process').css('width', '0');
                window.location.href = data.url;
            }, 2000);
        },
        fail: function (data) {
            toastr.error('エラーが発生しました');
        },
        error: function (data) {
            $('.upload-button-wrapper').css('display', 'None');
            $('.upload-button-wrapper').removeClass('clicked');
            $('.upload-button-wrapper .fill .process').css('width', '0');
            $("#over-budget").modal()
        }
    });
});
