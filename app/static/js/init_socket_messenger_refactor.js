if(typeof dict_chatsocket === 'undefined'){
    var dict_chatsocket = new Map();
}
var sk_protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
var socket_url = sk_protocol + window.location.host;
// let message_role = $('.pbanner').data('data-project-role');
var ROLE_ADMIN = 'admin';
var ROLE_CREATOR = 'creator';
var offer_role;
var messageReview = 'というオファーの評価が届いています。';


function initSocket(socket_id, tab = "") {
    if (window.localStorage.getItem("performance") || !window.localStorage.getItem("performance") && window.location.pathname.includes("/scene/") || !window.localStorage.getItem("performance") && window.location.search.includes("tab=messenger") || tab == "messenger") {
        let current_chatsocket = dict_chatsocket.get(socket_id);
        if (!current_chatsocket || current_chatsocket.readyState === '3') {
            current_chatsocket = new WebSocket(
                socket_url + '/ws/messenger/' + socket_id);
            dict_chatsocket.set(socket_id, current_chatsocket);

            current_chatsocket.onmessage = function (e) {
                let data = JSON.parse(e.data);
                let offer_id = data.event.offer_id;
                let target_offer;
                let role = user_role;
                let get_offer = $('.offer-' + offer_id);
                let type_message = data.event.type_message;
                let thread_name = data.event.thread_name;
                if (thread_name) {
                    thread_name = escapeHtml(thread_name)
                }
                sender_id = data.event.sender_id;

                let current_receiver = '';
                if (offer_id) {
                    if ($('.mitem.mactive').length > 0) {
                        current_receiver = $('.mitem.mactive').data('offer');
                    }

                    target_offer = $('.mitem[data-offer=' + offer_id + ']');

                } else if (type_message === 'project') {
                    offer_id = data.event.product_id;
                    current_receiver = $('.project-item.active').find('.pd-product-comment').attr('data-product-id');
                } else {
                    offer_id = data.event.scene_title_id;
                    current_receiver = $('.pd-scene-title-detail').attr('data-scene-title-id');
                }

                if (current_receiver) {
                    current_chatsocket = current_chatsocket.toString();
                }
                let infor_offer = $('.offer-' + offer_id + '_infor');
                let offer_socket = $('.mscene.mitem[data-offer=' + offer_id + ']');
                if (offer_socket.length) {
                    offer_role = offer_socket.attr('data-role-offer');
                }

                let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;

            switch (data.event.action) {
                case 'draft_message':
                    list_file_id = JSON.parse(data.event.list_file_id);
                    list_folder_id = JSON.parse(data.event.list_folder_id);
                    if(data.event.id_time === id_time || data.event.offer_id !== $(document).find('.mitem.mactive').attr('data-offer')){
                        return;
                    }
                    valInput = data.event.message;
                    if(!data.event.message && !Object.keys(list_file_id).length && !Object.keys(list_folder_id).length){
                        $(document).find('.mcomment-top').hide();
                        $(document).find('.mcomment-input-text').attr('type_input', '');
                        $(document).find('.mcomment-send.active').removeClass('active');
                        return;
                    }
                    $(document).find('.mcomment-bottom').trigger('click');
                    $(document).find('.mcomment-input-text').val(data.event.message);
                    $(document).find('.mcomment-input-text').focus();
                    var file_conatiner_dom = $(document).find('.mattach-previews');
                    file_conatiner_dom.empty();
                    for(var i = 0; i < data.event.file_name.length; i++ ){
                        file_conatiner_dom.append(`<div class="mattach-template collection-item item-template" data-total="" data-loaded="">
                        <div class="mattach-info" data-dz-thumbnail=""><div class="mcommment-file"><div class="progress"><div class="determinate" style="width: 100%; transition: all 1s ease 0s;" data-dz-uploadprogress=""></div></div>
                        <div class="mcommment-file__name" data-dz-name="">${data.event.file_name[i]}</div>
                        <div class="mcommment-file__delete" href="#!" data-dz-remove=""><span class="progress-text"></span><i class="icon icon--sicon-close"></i></div></div></div></div>`)
                    }
                    $(document).find('.mcomment-input-text').attr('type_input', '');
                    $(document).find('.mcomment-send').addClass('active');
                    break;
                case 'acr_result':
                    console.log('i got it', data.event.file);
                    let target = $('.tfile-infor.tfile-type.tfile-type--file[data-file-id=' + data.event.file + '] .acr-result-icon, .mfolder__sub[data-file-id=' + data.event.file + '] .acr-result-icon')
                    target.removeClass('hide deactive')
                    target.addClass(data.event.acr_class)
                    break;
                case 'new_message':
                    removeSystemMessage(data);
                    socketNewMessage(target_offer, get_offer, current_receiver, offer_id, data, socket_id, regex);
                    removeDuplicatedDate();
                    updateBatchNumberStorage(data);
                    if ($('.owner').hasClass('prdt')) {
                        calcMoreActionComment($('.prdt .mmessage'));
                    } else {
                        calcMoreActionComment($('.scene-style .mmessage'));
                    }
                    clickBtnActionMessage();
                    break;

                case 'seen':
                    socketSeenMessage(get_offer, offer_id, data);
                    break;

                case 'seen_comment':
                    socketSeenComment(data, current_receiver, offer_id);
                    if ($('.scene-style').length > 0) {
                        calcMoreActionComment($('.scene-style .mmessage'));
                    } else if ($('.main-talk-room').length > 0) {
                        calcMoreActionComment($('.main-talk-room .mmessage'));
                    }
                    break;

                case 'delete_message':
                    socketDeleteMessage(get_offer, offer_id, data, infor_offer);
                    removeDuplicatedDate();
                    updateBatchNumberStorage(data);
                    removeSystemMessage(data);
                    break;

                case 'update_message':
                    socketUpdateMessage(data, regex, socket_id, infor_offer, get_offer);
                    removeDuplicatedDate();
                    updateBatchNumberStorage(data)
                    if ($('.owner').hasClass('prdt')) {
                        calcMoreActionComment($('.prdt .mmessage'));
                    } else {
                        calcMoreActionComment($('.scene-style .mmessage'));
                    }
                    clickBtnActionMessage();
                    break;

                case 'resolve_message':
                    if (data.event.type_message === 'project') {
                        get_offer = $('.pd-product-comment[data-product-id=' + data.event.project_id + ']');
                    } else {
                        get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');
                    }
                    if (get_offer.length > 0) {
                        let message_id = data.event.message_id;
                        let message = get_offer.find('.mmessage[data-message-id=' + message_id + ']');
                        // let message_child = get_offer.find('.mmessage[data-parent-id=' + message_id + ']');
                        if (data.event.resolved) {
                            if (get_offer.hasClass('show-comment-unresolved')) {
                                if (message.find('.s-audio-control.active').length) {
                                    message.find('.s-audio-control.active').trigger('click')
                                }
                                // if (message_child.find('.s-audio-control.active').length) {
                                //     message_child.find('.s-audio-control.active').trigger('click')
                                // }
                                message.fadeOut('slow');
                                // message_child.fadeOut('slow');
                            }

                            setTimeout(function () {
                                message.addClass('resolved');
                                // message_child.addClass('resolved');
                            }, 300);

                            message.find('.mmessage-resolve ').addClass('mmessage-resolved');
                            // message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');

                        } else {
                            message.css('display', 'flex');
                            // message_child.css('display', 'flex');
                            message.find('.mmessage-resolve ').removeClass('mmessage-resolved');
                            // message_child.find('.mmessage-resolve ').removeClass('mmessage-resolved');
                            message.removeClass('resolved');
                            // message_child.removeClass('resolved');
                        }

                    }
                    break;

                case 'update_review':
                    socketReviewOffer(socket_id, data);
                    break;

                case 'offer_accept':
                    updateListOffer(data);
                    let offerDom = $('.mitem[data-offer^=' + offer_id + ']');
                    offerDom.find('.mscene__label .slabel').remove();
                    get_offer.find('.mmessage-confirm').remove();
                    offerDom.addClass('mprogress');
                    infor_offer.find('.minfo-contract').html(data.event.infor_html);
                    offer_role = offerDom.hasClass('offer-admin') ? ROLE_ADMIN : ROLE_CREATOR;
                    if (offer_role === ROLE_ADMIN) {
                        // toastr.success(thread_name + 'というオファーが請けました。');
                        if (offerDom.hasClass('mactive')) {
                            $('.button-delete_offer').remove()
                        }
                    }
                    updateOfferStatusInfo(data);
                    $('.messenger-file-component-container .messenger-file-component-content.open-preview-artist-contract-approve-modal[data-file-id='+ data.event.offer_creator_id +']').parents('.messenger-file-component-container').remove()
                    if (offer_role == ROLE_ADMIN){
                        $('#footerCommentBlockOffer .action-panel-head').prepend(`<div class="c-fab heading-13 u-gap8 u-mb8 button-confirm-offer-quick" onclick="acceptOfferArtist('${offer_id}')">
                            <span class="material-symbols-rounded">check_circle</span>
                            検収
                        </div>`)
                        $('.mmessage-list.mscrollbar.mscrollbar--vertical.mscrollbar--bottom').append(`<div class="contract-block c-btn-contract-done u-row-center u-mx-auto u-relative" data-offer="${offer_id}">
                            <div class="contract-block-button d-flex accept_production_file u-row u-gap16">
                                <div class="u-col-center u-gap4">
                                <div class="contract-block-button-child-top text-center">
                                    <span class="material-symbols-rounded u-text-blue">
                                    assignment_turned_in
                                    </span>
                                    <p class="label8">
                                    ${data.event.contract_file}
                                    </p>
                                </div>
                                </div>
                                <div class="contract-block-button-child u-col-center u-gap8">
                                <p class="heading-18-spacing u-mb8">
                                    締結完了
                                </p>
                                <p class="label8">締結日: ${data.event.time_deadline}</p>
                                </div>
                            </div>
                            <div class="contract-block-action u-absolute ">
                                <div class="contract-block-action-button" onclick="setUpToggleOffer(event)">
                                <span class="material-symbols-rounded u-text-blue c-icon-more-horiz">more_horiz</span>
                                </div>
                                <div class="contract-block-action-dropdown u-absolute hide">
                                <div class="contract-block-action-trigger d-flex" onclick="downloadFileOffer('${offer_id}')">
                                    <div class="contract-block-action-trigger-text">
                                    PDFをダウンロード
                                    </div>
                                    <div class="contract-block-action-trigger-icon">
                                    <div class="contract-block-action-trigger-icon">
                                        <span class="material-symbols-rounded">download</span>
                                    </div>
                                    </div>
                                </div>
                                <div class="contract-block-action-trigger d-flex top-border" data-toggle="modal" data-target="#modal-edit-offer" onclick="fillValueToModal('${offer_id}')">
                                    <div class="contract-block-action-trigger-text">
                                    条件を調整
                                    </div>
                                    <div class="contract-block-action-trigger-icon">
                                    <span class="material-symbols-rounded">contract_edit</span>
                                    </div>
                                </div>
                                <div class="contract-block-action-trigger d-flex top-border" data-toggle="modal" data-target="#closed-offer">
                                    <div class="contract-block-action-trigger-text">
                                    オファーを終了
                                    </div>
                                    <div class="contract-block-action-trigger-icon">
                                    <span class="material-symbols-rounded">contract_delete</span>
                                    </div>
                                </div>
                                </div>
                            </div>
                            </div>`);
                    }else{
                        $('.mmessage-list .mlast__content').before(`<div class="mmessage-container refactor"><div class="contract-block contract-block-blue-soremo contract-block-soremo-border" data-offer="${offer_id}">
                            <div class="contract-block-button justify-between d-flex" onclick="get_content_modal_contract('${offer_id} 1')">
                                <div class="contract-block-button-child text-center">
                                <div class="contract-block-button-child-top">
                                    <span class="material-symbols-rounded">
                                    handshake
                                    </span>
                                </div>
                                <div class="contract-block-button-child-bottom">
                                    <p class="contract-block-button-child-small-text">
                                        ${data.event.contract_file}
                                    </p>
                                </div>
                                </div>
                                <div class="contract-block-button-child text-center">
                                <div class="contract-block-button-child-top">
                                    <p class="contract-block-button-child-text">
                                    締結完了
                                    </p>
                                </div>
                                <div class="contract-block-button-child-bottom">
                                    <p class="contract-block-button-child-small-text">納期: ${data.event.time_deadline}</p>
                                </div>
                                </div>
                                <div class="contract-block-button-child text-center">
                                <div class="contract-block-button-child-top">
                                    <span class="material-symbols-rounded">download</span>
                                </div>
                                </div>
                            </div>
                        </div></div>`);
                    }
                    break;

                case 'checked_offer':
                    socketCheckedOffer(data, offer_id, infor_offer, thread_name);
                    break;

                case 'offer_closed':
                    socketClosedOffer(data, offer_id, infor_offer);
                    break;

                case 'create_offer':
                    var project_id = $('.project-item.active').attr('data-project-id');
                    if (data.event.project_id !== project_id) {
                        return
                    }
                    let budget = data.event.budget_or_reward;
                    let offer_side = data.event.offer_side;

                    let item_html = data.event.item_html;
                    let listItems = $(`.mlist .list--offers-project .mitem[data-type-offer="messenger_artist"][data-offer-side=${offer_side}]`)
                    if (listItems.length > 0) {
                        let lastItem = listItems.last()
                        if (lastItem.data('reward') > budget) {
                            $(item_html).insertAfter(lastItem)
                        } else {
                            listItems.each(function (index, element) {
                                var itemBudgetOrReward = $(element).data('reward')

                                if (budget >= itemBudgetOrReward) {
                                    $(item_html).insertBefore($(element));
                                    return false;
                                }

                            });
                        }
                    } else {
                        if(offer_side === 'creator')
                            if($(`.mlist .list--offers-project .mitem[data-type-offer="messenger_owner"]`).length > 0)
                                $(item_html).insertAfter($(`.mlist .list--offers-project .mitem[data-type-offer="messenger_owner"]`).last())
                            else
                                $('.mlist .list--offers-project').prepend(item_html);
                        else
                            $('.mlist .list--offers-project').append(item_html);
                    }
                    if (socket_id === data.event.creator_id) {
                        // toastr.success(thread_name + 'という新しいオファーが届いています。');
                    }
                    updateCountUnreadOfferComment(data.event, target_offer, get_offer);
                    break;

                case 'edit_offer':
                    $('.contract-block.u-bg-white[data-offer^=' + offer_id + ']').find('.contract-block-detail-line-value').eq(0).html(data.event.data_format);
                    $('.contract-block.u-bg-white[data-offer^=' + offer_id + ']').find('.contract-block-detail-line-value').eq(1).html(data.event.time_deadline);
                    $('.contract-block.u-bg-white[data-offer^=' + offer_id + ']').find('.contract-block-detail-line-value').eq(2).html(data.event.reward + "円 (税込)");
                    $('.contract-block.u-bg-white[data-offer^=' + offer_id + ']').find('.contract-block-detail-line-value').eq(3).html(data.event.allow_subcontracting == '1' ? '可' : '不可');
                    $('.contract-block.u-bg-white[data-offer^=' + offer_id + ']').find('.contract-block-detail-line-value').eq(4).html(data.event.note);
                    $('.contract-block.c-btn-contract-done[data-offer^=' + offer_id + ']').find('.contract-block-button-child .label8').html("締結日: " + data.event.time_deadline);

                    $('.mitem[data-offer^=' + offer_id + ']').find('.mscene__name .thread-name').html(thread_name);

                    let offer = $('.offer-' + offer_id + '_infor');
                    updateListOffer(data);

                    if (offer.length > 0) {
                        offer.find('.infor-offer').html(data.event.infor_offer);

                        let mess_id = data.event.message_id;
                        let message_dom = $('.mmessage[data-message-id=' + mess_id + ']');
                        if (message_dom.length > 0) {
                            if (message_dom.hasClass('mmessage--received')) {
                                message_dom.html(data.event.message_received_html);
                            } else {
                                message_dom.html(data.event.message_send_html);
                            }
                            if (message_dom.find('.s-text').length > 0) {
                                let mess = $('.mmessage[data-message-id=' + mess_id + ']').find('.s-text').html();
                                let message_regex = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
                                $('.mmessage[data-message-id=' + mess_id + ']').find('.s-text').html(message_regex);
                            }
                            updateFileInMessage(data, mess_id);
                            removeDuplicatedDate();
                        }
                    }

                    if(data.event.offer_creator_id) {
                       let button_contract =  $('.messenger-file-component-container .messenger-file-component-content[data-file-id='+ data.event.offer_creator_id +']').not('.btn-accept-offer').parent('.messenger-file-component-container')
                       let pinned_button_contract = $('.maction.maction-new .messenger-file-component-container .messenger-file-component-content[data-file-id='+ data.event.offer_creator_id +']').parent('.messenger-file-component-container')
                       if(button_contract.length) {
                            $(data.event.contract_html).insertBefore(button_contract);
                            button_contract.remove();
                       }
                       if(pinned_button_contract.length) {
                            $(data.event.btn_contract_html).insertBefore(pinned_button_contract);
                            pinned_button_contract.remove();
                       }
                    }

                    break;

                case 'download_file':
                    socketDownloadFile(get_offer, data);
                    break;
                case 'update_rating':
                    let updated_date = data.event.message;
                    for (let [key, value] of Object.entries(updated_date)) {
                        let product_scene = $(`[data-product-scene-id="${key}"]`);
                        value = value.split(",");
                        if(product_scene.length){
                            let rating_text = product_scene.find(".pd-chapter__rating");
                            rating_text.html(`${value[0]}(${value[1]})`);
                            // update star
                            let star = product_scene.find(".pd-chapter__title .average-star");
                            if(parseInt(value[0])){
                                star.addClass("selected");
                                let number_star = "star-" + String(parseInt(value[0]));
                                star.find("a").removeClass("active");
                                star.find("." + number_star).addClass("active");
                                star.attr("data-rating", parseInt(value[0]));
                            }else{
                                star.removeClass("selected");
                                star.find("a").removeClass("active");
                            }
                        }else{
                            let scene = $(`.project-delivery-item-content[data-scene-title-id="${key}"]`);
                            if(scene.length){
                                let star = scene.find(".stars");
                                if(parseInt(value[0])){
                                    star.addClass("selected");
                                    let number_star = "star-" + String(parseInt(value[0]));
                                    star.find("a").removeClass("active");
                                    star.find("." + number_star).addClass("active");
                                    star.attr("data-rating", parseInt(value[0]));
                                }else{
                                    star.removeClass("selected");
                                    star.find("a").removeClass("active");
                                }
                            }
                        }
                    }

                    updateProjectVideoWattingFeedback(data.event);
                    break;

                case 'offer_reject':
                    sub_msg = offer_role === ROLE_CREATOR ? 'を削除されました。' : 'を削除しました。';
                    // toastr.success('オファー' + thread_name + sub_msg);
                    socketRejectOffer(data);
                    break;
                case 'update_scene':
                    updateProjectVideoWattingFeedback(data.event);
                    break;
                case 'checked_plan':
                    checkedContractOffer(data);
                    break;
                case 'checked_contract':
                    checkedContractOffer(data);
                    break;
                case 'checked_done_offer':
                    checkedDoneOffer(data);
                    removeSystemMessage(data);
                    break;
                case 'upload_file_offer':
                    socketUploadFileOffer(data);
                    removeDuplicatedDate();
                    removeSystemMessage(data);
                    break;
                case 'delete_offer_product':
                    socketDeleteOfferProduct(data);
                    break;
                case 'payment_offer':
                    socketPaymentOffer(data);
                    break;
                case 'show_menu':
                    updateMenuProjectDetail(data.event);
                    break;
                case 'add_first_producer':
                    updateMessageWhenAddFirstProducer(data.event);
                    break;
                case 'delete_system_message':
                    removeSystemMessage(data);
                    break;
                default:
                    break;
            }
        };
        current_chatsocket.onclose = function (e) {
            console.error('Chat socket closed!');
            dict_chatsocket.delete(socket_id);
            current_chatsocket = undefined;
            initSocket(socket_id)
        };
        }
    }
}

function updateProjectVideoWattingFeedback(event) {
    if($('.tab--video-watting_checkback ').length > 0) {
        let projectId = $('.project-item').first().data('project-id');
        let projectItem = $('.project-item');

        if(event.project_id !== project_id) {
            return
        }
         // socket heart and comment
        // alert('socket ajaxUpdateListWattingsFeedback')
        // ajaxUpdateListWattingsFeedback(projectItem, projectId)

    }
}

function updateMenuProjectDetail(event) {
    let projectId = $('.project-item.active').first().data('project-id');

    if (event.project_id !== projectId) {
        return
    }
    if ($('.pbanner-info').hasClass('hide')) {
        $('.number-notification').attr('value', '0');
        $('.number-notification').text(0)
    }
    if (event.is_show) {
        $('.pbanner-info, .btn-tutorial-pc').removeClass('hide');
    } else {
         $('.pbanner-info, .btn-tutorial-pc').addClass('hide');
        $('.pbanner-tab-message').trigger('click');
    }
}

function socketNewMessage(target_offer, get_offer, current_receiver, offer_id, data, socket_id, regex) {
    let message;
    if (data.event.message) {
        message = JSON.parse(data.event.message)[0].fields;
    }
    let comment;

    if (message) {
        updateCountUnreadOfferComment(data.event, target_offer, get_offer);

        updateListOffer(data);
    } else {
        updateProjectVideoWattingFeedback(data.event);
        comment = JSON.parse(data.event.comment)[0].fields;
        get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');
    }
    if (data.event.type_message === 'project') {
        get_offer = $('.project-item.active').find('.pd-product-comment');
    }

    if ($('.project-item.active[data-project-id=' + data.event.project_id + ']').length) {
        if (!$('.mitem[data-offer=' + data.event.offer_id + ']').length) {
            $('.mlist .list--offers-project').prepend(data.event.item_scene_html);
        }
    }
    updateOfferStatusInfo(data)


    if (current_receiver == offer_id) {

        if (data.event.type_message === 'system_message') {
            $(data.event.message_system_html).insertBefore(get_offer.find('.mlast__content'));
            sScrollbarBottom();
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(data.event.infor_html)
        } else {
            let before_id = data.event.before_id;
            if (before_id) {
                let before_dom = $('.mmessage[data-message-id=' + before_id + ']');
                before_dom.addClass('mmessage-near');
                before_dom.find('.avatar--image:not(.avatar-seen)').remove();
            }

            let message_id = data.event.message_id;
            let sender_role = data.event.sender_role;
            let sender_id = data.event.sender_id;
            let sender_role_arr = data.event.sender_role_arr;

            if (message) {
                let form_edit_type = ['edit-plan', 'edit-contract', 'edit-bill']
                if(form_edit_type.includes(data.event.form_type)) {
                    if (data.event.old_message_file_id) {
                        let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
                        let old_button_message = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').parents('.messenger-file-component-container')
                        if(old_message.length) {
                            if (data.event.received_role) {
                                if (data.event.received_role === sender_role) {
                                    $(data.event.message_send_html).insertBefore(old_message);
                                    $(data.event.btn_message_send_html).insertBefore(old_button_message);
                                } else {
                                    $(data.event.message_received_html).insertBefore(old_message);
                                    $(data.event.btn_message_received_html).insertBefore(old_button_message);
                                }
                            } else {
                                if (sender_role_arr.includes(user_role)) {
                                    $(data.event.message_send_html).insertBefore(old_message);
                                    $(data.event.btn_message_send_html).insertBefore(old_button_message);
                                } else {
                                    $(data.event.message_received_html).insertBefore(old_message);
                                    $(data.event.btn_message_received_html).insertBefore(old_button_message);
                                }
                            }
                            old_message.remove()
                            old_button_message.remove()
                        }
                    }
                } else {
                    if (data.event.received_role) {
                        if (data.event.received_role === sender_role) {
                            $(data.event.message_send_html).insertBefore(get_offer.find('.mlast__content'));
                            $(data.event.btn_message_send_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        } else {
                            $(data.event.message_received_html).insertBefore(get_offer.find('.mlast__content'));
                            $(data.event.btn_message_received_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        }
                    } else {
                        if (sender_role_arr.includes(user_role)) {
                            $(data.event.message_send_html).insertBefore(get_offer.find('.mlast__content'));
                            $(data.event.btn_message_send_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        } else {
                            $(data.event.message_received_html).insertBefore(get_offer.find('.mlast__content'));
                            $(data.event.btn_message_received_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        }
                    }
    
                    if (data.event.old_file_message_receiver_id) {
                        let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.old_file_message_receiver_id +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
                        let old_button_message = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').parents('.messenger-file-component-container')
                        if(old_message.length) {
                            if (data.event.received_role) {
                                if (data.event.received_role === sender_role) {
                                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                                    // $(data.event.btn_old_file_message_send_html).insertBefore(old_button_message);
                                } else {
                                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                                    // $(data.event.btn_old_file_message_receiver_html).insertBefore(old_button_message);
                                }
                            } else {
                                if (sender_role_arr.includes(user_role)) {
                                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                                    // $(data.event.btn_old_file_message_send_html).insertBefore(old_button_message);
                                } else {
                                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                                    // $(data.event.btn_old_file_message_receiver_html).insertBefore(old_button_message);
                                }
                            }
                            old_message.remove()
                            old_button_message.remove()
                        }
                    }
                }
                

                if (sender_id !== socket_id) {
                    get_offer.find('.mmessage-list').addClass('not-seen');
                }
                if (message.has_file) {
                    let infor_file_html = data.event.file_infor_html;
                    $(infor_file_html).insertAfter($('.mlast__file'));
                    newWavesurferInit();
                }
            } else {
                if (!message && sender_role === user_role) {
                    if (sender_id === socket_id) {
                        $('.new-message').remove();
                        $(data.event.message_send_html).insertBefore(get_offer.find('.mlast__content'));

                    } else {
                        $(data.event.message_send_by_role_html).insertBefore(get_offer.find('.mlast__content'));
                        get_offer.find('.mmessage-list').addClass('not-seen');
                    }
                } else {
                    $(data.event.message_received_html).insertBefore(get_offer.find('.mlast__content'));
                    get_offer.find('.mmessage-list').addClass('not-seen');
                }
                if (comment.has_file) {
                    let infor_file_html = data.event.file_infor_html;
                    $(infor_file_html).insertAfter($('.mlast__file'));
                    newWavesurferInit();
                }
            }
            showLastCommentSceneDetailSP();
            actionShowIconResolved();
            sScrollbarBottom();
            let message_content_dom = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-audio-text, .s-filetext');
            if (message_content_dom.length > 0) {
                let message_content = message_content_dom.html();
                let message_regex = message_content.replace(regex, "<a target='_blank' href=$&>$&</a>");
                $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-audio-text, .s-filetext').each(function () {
                    if (!$(this).find('.icon')) {
                        $(this).html(message_regex);
                    }
                })
                let mess = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html();
                let message_regex_text = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
                $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html(message_regex_text);
            }
        }
    }
}


function socketSeenMessage(get_offer, offer_id, data) {
    let mscene_target = $('.mitem[data-offer^=' + offer_id + ']');
    get_offer.find(('.messenger-director__item-seen ')).addClass('hide');
    get_offer.find(('.messenger-director__item-seen ')).last().removeClass('hide');

    if (data.event) {
        updateCountUnreadOfferComment(data.event, mscene_target, get_offer)
    }
}

function socketSeenComment(data, current_receiver, offer_id) {
    let mscene_target = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    let get_offer = $('.offer-' + offer_id);
    get_offer.find(('.messenger-director__item-seen ')).addClass('hide');
    get_offer.find(('.messenger-director__item-seen ')).last().removeClass('hide');
    if (data.event) {
        updateCountUnreadOfferComment(data.event, mscene_target, get_offer)
    }
    if (offer_id == current_receiver) {
        let comment_id = data.event.comment_id;
        for (id of comment_id) {
            let message_dom = $('.mmessage[data-message-id=' + id + ']');
            if (message_dom.length > 0) {
                let user_seen = message_dom.find('.mmessage-user .mmessage-user-seen').length;
                if (message_dom.find('.notification--outline-gray').length) {
                    let user_count = message_dom.find('.notification--outline-gray').attr('data-value');
                    user_count++;
                    let count_show = '';
                    if (user_count >= 10) {
                        count_show = '+9+'
                    } else {
                        count_show = '+' + user_count
                    }
                    message_dom.find('.notification--outline-gray').attr('data-value', user_count);
                    message_dom.find('.notification--outline-gray').text(count_show);
                }
                if (user_seen < 3) {
                    if (message_dom.hasClass('mmessage--received')) {
                        message_dom.find('.mmessage-user').prepend(data.event.user_seen_html);

                    } else {
                        message_dom.find('.mmessage-user').append(data.event.user_seen_html);
                    }
                } else {
                    message_dom.find('.notification--outline-gray').removeClass('hide');
                }
            }
        }
    }
}

function socketDeleteMessage(get_offer, offer_id, data, infor_offer) {
    let offer_message = true;
    let mscene_target = $('.mitem[data-offer^=' + offer_id + ']');
    if (mscene_target.length > 0) {
        if (parseInt(mscene_target.find('.notification--round').html()) > 1) {
            let count_message = parseInt(mscene_target.find('.notification--round').html()) - 1;
            mscene_target.find('.notification--round').html(count_message)
        } else {
            get_offer.find('.mmessage-list').removeClass('not-seen');
            mscene_target.find('.notification--round').addClass('hide');
            mscene_target.find('.notification--round').html('0')
        }
    }

    if (data.event.unread_message_count !== undefined) {
        updateCountUnreadOfferComment(data.event, mscene_target, get_offer);
    }
    if (get_offer.length < 1) {
        if (data.event.type_message === 'project') {
            get_offer = $('.project-item').find('.pd-product-comment');
        } else {
            get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');

        }
        offer_message = false;
    }
    if (get_offer.length > 0) {
        let message_id = data.event.message_id;
        let messageDom = get_offer.find('.mmessage[data-message-id=' + message_id + ']');
        if(messageDom.hasClass('load-lasted-message')) {
            $('.load-lasted-message').prev().addClass('load-lasted-message')
        }
        let parentMessageDom = get_offer.find('.mmessage[data-parent-id=' + message_id + ']');

        messageDom.remove();
        // if (parentMessageDom.hasClass('load-lasted-message')) {
            // $('.load-lasted-message').prev().addClass('load-lasted-message');
        // }
        parentMessageDom.find('a.s-filedisable').addClass('hide');
        removeMessage(message_id, data.event.deleted_file_ids);
        if (infor_offer && data.event.offer_condition) {
            infor_offer.find('.minfo-contract').remove();
            infor_offer.find('.minfo--action').append(data.event.infor_html);
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(data.event.infor_html)
            if (data.event.message_infor_html !== undefined) {
                get_offer.find('.mmessage-confirm').remove();
                $(data.event.message_infor_html).insertBefore(get_offer.find('.mlast__content'));
            }
            if (data.event.offer_condition === '1' || data.event.offer_condition === '8') {
                infor_offer.find('.infor-offer').addClass('hide');
                let offer_dom = $('.mitem[data-offer^=' + data.event.offer_id + ']');
                if (offer_dom) {
                    offer_dom.removeClass('mprogress');
                }
            }
        }
    }
}

function socketUpdateMessage(data, regex, socket_id, infor_offer, get_offer) {
    let message;
    let comment;

    if (data.event.message) {
        message = JSON.parse(data.event.message)[0].fields;
    } else {
        comment = JSON.parse(data.event.comment)[0].fields;
    }

    let message_id = data.event.message_id;

    let message_dom = $('.mmessage[data-message-id=' + message_id + ']');
    if (message_dom.length > 0) {
        message_dom.addClass('clicked');
        if (message) {
            if (message_dom.hasClass('mmessage--received')) {
                message_dom.html(data.event.received_message_html);
            } else {
                message_dom.html(data.event.send_message_html);
            }

            updateFileInMessage(data, message_id)
        } else {
            if (message_dom.hasClass('mmessage--received')) {
                message_dom.html(data.event.received_message_html);
            } else {
                if (data.event.sender_id == socket_id) {
                    message_dom.html(data.event.send_message_html);
                } else {
                    message_dom.html(data.event.message_send_by_role_html);
                }
            }

            if (data.event.file_infor_html && data.event.file_infor_html !== '') {
                $('.tfile-infor[data-message-id=' + message_id + ']').remove();
                if (data.event.has_file) {
                    let infor_file_html = data.event.file_infor_html;
                    $(infor_file_html).insertAfter($('.mlast__file'));
                }
                newWavesurferInit();
            }
        }
        if (message_dom.find('.s-text').length > 0) {
            let mess = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html();
            let message_regex = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
            $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html(message_regex);
        }
    }
    if (get_offer.length > 0 && data.event.offer_condition) {
        infor_offer.find('.minfo-contract').remove();
        infor_offer.find('.minfo--action').append(data.event.infor_html);
        get_offer.find('.floating-button-container').empty()
        get_offer.find('.floating-button-container').append(data.event.infor_html)
        if (data.event.user_role === 'master_client') {
            if (data.event.message_infor_html !== undefined) {
                get_offer.find('.mmessage-confirm').remove();
                $(data.event.message_infor_html).insertBefore(get_offer.find('.mlast__content'));
            }
            if (data.event.offer_condition === '1' || data.event.offer_condition === '8') {
                infor_offer.find('.infor-offer').addClass('hide');
                let offer_dom = $('.mitem[data-offer^=' + data.event.offer_id + ']');
                if (offer_dom) {
                    offer_dom.removeClass('mprogress');
                }
            }
        }
    }
}

function updateBatchNumberStorage(data) {
    var project_id = $('.project-item.active').attr('data-project-id');
    let socketProject = data.event.project_id;
    if (!socketProject) {
        socketProject = data.event.product_id;
    }
    if (socketProject !== project_id) {
        return
    }
    let undownloadFile = data.event.count_file_un_download;
    if (!undownloadFile && undownloadFile  != 0) {
        return
    }
    $('.pbanner-tab--exchange .number-notification').attr('value', undownloadFile);
    $('.pbanner-tab--exchange .number-notification').text(undownloadFile);
}

function socketDownloadFile(get_offer, data) {
    var project_id = $('.project-item.active').attr('data-project-id');
    if (data.event.project_id !== project_id) {
        return
    }
    updateBatchNumberStorage(data);

    let type_file_download = data.event.type_file_download;
    if (type_file_download === 'project') {
        get_offer = $('.pd-product-comment[data-product-id^=' + data.event.project_id + ']');
    } else if (['scene', 'scene_comment'].includes(type_file_download)) {
        get_offer = $('.pd-scene-title-detail[data-scene-title-id^=' + data.event.scene_title_id + ']');
    } else if (['message_owner', 'message'].includes(type_file_download)) {
        get_offer = $('.offer-' + data.event.offer_id + '_infor')
    }
    if (get_offer.length > 0) {
        let file_dom;
        let file_dom2;
        if (['project', 'scene_comment', 'message', 'message_owner'].includes(type_file_download)) {
            file_dom = get_offer.find('.tfile-infor[data-file-id=' + data.event.file_id + ']');
            if ($('.prdt').length > 0) {
                if (['message_owner', 'message'].includes(type_file_download)){
                    file_dom2 = $('.DM-box-container.dm-block-message').find('.block-download-file[data-file-id=' + data.event.file_id + ']');
                }else {
                    file_dom2 = get_offer.find('.block-download-file[data-file-id=' + data.event.file_id + ']');
                }
            }
            if (file_dom.length < 1) {
                file_dom = get_offer.find('.mfolder__sub[data-file-id=' + data.event.file_id + ']');
                if (file_dom.length < 1 && $('.scene-detail-page').length > 0) {
                    file_dom = get_offer.find('.block-download-file[data-file-id=' + data.event.file_id + ']')
                }
            }
        } else if (type_file_download === 'scene') {
            file_dom = get_offer.find('.tfile-infor[data-scene-id=' + data.event.file_id + ']');
        }
        let file_modal = $('#folderModal').find('.list-group-item[data-file-id=' + data.event.file_id + ']');
        if (file_dom.length > 0 || file_modal.length > 0) {
            addIconDownload(file_dom, data);
            addIconDownload(file_modal, data);
        }
         if (file_dom2.length > 0 || file_modal.length > 0) {
            addIconDownload(file_dom2, data);
            addIconDownload(file_modal, data);
        }
    }
}

function addIconDownload(fileDom, data) {
    if (window.localStorage.getItem("performance") || !window.localStorage.getItem("performance") && window.location.pathname.includes("tab=product-comment")){
        let user_seen = fileDom.find('.sview-user-seen').length;
        if (fileDom.find('.notification--outline-gray').length) {
            let user_count = fileDom.find('.notification--outline-gray').attr('data-value');
            user_count++;
            let count_show = '';
            if (user_count >= 10) {
                count_show = '+9+'
            } else {
                count_show = '+' + user_count
            }
            fileDom.find('.notification--outline-gray').attr('data-value', user_count);
            fileDom.find('.notification--outline-gray').text(count_show);
            fileDom.addClass('has-user-seen');
        }
        if (user_seen < 3) {
            $(data.event.user_downloaded_html).insertBefore(fileDom.find('.notification.notification--outline-gray'));
        } else {
            fileDom.find('.notification--outline-gray').removeClass('hide');
        }
    }
}

function socketRejectOffer(data) {
    let offer_dom = $('.mscene.mitem[data-offer=' + data.event.offer_id + ']');

    if (offer_dom) {
        activeNextOffer(offer_dom)
    }
    updateCountUnreadOfferComment(data.event, null, null);
}

function socketReviewOffer(socket_id, data) {
    updateListOffer(data);
    updateOfferStatusInfo(data);
    let mitem_target = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    let offer_role = $('.mcolumn-content[data-offer^=' + data.event.offer_id + ']').attr('data-role-offer');
    if (mitem_target && offer_role !== data.event.role_review ) {
        switch (data.event.value) {
            case '1':
                mitem_target.find('.mscene__label .icon').addClass('icon--sicon-like');
                break;
            case '2':
                mitem_target.find('.mscene__label .icon').addClass('icon--sicon-smile');
                break;
            case '3':
                mitem_target.find('.mscene__label .icon').addClass('icon--sicon-unlike');
                break;
        }
        let thread_name = escapeHtml(data.event.thread_name);
        // toastr.success(thread_name  + messageReview);
    }
}

function socketCheckedOffer(data, offer_id, infor_offer, thread_name) {
    let offer_dom = $('.mitem[data-offer^=' + offer_id + ']');
    if (offer_dom) {
        offer_role = offer_dom.hasClass('offer-admin') ? ROLE_ADMIN : ROLE_CREATOR;
        updateListOffer(data);
        if (offer_role === ROLE_CREATOR) {
            // toastr.success(thread_name + 'というオファーが検収しました。')
        }
        if (!$('#offer-filter').is(':checked')) {
            activeNextOffer(offer_dom);
        } else {
            offer_dom.addClass('mchecked');
            offer_dom.removeClass('mprogress');
            infor_offer.find('.minfo-contract').html(data.event.infor_html);

            $(`<div class="minfo-rating">
                           <button class="btn btn--black btn--sm btn-icon-unlike" data-value="3">
                              <i class="icon icon--sicon-unlike"></i>
                              <span class="btn-text"></span>
                           </button>
                           <button class="btn btn--blue btn--sm btn-icon-like" data-value="1">
                              <i class="icon icon--sicon-like"></i>
                              <span class="btn-text"></span>
                           </button>
                           </div>`).insertAfter(infor_offer.find('.minfo-contract'));
        }
    }
    updateOfferStatusInfo(data);
}


function socketClosedOffer(data, offer_id, infor_offer) {
    let offer_dom = $('.mitem[data-offer^=' + offer_id + ']');
    if (offer_dom) {
        updateListOffer(data);
        offer_role = offer_dom.hasClass('offer-admin') ? ROLE_ADMIN : ROLE_CREATOR;
        if (offer_role === ROLE_CREATOR) {
            $('.mmessage[data-offer^=' + offer_id + ']').remove();
        }
        infor_offer.find('.scene-title__action .button-edit_offer').remove();
        if (!$('#offer-filter').is(':checked')) {
            activeNextOffer(offer_dom);
        } else {
            offer_dom.addClass('mchecked');
            offer_dom.removeClass('mprogress');
            infor_offer.find('.minfo-contract').html(data.event.infor_html);

        }
    }
}

function updateCountUnreadOfferComment(event, target_offer, get_offer) {
    var project_id = $('.project-item.active').attr('data-project-id');
    let new_item = $('.mitem[data-offer^=' + event.offer_id + ']');
    if(event.project_id !== project_id) {
        return
    }
    if (target_offer) {
        let countNewMessage = event.unread_message_offer >= 100 ? "99+" : event.unread_message_offer;
        new_item.find('.notification--round').html(countNewMessage);
        if (countNewMessage <= 0) {
            new_item.find('.notification--round').addClass('hide');
             get_offer.find('.mmessage-list').removeClass('not-seen');
        } else {
            new_item.find('.notification--round').removeClass('hide')
        }
    }

    var unreadMessageCount = event.unread_message_count;
    var iconMessageElm = $('.pbanner-tab-message');
    var numberCountMessElm = iconMessageElm.find('.number-notification')

    if(unreadMessageCount <= 0) {
        numberCountMessElm.remove()
    } else {
        countMessageText = unreadMessageCount >= 100 ? "99+" : unreadMessageCount;
        if(numberCountMessElm.length > 0) {
            numberCountMessElm.text(countMessageText);
            numberCountMessElm.val(unreadMessageCount)
        }
        else {
            iconMessageElm.find('.icon-with-badge').append(`<span class="number-notification unread-offer-comment" value="${unreadMessageCount}">${countMessageText}</span>`);
        }
    }
}

function activeNextOffer(offer_dom) {
    if (is_pc === 'True' && offer_dom.hasClass('mactive')) {
        let new_target_offer = (offer_dom.next('.mitem').length > 0) ? offer_dom.next('.mitem') : offer_dom.prev('.mitem');
        if (new_target_offer.length) {
            new_target_offer.trigger('click')
        } else {
            removeOffer()
        }
    } else {
        removeOffer()
    }
    offer_dom.hide('slow', function(){ offer_dom.remove();})
}

function removeOffer() {
    $('.mcolumn--right .mcolumn-back').trigger('click');
    $('.mcolumn--main .mcolumn-back').trigger('click');
    $('.mcolumn.mcolumn--main').empty();
    $('.mcolumn.mcolumn--right').empty();
}

function updateListOffer(data) {
    let new_item = $('.list--offers-project .mitem[data-offer^=' + data.event.offer_id + ']');
    let new_item_search = $('.list--offers-search .mitem[data-offer^=' + data.event.offer_id + ']');
    let reward = data.event.reward;
    if(new_item) {
        new_item_search.insertAfter($('.list--offers-search .count--list-offers-search'));
        new_item.find('.mscene__date').text(moment(new Date().toLocaleString("ja", {timeZone: "Asia/Tokyo"})).format('HH:mm'));
        let old_reward = new_item.data('reward');
        let created = new_item.data('created');
        let offer_side = new_item.data('offer-side');
        if(old_reward != reward) {
            if(!reward) {
                return
            }
            let listItems = $(`.mlist .list--offers-project .mitem[data-type-offer="messenger_artist"][data-offer-side=${offer_side}]`)
            new_item.data('reward', reward)
            listItems.each(function (index, element) {
                if (new_item.data('offer') !== $(element).data('offer')) {
                    var itemBudgetOrReward = $(element).data('reward')
                    var itemCreated = $(element).data('created')
                    if (reward > itemBudgetOrReward || (reward == itemBudgetOrReward && created >= itemCreated)) {
                        $(new_item).insertBefore($(element));
                        return false;
                    } else if (index === listItems.length - 1) {
                        $(new_item).insertAfter($(element));
                        return false;
                    }
                }
            })
        }
    }
    // let new_item = $('.list--offers-project .mitem[data-offer^=' + data.event.offer_id + ']');
    // let new_item_search = $('.list--offers-search .mitem[data-offer^=' + data.event.offer_id + ']');
    // if (new_item) {
    //     new_item_search.insertAfter($('.list--offers-search .count--list-offers-search'));
    //     new_item.insertBefore($('.list--offers-project').children().eq(0));
    //     new_item.find('.mscene__date').text(moment(new Date().toLocaleString("ja", {timeZone: "Asia/Tokyo"})).format('HH:mm'));
    // }
}

function updateFileInMessage(data, message_id) {
    if (data.event.file_infor_html && data.event.file_infor_html !== '') {
        removeMessage(message_id);
        if (data.event.has_file) {
            let infor_file_html = data.event.file_infor_html;
            $(infor_file_html).insertAfter($('.mlast__file'));
        }
        newWavesurferInit();
    }
}

function removeMessage(message_id, deleted_file_ids=[]) {
    deleted_file_ids.forEach(function(id) {
        $('.tfile-item .tfile-infor[data-file-id=' + id + ']').parents('.tfile-item').first().remove();
    })
    let file_dom = $('.tfile-infor[data-message-id=' + message_id + ']');
    let folder_dom = $('.sfolder[data-message-id=' + message_id + ']');
    file_dom.parents('.tfile-item').remove();
    folder_dom.parents('.tfile-item-content').prev().first().remove();
    folder_dom.remove();
}

function checkedContractOffer(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    file_contract = data.event.file_contract;
    let offer_id = event.offer_id;
    let offer_dom = $('.mitem[data-offer^=' + offer_id + ']');
    if (offer_dom) {
        updateListOffer(data);
        let infor_offer = $('.offer-' + offer_id + '_infor');
        let get_offer = $('.offer-' + offer_id);
        infor_offer.find('.minfo-contract').remove();
        get_offer.find('.minfo-contract').parents('.mmessage').remove();
        infor_offer.find('.minfo--action').append(event.file_infor_html);
        get_offer.find('.floating-button-container').empty()
        get_offer.find('.floating-button-container').append(event.infor_html)
        infor_offer.find('.scene-title__action').remove();
        offer_dom.find('.thread-name').html(escapeHtml(data.event.project_name));
        $('.messenger-add').removeClass('hide')

        if(data.event.item_infor_offer) {
            infor_offer.find('.minfo-list').empty()
            infor_offer.find('.minfo-list').html(data.event.item_infor_offer)
        }
    }
    updateOfferStatusInfo(data);

    if (data.event.old_file_message_receiver_html) {
        let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.file_id +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
        let btn_old_message = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ data.event.file_id +']').parents('.messenger-file-component-container')
        if(old_message.length) {
            let sender_role_arr = data.event.sender_role_arr;
            if (data.event.received_role) {
                if (data.event.received_role === sender_role) {
                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                } else {
                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                }
            } else {
                if (sender_role_arr.includes(user_role)) {
                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                } else {
                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                }
            }
            old_message.remove()
            btn_old_message.remove()
        }
    }

    $('.project-item.active').find('.pbanner-info').removeClass('hide');
    $('.btn-tutorial-pc').removeClass('hide');
    let title_page = data.event.title_page;
    changeTitlePage(title_page)
    if(user_role != 'master_client') {
        resetFormContract();
    }
}


function checkedDoneOffer(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    let offer_id = event.offer_id;
    let offer_dom = $('.mitem[data-offer^=' + offer_id + ']');
    if (offer_dom) {
        offer_dom.removeClass('mprogress').addClass('mchecked');
        updateListOffer(data);
        if (!$('#offer-filter').is(':checked')) {
            let get_offer = $('.offer-' + offer_id);
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(event.file_infor_html)
            activeNextOffer(offer_dom);
        } else {
            let infor_offer = $('.offer-' + offer_id + '_infor');
            let get_offer = $('.offer-' + offer_id);
            infor_offer.find('.minfo-contract').remove();
            infor_offer.find('.minfo--action').append(event.file_infor_html);
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(event.file_infor_html)
        }
    }
    updateOfferStatusInfo(data);

    if (data.event.old_file_message_receiver_html) {
        let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.file_id +']').parents('.messenger-file-component-container')
        if(old_message.length) {
            let sender_role_arr = data.event.sender_role_arr;
            if (data.event.received_role) {
                if (data.event.received_role === sender_role) {
                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                } else {
                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                }
            } else {
                if (sender_role_arr.includes(user_role)) {
                    $(data.event.old_file_message_send_html).insertBefore(old_message);
                } else {
                    $(data.event.old_file_message_receiver_html).insertBefore(old_message);
                }
            }
            old_message.remove()
        }
    }
}

function removeSystemMessage(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    let offer_id = event.offer_id;
    let get_offer = $('.offer-' + offer_id);
    let offer_dom = $('.mitem.mactive[data-offer^=' + offer_id + ']');
    if (offer_dom.length) {
        if (data.event.action === 'checked_done_offer' || data.event.action === 'delete_system_message') {
            $('.mmessage-system .mmessage-system__content:contains("全てのシーンの納品が完了しました。検収をお願いします。")').parents('.mmessage-system ').remove();
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(event.infor_html)
        } else if (data.event.action === 'upload_file_offer' || (data.event.action === 'new_message' && data.event.action_check === 'upload_file_plan_contract_offer')) {
            $('.mmessage-system .mmessage-system__content:contains("見積もり作成中..。今しばらくお待ちください。")').parents('.mmessage-system ').remove();
        }
    }
}


function socketUploadFileOffer(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    let offer_id = event.offer_id;
    let offer_dom = $('.mitem[data-offer^=' + offer_id + ']');
    if (offer_dom) {
        if (!offer_dom.hasClass('mchecked')) {
            offer_dom.addClass('mprogress')
        }
        updateListOffer(data);
        let infor_offer = $('.offer-' + offer_id + '_infor');
        let get_offer = $('.offer-' + offer_id);
        infor_offer.find('.minfo-contract').remove();
        // infor_offer.find('.minfo--action').append(event.infor_html);
        get_offer.find('.floating-button-container').empty()
        get_offer.find('.floating-button-container').append(event.infor_html)
        if(!data.event.is_not_approved) {
            infor_offer.find('.infor-offer').removeClass('hide');
            if (data.event.new_budget) {
                infor_offer.find('.show-budget-project').html(data.event.new_budget);
            }
        }
        
        $(event.file_html).insertAfter(infor_offer.find('.mlast__file-offer'));
        get_offer.find('.mmessage-confirm').remove();
        // $(event.message_infor_html).insertBefore(get_offer.find('.mlast__content'));
    }
}


function socketDeleteOfferProduct(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    $('.new-video-menu .project-item .pbanner__image').trigger('click')
}


function socketPaymentOffer(data) {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    file_bill = data.event.file_bill;
    $('.messenger-infor .minfo-contract').remove();
    $('.minfo-contract').parents('.mmessage-confirm').remove()
    $('.messenger-detail').find('.floating-button-container').empty();
    
    let old_file = $('.messenger-file-component-container .messenger-file-component-content[data-file-id='+ file_bill +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
    let btn_old_file = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ file_bill +']').parents('.messenger-file-component-container')
    if (old_file.length) {
        $(data.event.old_file_message_receiver_html).insertBefore(old_file);
        old_file.remove();
        btn_old_file.remove();
    }
    updateOfferStatusInfo(data);
}


function updateMessageWhenAddFirstProducer(event) {
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    updateCountUnreadOfferComment(event, undefined, undefined);
    if ($('.martist #offer-filter').is(':checked')) {
        get_messenger_artist(project_id, null, 'processing');
    } else {
        get_messenger_artist(project_id, null, 'waiting');
    }
}

function updateOfferStatusInfo(data) {
    let mscene_target = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    if(data.event.offer_status_info && mscene_target) {
        let offer_status_info = JSON.parse(data.event.offer_status_info)
        var color = offer_status_info.color
        var text = offer_status_info.text
        if(color)
            mscene_target.find('.progress-offer').css("background-color", color)
        if(text)
            mscene_target.find('.description-content-offer').text(text)
    }
}
