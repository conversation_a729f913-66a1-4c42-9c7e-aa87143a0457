.scene-expired-modal .modal-dialog-centered{
    margin: 0;
    top: 25%;
    transform: translateY(50%);
}

.scene-expired-modal .modal-content{
    padding-left: 10px;
    padding-right: 5px;
    padding: 0 5px 20px 10px;
}

.scene-expired-modal .modal-header{
    display:flex;
    border-bottom:none;
}

.scene-expired-modal .header-title{
    display:flex;
    flex-grow:1;
}

.scene-expired-modal .header-title p{
    padding: 0;
    display: inline-block;
    padding-left: 15px;
    padding-right: 15px;
}
.scene-expired-modal .header-title i{
    font-weight: 600;
    font-size: 10px;
    color: #929394;
    line-height: 20px;
}

.scene-expired-modal .modal-dialog .header-title p:first-child {
  padding-left: 0;
}

.scene-expired-modal .modal-body{
    padding-top: 0;
}

.scene-expired-modal .expired-link{
    display: flex;
    width: 97%;
    border: 1px solid #f0f0f0;
    border-radius: 3px;
    line-height: 40px;
    align-item: center;
}

.scene-expired-modal .expired-link input{
    overflow: hidden;
    display: inline-block;
    word-break: break-all;
    max-height: 40px;
    padding-left: 5px;
    border: none;
    flex-grow: 1;
}
.scene-expired-modal .expired-link input:focus{
    outline: none;
}

.scene-expired-modal .expired-link .copy-link{
    background-color: #009ACE;
    min-width: 88px;
    height: 34px;
    margin: 3px;
    color: white;
    text-align: center;
    line-height: 34px;
    border-radius: 3px;
    cursor: pointer;
}

.scene-expired-modal .expired-link .copy-link img{
    width: 12px;
    height: 16px;
    margin-right: 5px;
}

.scene-expired-modal .expired-time{
    margin-top: 15px;
    width: 97%;
}

.scene-expired-modal .expired-time form{
    display: flex;
    padding: 10px 0;
}

.scene-expired-modal .expired-time .form-check{
    flex-grow: 1;
    line-height: 35px;
}

.scene-expired-modal .form-check .form-check-label{
    font-weight: 300;
    font-size: 14px;
    color: #53565A;
    margin-left: 5px;
}
.scene-expired-modal .day-input{
    display: flex;
    border: 1px solid #D3D3D3;
    line-height: 35px;
    border-radius: 5px;
    padding-right: 15px;
    color: #53565A;
}

.switch-share-link{
    padding-top: 15px !important;
}


.scene-expired-modal .day-input input{
    flex-grow: 1;
    border: none;
    margin-left: 5px;
    margin-right: 5px;
}

.scene-expired-modal  .day-input input:focus{
    outline: none;
}

.scene-expired-modal  .expired-time span.expired-date{
    float: right;
    color: #53565A;
}

.switch-share-setting{
    margin: 15px 0 20px 25px;
    width: 100px;
}

.switch-share-setting .switch-slider{
    background-color: #a7a8a9;
}

.switch-share-setting.custom-switch .switch-label,
.switch-share-link.custom-switch .switch-label {
    color: #000;
}

#isSetExpired{
    transform: translateY(1px);
    -webkit-appearance: auto !important;
}

#isSetExpired:checked{
    width: 0;
    margin-right: 13px;
}

#isSetExpired:before{
    width: 13px;
    height: 13px;
    display: inline-block;
    transform: translateY(-2px);
}

#isSetExpired:checked:before{
    content: url('../images/checkbox.svg');
}

input::-webkit-inner-spin-button {
  margin-left: 8px;
}

#qrcode{
    display: block;
    width: 128px;
    margin: auto;
    margin-top: 32px;
}

@media (max-width: 768px){
    .scene-expired-modal .modal-dialog {
        width: 330px !important;
        margin: auto;
    }
    .scene-expired-modal .expired-time form{
        display: block;
        padding: 10px 0;
    }
}