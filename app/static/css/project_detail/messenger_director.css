

@keyframes modalslideup {
    from {
        bottom: -300px;
        opacity: 0;
    }
    to {
        bottom: 0;
        opacity: 1;
    }
}

@keyframes modalslidedown {
    from {
        bottom: 0;
        opacity: 1;
    }
    to {
        bottom: -300px;
        opacity: 0;
    }
}

.messenger-carousel .slick-dots {
    padding: 0;
    margin: 0 10%;
    list-style: none;
    position: absolute;
    text-align: center;
    bottom: 5px;
    display: flex;
    justify-content: center;
    width: 80%;
}

.messenger-carousel .slick-dots button {
    display: inline-block;
    width: 10px;
    height: 10px;
    background-color: #d6d6d6;;
    margin: 0 4px;
    padding: 0;
    font-size: 0;
    border: none;
    border-radius: 50%;
}

.messenger-carousel .slick-dots button:focus {
    outline: none;
}

.messenger-carousel .slick-dots li {
    display: inline-block;
}

.messenger-carousel .slick-dots li.slick-active button {
    background-color: #009dc4;
}

.slick-slide:focus {
    outline: none;
}

.messenger-carousel-item {
    position: relative;
    padding-right: 1px;
    max-height: calc(100vh - 250px);
}

.messenger-carousel-item__title {
    position: absolute;
    left: 12px;
    bottom: 12px;
    color: #333;
}

.messenger-carousel-for {
    margin-bottom: 15px;
}

.text-white {
    color: white;
}

.text-black {
    color: #333;
}

.messenger-detail .messenger-director__item--right .messenger-director__item-seen, .messenger__seen-img {
    position: absolute;
    left: 0;
    bottom: -25px;
    border-radius: 10px;
}

.messenger-detail .messenger-director__item-info {
    border: 1px solid #a7a8a9;
    border-radius: 10px;
    margin: 0 10px;
    padding: 5px 10px;
    position: relative;
}

.messenger-director__item-info {
    min-width: 30%;
    width: auto;
}

.messenger-director__item-avatar {
    text-align: center;
}

.messenger-director__item-time {
    margin-top: 8px;
    text-align: center;
}

.messenger-detail__input {
    min-height: 60px;
    border: 1px solid #009ace;
    border-radius: 10px;
}

.messenger-detail .messenger-detail-content {
    border-radius: 10px;
    max-height: calc(100vh - 150px);
}

.messenger-detail .messenger-detail-content textarea {
    min-height: 60px;
    border-radius: 10px
}

.messenger-director {
    margin-top: 75px;
}

body {
    overflow: auto;
}

.project-item__title:before {
    padding-right: 5px;
}

.project-item__title {
    text-transform: uppercase;
}

.messenger-director__item-reply .messenger-detail__input-text {
    padding: 20px 0 0 20px;
    height: 100%;
}

.messenger-director__item-reply {
    min-height: 60px;
    background: white;
    margin-bottom: 20px;
    border: none;
}

.messenger__order-right .sumo-select {
    min-width: 20px;
}

.SumoSelect > .CaptionCont > span {
    padding-right: 0;
}

.messenger-director__item {
    position: relative;
}

.messenger-director__item--right .messenger-director__item-seen,
.messenger__seen-img {
    position: absolute;
    left: 5px;
    bottom: 5px;
    border-radius: 10px;
}

.messenger-detail__button-send {
    position: absolute;
    right: 10px;
    bottom: 35%;
    min-width: 50px;
}

.messenger-director__item-reply .messenger-detail__input {
    position: relative;
}

.button-no-comment.disabled {
    border: 1px solid #e5e5e5;
    color: #e5e5e5;
}

img.messenger__seen-img {
    border-radius: 10px;
}

.messenger-detail .messenger-detail-content {
    border: 1px solid #009ace;
    padding: 0 5px;
    margin: 10px 20px;
}

.messenger-popup.background.button--background-gray.button--round.button--small.messenger-popup {
    position: relative;
    left: 45%;

}

.messenger-popup.background.button--background-gray.button--round.button--small.messenger-popup img {
    background: #53565A;
    padding: 10px;
    border-radius: 50px;
}

.input-project {
    width: 300px;
    height: 30px;
    margin-left: 20px;
}

.button-telephone {
    background-color: white;
    margin-bottom: 10px;
}

.card-select-container {
    display: flex;
    color: #fff;
    min-height: 100%;
    align-items: center;
    justify-content: center;
}

.InputGroup {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.card-select-container input[type="radio"] {
    visibility: hidden; /* 1 */
    height: 0; /* 2 */
    width: 0; /* 2 */
}

.card-select-container label {
    display: flex;
    flex: auto;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #a7a8a9;
    color: white;
    border-radius: 6px;
    transition: color .1s ease-out,
    background-color .1s ease-in,
    box-shadow .4s ease-in-out;
    user-select: none;
    padding: 15px;
    flex-direction: column;
}

.card-select-container label:last-of-type {
    margin-right: 0;
}

.card-select-container input[type="radio"]:checked + label {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    background-color: #0076a5;
    color: #fff;
}

.card-select-container input[type="radio"]:hover:not(:checked) + label {
    background-color: #009ace;
    color: #fff;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.messenger-director__item-info.info-offer {
    width: 70%;
}

.messenger-popup__form-input {
    margin-bottom: 10px;
}

.currency {
    position: relative;
}

.currency span {
    position: absolute;
    bottom: 5px;
    right: 10px;
}

.all-message {
    width: 100%;
    overflow: hidden auto;
    display: block;
    height: auto;
}

.upload--file {
    background-color: #F0F0F0;
    height: calc(100vh - 300px);
}

.project-item__member {
    position: absolute;
    top: 10px;
    right: 10px;
}

.messenger-carousel-item iframe {
    height: calc(100vh - 300px);
    width: 100%;
}

@media (max-width: 767px) {
    .messenger-carousel-item iframe {
        height: calc(100vh - 400px);
    }

    .messenger__item {
        margin-bottom: 10px;
    }

    .carosel-pdf .messenger-director__item {
        margin-bottom: 0;
        padding: 0 5px;
    }

    .messenger-carousel-for {
        margin-bottom: 0;
    }

    .upload--file {
        height: calc(100vh - 400px);
    }
}

@media (min-width: 768px) {
    .messenger__column-right,
    .messenger__column-left {
        width: 50%;
    }
}

.messenger__item {
    max-width: 400px;
}

.btn-create-offer {
    max-width: 400px;
    margin-bottom: 30px;
    padding: 9px 10px 9px 10px;
    position: relative;
    border-radius: 10px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    justify-content: center;
}

@media (max-width: 767px) {
    .btn-create-offer {
        margin-bottom: 10px;
    }
}

.btn-create-offer a {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 10px;
    width: auto;
}

.btn-create-offer a img {
    width: fit-content;
    border-radius: 50%;
    background: #53565a;
}

.btn-create-offer a p {
    margin: 5px 0 0;
    color: #333333;
}

.btn-create-offer a:hover img {
    background: #009ace;
}

.btn-create-offer a:hover p {
    color: #009ace;
}

.project-progress-action {
    position: absolute;
}

.project-progress-action-content {
    display: flex;
    align-items: center;
    flex-direction: column;
}

.project-progress-action-content > div {
    margin: 1rem;
}

.project-item {
    margin-bottom: 0;
}

.messenger-director .messenger__list {
    height: calc(100vh - 75px);
}

.messenger__status {
    top: 30px;
}


.video-comment-input-pin img {
    width: 20px;
    margin: 0 10px;
    filter: invert(87%) sepia(0%) saturate(5%) hue-rotate(352deg) brightness(104%) contrast(87%);
    top: 30%;
    position: relative;
    left: 15%;
}

.video-comment-input-pin {
    margin-left: 5px;
    min-width: 35px;
    cursor: pointer;
}

.video-comment-input-pin.active img {
    filter: invert(45%) sepia(100%) saturate(512%) hue-rotate(147deg) brightness(88%) contrast(104%);
}

.video-pin-time {
    position: relative;
}

.video-pin-time:hover span {
    opacity: 1;
}

.video-pin-time {
    display: inline-block;
    margin-right: 5px;
    padding: 15px;
    background: url('../images/icon-play-circle2.svg') no-repeat top center;
    background-size: contain;
    z-index: 10;
}

.video-pin-time.gray {
    color: #53565A;
}

.video-pin-time.gray {
    background: url('../images/icon-play-circle2-gray.svg') no-repeat top center;
    background-size: contain;
}

.video-pin-time:hover {
    filter: brightness(84%);
}

.video-pin-time {
    color: #009ace;
}
