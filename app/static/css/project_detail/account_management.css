.account-management {
    margin-top: 0px;
}

.account-management__add-artist {
    width: 100%;
    padding: 12px 0px;
    border: 1px dashed #F0F0F0;
    border-radius: 8px;
    margin: 20px 0px;
    background: #FFFFFF;
}

.account-management__add-artist__content{
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
}

.add-artist__content__icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(167, 168, 169, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-artist__content__icon i {
    font-size: 25px;
    color: #A7A8A9;
}

.add-artist_content__text {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    color: #000000;
    margin: 4px 0px;
    text-align: center;
    letter-spacing: 0.168824px;
    font-weight: 400;
}

.account-management__add-artist:hover {
    background: #A7A8A9;
    cursor: pointer;
}

.account-management__add-artist:hover .add-artist_content__text{
    color: white;
}

.account-management__add-artist:hover .add-artist__content__icon i{
    color: #A7A8A9;
}

.account-management__add-artist:hover .add-artist__content__icon{
    background: #FFFFFF;
}

.account-management__list-acount {
    overflow-x: auto;
}

.account-management__list-acount table thead {
    border-bottom: 1px solid #F0F0F0;
}

.account-management__list-acount table {
    width: 100%;
}

.account-management__list-acount table {
    margin-bottom: 12px;
}

.account-management__list-acount table>tbody>tr>td {
    border-top: none;
    padding: 8px;
    vertical-align: center;
    white-space: nowrap;
    width: 420px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    font-weight: 300;
    letter-spacing: 0.168824px;
    color: #000000;
}

.account-management__list-acount table>tbody>tr>td:not(:last-child) {
    text-align: center;
}

.account-management__list-acount table>tbody>tr>td:nth-child(2) {
    text-align: center;
}

.account-management__list-acount table>tbody>tr>td:last-child {
    text-align: right;
}

.account-management__list-acount table>tbody>tr>td:last-child .account-info__action {
    margin-right: 82px;
}

.account-management__list-acount table>thead>tr>th {
    white-space: nowrap;
    padding: 8px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #A7A8A9;
    text-align: center;
}

.account-info__left__avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    margin-right: 10px;
}

.account-info__left__avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.account-info {
    border-radius: 5px;
    border: 1px solid #f0f0f0f0;
    padding: 4px 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 335px;
    margin-left: 68px;
}

.avatar__bandage {
    position: absolute;
    width: 14px;
    height: 14px;
    bottom: 0;
    right: -3px;
    border-radius: 50%;
    background: #009ACE;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #FFFFFF;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
}

.account-info__left {
    display: flex;
    align-items: center;
}

.account-info__left__content {
    display: block;
}

.account-info__left__content__top {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400;
    text-align: left;
}

.account-info__left__content__bottom {
    display: flex;
    max-width: 150px;
    width: 150px;
}

.account-info__left__content__bottom__title, .account-info__left__content__bottom__organization-name {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    letter-spacing: 0.144706px;
    color: #000000;
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.account-info__left__content__bottom__organization-name {
    padding-left: 8px;
}

.account-info__right {
    display: block;
}

.account-info__right__manage {
    display: flex;
    justify-content: flex-end;
}

.account-info__right__manage__identification, .account-info__right__manage__NDA {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    margin-left: 10px;
    border: 1px solid #53565a;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 8px;
    line-height: 150%;
    letter-spacing: 0.120588px;
    color: #53565a;
}

.account-info__right__manage__identification:hover, .account-info__right__manage__NDA:hover {
    cursor: pointer;
    border: 1px solid #0076A5;
    color: #0076A5;
}

.account-info__right__manage .idetity-uploaded {
    background: #009ACE !important;
    color: #FFFFFF;
    border: 1px solid #009ACE;
}

.account-info__right__manage .idetity-uploaded:hover {
    background: #0076A5 !important;
    border: 1px solid #0076A5;
}

.account-info__right__manage .NDA-not-uploaded {
    background: #009ACE !important;
    color: #FFFFFF;
    border: 1px solid #009ACE;
}

.account-info__right__manage .NDA-not-uploaded:hover {
    background: #0076A5 !important;
    border: 1px solid #0076A5;
}

.account__trade-slider {
    display: flex;
    margin: 0 -10%;
    padding: 10px 0;
}

.account__trade-item.active:first-child:after,
.account__trade-item:first-child:after,
.account__trade-item.active:last-child:after,
.account__trade-item:last-child:after {
    left: calc(50% - 1.5px) !important;
}

.account__trade-item {
    width: 20%;
    position: relative;
}

.account__trade-item:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: var(--soremo-border);
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
}

.account__trade-item:after {
    content: "";
    position: absolute;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid var(--soremo-placeholder);
    background-color: var(--white-color);
    transform: translate(-50%, -50%);
}

.account__trade-item.active:first-child:after {
    left: calc(50% - 1.5px);
}

.account__trade-item.active:last-child:after {
    left: calc(50% + 1.5px);
}

.account__trade-item.active:after {
    width: 8px;
    height: 8px;
    background-repeat: no-repeat;
    background-size: 8px;
    background-position: center;
    background-color: var(--blue-color);
}

.account__tradeoff {
    margin-bottom: 0px;
}

@media (max-width: 992px) {
    .account-management__list-acount table>tbody>tr>td {
        width: auto;
    }

    .account-info {
        margin-left: 0px;
    }
}

.account-management__list-acount table>tbody>tr>td .custom-switch{
    display: flex;
    justify-content: center;
}

.account-info__action, .materials-used__action {
    opacity: 0;
    pointer-events: none;
}

.account-management__list-acount table>tbody>tr:hover .account-info__action, .account-management__list-acount table>tbody>tr>td>.materials-used:hover .materials-used__action {
    opacity: 1;
    pointer-events: all;
}

.materials-used {
    display: flex;
    justify-content: center;
}

.account-management__list-acount table>tbody>tr>td>.materials-used .materials-used__content:hover {
    color: #009ace;
    cursor: pointer;
}

.account-management__list-acount table>tbody>tr>td:nth-child(4) a:after,
.account-management__list-acount table>tbody>tr .account-info .account-info__left__avatar:after,
.account-management__list-acount table>tbody>tr .account-info .account-info__left:after,
.account-management__list-acount table>tbody>tr .account-info .account__trade-slider:after {
    content: "";
    padding: 3px 4px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    font-weight: 300;
    line-height: 175%;
    color: #000000;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    display: none;
}

.account-management__list-acount table>tbody>tr>td:nth-child(4) a:hover {
    color: #009ace !important;
    position: relative;
}

.account-management__list-acount table>tbody>tr>td:nth-child(4) a:hover,
.account-management__list-acount table>tbody>tr .account-info .account-info__left__avatar:hover,
.account-management__list-acount table>tbody>tr .account-info .account-info__left:hover,
.account-management__list-acount table>tbody>tr .account-info .account__trade-slider {
    position: relative;
}

.account-management__list-acount table>tbody>tr>td:nth-child(4) a:hover:after {
    display: block;
    content: "取引履歴";
    position: absolute;
    bottom: -32px;
    background: #fff;
    left: 50%;
    transform: translateX(-50%);
}

.account-management__list-acount table>tbody>tr .account-info .account-info__left__avatar:hover:after {
    display: block;
    content: "PROFILE";
    position: absolute;
    bottom: -24px;
    background: #fff;
    left: 50%;
    transform: translateX(-50%);
}

.account-management__list-acount table>tbody>tr .account-info .account-info__left:hover:after {
    display: block;
    content: "アカウント情報";
    position: absolute;
    bottom: -24px;
    background: #fff;
    left: 0;
}

.account-management__list-acount table>tbody>tr .account-info .account__trade-slider:hover:after {
    display: block;
    content: "利用設定";
    position: absolute;
    bottom: -24px;
    background: #fff;
    left: 50%;
    transform: translateX(-50%);
}

.materials-used__content {
    margin-top: 2px;
}

.materials-used__content span {
    font-size: 13px;
}

.account-info__action span, .materials-used__action span {
    color: #A7A8A9;
    background: transparent;
    font-size: 19px;
    padding-left: 4px;
    height: 100%;
}

.account-info__action span:hover, .materials-used__action span:hover {
    color: #0076A5;
    cursor: pointer;
}

.psearch-filter {
    margin-bottom: 16px;
}

.tabs-skill {
    display: flex;
    flex-wrap: wrap;
}

.tabs-skill .nav-item {
    position: relative;
}

.skill-selected {
    position: absolute;
    top: 0;
    right: 0;
}

.nav.tabs-skill .nav-item a.nav-link {
    color: #000000;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 400;
}

.nav.tabs-skill .nav-item.active a.nav-link, .nav.tabs-skill .nav-item.active a.nav-link:hover, .nav.tabs-skill .nav-item a.nav-link:hover, .nav.tabs-skill .nav-item.active a.nav-link:focus {
    color: #009ACE;
    border-bottom: 2px solid #009ACE;
    font-weight: 400;
}

.skills-list-selected {
    display: flex;
    flex-wrap: wrap;
}

.skills-item {
    font-size: 11px;
    line-height: 17px;
    padding: 4px 16px;
    color: #000000;
    background-color: #F0F0F0;
    border-radius: 4px;
    margin: 11px 8px 0 0;
    cursor: pointer;
    border: 1px solid #F0F0F0;
}

.skills-item:hover, .skills-item.selected:hover  {
    color: #FFF;
    background-color: #009ACE;
    border: 1px solid #009ACE;
}

.skills-item.selected {
    color: #53565A;
    background-color: #FFF;
    border: 1px solid #53565A;
}

.modal-header__invite-artist__content {
    width: 100%;
}

.invite-artist__content__title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 24px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    margin: 8px 0px;
    color: #000000;
    font-weight: 400;
}

.invite-artist__content__title-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    margin: 8px 0px;
    color: #000000;
    font-weight: 400;
}

.modal-header {
    /* margin-bottom: 32px; */
    padding: 0px 0px 28px 0px;
}

.popup-body label[class="label_field"] {
    margin-top: 48px;
    margin-bottom: 48px;
}

.popup-body label[class="label_field1"] {
    margin-bottom: 24px;
}

.artist-management__input-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.artist-management__input-container__input-1, .artist-management__input-container__input-2 {
    width: 50%;
}

.artist-management__input-container__input-1{
    margin-right: 8px;
}

.artist-management__input-container__input-2 {
    margin-left: 8px;
}

.account__jp-astarisk, .account__jp-astarisk-op {
    margin-left: 4px;
}

.label_field, .label_field1 {
    width: 100%;
}

.label-enterprise {
    width: 50%;
}

@media (max-width: 992px) {
    .artist-management__input-container__input-1, .artist-management__input-container__input-2 {
        width: 100%;
        margin-bottom: 5px;
        margin-left: 0px;
        margin-right: 0px;
    }

    .artist-management__input-container {
        display: block;
    }

    .label-enterprise{
        width: 100%;
    }
}

.popup-footer {
    padding-top: 32px;
    border-top: 1px solid #F0F0F0;
    margin-top: 64px;
}

.dropzone {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border: 1px dashed #D3D3D3 !important;
    box-sizing: border-box;
    border-radius: 6px;
    min-height: 100px !important;
}

.account_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
}

.account_upload-file .fallback:hover .icon, .account_upload-file .fallback:hover p{
    color: white !important;
}

.dropzone.dz-drag-hover {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.dropzone.dz-drag-hover .icon, .dropzone.dz-drag-hover p {
    color: white !important;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.dz-button {
    background: transparent;
}

.dz-button i {
    font-size: 21px;
    color: #A7A8A9;
}

.dz-button p {
    color: #A7A8A9;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.168824px;
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.drag-over {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
}

.mattach-preview-container .mattach-previews {
    overflow-y: hidden !important;
}

.mattach-preview-container .mcommment-file {
    background-color: #F0F0F0 !important;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
}

.mcommment-file__delete .icon, .mcommment-file__name .icon {
    color: #53565a;
}

.mcommment-file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 8px 25px 8px 16px;
    background-color: #F0F0F0;
    border-radius: 6px;
    margin: 4px;
}

.mcommment-file .icon {
    font-size: 15px;
    color: #A7A8A9;
    margin-right: 2px;
}

.drag-drop-container {
    margin-top: 32px;
}

.indentity-component-container {
    width: 169px;
    height: auto;
    padding: 8px 13px 7px;
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    background: #FCFCFC;
    margin-right: 16px;
    cursor: pointer;
}

.indentity-component-name {
    display: flex;
}

.indentity-component-name i {
    margin-right: 11px;
    font-size: 19px;
}

.indentity-component-name .indentity-component-name__content {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.indentity-component-iamge {
    width: 141px;
    height: 76px;
}

.indentity-container {
    margin-top: 32px;
    overflow-x: auto;
    display: flex;
    padding-bottom: 8px;
    margin-bottom: 10px;
}

.indentity-container .active {
    background: #009ACE;
    color: #FFFFFF;
}

.indentity-component-container:hover {
    background: #009ACE;
    color: #FFFFFF;
}

.indentity-container .active .indentity-component-name .indentity-component-name__content {
    /* background: #009ACE; */
    color: #FFFFFF;
}

.indentity-component-container:hover .indentity-component-name .indentity-component-name__content {
    /* background: #009ACE; */
    color: #FFFFFF;
}

.indentity-content-view-container {
    width: 100%;
    max-height: 447px;
    overflow: auto;
}

.indentity-content-view-container iframe {
    width: 100%;
    max-height: 447px;
}

.account_management__list-invited {
    padding: 24px 34px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    margin-bottom: 22px;
    border-radius: 16px;
}

.account_management__search-and-results {
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    padding: 24px 34px;
    margin-bottom: 32px;
    border-radius: 16px;
}

.list-invited__head {
    width: 100%;
    margin-bottom: 2px;
}

#account-management__search-artist-input {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    letter-spacing: 0.168824px;
}

.list-invited__head__content {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 16px;
    line-height: 150%;
    letter-spacing: -0.245455px;
    color: #000000;
    font-weight: 400;
}

.list-invited__list {
    padding: 0px 90px;
    max-height: 250px;
    overflow-y: auto;
}

.list-invited__component_container {
    display: flex;
    align-content: center;
    margin-bottom: 16px;
}

.list-invited__component__avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.list-invited__component__avatar {
    align-self: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 26px;
    min-width: 40px;
}

.list-invited__component__email {
    margin-right: 42px;
    display: flex;
    align-content: center;
    width: calc(100% - 315px);
    min-width: 200px;
}

.list-invited__component__email span{
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    align-self: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 992px) {
    .list-invited__list {
        padding: 0px;
    }

    .account-management__list-acount table>thead>tr>th:first-child {
        padding-left: 8px;
    }

    #modal-approval-of-indentity .modal-dialog {
        margin: auto;
        width: 100%;
    }

    .account-management__list-acount table>tbody>tr>td:last-child .account-info__action {
        margin-right: unset;
    }
}

.list-invited__component__action {
    display: flex;
    align-content: center;
    width: 275px;
}

.popup-close-approval-of-indentity {
    position: absolute;
    right: 10px;
    top: 10px;
    padding: 0;
    background-color: var(--white-color);
}

.account-info__right__tradeoff_slider {
    min-width: 100px;
}

.mattach-preview-container .mattach-previews {
    margin: 0px;
}

#modal-upload-NDA .mcommment-file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 2px 8px 2px 8px;
    background-color: #F0F0F0;
    border-radius: 6px;
    margin: 4px;
    justify-content: space-between;
}

#modal-upload-NDA .mcommment-file__name {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

#modal-upload-NDA .mcommment-file__name p {
    margin: 4px 0px 4px 4px;
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
}

#modal-upload-NDA .mcommment-file .icon {
    font-size: 15px;
    color: #A7A8A9;
}

#modal-upload-NDA .mcommment-file .mcommment-file__delete {
    margin-left: 12px;
    display: flex;
    align-content: center;
}

#modal-upload-NDA .mattach-preview-container .mcommment-file .mcommment-file__name {
    display: block;
}

.psearch-content {
    padding: 0px 0px 24px;
}

.account_management__search-and-results .nav .nav-item a.nav-link {
    background: #FFFFFF;
}

#modal-upload-NDA .mcommment-file {
    cursor: pointer;
    margin: 4px 4px 4px 0px;
}

#modal-upload-NDA .dropzone {
    cursor: pointer;
}

#modal-upload-NDA .mattach-preview-container .collection-item {
    margin: 0px;
}

#modal-upload-NDA .mattach-preview-container .mattach-previews {
    margin: 0px;
}

#modal-approval-of-indentity .popup-footer {
    margin-top: 23px;
}
