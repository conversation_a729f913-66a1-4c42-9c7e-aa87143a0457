:root {
    --hover-color: #009ace;
    --background-color: #fcfcfc;
    --gray1: #a7a8a9;
}

.profile-footer {
    min-height: 100px;
    background: var(--background-color);
}

.profile-footer:is(.editable, .new) {
    cursor: pointer;
}

.profile-footer-container  {
    display: flex;
    flex-direction: column;
    padding-top: 32px;
    padding-bottom: 32px;
}

.profile-footer-upper-container {
    position: relative;
    display: flex;
    align-items: stretch;
    justify-content: space-between;
}

.profile-footer-upper-right .profile-footer-menu-icon,
.footer-social-link-icon {
    height: 24px;
    width: 24px;
    background-image: url('../images/icon_home_new.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: auto 24px;
    cursor: pointer;
}

.profile-footer-upper-right .profile-footer-menu-icon:not(:last-child) {
    margin-right: 12px;
}

.profile-footer-upper-right .profile-footer-menu-icon:hover,
input:checked+.footer-social-link-icon,
.item-main-column1-menu-icon[data-social-icon='home'],
.footer-social-link-icon:hover {
    background-image: url('../images/icon_home_new_hover.svg');
}

.footer-social-link-icon {
    background-size: 100%;
}

.item-main-column1-menu-icon[data-social-icon='home'] {
    background-image: url('../images/icon_home_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='fb'],
.item-main-column1-menu-icon[data-social-icon='fb'],
.footer-social-link-icon[data-social-icon='fb'] {
    background-image: url('../images/icon_facebook_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='fb']:hover,
input:checked+.footer-social-link-icon[data-social-icon='fb'],
.footer-social-link-icon[data-social-icon='fb']:hover {
    background-image: url('../images/icon_facebook_new_hover.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='twitter'],
.item-main-column1-menu-icon[data-social-icon='twitter'],
.footer-social-link-icon[data-social-icon='twitter'] {
    background-image: url('../images/icon_twitter_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='twitter']:hover,
input:checked+.footer-social-link-icon[data-social-icon='twitter'],
.footer-social-link-icon[data-social-icon='twitter']:hover {
    background-image: url('../images/icon_twitter_new_hover.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='insta'],
.item-main-column1-menu-icon[data-social-icon='insta'],
.footer-social-link-icon[data-social-icon='insta'] {
    background-image: url('../images/icon_instagram_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='insta']:hover,
input:checked+.footer-social-link-icon[data-social-icon='insta'],
.footer-social-link-icon[data-social-icon='insta']:hover {
    background-image: url('../images/icon_instagram_new_hover.svg');
    background-color: transparent !important;
}


.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='tiktok'],
.item-main-column1-menu-icon[data-social-icon='tiktok'],
.footer-social-link-icon[data-social-icon='tiktok'] {
    background-image: url('../images/icon_tiktok_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='tiktok']:hover,
input:checked+.footer-social-link-icon[data-social-icon='tiktok'],
.footer-social-link-icon[data-social-icon='tiktok']:hover {
    background-image: url('../images/icon_tiktok_new_hover.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='note'],
.item-main-column1-menu-icon[data-social-icon='note'],
.footer-social-link-icon[data-social-icon='note'] {
    background-image: url('../images/icon_note_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='note']:hover,
input:checked+.footer-social-link-icon[data-social-icon='note'],
.footer-social-link-icon[data-social-icon='note']:hover {
    background-image: url('../images/icon_note_new_hover.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='youtube'],
.item-main-column1-menu-icon[data-social-icon='youtube'],
.footer-social-link-icon[data-social-icon='youtube'] {
    background-image: url('../images/icon_youtube_new.svg');
    background-color: transparent !important;
}

.profile-footer-upper-right .profile-footer-menu-icon[data-social-icon='youtube']:hover,
input:checked+.footer-social-link-icon[data-social-icon='youtube'],
.footer-social-link-icon[data-social-icon='youtube']:hover {
    background-image: url('../images/icon_youtube_new_hover.svg');
    background-color: transparent !important;
}

.profile-footer-upper,
.profile-footer-upper-right,
.profile-footer-lower-menu {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.profile-footer-upper-right {
    justify-content: flex-end;
    align-items: flex-end;
    flex-shrink: 0;
}

.profile-footer-upper-right.same-line {
    position: absolute;
    right: 0;
    bottom: 0px;
}

.profile-footer-upper span {
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
    color: #000;
}

.profile-footer-upper span:not(:last-child) {
    margin-right: 64px;
    position: relative;
}

.profile-footer-upper span:not(:last-child, .end-of-line):after {
    content: "|";
    color: #d3d3d3;
    position: absolute;
    right: -32px;
    top: 0;
}

.profile-footer-upper span.end-of-line.no-after:after,
.profile-footer-lower-menu span.end-of-line.no-after:after {
    content: "|";
    color: transparent;
    position: absolute !important;
    right: -16px;
    top: 0;
}

.profile-footer-lower-menu span {
    color: #a7a8a9;
    font-weight: 300;
    font-size: 11px;
    line-height: 200%;
}

.profile-footer-copyright {
    margin-right: 64px;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
}

.profile-footer-lower-menu span:not(:first-child, :last-child, .end-of-line),
.profile-footer-upper span.end-of-line.no-after,
.profile-footer-lower-menu span.end-of-line.no-after {
    margin-right: 32px;
    position: relative;
}

.profile-footer-lower-menu span:not(:first-child, :last-child, .end-of-line):after {
    content: "|";
    color: #d3d3d3;
    position: absolute;
    right: -16px;
    top: 0;
}

.profile-footer-upper span:hover,
.profile-footer-lower-menu span:not(.profile-footer-copyright):hover {
    cursor: pointer;
    color: #009ace;
}

.profile-footer-lower {
    display: flex;
    justify-content: space-between;
}

.change-language-button {
    width: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.change-language-button svg {
    height: 24px;
    width: 24px;
}

.change-language-button span {
    line-height: 175%;
    display: flex;
    align-items: flex-end;
    font-feature-settings: 'palt' on;
    transition: 0.3s;
}

.change-language-button:hover svg path {
    fill: var(--hover-color);
}

.change-language-button:hover span {
    color: var(--hover-color) !important;
}

.profile-footer hr {
    margin: 16px 0;
}

.header-profile-sp-dropdown .change-language-button {
    border-top: 1px solid #f0f0f0;
    line-height: 175%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    min-width: 100px;
    align-items: center;
    padding-top: 24px;
}

.header-profile-sp-dropdown .change-language-button span {
    font-size: 13px;
    line-height: 150%;
    display: flex;
    align-items: flex-end;
    color: var(--gray1) !important;
    position: relative;
    padding-left: 24px;
}

.header-profile-sp-dropdown .change-language-button[data-language='jp'] span:nth-child(1),
.header-profile-sp-dropdown .change-language-button[data-language='en'] span:nth-child(2) {
    color: #000 !important;
}

.header-profile-sp-dropdown .change-language-button span:nth-child(2):before {
    content: "|";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    color: #f0f0f0 !important;
    font-size: 16px;
    font-weight: 100;
}

@media (max-aspect-ratio: 4/5) {
    .profile-footer-container.container {
        padding: 20px !important;
    }

    .profile-footer-lower {
        display: flex;
        flex-direction: column-reverse;
        justify-content: space-between;
    }

    .profile-footer-lower-menu .profile-footer-copyright {
        margin-bottom: 8px;
        margin-right: 0;
        width: 100%;
    }

    .profile-footer-upper {
        flex-direction: column;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .profile-footer-upper-right {
        justify-content: flex-start;
        margin-top: 32px;
    }

    .profile-footer-upper span {
        margin-right: 0 !important;
        text-overflow: ellipsis;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
    }

    .profile-footer-upper span:after {
        content: '' !important;
    }

    .change-language-button {
        margin-bottom: 16px;
    }
}

#modal_profile_footer .modal-dialog {
    width: 800px;
}

.item-main .item-main-column1 {
    width: calc(50% + 16px);
    display: flex;
    justify-content: space-between;
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
    color: #000000;
}

.item-main .item-main-column1.social-icon {
    width: 24px;
    height: 24px;
    margin-right: 32px;
}

.item-main .item-main-column1.social-icon .item-main-column1-menu-icon {
    background-color: #f0f0f0;
    width: 24px;
    height: 24px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
}


.item-main .item-main-column2 {
    width: calc(50% - 16px);
    display: flex;
    justify-content: flex-start;
    font-weight: 300;
    font-size: 13px;
    line-height: 200%;
    color: #a7a8a9;
}


.item-main .item-main-column2.social-url {
    width: calc(100% - 56px);
}

.item-main .item-main-column1 .item-main-column1-menu-title,
.item-main .item-main-column1 .item-main-column1-menu-title-en {
    width: calc(50% - 16px);
    margin-right: 16px;
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.item-main-column2-menu-link {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.footer-social-link-container {
    padding: 36px 5px;
    background: #FCFCFC;
    border-radius: 4px;
    margin-bottom: 64px;
    display: flex;
    justify-content: center;
    width: 100%;
}

.footer-social-link-container label {
    margin: 0 26px;
}

@media (max-aspect-ratio: 4/5) {
    .item-main .item-main-column1 {
        width: calc(50% - 16px);
        flex-direction: column;
        justify-content: center;
        margin-right: 32px;
        position: relative;
        font-size: 11px;
    }

    .item-main .item-main-column1.social-icon {
        width: 24px;
        height: 24px;
        margin-right: 32px;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .item-main .item-main-column2 {
        font-size: 11px;
    }

    .item-main .item-main-column1:after {
        content: ' ';
        position: absolute;
        height: 100%;
        width: 1px;
        background: #f0f0f0;
        right: -16px;
    }
    
    .item-main .item-main-column1 .item-main-column1-menu-title,
    .item-main .item-main-column1 .item-main-column1-menu-title-en {
        width: 100%;
        margin-right: 0;
    }

    .footer-social-link-container {
        padding: 32px 5px;
        background: #FCFCFC;
        border-radius: 4px;
        margin-bottom: 64px;
        display: flex;
        justify-content: center;
    }
    
    .footer-social-link-container label {
        margin: 0 11px;
    }

    .profile-footer-upper-container {
        flex-direction: column;
    }
}
