.gallery-search-delete {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a7a8a9;
    display: none;
}

.gallery-search-delete:hover {
    color: #009ace;
    cursor: pointer;
}

.gallery-seach-result-container .gallery__list-new-works {
    flex-wrap: wrap;
}

.gallery-seach-result-container .list-new-works__item-container {
    margin-right: 32px;
    margin-bottom: 32px;
}

.gallery-seach-result-container .gallery__title-new-works__container {
    margin: 64px 0 32px;
}

.search-result-count {
    color: #a7a8a9;
    margin-left: 24px;
}

.gallery-seach-result-container {
    min-height: 700px;
}

footer .footer-logo {
    margin-top: 0;
}

#modal-create-success .modal-dialog {
    width: 430px !important;
    border-radius: 12px;
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

#modal-create-success .popup-footer .btn {
    margin: 0;
    padding: 12px 66px !important;
}
