.button-upload-video-scene-title {
    flex-grow: 1;
    text-align: center;
    padding: 50px 0;
}

.daterangepicker {
    z-index: 9999 !important;
}

.button-upload-video-scene-title img {
    font-size: 2em;
    border-radius: 50%;
    background: #707070;
    padding: 20px;
}

.button-upload-video-scene-title img:hover {
    background: #009ace;
}

.modal-container {
    display: none;
    align-items: center;
    justify-content: center;
}

.modal-container .button {
    text-decoration: none;
    font-size: 2.5rem;
    font-weight: 300;
    text-transform: uppercase;
    display: inline-block;
    border-radius: 3rem;
    background-color: #fff;
    color: #fff;
    padding: 1rem 2rem;
}

.modal-container .popup {
    display: none;
    align-items: center;
    justify-content: center;
    position: fixed;
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.05);
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    overflow: hidden;
    transition: 0.3s ease-in-out;
}

/* .modal-container .popup-inner {
    position: relative;
    bottom: -100vw;
    right: -100vh;
    display: flex;
    align-items: start;
    width: 90%;
    margin-top: 2rem;
    height: 80%;
    background-color: #fff;
    transform: rotate(32deg);
    transition: 0.3s ease-in-out;
    justify-content: center;
    flex-wrap: wrap;
    overflow-y: scroll;
    overflow-x: hidden;
} */

.modal-container .preview {
    height: auto;
    overflow: hidden;
    transition: .3s ease-in-out;
}

.modal-container .preview {
    padding: 10px;
}

.modal-container.file-selected .preview {
    overflow-y: hidden;
}

.modal-container .popup__text {
    display: flex;
    flex-direction: column;
    justify-content: start;
    padding: 4rem;
}

.modal-container .popup__text h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-transform: uppercase;
    color: #0a0a0a;
}

.modal-container .popup__text p {
    font-size: 0.875rem;
    color: #686868;
    line-height: 1.5;
}

.modal-container .popup:target {
    visibility: visible;
    opacity: 1;
    display: flex;
}

/* .modal-container .popup:target .popup-inner {
    bottom: 0;
    right: 0;
    transform: rotate(0);
} */

.modal-container .popup__close {
    position: absolute;
    right: 4%;
    top: 10%;
    width: 3rem;
    height: 3rem;
    font-size: 1.2rem;
    font-weight: 500;
    border-radius: 100%;
    background-color: #0a0a0a;
    z-index: 998;
    color: #fff;
    line-height: 3rem;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
}

.modal-container label {
    color: #000;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-container input {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    font-size: 1.3rem;
}

.video-demo-item, .video-demo-header {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.video-demo-header {
    justify-content: left;
}

.video-demo-header .video-header,
.video-demo-header .thumbnail-header {
    color: #000;
    font-size: 1.5rem;
    font-weight: 600;
    width: 50%;
    margin-bottom: 10px;
}

.video-demo-item .dom-video, .canvas {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 50%;
}

.preview video {
    width: 100%;
    padding: 5px;
}

.preview canvas {
    width: 100%;
    padding: 5px;
}

.preview img {
    width: 50%;
}

.center-img-thumbnail {
    width: 50%;
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
}

.error-message {
    padding: 10px;
    color: red;
    background: white;
    display: none;
}

.error-message .show {
    display: block;
}

img.video-upload {
    position: absolute;
    right: 25px;
    background-color: #737373;
    padding: 10px;
    border-radius: 50px;
    top: 5px;
    width: 10px;
    background-image: url('../../images/icon-plus.svg');
    background-size: contain;
}

img.video-upload:hover {
    background-color: #009ace;
}

.upload-version-admin {
    height: 30px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.upload-version-admin img {
    background-color: #737373;
    padding: 10px;
    border-radius: 50px;
    background-image: url('../../images/icon-plus.svg');
    background-size: contain;
    width: 10px;
    height: 10px;
}

.upload-version-admin img:hover {
    background-color: #009ace;
}

.variation-values {
    padding: 5px 15px;
}

.button-danger {
    background: red !important;
    border: 2px solid red;
}

.button-danger:hover {
    border: 2px solid red !important;
    color: red !important;
    background: white !important;
}

.variation-values p {
    font-size: 1rem;
}

.price-user {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 8px 14px;
    position: relative;
    border: 1px solid #a7a8a9;
    border-radius: 10px;
}

.price-user__avatar {
    width: 75px;
    align-self: flex-end;
    position: relative;
}

.price-user__info {
    margin-left: 10px;
    width: calc(100% - 75px);
    -ms-grid-row-align: center;
    align-self: center;
}

.price-user__name {
    color: #333;
    font-weight: 700;
}

.price-user__set {
    display: flex;
    align-items: center;
}

.price-user__min {
    display: flex;
    justify-content: flex-end;
}

.project-setting-price__button {
    border: 1px solid #a7a8a9;
    padding: 15px;
    text-align: center;
}

.project-setting-price__button-desc {
    color: #333;
}

.project-setting-price__current {
    display: flex;
    justify-content: space-between;
    padding: 10px;
}

.select-admin__item {
    margin: 0 10px;
}

.select-admin__list {
    display: flex;
    overflow-x: scroll;
    margin: 40px 0;
}

.select-admin__list.searching .select-admin__item:not(.search-found) {
    display: none;
}

.select-admin__avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.selected .select-admin__avatar-img {
    border: 2px solid #009ace;
}

.select-admin__avatar-img {
    width: 128px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.select-admin__item .select-admin__title {
    margin-bottom: 5px;
    font-size: 1.2em;
    word-break: break-word;
}

.select-admin__item .select-admin__type {
    margin-bottom: 5px;
    font-size: .8em;
    word-break: break-word;
}

.right-align-input {
    text-align: right;
    height: 30px;
    padding-right: 15px;
}


.right-align-input::-webkit-inner-spin-button {
    margin-left: 5px;
    z-index: 2;
}

.rel {
    position: relative;
}

.price-user__currency {
    position: absolute;
    right: 3px;
    top: 5px;
}

.w-150 {
    width: 150px;
}

.price-user__min {
    color: #009ace;
    font-weight: 700;
    padding: 10px 0 0;
}

.total-price input {
    padding-right: 20px;
}

.total-price .price-user__currency {
    right: 5px;
}

.pr-5 {
    padding-right: 5px;
}

.price-user__avatar-img {
    height: 60px;
}

.project-setting-price__button {
    cursor: pointer;
}

.project-setting-price__button:hover {
    border-color: #009ace;
}

.project-setting-price__button:hover .project-setting-price__button-desc {
    color: #009ace;
}

.project-setting-price__button:hover .button--icon-add:before {
    background-image: url('../../images/icon-add-b.svg')
}

.select-admin__title {
    font-size: 1.5em;
    font-weight: 700;
    color: #333333;
    margin-bottom: 20px;
}

.select-admin__form {
    margin: 5px 10px;
}

.form-control {
    border-radius: 0;
    border: 1px solid #333333;
    margin: 5px 0;
}

.no-search-result {
    height: 180px;
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.form-row {
    display: flex;
    justify-content: center;
    margin: 20px 0 5px;
}

.button--background-secondary {
    padding: 5px 20px;
}

.button--background-secondary:hover {
    color: white;
}

.modal .modal-body {
    max-height: 550px;
}

input:disabled {
    border-color: #a7a8a9;
    background-color: #f0f0f0 !important;
}
