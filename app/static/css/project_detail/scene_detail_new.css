body {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif' !important;
}
.audio-pc {
  display: inline-block;
}

.block-right a {
  width: 64px;
}

.scene-detail-heart .txt-bellow-icon {
  font-weight: 300;
}

.header-page .block-rating .block-starts .stars,
.block-starts .stars:before {
  display: inline-block;
}

.header-page .block-rating .block-starts .stars span a,
.block-starts .stars span a:before {
  font-size: 16px;
}

.header-page .block-rating .block-starts .stars {
  font-size: 16px !important;
  letter-spacing: 1px;
}

#modal-upload-scene {
  margin: 0;
}

.form-row__mobile-date {
  padding-right: 0 !important;
  padding-bottom: 1rem;
}

.block-right a p {
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
}

.icon-edit {
  font-size: 24px !important;
  color: #a7a8a9 !important;
}

.audio-control-custom {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 1rem;
}

.block-content-scene {
  width: 100%;
  display: flex;
}

.save-bookmark-pc {
  min-width: 50px;
  font-size: 24px;
}

.share-scene-pc {
  text-align: center !important;
}

.share-scene {
  font-weight: 300;
  color: #212529;
  text-align: inherit;
  font-size: 1rem;
}

.can-share-new span,
.can-share-new p {
  color: #009ace !important;
}

.icon-share {
  font-size: 24px;
  padding-right: 0 !important;
}

.share-scene {
  color: #a7a8a9;
}

.modal-open {
  padding-right: 0;
}

.share-modal .fade:not(.show) {
  opacity: 1;
}

.save-bookmark-pc i {
  font-size: 24px;
}

.messenger-content-file .s-audio-waveform wave wave {
  border-right: none;
}

.header-page {
  height: fit-content;
  width: 100%;
  min-height: 3.5rem;
  padding: 0 0 24px 0;
  display: flex;
  align-items: center;
}

.img-back-previous {
  width: 32px;
  height: 32px;
  cursor: pointer;
}

.bookmark-button {
  color: #a7a8a9;
}

.bookmark-button .icon--sicon-bookmark-o {
  color: #a7a8a9;
}

.bookmark-button .icon--sicon-bookmark-o .txt-bookmark {
  color: #000 !important;
}

.tab-content > .tab-pane.active {
  opacity: 1;
}

.img-check-circle {
  width: 48px;
  height: 48px;
}

.txt-bellow-icon {
  margin: 0;
  font-size: 8px;
  color: #000;
  font-weight: 300;
  line-height: 100%;
  text-align: center;
}

.block-rating {
  padding-left: 48px;
}

.block-starts {
  height: 24px;
  display: flex;
  align-items: center;
  margin-left: 0;
}

.block-txt-name {
  padding-left: 64px;
  overflow: hidden;
}

.txt-des-above {
  color: #a7a8a9;
  font-size: 11px;
  font-weight: 300;
  line-height: 200%;
  margin-bottom: 0;
  text-align: left;

  /*  line-break: anywhere; */
  white-space: nowrap; /* 改行しない */
  overflow: hidden; /* はみ出たテキストを隠す */
  text-overflow: ellipsis; /* はみ出たテキストを...で表示 */
}

.txt-des {
  color: #000;
  font-size: 24px;
  font-weight: 400;
  letter-spacing: 2.5px;

  /*  line-break: anywhere; */
  white-space: nowrap; /* 改行しない */
  overflow: hidden; /* はみ出たテキストを隠す */
  text-overflow: ellipsis; /* はみ出たテキストを...で表示 */

  line-height: 100%;
  width: auto;
}

.block-right {
  display: flex;
  margin-left: auto;
  gap: 16px;
}

.block-right a:hover {
  color: #a7a8a9;
}

.pd-scene {
  padding-top: 8px;
}

.pd-comment {
  margin: 0px 0px;
  border-left: 1px solid #f0f0f0;
}

.icon-share {
  padding-right: 1.5rem;
  text-align: center;
}

.icon-edit-new {
  text-align: center;
}

.block-input-message-new {
  display: none;
}

.bookmark-button span {
  font-size: 1.5rem;
}

.padding-txt-icon {
  padding-top: 4px;
}

.txt-bookmark {
  color: #009ace !important;
}

.pd-section__content {
  width: auto !important;
}

.stars {
  font-size: 22px !important;
}

.stars a {
  color: #f0f0f0 !important;
}

.txt-rating {
  padding-top: 12px;
  color: #000;
  min-height: 8px;
  font-size: 8px;
  font-weight: 300;
  line-height: 100%;
}

#comment-tab {
  padding: 2px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

#comment-tab li {
  width: 49.5%;
}

#comment-tab .nav-item button {
  width: 100%;
  color: #a7a8a9;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  line-height: 200%;
  border-radius: 6px;
}

#comment-tab .nav-item .active {
  background-color: #a7a8a9;
  color: #fff;
}

.cscene--video {
  text-align: center;
}

.block-tab-comment {
  margin: 8px 0px 0px 8px;
  padding: 1px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  display: flex;
}

.tab-comment {
  background-color: #fcfcfc;
  display: inline-block;
  padding: 0px;
  cursor: pointer;
  margin: 0;
  width: 100%;
  border-radius: 6px;
  color: #a7a8a9;
  text-align: center;
  font-size: 13px;
  font-weight: 300;
  line-height: 200%;
}

.block-tab-comment .active {
  background-color: #a7a8a9 !important;
  color: #fff;
}

.list-scene-horizontal
  .active
  .variation-button-name-tooltip-container
  .txt-variation {
  color: #000 !important;
}


.variation-button-container:hover {
  display: inherit;
}

.name-responsive {
  display: none;
}

body .show {
  opacity: 1 !important;
}

body .show .modal-dialog {
  top: 40%;
}

.ccscene__thumb video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* .s-audio-time {
  padding-left: 10px;
} */

@media (min-width: 1400px) {

  .more-action-responsive {
    display: none;
  }

  .block-right {
    display: flex;
  }
}

.hidden-pc {
  display: none;
}

@media (max-width: 1400px) {
  .dropdown-menu-responsive a i,
  .dropdown-more-sp a i {
    font-size: 24px;
  }

  .block-right-responsive a span,
  .dropdown-more-sp a span {
    color: #000;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
  }

  .txt-bookmark-o {
  }

  .block-right {
    display: flex;
  }

  .more-action-responsive {
    display: none;
  }

  .block-right-responsive {
    text-align: end;
  }

  .block-right-responsive .dropdown-menu a:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }

  .block-dropdown-more {
    width: 200px;
    text-align: end;
    position: absolute;
    right: 2%;
  }
}

.dropdown-menu-responsive,
.dropdown-more {
  width: 203px;
  padding: 0px 8px;
  border-radius: 6px;
}

.dropdown-menu-responsive a,
.dropdown-more a {
  display: flex;
  place-content: space-between;
  align-items: center;
  padding: 8px 0px;
}

.header-page .scene-detail-heart {
  padding-left: 16px;
}

.header-page .scene-detail-heart.icon--sicon-heart-o:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-checked-readonly.svg");
  width: 64px;
  height: 64px;
  content: "";
  background-size: 64px;
  background-repeat: no-repeat;
  display: inline-block;
}

.header-page .scene-detail-heart:before,
.header-page .scene-detail-heart:hover:before {
  width: 64px;
  height: 64px;
  content: "";
  background-size: 64px;
  background-repeat: no-repeat;
  display: inline-block;
}

/* start style icon--sicon-heart check without class "cannot-check" */

.header-page
  .scene-detail-heart.project-chapter-video-undone.icon--sicon-heart:not(
    .cannot-check
  ):before {
  background-image: url("/static/images/scene-detail/icon-heart-new-hover.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-undone.icon--sicon-heart.is-owner:not(
    .cannot-check
  ):before {
  background-image: url("/static/images/scene-detail/icon-heart-new-checked.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-undone.icon--sicon-heart.is-owner:hover:not(
    .cannot-check
  ):before {
  background-image: url("/static/images/scene-detail/icon-heart-new-hover-checked.svg");
}

/* end style */
.header-page
  .scene-detail-heart.project-chapter-video-done.icon--sicon-heart:not(
    .cannot-check
  ):before {
  background-image: url("/static/images/scene-detail/icon-heart-new-default.svg");
}

/* start style icon--sicon-heart hover check without class "cannot-check" */
.header-page
  .scene-detail-heart.project-chapter-video-done.icon--sicon-heart:not(
    .cannot-check
  ):hover:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-hover.svg");
}

/* end style */

/* start style have class "cannot-check" */
.header-page
  .scene-detail-heart.project-chapter-video-done.icon--sicon-heart.cannot-check:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-default.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-undone.icon--sicon-heart.cannot-check:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-checked-readonly.svg");
}

/* end style */

/* start style icon--sicon-heart-o */
.header-page
  .scene-detail-heart.project-chapter-video-undone.icon--sicon-heart-o:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-checked-readonly.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-done.icon--sicon-heart-o:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-default.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-done.cscene__wishlist.is-owner:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-default.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-done.cscene__wishlist.is-owner:hover:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-hover.svg");
}

/* end style */

/* start style have class cscene__wishlist */
.header-page
  .scene-detail-heart.project-chapter-video-undone.cscene__wishlist.is-owner:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-checked.svg");
}

.header-page
  .scene-detail-heart.project-chapter-video-undone.cscene__wishlist.is-owner:hover:before {
  background-image: url("/static/images/scene-detail/icon-heart-new-hover-checked.svg");
}

/* end style */

@media (min-width: 768px) and (max-width: 991px) {
  .txt-des-above {
    line-break: anywhere;
  }

  .txt-bookmark-o {
  }

  .block-scene-video {
    padding-right: 10px;
  }

  .btn-tutorial-sp {
    bottom: 1vw !important;
  }

  #modal-upload-scene
    .modal-dialog
    .create-scene__action
    .create-scene__action__container {
    padding-right: 10%;
  }

  .header-page .scene-detail-heart {
    padding-left: 32px;

  }

  .block-right {
    display: flex;
    margin-left: auto;
    gap: 0;
  }

  .block-txt-name {
    padding-left: 32px;
    flex-grow: 1;
  }

  .header-page .block-rating {
    padding-right: 32px !important;
    padding-left: 24px;
  }

  .pd-scene-title-detail .pd-section__content {
    display: initial;
  }

 .scene-style .message-list-new .mmessage.mmessage--sent.clicked .mmessage-main {
    max-width: calc(100% - 44px);
  }

  .scene-style .message-list-new .mmessage.mmessage--received.clicked .mmessage-main {
    max-width: calc(100% - 51px);
  }

  .scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.messager-folder .messenger-content.messenger-content-file .s-file.s-file--file.s-file--gray {
    min-width: auto !important;
  }

  .scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.messager-folder .messenger-content.messenger-content-file .s-file.s-file--file.s-file--gray p {
    min-width: auto !important;
  }

  .scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio {
    max-width: calc(100% - 40px);
  }

  .scene-style .mmessage.mmessage--sent.clicked .mmessage-main .messages-sent-comment {
        max-width: calc(100% - 8px);
  }

  .scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .comment-audio-content .mmessenger.mmessenger--audio-wave.mmessenger--black .messenger-content.messenger-content-file .s-audio.s-audio--audio-wave.s-audio--black.wave-file-cmt {
    min-width: auto;
  }

  .scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .comments-audio-block.item-comment .comment-audio-content .mmessenger.mmessenger--audio-wave.mmessenger--gray.message-audio-block .messenger-content.messenger-content-file .s-audio.s-audio--audio-wave.s-audio--gray {
    min-width: auto;
  }

  .scene-style .mmessage.mmessage--received.clicked .mmessage-main .comments-audio-block .messenger-content .s-audio-control {
    margin-right: 0;
  }

  .scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action .file-name-cmt {
    max-width: 80% !important;
  }
}

@media (min-width: 768px) {
  /* css for PC */
  .cscene__version.slick-slide.slick-current.slick-active .cscene__version-horizontal.cscene__version-horizontal-document.ccscene__thumb.scene-type-document {
      top: 50%;
  }
  .audio-control-custom .s-audio-control {
    margin-right: 0;
    font-size: 64px;
    padding: 0 32px;
  }

  .audio-control-custom a img {
    height: 32px;
  }

  .s-audio-time {
    text-align: end;
  }
}

@media (max-width: 767px) {
  .scene-style .message-list-new .mmessage.mmessage--sent.clicked .mmessage-main {
    max-width: calc(100% - 44px);
  }

  .scene-style .message-list-new .mmessage.mmessage--received.clicked .mmessage-main {
    max-width: calc(100% - 51px);
  }

  .txt-des {
    max-width: 85%;
  }

  .txt-bookmark-o {
    color: #212529 !important;
  }

  .messenger-content .s-audio--audio-wave {
    flex-grow: 1;
    display: inline-block;
    padding-left: 0;
    padding-right: 0;
  }

  .block-right {
    display: none;
  }

  .more-action-responsive {
    display: none;
  }

  .block-txt-name {
    display: none;
  }

  .block-rating {
    border: none;
  }

  .pd-section__content {
    padding: 0;
  }

  .block-tab-comment {
    margin: 1rem !important;
  }

  .block-content-scene {
    display: inline-block;
    height: auto;
  }

  .container-new {
    padding: 0;
  }

  .header-page {
    border-bottom: none;
    padding: 48px 16px 16px;
  }

  .cscene-horizontal-dots {
    margin: 0 !important;
  }

  .list-scene-horizontal .variation-button-container {
    margin-bottom: 0;
  }

  .list-variation-container {
    margin: 0px 16px 0px;
  }

  .header-page a {
    margin-bottom: auto;
  }

  .name-responsive {
    display: block;
    padding: 0 16px;
  }

  .dropdown-more {
    right: 0;
  }

  .dropdown-more a:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }

  .line-block-name {
    padding-top: 12px;
    border-bottom: 1px solid #f0f0f0;
    margin: auto;
  }

  .maction {
    margin: 0;
  }

  .mcommment {
    border: none;
    border-top: 1px solid #f0f0f0;
  }

  .pd-comment {
    height: auto;
    max-height: 260px;
  }

  .mmessage-list {
    max-height: 150px;
  }

  .pd-comment__main {
    height: auto;
  }

  .pd-comment__content {
    height: 100%;
  }

  .mmessage-component {
    height: 100%;
  }

  .pd-section--detail {
    border-bottom: none;
  }

  .block-input-message-new {
    display: inline-block;
    flex: 0.5;
    padding: 1rem 1rem 0.5rem 1rem;
    border-top: 1px solid #f0f0f0;
  }

  .input-message-new textarea {
    border: none;
    width: 100%;
    color: #0f0f0f;
    font-size: 13px;
    font-weight: 400;
    line-height: 150%;
  }

  .input-message-new textarea::placeholder {
    color: #a7a8a9;
  }

  .input-message-new textarea:focus {
    outline: none;
  }

  .project-item {
    margin-bottom: 0;
  }

  .container-new {
    background-color: #fff;
  }

  .hidden-sp {
    display: none;
    visibility: hidden;
  }

  .s-audio-time {
    text-align: end;
    padding-top: 0.5rem;
    min-height: 24px;
  }

  .audio-control-custom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0;
  }

  .nav-play-sp {
    margin-right: 0;
  }

  .nav-play-sp i {
    font-size: 64px;
  }

  .forward-audio,
  .backward-audio {
    display: block;
  }

  .forward-audio {
    padding-left: 4rem;
  }

  .backward-audio {
    padding-right: 4rem;
  }

  .mcomment-bottom {
    padding: 8px 0px 48px;
  }

  .btn-tutorial-sp {
    bottom: 2vw !important;
  }

  .tutorial-container-background {
    background-color: #fcfcfc;
  }

  #input-expired-day {
    width: 100%;
  }

  .hidden-pc {
    display: inline-block;
  }

  body {
    width: fit-content;
  }

  .pd-section__content .header-page .scene-detail-heart:before,
  .header-page .scene-detail-heart:hover:before {
    width: 48px;
    height: 48px;
    background-size: 48px;
  }

  /* .list-variation-container .list-scene-horizontal .variation-button-container {
    margin-bottom: 10px;
  } */

  .message-list-new
    .mmessage
    .mmessage-main
    .mmessage-content
    .mmessenger--audio-wave
    .messenger-content
    .s-audio--audio-wave {
    display: flex;
    min-width: 200px;
    padding: 0.5rem 1rem;
  }

  .on-mobile {
    overflow-x: hidden;
    min-height: 100vh;
    height: auto;
  }

  .block-scene-video .cscene--video--new {
    overflow: initial;
    padding-bottom: 0 !important;
  }

  .rm-pdb {
    padding-bottom: 0 !important;
  }

  .pd-scene-title-detail {
    margin-bottom: 0;
  }

  /*new code*/
  .project-item__content {
    height: 100vh;
  }

  .project-tab-scene-detail,
  .pd-scene-title-detail {
    height: 100%;
  }

  .project-tab-scene-detail .pd-scene-title-detail .pd-section__content {
    display: inline-block;
  }

  .pd-comment {
    position: absolute;
    bottom: 0;
    padding-bottom: 48px;
  }

  .load-lasted-message {
    margin: 0 16px 32px 16px;
  }

  .message-list-new {
    padding: 0;
    display: contents;
  }

  .show-message-btn {
    min-height: 28px;
  }

  .pdf-h100 {
    height: 100% !important;
  }

  .pdf-w100 {
    width: 100% !important;
  }

  .remove-pd-bottom {
    padding-bottom: 0 !important;
  }

  .not-variation-pd-bottom {
    padding-bottom: 12px !important;
  }

  .scene-vertical-center {
    display: flex;
    justify-content: center;
  }

  .h-auto {
    height: auto;
  }

  .mmessage--sent {
    display: none;
  }

  .load-lasted-message {
    display: flex;
  }

  .block-tab-comment {
    display: none;
  }

  .pdt-16 {
    padding-top: 16px;
  }

  .block-scene-video .hw-100 {
    height: 100%;
    width: 100%;
  }

  .cscene__version.slick-slide .scene-type-document {
    top: 50%;
  }

  .cscene__version.slick-slide {
    border: none;
  }

  .s-audio-control.pin-time-audio.hidden-sp.hidden-pc {
    display: none;
  }

  .list-variation {
    margin-top: 53px;
  }

  .header-page .scene-detail-heart {
    margin-bottom: initial;
    padding-left: 48px;
  }

  .message-list-new.mmessage-list .mmessage {
    display: none;
  }

  .mmessage-component .message-list-new .load-lasted-message {
    display: flex;
  }

  .block-scene-video .cscene--video--new .cscene-meta {
    margin-bottom: 0;
  }
}

@media (min-width: 768px) {

  /*css man hinh PC*/
  .scene-type-audio.active .mmessenger.mmessenger--audio-wave{
      display: flex;
  }

  .cscene__version.slick-slide.slick-current.slick-active .space-top {
    top: 51%;
  }

  .ccscene__thumb.scene-type-document.active {
        top: 50%;
    }
  /*css tam cho man hinh lon*/
  .ccscene__thumb video {
    object-fit: contain;
  }

  .block-content-scene .block-scene-video {
    margin-right: 8px;
    padding-right: 0;
  }

  /*end css tam cho man hinh lon*/
  .pd-section__content {
    width: 100% !important;
  }

  .cscene-horizontal {
    width: 100%;
  }

  .cscene--video {
    width: 100%;
  }

  .cscene-horizontal .slick-list {
    overflow: hidden;
  }

  .pd-comment {
    max-width: 640px;
  }

  .pd-section__content {
    padding: 32px 32px 0 32px;
  }

  .list-variation {
    max-height: 358px;
  }

  .project-item {
    margin-bottom: 0;
  }

  .block-scene-video .pdbt0 {
    padding-bottom: 0 !important;
  }

  .block-scene-video .pdbt12 {
    padding-bottom: 12px !important;
  }

  .pd-scene-title-detail {
    margin-bottom: 0;
  }

  .h-100 {
    height: 100% !important;
  }

  .h-auto {
    height: auto;
  }

  .initialDl {
    display: initial;
  }

  .blockDl {
    display: block;
  }

  .block-content-scene .block-scene-video .cscene--video--new.rm-mgb {
    margin-bottom: 0 !important;
  }

  .owner-top {
    min-height: 100vh;
    height: 100vh;
  }

  .pd-scene-title-detail {
    height: 100vh;
  }

  .cscene--video--new.cscene--video--new {
    display: inline-flex;
  }

  .cscene__version.slick-slide.slick-current.slick-active
  .cscene__version-vertical.ccscene__thumb.scene-type-video {
    left: 0;
    transform: translateX(0);
  }

  .list-variation .list-variation-container:not(.hide):first-child {
    margin-bottom: 0;
  }

  .header-page .block-rating {
    padding-right: 64px;
  }

  .header-page .block-txt-name {
    border-left: 1px solid #F0F0F0;
    max-width: 50%;
  }

  .line-header {
    min-height: 1px;
    background-color: #F0F0F0;
    width: 100%;
  }

  .item-padding-right {
    padding-right: 56px !important;
  }

  .item-padding-left {
    padding-left: 56px !important;
  }
}

@media (max-width: 450px) {
  .pd-comment {
    padding: 0 0px 0;
  }
}

.mg-bt-video-new {
  margin-bottom: -52px !important;
  padding-bottom: 52px !important;
}

.cscene-vertical .mmessenger .messenger-content {
  width: 100%;
}

.cscene-vertical .mmessenger .messenger-content .s-audio {
  padding: 8px 0px;
}

.cscene-vertical .mmessenger .messenger-content .s-audio-waveform {
  flex: none;
  width: 100%;
  display: block;
}

.list-variation-container
  .list-scene-horizontal
  .variation-button-container:not(.active):hover {
  outline: 1px solid #a7a8a9 !important;
  outline-offset: -1px;
  box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.1);
}

.list-variation-container .list-scene-horizontal .active:hover {
  outline: 2px solid #0076a5 !important;
  outline-offset: -1px;
  box-shadow: 2px 5px 8px 0 rgba(0, 154, 206, 0.1);
}

.list-variation-container .list-scene-horizontal .active:hover .txt-variation {
  color: #0076a5;
}

.list-variation-container .list-scene-horizontal .active .txt-variation {
  color: #009ace;
}

.slick-current .active .slick-list .slick-track .slick-active {
  width: 100% !important;
}

#modal-upload-scene
  .modal-dialog
  .create-scene__action
  .create-scene__action__container
  .btn-popup-close {
  display: block !important;
  background-color: #fcfcfc !important;
}

#modal-upload-scene
  .modal-dialog
  .create-scene__action
  .create-scene__action__container
  .btn-popup-close
  .btn-content
  .btn-text {
  color: #53565a;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 200%;
}

.btn-tutorial-pc {
  display: none;
  right: 2vh;
  bottom: 3vh;
}

.list-variation-container
  .list-scene-horizontal
  .active
  .variation-button-name {
  color: #009ace;
}

.list-variation-container
  .list-scene-horizontal
  .active
  .variation-button-name:hover {
  color: #0076a5;
}

.message-list-new
  .mmessage
  .mmessage-main
  .mmessage-content
  .mmessenger--text
  .messenger-content
  .messenger-content
  .s-audio--audio
  .s-audio-icon {
  align-items: center;
}

.message-list-new
  .mmessage
  .mmessage-main
  .mmessage-content
  .mmessenger--text
  .messenger-content
  .messenger-content
  .s-audio--audio
  .s-audio-icon
  .video-pin-start {
  display: flex;
  align-items: center;
  margin-top: 0;
}

.sheader {
  display: none;
}

body .owner-top {
  margin-top: 0;
}

.pdb-take {
  padding-bottom: 52px !important;
}

.pd-scene.block-scene-video .pbt-0 {
  padding-bottom: 0 !important;
}

.active-scene {
  border: 5px solid #009ace;
}

.clear-border {
  border: none !important;
}

.cscene__version.slick-slide {
  border: none;
}

.scene-type-video video {
  background: none;
}

.padding-pin {
  padding: 8px 8px 48px 16px;
}

.disabled-mcomment {
  pointer-events: none;
}

.scene-detail-heart.cscene__wishlist {
  font-size: 24px;
  line-height: 22px;
}

.block-rating .cvideo__rating.block-starts {
  margin-left: 0;
  padding-bottom: 12px;
  height: auto;
}

.block-rating .cvideo__rating.block-starts .stars {
  margin-bottom: 0;
}

.block-rating .cvideo__rating.block-starts .stars span {
  margin-bottom: 0;
}

.block-rating .cvideo__rating.block-starts .stars span a {
  line-height: 2rem;
  width: 19%;
  font-size: 16px;
}

/* .header-page .block-rating {
  margin-top: auto;
} */

.header-page .block-rating .txt-rating {
  padding-top: 0;
}

/* .block-rating .block-starts .stars.admin-rate .star-1:before {
  content: url("../images/scene-detail/star_rate-cropped.svg");
}

.block-rating .block-starts .stars.admin-rate .star-2:before {
  content: url("../images/scene-detail/star_rate-cropped.svg");
}

.block-rating .block-starts .stars.admin-rate .star-3:before {
  content: url("../images/scene-detail/star_rate-cropped.svg");
}

.block-rating .block-starts .stars.admin-rate .star-4:before {
  content: url("../images/scene-detail/star_rate-cropped.svg");
}

.block-rating .block-starts .stars.admin-rate .star-5:before {
  content: url("../images/scene-detail/star_rate-cropped.svg");
} */

.block-rating .block-starts .stars.admin-rate:before {
  color: #F0F0F0;
}

.block-rating .block-starts .stars.admin-rate.selected a.active ~ a:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
}

/* .block-rating .block-starts .stars.not-yet-rate.admin-rate .star-1:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
}

.block-rating .block-starts .stars.not-yet-rate.admin-rate .star-2:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
}

.block-rating .block-starts .stars.not-yet-rate.admin-rate .star-3:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
}

.block-rating .block-starts .stars.not-yet-rate.admin-rate .star-4:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
}

.block-rating .block-starts .stars.not-yet-rate.admin-rate .star-5:before {
  content: url("../images/scene-detail/star_rate_not_check-cropped.svg");
} */

.block-back-scene-detail {
  min-width: 32px;
  min-height: 32px;
}

.mmessage .mmessage-main .border-audio-messages {
  padding: 8px;
  border: 1px solid #F0F0F0;
  margin: 0 4px;
  border-radius: 6px;
  max-width: 91%;
}

.pd-section__content:not(.active) .mmessage--received:not(.mmessage-near) .message-info-audio {
  margin-left: 4px;
}

.border-audio-message {
  padding: 8px;
  border: 1px solid #F0F0F0;
  border-radius: 6px;
}

.bg-comment-audio {
  background-color: #FCFCFC;
}

.comment-audio-content {
  /*margin-bottom: 4px;*/
  background-color: #FFF;
}

.mmessage--received:not(.reply) .mmessage-main .message-audio-block .messenger-content .s-audio.s-audio--audio-wave.s-audio--gray:not(.active) {
  background-color: #FFF;
}

.mmessage--received:not(.reply) .border-audio-messages .mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  background-color: #FCFCFC;
  border: none;
  color: #000;
}

.mmessage--received .border-audio-messages .mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  border: none;
}

.block-name-action-audio {
  display: flex;
  place-content: space-between;
  padding-bottom: 6px;
  align-items: center;
}

.audio-type-info-msg .file-info-message {
  margin-bottom: 0;
  line-height: 100%;
  font-weight: 300;
  font-style: normal;
  font-size: 8px;
  color: #A7A8A9;
}

.block-name-action-audio .file-name-message {
  margin-bottom: 0;
  line-height: 150%;
  font-weight: 300;
  font-style: normal;
  font-size: 13px;
  color: #000;
  max-width: 80%;
}

.audio-type-info-msg {
  display: flex;
  place-content: space-between;
  align-items: center;
}

.info-item-audio-comment {
  margin-top: 8px;
}

.comment-audio-content .mmessenger.message-audio-block {
  margin-bottom: 4px;
  border-bottom: 1px solid #F0F0F0;
}

.block-btn-action-audio-msg .btn-finger-print {
  padding-right: 16px;
}

.block-btn-action-audio-msg .btn-finger-print img {
  max-width: 20px;
  max-height: 20px;
}

.scene-style .mmessage .peoples-downloaded-audio-msg .has_user_downloaded .sview-user .sview-user-seen .avatar {
  flex: initial;
}
.scene-style .mmessage .comments-audio-block .messenger-content .s-audio-control {
  font-size: 32px;
}

.scene-style .mmessage .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio {
  max-width: 100%;
}

.scene-style .mmessage .comments-audio-block .messenger-content {
  margin-bottom: 4px;
}

.scene-style .mmessage .messages-sent-comment {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  padding: 8px;
  margin: 0 4px;
}

.scene-style .mmessage.mmessage--sent .comments-audio-block .messenger-content, .scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content {
  justify-content: flex-start;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .comments-audio-block {
  /*border: 1px solid #F0F0F0;*/
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .comments-audio-block:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action {
  width: 100%;
  justify-content: space-between;
  border: 1px solid #F0F0F0;
  background-color: #FFF;
  padding: 8px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other:not(.messager-folder) .messenger-content .file-comment-action {
  flex-direction: column-reverse;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action .info-message-2 {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action .comment-file-content {
  place-content: space-between;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action .info-message-2 .size-file-message {
  margin-bottom: 0;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  color: #A7A8A9;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other .messenger-content .file-comment-action .file-name-cmt {
  color: #000;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 200%;
  max-width: 85%;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment.border-audio-message {
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .mmessenger.mmessenger--text.mmessenger--black .messenger-content .s-text.s-text--black {
  color: #FFF;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment  .mmessenger--audio-wave .messenger-content .wave-file-cmt:not(.active) {
  background-color: #FFF;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment  .mmessenger--audio-wave .messenger-content .wave-file-cmt {
  width: 100%;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .messages-sent-comment .comments-audio-block .mmessenger--audio-wave {
  margin-bottom: 4px;
  border-bottom: 1px solid #F0F0F0;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .btn-download-file  {
  display: flex;
  place-items: center;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .btn-download-file .scene-file-download {
  width: 24px;
  height: 24px;
}

.scene-style .mmessage .block-btn-action-audio-msg {
  display: flex;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .comments-audio-block {
  margin-bottom: 0;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment.border-audio-message .mmessenger.mmessenger--text.mmessenger--black {
  margin-bottom: 0;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment.border-audio-message .mmessenger.mmessenger--text.mmessenger--black .messenger-content {
  justify-content: flex-start;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment.border-audio-message .mmessenger.mmessenger--text.mmessenger--black .s-text.s-text--black {
  padding: 0;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment:not(.border-audio-message) .mmessenger.mmessenger--text.mmessenger--black .s-text.s-text--black {
  padding: 0;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .messages-sent-comment.border-audio-message:not(.has-message) {
  padding: 0;
  background-color: #FCFCFC;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .messages-sent-comment.border-audio-message:not(.has-message) .comment-audio-content.comments-audio-block:not(.border-audio-message) {
  border: none;
}

.scene-style .mmessage.mmessage--sent .mmessage-content .file-comment-action .block-download-file {
  max-width: 24px;
  max-height: 24px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment {
  background-color: #009ace;
  border-radius: 6px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .txt-below-message-reply {
  background-color: #009ace;
  border: none;
  padding: 0;
  margin-left: initial;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #FFF;
  margin-top: 4px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .s-filedisable-wrap .txt-message-reply {
  padding: 8px;
  border-radius: 4px;
  color: #FFF;
  background-color: #0076A5;
  margin-left: initial;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
}

.scene-style .mmessage.mmessage--received .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  padding: 0;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  padding-bottom: 4px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .s-filedisable-wrap .s-filetext {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%; /* 19.5px */
  padding: 0;
  margin-top: 4px;
  background-color: #FCFCFC;

}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .s-filedisable-wrap .s-filedisable.s-filedisable--filedisable.s-filedisable--gray {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%; /* 19.5px */
  color: #000;
  padding: 8px;
  background-color: #F0F0F0;
  border-radius: 4px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black {
  background-color: #009ACE;
  border: none;
  padding: 0;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black .s-audio-icon .s-audio-control.video-pin-time .icon.icon--sicon-play {
  color: #FFFFFF;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black .s-audio-icon {
  flex-direction: column;
  margin-right: 8px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black .s-audio-icon .s-audio-time.video-pin-start {
  color: #FFFFFF;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  margin: 2px 0 0 0;
  text-align: center;
  padding: 0;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black .s-audio-icon .s-audio-control.video-pin-time {
  margin: 0;
}
.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger .messenger-content .s-audio.s-audio--audio.s-audio--black .s-audio-text {
  color: #FFF;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray {
  padding: 0;
  border: none;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray .s-audio-icon .s-audio-control.video-pin-time {
  margin-right: 0;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray .s-audio-icon {
  flex-direction: column;
  margin-right: 8px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray .s-audio-time.video-pin-start {
  padding-left: 0;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  color: #A7A8A9;
  margin: 2px 0 0 0;
  text-align: center;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray .s-audio-text {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #000;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio:has(> .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray.active) {
  background-color: #009ACE;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray.active .s-audio-text {
  color: #FFF;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--audio.s-audio--gray.active .s-audio-icon .s-audio-time.video-pin-start {
  color: #FFF;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray {
  padding: 0;
  background-color: #FFF;
  border: none;
  width: 100%;
  place-content: space-between;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray .btn-download-file {
  max-width: 24px;
  max-height: 24px;
  min-width: 24px;
  min-height: 24px;
  margin-left: 8px
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray .btn-download-file .scene-file-download {
  display: flex;
  width: 24px;
  height: 24px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment.border-audio-message {
  padding: 8px;
  border-radius: 6px;
  background-color: #009ace;
  border: none;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment.border-audio-message .mmessenger.mmessenger--file.mmessenger--black.message-file-other:not(:last-child) {
  margin-bottom: 0;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content {
  margin-bottom: 0;
  flex-direction: column;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .comments-audio-block .comment-audio-content {
  border: 1px solid #F0F0F0;
  border-radius: 6px;
  padding: 8px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages:not(.multiple-file-message) {
  background-color: #FCFCFC;
}

/*image comment*/

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--black.message-file-other .messenger-content .s-file.s-file--black.messenger-image-preview-content.file-comment-action {
  flex-direction: column-reverse !important;
}

.scene-style .message-list-new.mmessage-list .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content {
  display: flex;
  flex-direction: column-reverse;
}

.scene-style .message-list-new.mmessage-list .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content:has(> .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.block-download-file) {
  display: flex;
  flex-direction: column;
  background-color: #FFF;
}


.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment:has(> .mmessenger--file.mmessenger--black.message-file-other .messenger-content .messenger-image-preview-content) .mmessenger.mmessenger--text.mmessenger--black .messenger-content .s-text.s-text--black {
  padding: 0;
  border: none;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #FFF;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other:has(> .messenger-content .messenger-image-preview-content):not(.single-file-image) {
  margin: 0;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other:has(> .messenger-content .messenger-image-preview-content):last-child {
  padding-bottom: 0;
}


.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other:has(> .messenger-content .messenger-image-preview-content):last-child {
  border-bottom: none;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other:has(> .messenger-content .messenger-image-preview-content) .messenger-content .s-file--file.s-file--black.messenger-image-preview-content.file-comment-action .comment-file-content p {
  font-size: 11px;
  font-style: normal;
  font-weight: 300;
  line-height: 200%;
  color: #000;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other .messenger-content .s-file--file.s-file--black.messenger-image-preview-content.file-comment-action {
  padding: 8px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file.mmessenger--black.message-file-other .messenger-content .s-file--file.s-file--black.messenger-image-preview-content.file-comment-action .comment-file-content {
  justify-content: space-between;
  margin-bottom: 0;
  margin-top: 4px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray.messenger-image-preview-content {
  flex-direction: column-reverse !important;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio:has(> .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray.messenger-image-preview-content) .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #000;
  padding-bottom: 8px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .s-file.s-file--file.s-file--gray.messenger-image-preview-content .comment-file-content {
  margin: 0;
  justify-content: space-between;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .s-file.s-file--file.s-file--gray.messenger-image-preview-content .comment-file-content p {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #000;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages:has(> .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray.messenger-image-preview-content) {
  padding: 8px;
  border: 1px solid #F0F0F0;
  background-color: #FCFCFC;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray.messenger-image-preview-content {
  background-color: #FFF;
  padding: 8px;
  border: 1px solid #F0F0F0;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray.single-file-image .messenger-content:has(> .s-file.s-file--file.s-file--gray.messenger-image-preview-content) {
  margin-bottom: 0;
}

.scene-style .mmessage .mmessage-main .mmessage-content.messages-sent-comment {
  width: 100%;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  padding: 8px;
  color: #FFF !important;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.messager-folder .messenger-content.messenger-content-file .s-file.s-file--file.s-file--gray {
  background-color: #FCFCFC;
  border: 1px solid #F0F0F0;
  padding: 8px 4px;
  min-width: 150px;
  width: 100%;
  justify-content: space-between;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.messager-folder .messenger-content.messenger-content-file .s-file.s-file--file.s-file--gray p {
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%;
  color: #000;
  min-width: 150px;
  padding-left: 4px
}

.scene-style .fade.in {
  opacity: 1;
}

.scene-style .mmessage .mmessage-main .mmessage-content .comments-audio-block .comment-audio-content .mmessenger.mmessenger--audio-wave.mmessenger--gray.message-audio-block .messenger-content.messenger-content-file .s-audio.s-audio--audio-wave.s-audio--gray {
  width: 100%;
}

/*dropdown button*/

.owner-top:not(.scene-style) .dropdown-comment-new {
  display: none;
}

.dropdown-comment-new {
  place-self: center;
  cursor: pointer;
}

.dropdown-menu-comment {
  border-radius: 6px;
  background-color: #FFF;
  min-width: 200px;
  box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
  border-color: #FFF;
  padding: 0 8px;
}

.dropdown-menu-comment li .dropdown-item {
  display: flex;
  justify-content: space-between;
  padding: 0;
}

.dropdown-menu-comment li .dropdown-item:hover, .dropdown-menu-comment li .dropdown-item:focus {
  background-color: #FFF;
}

.dropdown-menu-comment li .dropdown-item .txt-item-comment {
  font-weight: 300;
  font-size: 13px;
  color: #000;
  font-style: normal;
  line-height: 200%;
}

.dropdown-menu-comment li {
  padding: 8px 0;
}

.dropdown-menu-comment li:not(.last-action-comment) {
  border-bottom: 1px solid #F0F0F0;
}

.dropdown-item .txt-green {
  color: #2CC84D !important;
}

.last-action-comment .mmessage-reply.active, .mmessage--sent .dropdown-menu-comment li .dropdown-item.active {
  background-color: #FFF;
}

.fade.in {
  opacity: 1;
}

.scene-style .folder-modal.show .modal-dialog .modal-content .modal-header {
  justify-content: end;
}

.scene-style .messenger-content.messenger-content-file {
  align-items: center;
}

.scene-style .dropdown.dropdown-file {
  margin: 0 4px;
  cursor: pointer;
}

.scene-style .dropdown.dropdown-file .btn-more-horiz-file {
  min-width: 24px;
}

.scene-style .dropdown-file .dropdown-menu {
  border-radius: 6px;
  background-color: #FFF;
  min-width: 200px;
  border-color: #FFF;
  padding: 0 8px;
  box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}

.scene-style .dropdown-file .dropdown-menu li {
  padding: 8px 0;
}

.scene-style .dropdown-file .dropdown-menu li:not(.last-action-comment) {
  border-bottom: 1px solid #F0F0F0;
}

.scene-style .dropdown-file .dropdown-menu li .dropdown-item.active {
  background-color: #FFF;
}

.scene-style .dropdown-file .dropdown-menu li .dropdown-item {
  display: flex;
  place-content: space-between;
  padding: 0;
}

.scene-style .dropdown-file .dropdown-menu li .dropdown-item .txt-item-comment {
  font-weight: 300;
  font-size: 13px;
  color: #000;
  font-style: normal;
  line-height: 200%;
}

.scene-style .mmessage-main .has_user_downloaded .avatar {
  flex: initial;
}

/*end dropdown button*/

.scene-style .message-list-new.mmessage-list .mmessage.clicked .mmessage-info .dropdown.dropdown-comment-new {
  height: calc(50% + 5px);
  display: flex;
  align-items: end;
}

.scene-style .mmessage.mmessage--received .mmessage-main .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content.messenger-content-file .s-file.s-file--gray.messenger-image-preview-content .info-message-2 {
  display: flex;
  place-content: space-between;
  width: 100%;
  align-items: center;
  padding-top: 8px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content.messenger-content-file .s-file.s-file--gray.messenger-image-preview-content .info-message-2 .size-file-message {
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  color: #A7A8A9;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray.block-download-file .messenger-content.messenger-content-file .info-message-2 {
  display: flex;
  place-content: space-between;
  width: 100%;
  align-items: center;
  padding-top: 8px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray.block-download-file .messenger-content.messenger-content-file .info-message-2 .size-file-message {
  margin-bottom: 0;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%; /* 8px */
  color: #A7A8A9;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessenger.mmessenger--file.mmessenger--gray.messager-folder .messenger-content.messenger-content-file .s-file.s-file--gray .scene-folder-message {
  width: 24px;
  height: 24px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessenger.mmessenger--file.mmessenger--gray.messager-folder .messenger-content.messenger-content-file .s-file.s-file--gray .scene-navigation-next {
  width: 24px;
  height: 24px;
  padding-left: 4px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .block-bellow-message-file .txt-file-message {
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  color: #A7A8A9;
  margin-bottom: 0;
  padding-top: 6px;
}

.scene-style .mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .messenger-content.messenger-content-file .s-file.messenger-image-preview-content.file-comment-action .image-preview-comment.active-view img {
  border-radius: 6px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file .messenger-content.messenger-content-file .messenger-image-preview-content.file-comment-action .info-message-2 {
  display: flex;
  place-content: space-between;
  width: 100%;
  align-items: center;
  padding-top: 8px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content.messages-sent-comment .mmessenger.mmessenger--file .messenger-content.messenger-content-file .messenger-image-preview-content.file-comment-action .info-message-2 .size-file-message {
  margin-bottom: 0;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%; /* 8px */
  color: #A7A8A9;
}

.scene-style .mmessage.mmessage--received .mmessage-main .multiple-file-message .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray.messager-document-other {
  border: 1px solid #F0F0F0;
  padding: 8px;
  border-radius: 6px;
  background-color: #FCFCFC;
}

.scene-style .dropdown-item.mmessage-edit .img-edit-comment, .scene-style .dropdown-item.mmessage-reply .img-reply-comment {
  width: 24px;
  height: 24px;
}

.scene-style .dropdown-item.mmessage-edit .img-edit-comment {
  background: url("../images/scene-detail/icon-edit.svg");
}

.scene-style .mmessage.clicked.editing .dropdown-item.mmessage-edit .img-edit-comment {
  background: url("../images/scene-detail/icon-edit-active.svg") !important;
}

.scene-style .mmessage.clicked.editing .dropdown-item.mmessage-edit .txt-item-comment.txt-edit-comment, .scene-style .mmessage.clicked.reply .dropdown-item.mmessage-reply .txt-item-comment.txt-reply-comment {
  color: #009ACE !important;
}

.scene-style .dropdown-item.mmessage-reply .img-reply-comment {
  background: url("../images/scene-detail/icon-reply.svg");
}

.scene-style .mmessage.clicked.reply .dropdown-item.mmessage-reply .img-reply-comment {
  background: url("../images/scene-detail/icon-reply-active.svg") !important;
}

.scene-style .mmessage.mmessage--received.reply .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray p {
  color: #000000;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.bg-comment-audio .comments-audio-block .mmessenger.mmessenger--file.mmessenger--gray .messenger-content .s-file.s-file--file.s-file--gray p {
  color: #000;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 150%; /* 19.5px */
}

.scene-style .dropdown-item.mmessage-resolve .img-resolve-comment {
  width: 24px;
  height: 24px;
}

span.material-symbols-rounded.img-resolve-comment {
  color: #F0F0F0;
}

.scene-style .dropdown-item.mmessage-resolved .txt-item-comment {
  color: #009ACE !important;
}

.scene-style .dropdown-item.mmessage-resolve .img-resolve-comment {
  background: url("../images/scene-detail/icon-resolve.svg");
}

.scene-style .dropdown-item.mmessage-resolved .img-resolve-comment {
  background: url("../images/scene-detail/icon-resolve-active.svg") !important;
}

.scene-style .message-list-new .mmessage .mmessage-info .message-info-container .mmessage-status .mmessage-time {
  margin-bottom: 0;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content .file-comment-action {
  min-width: 150px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content .file-comment-action .left-folder-message {
  display: flex;
  max-height: 24px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content .file-comment-action .left-folder-message .file-name-cmt {
  padding-left: 4px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content:has(.txt-total-file) {
  display: inline-block;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content .txt-total-file {
  color: #FFFFFF;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;
  line-height: 100%;
  margin-bottom: 0;
  padding-top: 6px;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content {
  width: 100%
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content.border-audio-messages.multiple-file-message .messager-folder .messenger-content-file .s-file--gray .left-folder-message {
  display: flex;
  align-items: center;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message {
  width: 100%;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file:has(.message-video) {
  padding: 8px;
  border: 1px solid #F0F0F0;
  border-radius: 6px;
  background-color: #FFF;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message .action-right-video {
  display: flex;
  align-items: center;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message .action-right-video .btn-finger-print {
  padding-right: 16px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message .action-right-video .btn-finger-print img {
  max-width: 20px;
  max-height: 20px;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message .action-right-video .btn-download-file {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  margin-left: 0 !important;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray .messenger-content-file .message-video .block-info-video-message .action-video-message .action-right-video .btn-download-file .scene-file-download {
  max-width: 24px;
  max-height: 24px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .messenger-content-file .message-video-file .block-info-video-message {
  width: 100%;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .message-video-file .comment-file-content .action-right-video {
  display: flex;
  align-items: center;
  z-index: 1;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .message-video-file .comment-file-content .action-right-video .btn-finger-print {
  padding-right: 16px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .message-video-file .comment-file-content .action-right-video .btn-finger-print img {
  max-width: 20px;
  max-height: 20px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .message-video-file .comment-file-content .action-right-video .btn-download-file {
  max-width: 24px;
  max-height: 24px;
}

.scene-style .mmessage.mmessage--sent .mmessage-main .mmessage-content .mmessenger--file .message-video-file .comment-file-content .action-right-video .btn-download-file .scene-file-download {
  width: 24px;
  height: 24px;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .s-filedisable-wrap .s-filetext {
  padding: 8px;
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--gray {
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-audio.s-audio--gray .s-audio-text {
  color: #FFF !important;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages {
  background-color: #009ace !important;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content.bg-comment-audio {
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  padding: 0 0 4px 0;
}

.scene-style .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content.multiple-file-message .comments-audio-block.item-comment .mmessenger--file.mmessenger--gray.block-download-file .messenger-content.messenger-content-file {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content.multiple-file-message .comments-audio-block.item-comment .mmessenger--gray.block-download-file .messenger-content-file .messenger-image-preview-content .comment-file-content {
  justify-content: space-between;
}

.scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger--file.block-download-file.messager-folder .messenger-content-file .file-comment-action {
  border-radius: 6px;
  background-color: #FCFCFC;
  border: 1px solid #F0F0F0;
}

.scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content.messages-sent-comment.border-audio-message .mmessenger.mmessenger--file.block-download-file.mmessenger--black.message-file-other:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content.border-audio-message.has-message .mmessenger.mmessenger--text.mmessenger--black {
  padding-bottom: 8px;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .comments-audio-block.item-comment .comment-audio-content:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .comments-audio-block.item-comment .mmessenger.mmessenger--file.mmessenger--gray.messager-document-other.block-download-file:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .comments-audio-block.item-comment .mmessenger.mmessenger--file.mmessenger--gray.block-download-file:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content.bg-comment-audio.border-audio-messages.multiple-file-message .mmessenger.mmessenger--file.mmessenger--gray.messager-folder.block-download-file:not(:last-child) {
  margin-bottom: 4px;
}

.scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--file.messager-folder .messenger-content.messenger-content-file .s-file.s-file--file.s-file--black.file-comment-action {
  padding: 8px 4px
}

   .scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .comments-audio-block .comment-audio-content .info-item-audio-comment .block-name-action-audio .block-btn-action-audio-msg .btn-download-file {
    min-width: 24px;
    min-height: 24px;
  }

  .scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .comments-audio-block .comment-audio-content .info-item-audio-comment .block-name-action-audio .block-btn-action-audio-msg .btn-finger-print {
    min-width: 20px;
    min-height: 20px;
  }

  .scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .comment-audio-content .info-item-audio-comment .block-name-action-audio .block-btn-action-audio-msg .btn-download-file {
    min-width: 24px;
    min-height: 24px;
  }

  .scene-style .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .comment-audio-content .info-item-audio-comment .block-name-action-audio .block-btn-action-audio-msg .btn-finger-print {
    min-width: 20px;
    min-height: 20px;
  }

.scene-style .mmessage.mmessage--sent.clicked .mmessage-info .dropdown.dropdown-comment-new.dropdown-comment {
  justify-content: start;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received {
  justify-content: end;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block .mmessenger--gray.single-file-image .messenger-content-file:has(.message-video) {
  flex-direction: column;
  margin: 0;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content .comments-audio-block.item-comment .mmessenger--gray.single-file-image .messenger-content.messenger-content-file {
  padding: 8px;
  border: 1px solid #F0F0F0;
  border-radius: 6px;
  background-color: #FFF;
}

.scene-style .mmessage.mmessage--received .mmessage-main .mmessage-content:has(> .comments-audio-block.item-comment .mmessenger--file.mmessenger--gray.single-file-image) {
  padding: 0 !important;
  border: none !important;
}

.scene-style .mmessage.mmessage--received.reply .mmessage-main .mmessage-content .comments-audio-block.item-comment .mmessenger--file.mmessenger--gray.single-file-image{
  padding: 8px;
  border: 1px solid #009ace;
  border-radius: 6px;
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--sent.reply .mmessage-main .mmessage-content .comment-audio-content.comments-audio-block .mmessenger.mmessenger--audio-wave.mmessenger--black .messenger-content.messenger-content-file .s-audio--audio-wave.s-audio--black.wave-file-cmt {
  background-color: #009ace;
}

.scene-style .mmessage.mmessage--sent.editing .mmessage-main .mmessage-content .comment-audio-content.comments-audio-block .mmessenger.mmessenger--audio-wave.mmessenger--black .messenger-content.messenger-content-file .s-audio--audio-wave.s-audio--black.wave-file-cmt {
  background-color: #009ace;
}

.scene-style .mmessage.clicked .mmessage-info .message-info-container .mmessage-status .mmessage-user .mmessage-user-seen .avatar--image.avatar--14.avatar-seen .avatar-image {
  width: 12px;
}

.scene-style .mmessage.mmessage--received.clicked .only-text-message .mmessenger.mmessenger--text.mmessenger--gray {
  margin-bottom: 0;
}

.scene-style .mmessage.mmessage--received.clicked .only-text-message .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
  padding-bottom: 0;
}

.scene-style .message-list-new.mmessage-list .mmessage.clicked .mmessage-info .message-info-container {
  height: calc(50% - 5px);
}

.scene-style .message-list-new.mmessage-list .mmessage.clicked .acr-result-icon {
  position: initial;
  transform: initial;
  margin-right: 0;
}

.scene-style .message-list-new.mmessage-list .mmessage.clicked .btn-finger-print:not(.active) {
  cursor: initial;
}

.scene-style .message-list-new.mmessage-list .mmessage.clicked .more-action-hoz {
  width: 20px;
  height: 10px;
}

.scene-style .message-list-new.mmessage-list .mmessage.mmessage--sent.clicked .mmessage-info .dropdown.dropdown-comment-new .dropdown-toggle.show-more-comment {
  padding-left: 1px;
  display: none;
}

.scene-style .message-list-new.mmessage-list .mmessage.mmessage--received.clicked .mmessage-info .dropdown.dropdown-comment-new .dropdown-toggle.show-more-comment-received {
  padding-right: 1px;
  display: none;
}

.scene-style .message-list-new.mmessage-list .mmessage.mmessage--received.clicked .comment-audio-content {
  padding: 8px;
  border: 1px solid #F0F0F0;
  border-radius: 6px;
}

.scene-style .mattach-preview-container .progress {
  border: none;
}

.scene-style .folder-modal.show .modal-dialog {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.scene-style .mmessage.mmessage--sent.clicked .mmessenger.mmessenger--file.block-download-file.messager-folder {
  display: flex;
}

.scene-style .mmessage.mmessage--sent .messages-sent-comment .message-file-other.messager-folder .messenger-content .file-comment-action .file-name-cmt {
  max-width: 90%
}

.scene-style .list-variation {
  flex-direction: column;
}

.scene-style .block-list-variation {
  display: flex;
  flex-direction: column-reverse;
}

.scene-style .mmessage.mmessage--sent .messenger-content-file .s-file.file-comment-action .document-file {
  margin-bottom: 6px;
}

.scene-style .mmessage.clicked .block-pdf-image {
  height: 300px;
}

.scene-style .mmessage.clicked .block-pdf-image .pdf-image {
  max-height: 100%;
}

.scene-style .mmessage.mmessage--received.clicked.load-lasted-message .comments-audio-block.item-comment .mmessenger.mmessenger--file.mmessenger--gray.messager-document-other.block-download-file .messenger-content.messenger-content-file {
 display: block;
}

.scene-style .mmessage.mmessage--received.clicked .mmessage-main .mmessenger.mmessenger--file.mmessenger--gray.messager-document-other.block-download-file .messenger-content.messenger-content-file {
  display: block;
}

#modal-ACR-check-popup .modal-dialog .modal-content {
  border: none;
}

/* .owner-top.scene-detail-page.scene-style:has(#modal-ACR-check-popup) {
  z-index: 2;
} */

.bootbox.modal.fade.bootbox-confirm.show {
  z-index: 999999
}

.show-more-action-message {
    display: none
}

.show-more-action-message.show-action{
    display: flex !important;
}

.mmessage--sent.clicked .mmessage-info .message-info-container .mmessage-status .mmessage-user .notification.notification--outline-gray.notification--round  {
  margin-left: 2px
}

.scene-style .mmessage.mmessage--sent.clicked .s-text.s-text--black a {
    color: #FFFFFF;
}

.scene-style .mmessage.mmessage--sent.clicked .s-text.s-text--black a:hover {
    color: #efeded;
    text-decoration: underline;
}

.scene-style .mmessage.mmessage--received.clicked .s-text.s-text--gray a:hover {
    text-decoration: underline;
}