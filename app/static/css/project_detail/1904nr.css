@charset "UTF-8";

*,*::before, *::after {
    box-sizing: border-box;
}
.newsSec {
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    display: block;
    padding-left: 5px;
    padding-right: 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.release {
}
.newscap {
    font-size: 1.5rem;
    line-height: 1.8;
}
.Title {
    color: #000000;
    text-align: center;
    font-size: 1.8rem;
    font-family: 'A+mfCv-AXISラウンド 100 B StdN';
    line-height: 1.8;
    margin-top: 15px;
}
.Title .subTitle {
    font-size: 1.6rem;
    color: #009DC4;
}
.newsText {
    font-size: 1.5rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    color: #333333;
    line-height: 1.8;
    text-align: justify;
}
.newscap .newsdate {
    text-align: right;
}
.newscap .release {
    text-align: left;
}
.youtube {
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 60px;
    margin-bottom: 60px;
    height: 100%;
}
.line {
    color: #333;
    font-size: 2rem;
    display: block;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    padding-bottom: 5px;
    margin-bottom: 15px;
}
.fullText {
    font-size: 1.5rem;
    line-height: 1.8;
}

.line::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #D0D0D0;
}
.boxTitle {
    color: #009DC4;
    font-size: 1.5rem;
    background-color: #F0F0F0;
    margin-top: 10px;
    margin-bottom: 5px;
    text-indent: 5px;
    padding-top: 2px;
    padding-bottom: 2px;
}

.signUp {
    background-repeat: no-repeat;
    background-image: url("/static/images/btn_signup_hover.jpg");
    text-align: center;
    background-position: center 0;
}

.signUp a{
    display: inline-block;
    text-align: center;
}

.signUp a:hover{
    background: none;
}

.signUp a:hover img {
    visibility: hidden;
}



/*++++++++++++++++++++++++++++*/
/*common*/
/*++++++++++++++++++++++++++++*/
@media screen and ( max-width:999px ){/* SP+TB =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=- */
#container .pc{ display: none;}
#container .sp{}

.Title {
    color: #000000;
    text-align: center;
    font-size: 1.8rem;
    font-family: 'A+mfCv-AXISラウンド 100 B StdN';
    line-height: 1.8;
    margin-top: 15px;
}

}
@media screen and ( min-width:1000px ){/* PC =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=- */
#container .sp{ display: none;}
#container .pc{}

.Title {
    color: #000000;
    text-align: center;
    font-size: 2.5rem;
    font-family: 'A+mfCv-AXISラウンド 100 B StdN';
    line-height: 1.8;
    margin-top: 15px;
}

}

/*++++++++++++++++++++++++++++*/
/*news*/
/*++++++++++++++++++++++++++++*/

/*==========================================================*/
/* footer */
/*==========================================================*/
footer .logoBox{
display: none;
}
.screenShot {
    display: table;
    margin-top: 30px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}
.heroImage {
    margin-top: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.screenShot .threeDiv {
    display: table-cell;
    padding-left: 5px;
    padding-right: 5px;
}
.screenShot .threeDiv p {
    color: #009DC4;
    text-align: left;
    font-size: 1.1rem;
}
