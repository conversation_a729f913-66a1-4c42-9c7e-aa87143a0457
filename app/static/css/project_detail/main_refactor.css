/* 
.position-sticky {
    position: sticky;
}

.position-absolute {
    position: absolute;
    bottom: 0;
}

.top-0 {
    top: 0;
}

.top-75 {
    top: 75px;
}

.sheader-scroll-up {
    transition: top 0.2s ease 0s;
    top: 0;
}

.sheader-scroll-down {
    transition: top 0.2s ease 0s;
    top: -64.9006px;
}

.footer-comment-block-up {
    bottom: 100px!important;
}

.sheader-pc-placeholder {
    display: flex;
    width: 100%;
    height: 64px;
    transition: height 0.2s ease 0s;
}
  
.sheader-pc-placeholder.up {
    transition: height 0.2s ease 0s;
    height: 0px;
}

.sheader-pc-placeholder.down {
    transition: height 0.2s ease 0s;
    height: 64px;
}

.refactor .mcolumn.mcolumn--left.column-list-offer.resize-drag{
    margin-top: 44px;
}

.action-panel-head.action-panel-head-custom.action-add-offer.refactor {
    position: absolute;
}

.menu-offer-block-up {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 64px - 75px - 80px - 44px)!important;
}

.menu-offer-block-down {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 75px - 44px)!important;
}

.download-block-up {
    height: calc(100svh - 64px - 75px - 80px)!important;
}

.minfo-section.minfor-document {
    height: 100%;
}

.minfo-wrap.mscrollbar.mscrollbar--vertical {
    height: 100%;
}

.content-block-up {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 64px - 75px - 80px)!important;
}

.offer-content-message {
    height: 100%;
}

.content-block-down {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 75px)!important;
}

.mcolumn.mcolumn--main.DM-box-container.dm-block-message {
    max-height: 100%;
}

.mcontent.refactor {
    width: clamp(320px, 100%, 740px);;
}
.banner-project-backgroud {
    transition: top 0.2s ease 0s;
}

.banner-project-down {
}

.banner-project-up {
}

.bottom-navigation-down {
    bottom: -80px;
}

.bottom-navigation-up {
    bottom: 0;
}

.navigation-top-bar-down {
    top: 35px;
}

.navigation-top-bar-up {
    top: 75px
}

.mmessage--sent:hover>.mmessage-info>.dropdown-comment>.show-more-action-message{
    display: flex!important;
}

.mmessage--received:hover>.mmessage-info>.dropdown-comment>.show-more-action-message{
    display: flex!important;
}


.li-reply-message>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply.svg');
}

.li-reply-message:hover>.mmessage-reply>.txt-reply-comment {
    color: #009ACE!important;
}
.li-reply-message:hover>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply-active.svg');
}


.li-edit-message>.mmessage-edit>.img-edit-comment {
    background: url('/static/images/scene-detail/icon-edit.svg');
}

.li-edit-message:hover>.mmessage-edit>.txt-edit-comment {
    color: #009ACE!important;
}
.li-edit-message:hover>.mmessage-edit>.img-edit-comment {
    background: url('/static/images/scene-detail/icon-edit-active.svg');
}


.li-resolve-message>.mmessage-resolve>.img-resolve-comment {
    background: url('/static/images/scene-detail/icon-resolve.svg');
}

.li-resolve-message:hover>.mmessage-resolve>.txt-item-comment {
    color: #009ACE!important;
}
.li-resolve-message:hover>.mmessage-resolve>.img-resolve-comment {
    background: url('/static/images/scene-detail/icon-resolve-active.svg');
}
.last-action-comment>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply.svg');
}

.last-action-comment:hover>.mmessage-reply>.txt-reply-comment {
    color: #009ACE!important;
}
.last-action-comment:hover>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply-active.svg');
}

.mmessage-component.refactor {
    max-height: 611.491px;
}

.pd-section .mmessage-list.refactor {
    padding: 16px 0 92px 8px;
}
.max-height-mmessage-component-up {
    max-height: 611.491px!important;
}

.max-height-mmessage-component-down {
    max-height: 91svh!important;
}

.max-height-pd-section-file-up {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 64px - 75px - 80px)!important;
}

.max-height-pd-section-file-down {
    transition: height 0.2s ease 0s;
    height: calc(100svh - 75px)!important;
}


@media (max-width: 576px){
    .refactor .mcolumn.mcolumn--left.column-list-offer.resize-drag{
        margin-top: 0px;
    }
    .mcolumn.mcolumn--right {
        padding-top: 0px!important;
    }
}

.sidebar.refactor {
    position: absolute;
    top: 0px;
}

.refactor .project-tab.project-tab-product-comment {
    position: relative;
    height: 100%;
}

.refactor .pd-section.pd-section--detail {
    position: relative;
    height: 100%;
}

.refactor .pd-section__content.main-talk-room {
    position: relative;
    height: 100%;
}

.refactor .pd-comment {
    position: relative;
    height: 100%;
}

.refactor .pd-comment__content {
    position: relative;
    height: 100%;
}

.refactor .mmessage-component {
    position: relative;
    height: 100%;
}

.refactor .pd-comment__main {
    position: relative;
    height: 100%;
}

.mmessage-component.refactor.mmessage-component-refactor {
    max-height: 100%;
}

.project-item__content.refactor {
    height: calc(100svh - 64px - 75px - 80px);   
}

.project-tab.project-tab-progress.active {
    margin-top: 45px;
}

.mcolumn.mcolumn--right {
    padding-top: 40px;
} */