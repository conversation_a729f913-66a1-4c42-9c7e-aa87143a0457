.modal-centered:before {
    content:"";
    display:inline-block !important;
    width:0;
    height:100% !important;
    vertical-align:middle !important;
}

@media (max-width: 576px) {
    .modal-centered:before {
        height: 0 !important;
    }
}

.modal-centered {
    vertical-align: middle !important;
}

.modal-centered .modal-dialog.popup-dialog {
    vertical-align: middle;
}

.long-modal.modal .modal-body,
.long-modal.modal .popup-body {
    overflow-y: scroll;
    max-height: calc(100vh - 175px);
}

.error-message {
    position: absolute;
    bottom: -16px;
    left: 9px;
    color: #2cc84d !important;
    padding: 0 !important;
}

.popup-footer {
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
    padding-top: 20px;
}
