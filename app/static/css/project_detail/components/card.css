.card-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important;
    margin-top: 12px;
}

.input-radio-card {
    width: 192px;
    filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.05));
    background: #fff;
    border-radius: 12px;
    cursor: pointer;
}

.input-radio-card.btn--disabled {
    filter: grayscale(1);
}

.input-radio-card:not(:last-child) {
    margin-right: 32px !important;
}

.input-radio-card-content {
    padding: 12px;
}

.input-radio-card-content-highlight {
    color: #009ace !important;
    padding: 12px 0 4px;
}

.input-radio-card-img {
    background: #fff;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 192px;
    width: 192px;
    border-radius: 12px 12px 0 0;
}

input[type="radio"]:checked + label.input-radio-card {
    outline: 3px solid #009ace;
}

@media screen and (max-width: 767px) {
    .card-container {
        overflow-x: scroll;
        overflow-y: hidden;
        padding: 12px 24px 24px 12px;
    }
}
