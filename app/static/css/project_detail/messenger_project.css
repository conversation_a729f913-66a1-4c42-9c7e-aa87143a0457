.messenger-project {
    margin-top: 75px;
}

.project-item__info {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.project-item__title:before {
    padding-right: 5px;
}

.project-item__title {
    text-transform: uppercase;
}

.project-item__member-item img {
    border-radius: 50%;
}

.modal:before {
    height: 75px;
}

.messenger-director__info {
    background: white;
    padding: 20px 10px;
}

.messenger-carousel-item__type {
  position: absolute;
  z-index: 11;
  padding: 2px 15px;
  top: 5px;
  right: 10px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  cursor: pointer;
  font-weight: 700;
  text-align: center;
  background: #009ace;
  color: white;
  border-radius: 10px;
}

.messenger-carousel-item__type:hover {
  background: #0076a5;
}

.pdf-item-button-bottom {
  position: absolute;
  bottom: calc(50% - 80px);
  left: 50%;
  width: 125px;
  height: 125px;
  padding-top: 20px;
  transform: translateX(-50%);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  opacity: 0;
  z-index: 100;
  transition: .2s ease-in-out;
  display: flex !important;
  justify-content: center;
  flex-direction: column;
  background-color: rgba(167, 168, 169, 0.6);
  font-weight: 600;
}

.pdf-item-button-bottom:before {
  content: '';
  display: block;
  width: 28px;
  height: 28px;
  background-repeat: no-repeat;
  background-image: url("../images/icon-check.svg");
  background-size: 24px;
  background-position: center;
  filter: invert(93%) sepia(93%) saturate(28%) hue-rotate(20deg) brightness(107%) contrast(105%);
  margin: -20px auto 8px;
  transition: .2s ease-in-out;
}

.pdf-item-button-bottom:hover {
  background-color: rgba(167, 168, 169, 0.8);
  cursor: pointer;
}

.mark-as-ok-plan.pdf-item-button-bottom:before {
  background-image: url("../images/icon-check.svg");
}

.mark-as-ok-contract.pdf-item-button-bottom:before {
  background-image: url("../images/icon-heart.svg");
}

.mark-as-ok-bill.pdf-item-button-bottom:before {
  background-image: url("../images/icon-payment.svg");
  filter: none;
}

.upload__file-admin.pdf-item-button-bottom:before {
  background-image: url("../images/icon-upload.svg");
}

.messenger-carousel-item:hover .pdf-item-button-bottom {
  opacity: 1;
}

@media (max-width: 768px) {
  .on-mobile .pdf-item-button-bottom {
    bottom: 20%;
    left: 50%;
    right: auto;
    top: auto;;
  }
}

.project-item__filter-item {
  margin: 0;
  border-radius: 50px;
  width: auto;
  min-width: 50px;
  padding: 0;
}

.download__file-pdf {
    position: absolute;
    right: 5px;
}

.comment__download--bottom {
  margin-left: 0;
}

.icon-check-status {
  width: 30px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.icon-plan-undone:hover{
  background-image: url("../images/icon-check-hover.svg");
  cursor: pointer
}

.icon-plan-done {
  background-image: url("../images/icon-check-done.svg");
}

.icon-plan-undone {
  background-image: url("../images/icon-check-undone.svg");
}

.icon-plan-disable {
  background-image: url("../images/icon-check-disable.svg");
}

.icon-contract-done {
  background-image: url("../images/icon-heart-done.svg");
}

.icon-contract-undone {
  background-image: url("../images/icon-heart-undone.svg");
  cursor: pointer
}

.icon-contract-undone:hover {
  background-image: url("../images/icon-heart-hover.svg");
}

.icon-download {
  background-image: url("../images/icon-download-3.svg");
}

.icon-download {
  background-image: url("../images/icon-download-3.svg");
  height: 15px;
  cursor: pointer
}

.icon-download:hover {
  background-image: url("../images/icon-download-hover.svg");
  height: 15px;
}

.messenger__item-menu {
  background: #333333;
  margin-top: -5px;
  display: none;
  position: absolute;
  bottom: -10px;
  right: 0;
  z-index: 1000;
  min-width: 100px;
}

.messenger__item:hover .messenger__item-menu {
  display: block;
}

.messenger__item-menu .messenger__item-menu-option {
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.messenger__item-menu .messenger__item-menu-option:hover {
  background: #0076a5;
}

.messenger__item-menu .messenger__item-menu-option img {
  height: 1.5em;
  width: 1.5em;
  align-self: flex-start;
  padding: 2px;
}

.messenger__item-menu .messenger__item-menu-option span {
  align-self: flex-end;
  line-height: 1em;
  font-size: .8em;
  margin: auto 0;
  color: white;
  padding-left: 5px;
}

.messenger__icon {
  position: absolute;
  right: 25px;
  bottom: 65%;
  width: 1px;
}

.messenger__icon img {
  width: 20px;
}

.messenger-director__review {
  margin: 0;
}

.messenger-director__review a {
  min-width: 80px !important;
  width: auto;
  justify-content: center;
  align-content: center;
  display: flex;
}

.messenger-director__item-reply .messenger-detail__input-text {
  background: none;
}

.messenger-director__mess {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  white-space: pre-line;
  word-break: break-word;
  line-height: 20px;
  padding: 0 0 12px;
  margin-top: -6px;
}
