:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --boder-color: #F0F0F0;
}

/* Tabs */
.user-info__tabs-list {
    margin: 0 -4px;
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding-left: 0;
}

.user-info__tabs-item {
    font-size: 12px;
    font-weight: 300;
    line-height: 18px;
    color: var(--black2-color);
    padding: 4px 16px;
    margin: 0 4px;
}

.user-info__tabs-item.active {
    background-color: var(--black2-color);
    border-radius: 4px;
}

.user-info__tabs-list .user-info__tabs-item.active a, .user-info__tabs-list .user-info__tabs-item.active a:hover, .user-info__tabs-list .user-info__tabs-item.active a:focus {
    color: var(--white-color);
}


.user-info__tabs-list li a, .user-info__tabs-list li.active a, .user-info__tabs-list li.active a:focus, .user-info__tabs-list li.active a:hover, .user-info__tabs-list li a:hover {
    color: var(--black2-color);
}
/* End tabs */

/* Content tabs */
.user-info__wrap {
}

.user-info__heading {
}

.user-info__heading h3 {
    font-size: 24px;
    font-weight: 400;
    line-height: 36px;
    padding: 24px 0;
    margin: 0;
    color: var(--black1-color);
}

.user-info__content {
    border: 1px solid var(--boder-color);
    border-radius: 12px;
    padding: 32px;
}

.user-info__images img {
    width: 120px;
    height: 120px;
}

.user-info__content .user-info__form {
    max-width: 100%;
}

.account__form-group .form-group {
    margin: 0 -12px 24px;
    padding: 0;
}

.account__form-group .form-group label{
    margin: 0;
    padding: 0 12px;
}

.account__field-label {
    display: block;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    min-height: 21px;
    color: var(--black1-color);
    margin-bottom: 16px;
}

.account__jp-astarisk-op {
    font-size: 10px;
    font-weight: 300;
    line-height: 15px;
    color: var(--black2-color);
    margin-left: 8px;
}

.account__field-hint {
    display: block;
    font-size: 12px;
    font-weight: 300;
    line-height: 18px;
    color: var(--black2-color);
    margin-bottom: 8px
}

.form-group .form-control.account__input-text {
    padding: 12px 16px;
    color: var(--black2-color);
    border: 1px solid var(--boder-color);
    background-color: var(--white-color);
    height: 45px;
    max-width: 340px;
    font-size: 14px;
    border-radius: 4px !important;
}

input::placeholder {
    font-size: 13px;
    color: var(--soremo-placeholder);
}

/* End content tabs */