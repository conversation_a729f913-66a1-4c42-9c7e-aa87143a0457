@charset "UTF-8";

.inner {
    width: 999px; height: 84px;
    margin: 0 auto;
}
.inner:after {
    content: "";
    clear: both;
    display: block;
}

/* header */
#top-head {
    top: -100px;
    position: absolute;
    width: 100%;
    margin: 100px auto 0;
    padding: 84px 0 0;
    line-height: 1;
    z-index: 999;
}
#top-head a,
#top-head {
    color: #fff;
    text-decoration: none;
}
#top-head .inner {
    position: relative;
}

#top-head .logo {
    float: left;
}
#global-nav ul {
    list-style: none;
    position: absolute;
    right: 0;
    bottom: 0;
	margin:0 12%;
}
#global-nav ul li {
	float: left;
	border-bottom:solid 1px #fff;
	font-size:14px;
}
#global-nav ul li:last-child{border-bottom:none; padding:5px 0;}
#global-nav ul li a {
    padding: 0;
}

#global-nav ul li ul li {
	border-bottom:none;
	font-size:12px;
}
#global-nav ul li ul li:last-child{
border-bottom:none; padding:0 0 5px 0;
}


/* Fixed */
#top-head.fixed {
    margin-top: 0;
    top: 0;
    position: fixed;
    padding-top: 10px;
    height: 84px;
    background: #fff;
    background: rgba(255,255,255,.7);
    transition: top 0.65s ease-in;
    -webkit-transition: top 0.65s ease-in;
    -moz-transition: top 0.65s ease-in;
}
#top-head.fixed .tw {
    font-size: 24px;
    color: #333;
}
#top-head.fixed .logo {
    font-size: 24px;
    color: #333;
}
#top-head.fixed #global-nav ul li a {
    color: #333;
    padding: 0 20px;
}

/* Toggle Button */
#nav-toggle {
    display: none;
    position: absolute;
    left: 15px;
    top: 26px;
    width: 29px;
    height: 29px;
    cursor: pointer;
    z-index: 101;
}
#nav-toggle div {
    position: relative;
}
#nav-toggle span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: #000;
    left: 0;
    -webkit-transition: .35s ease-in-out;
    -moz-transition: .35s ease-in-out;
    transition: .35s ease-in-out;
}
#nav-toggle span:nth-child(1) {
    top: 0;
}
#nav-toggle span:nth-child(2) {
    top: 10px;
}
#nav-toggle span:nth-child(3) {
    top: 20px;
}

/* SP set ============================================================== */
@media screen and (max-width: 999px) {
    #top-head,
    .inner {
        width: 100%;
        padding: 0;
    }
    #top-head {
        top: 0;
        position: fixed;
        margin-top: 0;
    }
    /* Fixed reset */
    #top-head.fixed {
        padding-top: 0;
        background: transparent;
    }
    #mobile-head {
        /*background: #000;
        background-color: rgba(0,0,0,0.8);*/
        width: 100%;
        height: 84px;
        z-index: 999;
        position: relative;
    }
    #top-head.fixed .logo,
    #top-head .logo {
    	width: 165px;
        position: absolute;
        left: 50%;
        top: 70px;
        color: #fff;
	margin-left:-83px;
        font-size: 26px;
    }
    #top-head.fixed .logo img,
    #top-head .logo img{
    width: 100%; height: auto;
    }
    #global-nav {
        position: absolute;
        /* 開いてないときは画面外に配置 */
        top: -600px;
        background-color: rgba(0,0,0,1);
        width: 100%;
        padding: 120px 0 20px 0;
        -webkit-transition: .5s ease-in-out;
        -moz-transition: .5s ease-in-out;
        transition: .5s ease-in-out;
    }
    #global-nav ul {
        list-style: none;
        position: static;
        right: 0;
        bottom: 0;
        font-size: 12px;
    }
    #global-nav ul li {
        float: none;
        position: static;
    }
    #top-head #global-nav ul li a,
    #top-head.fixed #global-nav ul li a {
        width: 100%;
        display: block;
        color: #fff;
        padding: 10px 0;
    }
    #global-nav ul li ul {
        list-style: none;
        position: static;
        right: 0;
        bottom: 0;
        font-size: 12px;
    }
    #top-head #global-nav ul li ul li a,
    #top-head.fixed #global-nav ul li ul li a {
        width: 100%;
        display: block;
        color: #999;
        padding: 0 0 10px 0;
    }
    #nav-toggle {
        display: block;
    }
    /* #nav-toggle 切り替えアニメーション */
    .open #nav-toggle span:nth-child(1) {
        top: 11px;
        -webkit-transform: rotate(315deg);
        -moz-transform: rotate(315deg);
        transform: rotate(315deg);
    background: #fff;
    }
    .open #nav-toggle span:nth-child(2) {
        width: 0;
        left: 50%;
    background: #fff;
    }
    .open #nav-toggle span:nth-child(3) {
        top: 11px;
        -webkit-transform: rotate(-315deg);
        -moz-transform: rotate(-315deg);
        transform: rotate(-315deg);
    background: #fff;
    }
    /* #global-nav スライドアニメーション */
    .open #global-nav {
        /* #global-nav top + #mobile-head height */
        -moz-transform: translateY(556px);
        -webkit-transform: translateY(556px);
        transform: translateY(556px);
    }
}

@media screen and (min-width: 1000px) {
#top-head{ display: none;}
}
