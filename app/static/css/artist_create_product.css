@charset "utf-8";
/* アニメーションの設定 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes blink {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}

@keyframes pulse {

    0%,
    100% {
        box-shadow: 0px 0px 12px 0px rgba(0, 154, 206, 0.75);
    }

    50% {
        box-shadow: 0px 0px 12px 0px rgba(0, 154, 206, 0.25);
    }
}



html {
    width: 100%;
    /* コンテナクエリ親 */
    container-type: inline-size;
}


.div-artist-create-product {
    overflow-y: auto;
    position: initial !important;
}



/* material icon */
.indicator-icon {
    color: var(--soremo-light-gray);
    text-align: center;

    .material-symbols-rounded {
        font-size: 128px;
        font-display: block;
    }
}


nav {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;
    padding: 32px 0px 16px 0px;
}

nav h2 {
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;

    /* Heading 24 Spacing */
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: clamp(24px, 5vw, 40px);
    font-style: normal;
    line-height: 100%;
    /* 24px */
    letter-spacing: 2.5px;

    padding: 12px 0;
}

form#payment-form {
    margin-top: 0px;
}

input#cardname {
    padding-block: 1px;
    padding-inline: 2px;
    font-size: inherit;
    line-height: inherit;
}

.payment__action {
    gap: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}


input, select, textarea {
    box-shadow: none !important;
    appearance: none !important;
    -webkit-appearance: none !important;
}

.card-select-container {
    display: flex;
    color: #fff;
    min-height: 100%;
    align-items: center;
    justify-content: center;
}

.card-select-container .InputGroup {
    display: flex;
    flex-direction: column;
    /*width: 100%;*/
}


.card-select-container label {
    display: flex;
    flex: auto;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #a7a8a9;
    color: white;
    border-radius: 6px;
    transition: color .1s ease-out,
        background-color .1s ease-in,
        box-shadow .4s ease-in-out;
    user-select: none;
    padding: 15px;
    flex-direction: column;
}

.card-select-container label:last-of-type {
    margin-right: 0;
}

.card-select-container input[type="radio"]:checked+label {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    background-color: #0076a5;
    color: #fff;
}

.card-select-container input[type="radio"]:hover:not(:checked)+label {
    background-color: #009ace;
    color: #fff;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

body {
    width: 100%;
    height: 100vh;

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* step毎のセクションを隠すためのクラス */
.hidden {
    display: none;
}

.active {
    display: block;
}

section {
    width: min(100% - 32px, 756px);
    margin-inline: auto;
    padding: 0px 0px 104px;

    /* 他のflex子要素を固定するためにスクロールさせる設定 */
    flex: 1;
    /* 横軸のスクロールを禁止 */
    overflow-x: hidden;
    overflow-y: auto;
}

section form,
section .form-input {
    margin-top: 16px;
}

section p,
section li,
section input[type="text"],
section input[type="number"],
section input + span {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
    font-weight: normal;
    color: #000;
    font-feature-settings: 'palt', 'clig' off, 'liga' off;
    font-size: 13px;
    line-height: 200%;

    @container (width > 640px) {
        font-size: 16px;
    }
}

section p {
    padding-bottom: 8px;
}

section img {
    border-radius: 12px;
    display: block;
    margin-inline: auto;
    margin-bottom: 16px;

}

section .keyword,
section .genre,
section .platform {
    margin: 16px 4px;
    padding: 4px 4px;
    border: 1px solid #fff;
    border-radius: 4px;

    color: #a7a8a9;
    user-select: none;

}

section .keyword:hover,
section .genre:hover,
section .platform:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-light-gray);
    color: #FFF;
    background-color: var(--soremo-light-gray);
}

section .label-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    align-self: stretch;
}

section label {
    /* Heading 16 設定 */
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 13px;
    line-height: 200%;

    @container (width > 640px) {
        /* BodyText 16 設定 */
        font-size: 16px;
    }
}

section label + span {
    /* label 8 設定 */
    font-size: 8px;
    font-style: normal;
    line-height: 100%;
    color: var(--soremo-blue);
    padding: 0 0 0 4px;
}

section .group {
    border-radius: 4px;
    border: 1px solid var(--soremo-border, #F0F0F0);
    background: rgba(255, 255, 255, 0.5);
    padding: 16px 8px 16px 8px;
}

section .group ul {
    list-style: none;
}

section .group li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

section .group li .currency {
    flex: 1;
    text-align: right;
}

section .input-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
}


section input + span,
section li > span {
    min-width: 68px;
    text-align: left;

    @container (width > 640px) {
        /* BodyText 16 設定 */
        min-width: 80px;

    }
}

section .card-image {
    width: 92px;
    aspect-ratio: 1.414 / 1;
    background: url('/static/images/mileage_1.jpg') no-repeat center center / cover;
    object-fit: cover;
    border-radius: 4px;
    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    margin: 16px 0 0px;

    @container (width > 640px) {
        width: 112px;

    }
}

section .card-image + span {
    padding: 0 0 0 4px;
    margin: 16px 0 0px;
    font-size: 11px;

    @container (width > 640px) {
        /* BodyText 13 設定 */
        font-size: 13px;
    }
}

section .center-container {
    text-align: center;
}

section .show-if-deficit {
    display: none;
}

section .show-if-no-deficit {
    display: flex;
}

/* ステッパーの設定 */

section .stepper-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;

    width: min(100% - 16px, 756px);
    list-style: none;
    margin: 8px 0;
    padding: 0px 0px 16px;
}

section .stepper {
    width: min((100% - 16px) / 4, 756px / 4);

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    top: 50%;
    counter-increment: list-counter;
    position: relative;

}

section .stepper > span {
    /* label 11 設定 */
    font-size: 11px;
    line-height: 100%;
    text-align: center;

    @container (width > 640px) {
        /* BodyText 13 設定 */
        font-size: 13px;
    }
}

section .stepper:before {
    width: 32px;
    height: 32px;
    background: var(--soremo-light-gray);
    border-radius: 16px;
    text-align: center;
    line-height: 32px;
    color: #fff;

    content: counter(list-counter);
}

section .stepper.completed:before {
    background-color: var(--soremo-blue);
}

section .stepper.active:before {
    background-color: var(--soremo-blue);
    animation: pulse 1.5s infinite;
}

section .stepper:after {
    content: "";
    width: min(100% - 26px, 756px);
    height: 1px;
    /* 線の色 */
    background-color: var(--soremo-light-gray);
    /* 位置の調整 */
    position: absolute;
    top: 16px;
    left: calc(50% + 16px);
}

section .stepper.completed:after {
    background-color: var(--soremo-blue);
}


section .stepper:last-child:after {
    /* 最後の要素のためのスタイルを無効化 */
    content: none;
}

/* SWIPER */

section .swiper-slide {
    padding: 0 16px;
    border-radius: 16px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;

    position: relative;

    /* adobeフォントの読み込み */
    font-family: "itc-avant-garde-gothic-pro", sans-serif;
    font-weight: 100;
    font-style: normal;
}

.card-number {
    font-size: clamp(20px, 4vw, 28px);
    color: #fff;
}

.card-expiry {
    font-size: clamp(12px, 2vw, 20px);
    color: #fff;
}

.card-brand {
    position: absolute;
    bottom: 16px;
    right: 16px;

    font-size: clamp(16px, 4vw, 20px);
    color: #fff;
    opacity: 0.25;
}

section .swiper {
    width: min(100% - 32px, 360px);
    aspect-ratio: 1.58 / 1;
}

section .credit-card1 {
    background: linear-gradient(135deg, rgb(208, 247, 250), rgb(198, 233, 234), rgb(187, 219, 219), rgb(177, 206, 203), rgb(167, 192, 188));
}

section .credit-card2 {
    background: linear-gradient(135deg, rgb(222, 227, 229), rgb(216, 218, 220), rgb(209, 209, 210), rgb(203, 201, 201), rgb(197, 192, 192));
}

section .credit-card3 {
    background: linear-gradient(135deg, rgb(240, 245, 214), rgb(228, 230, 202), rgb(216, 216, 189), rgb(204, 202, 177), rgb(192, 188, 165));
}

section .credit-card4 {
    background: linear-gradient(135deg, rgb(212, 227, 232), rgb(157, 168, 172), rgb(106, 114, 116), rgb(59, 63, 65), rgb(16, 18, 19));
}


footer {
    width: 100%;
    height: 80px;
    padding: 16px 16px 16px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);
    border: 1px solid rgba(255, 255, 255, 0.16);

    position: fixed;
    inset: auto auto 0;
}

footer .button-wrapper {
    width: min(100%, 1140px);
    margin-inline: auto;

    display: flex;
    justify-content: center;
    gap: 16px;

    @container (width > 640px) {
        justify-content: flex-end;
    }
}

.p-top-up {
    padding: 8px 0 0;
}
