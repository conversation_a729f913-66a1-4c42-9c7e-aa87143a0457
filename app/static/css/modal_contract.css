#contract-modal .modal-dialog {
    width: 80vw;
    max-width: 1140px;
    margin: 0;
    transform: translate(-50%, -50%);
    position: fixed;
    top: 50%;
    left: 50%;
}

@media screen and (max-width: 767px) {
    #contract-modal .modal-dialog {
        width: calc(100vw - 32px) !important;
    }
}

#contract-modal .modal-body {
    max-height: 70vh;
    padding: 50px;
}

@media screen and (max-width: 767px) {
    #contract-modal .modal-body {
        padding: 30px 15px;
    }
}

.contract-modal {
    color: #333;
    line-height: 1.6;
}

.contract-modal__logo {
    text-align: right;
    margin-bottom: 15px;
}

.contract-modal__logo img {
    max-height: 70px;
}

.contract-modal__title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
}

.contract-modal__description {
    margin-bottom: 15px;
}

.contract-modal__section {
    border-top: 1px solid #d6d6d6;
    display: flex;
    padding-top: 15px;
    padding-bottom: 5px;
}

@media screen and (max-width: 767px) {
    .contract-modal__section {
        display: block;
    }
}

.contract-modal__heading {
    font-size: 16px;
    font-weight: 700;
    flex: 0 0 20%;
}

@media screen and (max-width: 767px) {
    .contract-modal__heading {
        margin-bottom: 15px;
    }
}

@media screen and (max-width: 767px) {
    .contract-modal__text--date {
        margin-bottom: 10px;
    }
}

.contract-modal__text--date span {
    margin-right: 15px;
}

.contract-modal__text--date span:last-child {
    margin-right: 0;
}

.contract-modal h6 {
    font-size: 14px;
}

.contract-modal p {
    margin-bottom: 10px;
}

.contract-modal ul {
    padding-left: 10px;
    list-style: none;
}

.contract-modal__columns {
    display: flex;
}

.contract-modal__column {
    flex: 0 0 50%;
}

.contract-modal__mark {
    padding-right: 15px;
    padding-top: 20px;
    text-align: right;
}

.contract-modal__label {
    font-weight: 700;
    display: inline-block;
}

.contract-modal__label--spec {
    min-width: 130px;
}

.contract-modal__label--deliver {
    min-width: 95px;
}

.contract-modal__occupation {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
}

.contract-modal__download {
    text-align: right;
}

.contract-modal__download-btn {
    display: inline-block;
    width: 24px;
    height: 16px;
    background-color: #333;
    background-repeat: no-repeat;
    -webkit-mask-image: url('../images/icon-download.svg');
    mask-image: url('../images/icon-download.svg');
    -webkit-mask-size: cover;
    mask-size: cover;
}

.contract-modal__download-btn:hover {
    cursor: pointer;
    background-color: #009dc4;
}

.contract-modal__close {
    text-align: center;
}

.contract-modal__close-btn {
    border-radius: 30px;
    font-size: 16px;
    font-weight: 700;
    min-width: 200px;
    background-color: rgba(0, 157, 196, 0.73);
}
