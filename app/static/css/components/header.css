:root {
    --key-visual-pc: #fcfcfc;
    --key-visual-sp: #fcfcfc;
    --banner: #fcfcfc;
}
html {
    font-size: 16px;
}

.header-fullscreen-container {
    height: 100vh;
    background-color: #fcfcfc;
    width: 100%;
    margin-top: 65px;
    background: var(--key-visual-pc);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.header-fullscreen-container.noheader {
    margin-top: 0;
}

.header-fullscreen-container-content {
    height: 100%;
    display: flex;
    align-items: left;
    justify-content: space-between;
    flex-direction: column;
    padding: 0;
}

.style2 .header-fullscreen-container-content {
    height: auto;
    aspect-ratio: 235/100;
    background: var(--banner);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*2);
}

.logo-container {
    position: fixed;
    top: 65px;
    left: 50%;
    height: 70px;
    padding: 15px;
    transform: translateX(-50%);
    z-index: 995;
    opacity: 0.9;
}

.noheader .logo-container {
    top: 0;
    padding: 15px 0;
}

.header-fullscreen-container.new,
.header-fullscreen-container.editable {
    cursor: pointer;
}

.container:before,
.container:after {
    display: none;
}

.srm3 main.header-fullscreen {
    margin-top: 0;
    background: #fff;
}

.noheader .header-profile-artist {
    height: 70px;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    padding: 0 16px;
}

.header-profile-artist {
    height: 70px;
    display: flex;
    align-items: center;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    padding: 0;
    z-index: 996;
    padding: 0 16px;
    mix-blend-mode: exclusion;
}

.style2:not(.noheader) .header-profile-artist {
    margin-top: -70px;
}

.header-profile-artist .header-profile-pc .sheader-link,
.header-profile-pc-right-link {
    color: #d6d6d6;
}

.header-profile-pc {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: auto 0;
    width: 100%;
}

.header-profile-sp {
    display: none
}

.header-profile-pc-logo {
    width: auto;
    min-width: 40px;
    height: 40px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.header-profile-pc-logo.nologo {
    visibility: hidden;
}

.logo-container.op1 {
    opacity: 1 !important;
}

.header-profile-pc-right {
    display: flex;
    justify-content: flex-end;
}

.header-profile-pc-right {
    color: #000;
}

.header-profile-pc-right-link {
    cursor: pointer;
    font-size: 13px !important;
    display: flex;
    align-items: center;
    line-height: 150%;
    text-align: center;
    font-style: normal;
    font-weight: 300;
}

.header-profile-artist .header-profile-pc .sheader-link:hover,
.header-profile-pc-right-link:hover {
    color: #fa6934;
}


.header-profile-pc-right .header-profile-pc-right-link:not(:first-child) {
    margin-left: 64px;
}

.profile-artist-catch-phrase {
    width: 100%;
    height: 100%;
    text-align: left;
    vertical-align: center;
    font-size: 20px;
    font-weight: 400;
    mix-blend-mode: normal;
    text-shadow: 1px 3px 5px rgba(0, 0, 0, 0.25);
    display: flex;
    align-items: center;
    text-align: center;
    white-space: nowrap;
    color: #fff;
    line-height: 175%;
    background: transparent;
    font-family: "A+mfCv-AXISラウンド 50 B StdN";
    padding: 0 15px;
    opacity: 0;
}

.profile-artist-info {
    position: absolute;
    height: 140px;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 15px;
    opacity: 0;
}

.profile-artist-info-name {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
    'M PLUS 1p',
    sans-serif;
    color: #fff;
    line-height: 200%;
    margin-right: 32px;
    text-shadow: 1px 3px 8px rgba(0, 0, 0, 0.25);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    letter-spacing: 2.5px;
    font-size: clamp(1.5rem, 1.136rem + 1.82vw, 2.5rem);
}

.profile-artist-info-title {
    color: #fff;
    text-shadow: 1px 3px 8px rgba(0, 0, 0, 0.25);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 8px;
    letter-spacing: 2.5px;
    font-size: clamp(0.813rem, 0.744rem + 0.34vw, 1rem);
}

.style2.header-fullscreen-container {
    height: auto;
    background: #fff;
    min-height: 100px !important;
    margin-top: 135px;
    position: relative;
}

.style2.header-fullscreen-container:before {
    content: ' ';
    position: absolute;
    width: 100%;
    height: 135px;
    background-color: #fff;
    top: -70px;
    left: 0;
    z-index: -1;
}

.style2.noheader.header-fullscreen-container {
    margin-top: 70px;
}

.style2 .logo-container,
.style2 .header-profile-pc-logo {
    visibility: hidden;
}

.style2 .profile-artist-info {
    position: absolute;
    bottom: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*-2);
    left: 0;
    width: 100%;
    height: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*2);
    display: flex;
    align-items: center;
    opacity: 1 !important;
}

.style2 .profile-artist-info-name,
.style2 .profile-artist-info-title {
    color: #000;
    text-shadow: none;
}

.style2 .profile-artist-info-name {
    /* font-size: 18px; */
    font-size: clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem);
    margin-right: 32px;
}


.style2 .profile-artist-info-title {
    /* font-size: 18px; */
    font-size: clamp(0.688rem, 0.574rem + 0.57vw, 1rem);
}

.lang-en .profile-artist-catch-phrase span:not(:last-child) {
    margin-right: 12px;
}

.style2 .profile-artist-catch-phrase span {
    display: none;
}

@media (max-aspect-ratio: 4/5) {
    .profile-artist-catch-phrase {
        flex-direction: column;
        align-items: start;
        font-size: 15px;
        justify-content: center;
    }

    .profile-artist-info {
        flex-direction: column;
        align-items: start;
        justify-content: center;
        left: 20px;
        padding: 0;
        max-width: calc(100% - 40px);
    }

    .profile-artist-info-name {
        /* font-size: 24px; */
        font-size: clamp(1.5rem, 1.136rem + 1.82vw, 2.5rem);
        max-width: 100%;
        margin-right: 0;
    }

    .profile-artist-info-title {
        /* font-size: 13px; */
        font-size: clamp(0.813rem, 0.744rem + 0.34vw, 1rem);
        padding: 0;
        max-width: 100%
    }

    .profile-artist-catch-phrase {
       padding-left: 20px;
    }

    .lang-en .profile-artist-catch-phrase span:not(:last-child) {
        margin-right: 0;
    }
}

@media (max-aspect-ratio: 4/5) {
    .container {
        padding: 0 !important;
    }

    .header-fullscreen-container {
        background: var(--key-visual-sp);
        background-size: auto 100% !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        height: 100vh !important;
        background: var(--key-visual-sp);
        margin-top: 0;
    }
    
    .noheader.header-fullscreen-container:not(.style2) {
        height: 100vh !important;
    }

    .mobile .header-fullscreen-container {
        height: calc(100vh - 65px) !important;
        height: calc(100svh - 65px) !important;
    }

    .mobile .header-fullscreen-container:not(.style2) {
        height: 100vh !important;
        height: 100svh !important;
    }

    .mobile.android .header-fullscreen-container:not(.style2) {
        height: calc(100vh - 50px) !important;
        height: 100svh !important;
    }

    .mobile .noheader.header-fullscreen-container {
        height: 100vh !important;
        height: 100svh !important;
    }

    .header-profile-pc {
        display: none;
    }

    .header-profile-pc-logo {
        margin-left: 20px;
    }

    .header-profile-sp {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: auto 20px;
        width: calc(100% - 120px);
        position: relative;
    }

    .header-profile-sp-button {
        height: 32px;
        width: 32px;
        background-image: url('/static/images/icon_hamburger.svg');
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }

    .header-profile-sp-button-close {
        height: 32px;
        width: 32px;
        background-image: url('/static/images/icon_hamburger_close.svg');
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }

    .header-profile-artist {
        width: 100%;
        margin-top: 65px;
        justify-content: space-between;
        mix-blend-mode: unset;
    }

    .noheader .header-profile-artist {
        margin-top: 0;
    }

    .logo-container {
        opacity: 0.5;
        width: 100% !important;
        padding: 15px 20px !important;
    }

    .header-profile-sp-dropdown {
        display: none;
    }

    .header-profile-sp-dropdown.prepare-shown {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        opacity: 0;
        background: #fff;
        min-height: 100px;
        width: auto;
        position: absolute;
        top: -20px;
        right: -20px;
        transition: 0.3s ease;
        padding-right: 20px;
        padding-top: 20px;
        min-width: 150px;
        border-radius: 0 0 0 12px;
        box-shadow: 0 10px 12px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    .header-profile-sp-dropdown.shown {
        opacity: 1;
    }

    .header-profile-sp-link {
        margin-top: 24px;
        padding-left: 20px;
        max-width: 80vw;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-size: 13px !important;
        font-style: normal;
        font-weight: 300;
        font-size: 13px;
        line-height: 150%;
        display: flex;
        align-items: center;
        text-align: right;
        color: #000;
    }

    .header-profile-artist .header-profile-sp-link .sheader-link:hover,
    .header-profile-sp-link:hover {
        cursor: pointer;
        color: #009ace;
    }

    .header-profile-sp-link:last-child {
        margin-bottom: 24px;
    }

    .style2.header-fullscreen-container,
    .mobile .style2.noheader.header-fullscreen-container {
        height: auto !important;
    }

    .style2 .header-fullscreen-container-content {
        margin-bottom: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*2);
    }

    .style2 .profile-artist-info-name {
        /* font-size: 18px; */
        font-size: clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem);
        margin-right: 24px;
    }

    .style2 .profile-artist-info-title {
        /* font-size: 11px; */
        font-size: clamp(0.688rem, 0.574rem + 0.57vw, 1rem);
        font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    }

    .style2 .profile-artist-info {
        flex-direction: row;
        justify-content: left;
        margin-left: 20px;
        height: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*2);
        bottom: calc(clamp(1.125rem, 0.625rem + 2.5vw, 2.5rem)*-2);
    }

    .header-profile-artist {
        left: 50%;
    }
}

@media (max-width: 767px) {
    .logo-container {
        width: 100% !important;
    }

    .header-profile-artist {
        width: 100% !important;
        padding: 0 15px;
        justify-content: space-between;
    }

    .header-profile-pc {
        width: auto;
        align-self: flex-end;
    }
}

@media (min-width: 767px) and (max-aspect-ratio: 4/5) {
    .style2 .header-fullscreen-container-content {
        width: 100%;
    }
}

.dropzone-area {
    width: 100%;
    cursor: pointer;
    background: #fff;
    border: 1px dashed #D3D3D3;
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 8px;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.dropzone .dz-message .dz-button {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
}

.dropzone-area .dz-button {
    padding: 24px;
}

.dropzone-area .dz-button .icon {
    font-size: 20px;
    color: #a7a8a9;
}

.dropzone-area .dz-button p {
    font-size: 13px;
    color: #a7a8a9;
    line-height: 200%;
    margin: 0;
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropzone-area:hover .icon, .dropzone-area:hover p {
    color: white !important;
}

.dropzone-area:hover .dz-clickable {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
    border-radius: 6px;
}

.drag-over .dz-clickable,
.drag-over.dropzone-area .dz-clickable {
    background-color: #009ACE !important;
    border-radius: 6px;
}

.drag-over.dropzone-area .icon,
.drag-over.dropzone-area p {
    color: white !important;
}

.mattach-preview-container .mattach-previews {
    overflow-y: hidden !important;
}

.mattach-preview-container .mcommment-file {
    background-color: #F0F0F0 !important;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
}

.preview-header-logo,
.preview-header-keyvisual-pc,
.preview-header-keyvisual-sp,
.preview-header-banner {
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.preview-header-logo {
    height: 100px;
    width: 100px;
}

.preview-header-keyvisual-pc {
    height: 90px;
    width: 160px;
}

.preview-header-keyvisual-sp {
    height: 195px;
    width: 90px;
}

.preview-header-banner {
    height: 100px;
    width: 235px;
}

#modal_profile_header .popup-content {
    padding: 32px 0;
}

#modal_profile_header .popup-body {
    padding: 0 32px;
}

#modal_profile_header .popup-footer {
    padding: 32px 32px 0;
}

.long-modal.modal .popup-body {
    overflow-y: scroll;
    max-height: calc(100vh - 240px);
}

.new-button {
    display: none;
    color: #fff;
}

.new.header-fullscreen-container:after {
    display: none !important;
}

.new[data-approve=true].header-fullscreen-container .new-button,
.new[data-edited=true].header-fullscreen-container .new-button,
.new[data-rejected=true].header-fullscreen-container .new-button {
    color: transparent;
}

#modal_profile_header hr {
    border: none;
    border-top: 1px solid #f0f0f0;
    height: 1px;
    width: 100%;
    margin-top: 16px;
}

.current-file {
    cursor: pointer;
}

#bgVideoPC,
#bgVideoSP,
#bgVideoSP.shown,
#bgVideoBanner,
#bgVideoBanner.shown {
    position: absolute;
    width: 100%; 
    height: auto;
    top: 0;
    left: 0;
    z-index: 0;
    object-fit: cover;
    display: none;
}


#bgVideoBanner,
#bgVideoBanner.shown {
    height: auto;
    aspect-ratio: 235/100;
    object-position: center;
}

#bgVideoSP,
#bgVideoSP.shown {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    object-fit: cover;
    object-position: center;
}

#bgVideoPC.shown {
    display: block;
}

.style2 #bgVideoPC.shown,
.style2 #bgVideoSP.shown {
    display: none;
}

.style2 #bgVideoBanner.shown {
    display: block;
}

@media (max-aspect-ratio: 4/5) {
    #bgVideoPC.shown {
        display: none;
    }

    .header-fullscreen-container:not(.style2) #bgVideoSP.shown {
        display: block;
    }
}

.input-radio-card-content-title {
    line-height: 200%;
    margin-bottom: 4px;
}

.input-radio-card-content-description {
    line-height: 200%;
}

#modalCrop.modal .modal-body {
    overflow: visible;
}

.scroll-to-top {
    cursor: pointer;
}