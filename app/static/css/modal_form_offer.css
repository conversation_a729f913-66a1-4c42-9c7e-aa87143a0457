/* Color, font-size */
:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}
/* End color, font-size  */

#modal-create-offer .SumoSelect, #modal-edit-offer .SumoSelect {
    width: 100%;
}

#modal-create-offer .form-textarea-form-offer-container label, #modal-edit-offer .form-textarea-form-offer-container label {
    margin-bottom: 32px;
}

#modal-create-offer .expand-detail-offer .component-datetime-container.calendar-time, #modal-edit-offer .expand-detail-offer .component-datetime-container.calendar-time {
    width: 100% !important;
}

#modal-create-offer .expand-detail-offer .component-datetime-container.calendar-time input, #modal-edit-offer .expand-detail-offer .component-datetime-container.calendar-time input {
    width: 100% !important;
}

.modal-create-edit-offer .sform-group__label-text {
    font-family: var(--font-family-R);
    font-weight: 400;
    font-size: 13px;
    line-height: 200%;
    color: var(--black1-color);
    display: flex;
    align-items: center;
}

.modal-create-edit-offer .sform-group__label-hint {
   
    font-style: normal;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
    margin-left: 4px;
}

.modal-create-edit-offer .sform-group__label-hint.not-required {
    color: var(--grey1-color);
}

.modal-create-edit-offer .sform-group__label-hint.required {
    color: var(--blue-color);
}

.modal-create-edit-offer .component-input-text-area {
    margin-bottom: 0;
}

.modal-create-edit-offer .component-text-area {
    margin: 0;
}

.modal-create-edit-offer .mcomment-attached:has(.mattach-preview-container .mattach-previews:empty) {
    padding: 0;
}

.modal-create-edit-offer .mattach {
    margin-bottom: 0;
}

.modal-create-edit-offer .account_upload-file #create-offer-upload-form p {
    color: #000;
    margin-top: -8px;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
}

.modal-create-edit-offer .component-datetime-container.calendar-time {
    width: 100%;
}

.tabs-skill-offer {
    display: flex;
    /* flex-wrap: wrap; */
    width: 100%;
}

.tabs-skill-offer .nav-item {
    position: relative;
    white-space: nowrap;
}

.skill-selected {
    position: absolute;
    top: 0;
    right: 0;
}

.nav.tabs-skill-offer .nav-item a.nav-link {
    color: #000000;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 400;
    text-transform: uppercase;
}

.nav.tabs-skill-offer .nav-item.active a.nav-link, .nav.tabs-skill-offer .nav-item.active a.nav-link:hover, .nav.tabs-skill-offer .nav-item a.nav-link:hover, .nav.tabs-skill-offer .nav-item.active a.nav-link:focus {
    color: #009ACE;
    border-bottom: 2px solid #009ACE;
    font-weight: 400;
}

.skills-list-offer-selected {
    display: flex;
    flex-wrap: wrap;
}

.skills-item-offer {
    font-size: 11px;
    line-height: 17px;
    padding: 4px 16px;
    color: #000000;
    background-color: #FFFFFF;
    border-radius: 4px;
    margin: 11px 8px 0 0;
    cursor: pointer;
    border: 1px solid #F0F0F0;
}

.skills-item-offer:not(.disabled):hover, .skills-item-offer.selected:not(.disabled):hover  {
    color: #FFF;
    background-color: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
}

.skills-item-offer.selected {
    color: #fff;
    background-color: var(--blue-color);
    border: 1px solid var(--blue-color);
}

.tab-content-offer>.tab-pane-offer.active {
    display: block;
}

.tab-content-offer>.tab-pane-offer {
    display: none;
}

.job-title-container {
    margin-bottom: 12px;
    width: 100%;
}

.modal-create-edit-offer .switch-checkbox-public {
    background: #F0F0F0;
    border: 2px solid var(--soremo-border);
    box-shadow:none;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-left: 8px;
    height: 50px;
    width: 100%;
}

.modal-create-edit-offer .switch-checkbox-public span {
    color: var(--grey1-color);
}

.modal-create-edit-offer .switch-checkbox-public.checked span {
    color: var(--black1-color);
}

.modal-create-edit-offer .switch-checkbox-public.checked {
    background: var(--white-color);
    border: 2px solid var(--blue-color);
    box-shadow:none;
    border-radius: 4px;
    width: 100%;
}

.modal-create-edit-offer .custom-switch .form-check-label {
    margin-bottom: 0px !important;
}

.modal-create-edit-offer .form-group.custom-input-component input.form-control {
    margin: 0px !important;
}

.modal-create-edit-offer #create-offer-upload-form {
    height: 96px;
    min-height: 96px !important;
}

.create-offer {
    max-height: calc(100vh - 152px - 104px) !important;
    width: 100%;
    display: flex;
    justify-content: center;
}

.id_contract .custom-input-component {
    margin-bottom: 8px;
}

.close-modal-offer, .close-modal-offer:focus {
    background-color: transparent !important;
}

.modal-create-edit-offer .button-scroll-top {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 44px;
    padding-right: 8px;
    /* border-top: 1px solid #F0F0F0; */
}

.modal-create-edit-offer .button-scroll-top-container:hover svg path {
    fill: var(--soremo-deep-blue);
}

.modal-create-edit-offer .button-scroll-top svg:hover {
    cursor: pointer;
}

.modal-create-edit-offer .input-unit-block-right {
    width: 76px;
    max-height: 50px;
    margin-left: 4px;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    min-width: 76px;
}

.modal-create-edit-offer .skills-item-offer.disabled{
    /* background-color: #fff; */
    cursor: not-allowed;
}

.modal-create-edit-offer .errorlist {
   
    font-style: normal;
    font-weight: 300;
    font-size: 11px !important;
    color: var(--error-color);
}

.upload_file_container {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}