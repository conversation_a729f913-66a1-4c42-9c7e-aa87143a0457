html {
      /* スクロールバーの調整 */
    /* スクロールバー全体のスタイル設定 */
    ::-webkit-scrollbar {
      /* スクロールバーの幅 */
      width: 4px;
      height: 4px;
  }

  /* スクロールバーのトラック（背景部分）のスタイル設定 */
  ::-webkit-scrollbar-track {
      /* トラックの背景色 */
      background: #fcfcfc;
  }

  /* スクロールバーのつまみのスタイル設定 */
  ::-webkit-scrollbar-thumb {
      /* つまみの背景色 */
      background: #f0f0f0;
      /* つまみの丸み */
      border-radius: 6px;
  }

  /* スクロールバーのつまみにホバーした時のスタイル設定 */
  ::-webkit-scrollbar-thumb:hover {
      /* ホバー時の背景色 */
      background: #a7a8a9;
  }
}


html, body {
  overscroll-behavior-x: none !important;
}

/*cscene*/

.cscene {
  width: 534px;
  max-width: 100%;
  position: relative;
  padding-bottom: 80px !important;
  overflow: hidden;
  /* stylelint-disable */
  /* stylelint-enable */
}

.cscene .cscene-heading {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  line-height: 25px;
}

.cscene .cscene-heading__product {
  padding-right: 16px;
  max-width: 150px;
}

.cscene .cscene-heading__title {
  padding-right: 16px;
  color: #000000;
}

.cscene .cscene-heading__title::before {
  content: ">";
  color: #a7a8a9;
  padding-right: 16px;
}

.cscene-heading__product:hover {
  color: #009ace;
  cursor: pointer;
}

.cscene .cscene-heading__title,
.cscene-heading__product.sheading,
.pd-chapter__name {
  font-family: "A+mfCv-AXISラウンド 50 R StdN", "Noto Sans Japanese",
    "sans-serif";
  font-size: 18px;
  letter-spacing: 2.5px;
  line-height: 100%;

  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.cscene .cscene-meta {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.cscene .cscene-meta__date {
  width: 80px;
  font-size: 10px;
  text-align: right;
  margin-left: auto;
  color: #a7a8a9;
}

.cscene-vertical.active {
  border: 5px solid #009ace;
}

.cscene .cscene-variation {
  width: calc(100% - 50px);
  font-size: 12px;
}

.cscene .cscene-variation .SumoSelect {
  width: 100%;
}

.cscene .cscene-variation .SumoSelect .select {
  padding-left: 0;
}

.cscene .cscene-variation .sselect-wrapper .SumoSelect > .CaptionCont {
  border: none;
  padding-left: 0;
}
.pd-section .video-order-by {
  width: 180px;
  max-width: 180px;
}

.pd-section__header-left {
  flex-grow: 1;
  justify-content: space-between;
  /* max-width: 60% !important; */
  /* min-width: 500px; */
}

.video-order-by .sselect-wrapper {
  position: relative;
}

.video-order-by .sselect-wrapper .icon {
  position: absolute;
  top: 16px;
  right: 16px;
  color: #a7a8a9;
}

.video-order-by .sselect-wrapper #video-orderby {
  /* font-size: 11px !important; */
  color: black !important;
}

.pd-section__header-left .video-order {
  padding-right: 0 !important;
}

.pd-section__header-left .order-container {
  display: flex;
}

.pd-section__header-left .video-filter {
  margin-left: 0 !important;
}

/*.cscene .cscene-meta__variation {*/
/*    flex: 0 0 130px;*/
/*}*/

/*.cscene .cscene-meta__variation .SumoSelect {*/
/*    width: 100%;*/
/*}*/

/*.cscene .cscene-meta__variation .sselect-wrapper .SumoSelect > .CaptionCont {*/
/*    border: none;*/
/*    padding-left: 0;*/
/*}*/

/*.cscene .cscene-meta__date {*/
/*    width: 110px;*/
/*    height: 26px;*/
/*    background-color: #53565a;*/
/*    color: #fff;*/
/*    font-size: 12px;*/
/*    border-radius: 4px;*/
/*    text-align: center;*/
/*    line-height: 26px;*/
/*    margin-top: 5px;*/
/*}*/

.cscene__variation {
  width: 100%;
  position: relative;
  margin-bottom: 0;
}

.cscene__version {
  position: relative;
  padding-bottom: 56.25%;
  border-radius: 6px;
  background-color: #a7a8a9;
}

.cscene__version-vertical {
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  transform: translateX(-50%);
}

.cscene__version-horizontal {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  text-align: center;
}

.cscene__version-horizontal-document {
  height: 100%;
  top: 51%;
}

.cscene__ratio-35 {
  height: 100%;
  width: auto;
  background-size: cover;
  background-position: center center;
}

.cscene__ratio-43 {
  width: 100%;
  height: auto;
  background-size: cover;
  background-position: center center;
}

.cscene__meta-date {
  width: 110px;
  height: 26px;
  background-color: #53565a;
  color: #fff;
  font-size: 12px;
  border-radius: 4px;
  text-align: center;
  line-height: 26px;
  position: absolute;
  top: -50px;
  right: 0;
}

.cscene__thumb {
  background-size: cover;
  /*border-radius: 6px;*/
  /*!*padding-bottom: 56.25%;*!*/
  /*position: relative;*/
  position: absolute;
}

.cscene .cscene-horizontal-dots.horizontal-2-dots {
  width: 72px;
}

.cscene .cscene-horizontal-dots.horizontal-2-dots .cscene__variation-dot {
  transform: scale(1) translateX(8px) !important;
}

.cscene
  .cscene-horizontal-dots.horizontal-2-dots
  .cscene__variation-dot:before {
  right: 0;
  left: auto;
}

.cscene .cscene-horizontal-dots.horizontal-1-dots {
  width: 164px;
}

.cscene__thumb:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  /*background-color: rgba(51, 51, 51, 0.3);*/
  border-radius: 6px;
  opacity: 1;
  visibility: visible;
  transition: all 0.3s;
}

.cscene__play {
  display: inline-flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  color: #fff;
}

.cscene__play:hover {
  color: #fff;
}

.cscene__options {
  display: flex;
  align-items: center;
  font-size: 24px;
  color: #a7a8a9;
  line-height: 1;
  position: absolute;
  width: 100%;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.cscene__options > a {
  color: #a7a8a9;
}

.cscene__options > a:hover {
  color: #009ace;
}

.cscene__can-share {
  color: #009ace !important;
}
.cscene__can-share:hover .icon {
  color: #0076a5 !important;
}

.cscene__wishlist:before {
  content: "";
  font-family: "soremoicons";
  /* stylelint-disable-line */
}

.cscene__wishlist:hover:before {
  content: "";
  font-family: "soremoicons";
  /* stylelint-disable-line */
}

.cscene__wishlist.project-chapter-video-undone,
.cscene__wishlist.project-chapter-video-done,
.cvideo__wishlist.project-chapter-video-undone,
.cvideo__wishlist.project-chapter-video-done,
.icon--sicon-heart.project-chapter-video-undone {
  color: #009ace;
}

.cvideo__wishlist:not(.cannot-check):hover {
  color: #009ace;
}

.cvideo__thumb-list-update
  .cvideo__heading
  .icon--sicon-heart-o.project-chapter-video-done {
  font-size: 24px;
}

.cvideo__wishlist.project-chapter-video-undone:not(.cannot-check):hover,
.cscene__options .icon--sicon-heart-o.cannot-check:hover {
  color: #f0f0f0;
}

@media (hover: hover) {
  .video-filter .icon--sicon-heart-o:hover {
    color: #009ace;
  }
}

.icon--sicon-bookmark {
  color: #009ace;
}

.cscene__share {
  margin-left: auto;
  font-size: 16px;
}

.cscene__bookmark {
  /*  margin-left: 16px; */
}

.cscene__setting {
  margin-left: auto;
  color: #a7a8a9;
  font-size: 24px;
}

.cscene__setting:hover {
  color: #009ace;
}

.cscene__setting:hover {
  cursor: pointer;
}

.cscene:hover .cscene__thumb:before {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.cscene .cscene-horizontal-dots-wrap {
  position: relative;
}

.cscene .cscene-horizontal-dots {
  width: 165px;
  margin: 24px auto 0 auto;
  z-index: 2;
}

.cscene .cscene-horizontal-dots .cscene__variation-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  display: block;
  margin: 0;
  transform: scale(0.7);
  position: relative;
  transition: all 0.3s;
}

.cscene .cscene-horizontal-dots .cscene__variation-dot:hover {
  cursor: pointer;
}

.cscene .cscene-horizontal-dots .cscene__variation-dot:before {
  content: "";
  background-color: #f0f0f0;
  top: 0;
  left: 50%;
  width: 8px;
  height: 8px;
  position: absolute;
  border-radius: 4px;
  transform: translateX(-50%);
}

.cscene .cscene-horizontal-dots .cscene__variation-dot.slick-current {
  transform: scale(1);
  width: 16px;
  transition: all 0.9s;
}

.cscene .cscene-horizontal-dots .cscene__variation-dot.slick-current:before {
  background-color: #009ace;
}

.cscene .cscene-horizontal-dots .cscene__variation-dot.slide-prev,
.cscene .cscene-horizontal-dots .cscene__variation-dot.slide-next {
  transform: scale(1);
  transition: all 0.3s;
}

.cscene-vertical .messenger-content {
  width: 90%;
  margin: auto;
}

.cscene-vertical .messenger-content .s-audio {
  width: 100%;
}

.cscene .cscene-vertical-dots {
  width: 8px;
  position: absolute;
  top: calc(100% + 22px);
  left: 50%;
  transform: translateX(-50%);
  height: 36px;
  z-index: 2;
}

.cscene .cscene-vertical-dots .cscene__version-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
  display: block;
  margin: 2px 0;
  transition: all 0.3s;
}

.cscene .cscene-vertical-dots .cscene__version-dot:hover {
  cursor: pointer;
}

.cscene .cscene-vertical-dots .cscene__version-dot.slick-current {
  background-color: #009ace;
  transform: scale(1.1);
  transition: all 0.3s;
}

.cscene .slick-prev {
  left: 16px;
}

.cscene .slick-prev:before {
  content: "\e90f";
}

.cscene .slick-next {
  right: 16px;
}

.cscene .slick-next:before {
  content: "\e914";
}

.cscene .slick-arrow.slick-disabled {
  display: none !important;
}

.cscene .slick-prev,
.cscene .slick-next {
  font-size: 0;
  padding: 0;
  z-index: 99;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: #fff;
}

.cscene .slick-prev:before,
.cscene .slick-next:before {
  opacity: 1;
  font-family: "soremoicons";
  font-size: 20px;
  visibility: visible;
  transition: all 0.3s;
}

.cscene .slick-prev:focus,
.cscene .slick-prev:active,
.cscene .slick-next:focus,
.cscene .slick-next:active {
  outline: none;
}

.cscene .slick-prev:hover,
.cscene .slick-next:hover {
  cursor: pointer;
  background-color: #009ace;
  transition: all 0.3s;
}

.cscene .slick-dots {
  padding: 0;
  margin: 0;
  list-style: none;
}

.cscene .slick-dots button {
  font-size: 0;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
  border: none;
  display: block;
  padding: 0;
}

.cscene .slick-dots button:focus {
  outline: none;
}

.cscene .slick-dots button:hover {
  cursor: pointer;
}

.cscene .slick-dots li.slick-active button {
  background-color: #009ace;
}

.cscene-horizontal {
  position: relative;
  z-index: 3;
}

.cscene-horizontal > .slick-list {
  overflow: visible;
}

.cscene-horizontal > .slick-prev,
.cscene-horizontal > .slick-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.cscene-horizontal > .slick-dots {
  text-align: center;
}

.cscene-horizontal > .slick-dots li {
  display: inline-block;
  margin: 0 2px;
}

.cscene-horizontal > .slick-dots li.slick-active button {
  width: 16px;
}

.cscene-vertical .slick-prev {
  top: 16px;
}

.cscene-vertical .slick-next {
  bottom: 16px;
}

.cscene-vertical .slick-prev,
.cscene-vertical .slick-next {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) rotate(90deg);
}

.cscene-vertical > .slick-dots {
  position: absolute;
  left: 50%;
  top: 68%;
  transform: translateX(-50%);
}

.cscene-vertical > .slick-dots li {
  margin: 2px 0;
}

.cscene--audio .cscene-horizontal .slick-prev,
.cscene--audio .cscene-horizontal .slick-next {
  display: none !important;
}

.cscene--audio .cscene__version .messenger-content {
  display: block;
}

/*cvideo*/

/* .cvideo {
  max-width: 320px;
} */

.video-menu .menu-button {
  margin: 8px 0;
}

.cvideo__title {
  color: #000;
  cursor: pointer;
  /* font-size: 13px; */
  /* padding-left: 4px; */
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  /* word-break: break-word; */
  /* font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-style: normal;
  font-weight: 300;
  line-height: 175%; */
  /* オーバーフロー */
  text-overflow: ellipsis;
}

.project-tab-progress.view_only .cvideo__title,
.project-tab-progress.view_only .pd-chapter__title,
.mmessage-list.view_only .mmessage,
.pd-section--update .cvideo__title,
.pd-section--delivery-video .cvideo__title {
  cursor: default !important;
}

.cvideo__thumb {
  background-size: cover;
  border-radius: 6px;
  /*padding-bottom: 56.25%;*/
  position: relative;
  /* min-height: 144px; */
}

.cvideo__thumb .thumb-schedule-video {
  width: 100%;
  height: 100%;
  background-color: var(--soremo-border);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 144px;
  cursor: pointer;
  background-position: center;
  background-size: cover;

  border-radius: 6px;
}

.cvideo__thumb .thumb-schedule-video span {
  color: #ffffff;
  text-align: center;
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-size: 13px;
}

/* .cvideo__thumb-list-update .cvideo__thumb {
  height: 200px;
} */

/* .pd-section__video .cvideo__thumb-list-update {
  flex: 0 0 400px !important;
  max-width: 364px !important;
} */

/* @media (max-width: 992px) {
  .pd-section__video .cvideo__thumb-list-update {
    flex: 0 0 330px !important;
    max-width: 364px !important;
  }
} */

.cvideo__date-time {
  margin-left: auto;
  font-size: 11px;
  display: flex;
  align-items: center;
}

.cvideo__time {
  font-size: 11px;
  margin-left: 4px;
}

.cvideo__play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  color: #fff;
}

.cvideo__play:hover {
  color: #fff;
}

.cvideo__meta {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #a7a8a9;
  line-height: 1;
}

.video-action-done-rate {
  min-height: 24px;
}

.release-soon {
  /* font-style: normal; */
  font-weight: 300;
  font-size: 11px;
  line-height: 150%;
  color: #a7a8a9;
}

/* Hover last message */
.cvideo__last-msg {
  margin: 4px 0px;
  /* min-height: 40px; */
  width: 100%;
  background-color: #ffffff;
  position: relative;
  z-index: 1;
  list-style: none;
  max-height: 50dvh;
  overflow: hidden;
}

.cvideo__last-msg::after {
  content: "";
  display: block;
  position: absolute;
  top: -8px;
  left: 0;
  width: 54%;
  height: 8px;
}

.msg__wrap {
  display: flex;
  align-items: flex-end;
  gap: 4px;
}

.msg__avatar img {
  border-radius: 13px;
}

.msg__cmt {
  font-size: 13px;
  color: #000000;
  font-weight: normal;
  overflow: hidden;
  display: block;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 7;
  text-overflow: ellipsis;
  /* padding-top: 3px;
  max-height: 180px; */
  word-break: break-word;
  white-space: pre-line;
}

.msg__info {
  /* width: calc(100% - 24px); */
  padding: 8px 8px;
  /* max-height: 180px; */
  background-color: var(--soremo-bg-white);
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.msg__file {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-top: 4px;
}

.msg__file-text-none {
  display: block;
  width: calc(100% - 24px);
}

.msg__file-text-none .msg__file {
  margin: 0px 0px 0px 6px;
}

.msg__file .icon {
  font-size: 24px;
  color: #a7a8a9;
}

.msg__file-name {
  display: block;

  padding: 8px 8px;
  background-color: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 6px;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  margin-left: 4px; 


}

.loading-msg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* End hover last message */

.cvideo__heading {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  margin-top: 8px;
}

.cvideo__heading .scene-title__action {
  display: none;
  align-items: center;
  font-size: 12px;
  margin-left: 4px;
  transition: all 0.3s;
  flex-direction: row-reverse;
}

.cvideo__heading .scene-title__action.active {
  display: flex;
}

.cvideo__heading .scene-title__action.active-hover {
  display: flex;
}

.cvideo__heading .scene-title__action .scene-title-button {
  width: 22px;
  height: 22px;
  background-color: rgba(167, 168, 169, 0.2);
  border-radius: 50%;
  color: #a7a8a9;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.cvideo__heading .scene-title__action .scene-title__delete {
  margin-right: 6px;
}

.cvideo__heading .scene-title__action .scene-title__edit {
  margin: 0 7px;
}

.cvideo__heading .scene-title__action .scene-title-button:hover {
  background-color: #009ace;
  color: #fff;
}

.cvideo__heading .icon--sicon-heart,
.cvideo__heading .icon--sicon-heart-o {
  font-size: 18px;
  line-height: 18px;
}

.bootbox-body .edit-title {
  font-family: "A+mfCv-AXISラウンド 50 L StdN", "Noto Sans Japanese", sans-serif;
  color: black;
  font-weight: 400;
  margin-bottom: 30px;
  display: inline-block;
  margin-left: 20px;
}

.bootbox-body .edit-chapter-title {
  font-size: 20px;
}

#edit-chapter-title input {
  width: 65%;
}

.bootbox-edit-accept {
  background: #009ace !important;
  color: white !important;
}

.bootbox-edit-cancel {
  background: white !important;
  color: black !important;
}

.bootbox-edit-title {
  margin-left: 20px;
}

.bootbox-edit-title label {
  color: #000000;
  font-weight: 400;
  font-size: 20px;
  display: inline-block;
  margin-right: 15px;
}

.bootbox-edit-title input {
  border-radius: 3px;
  font-size: 17px;
  padding: 5px;
  border: 1px solid #383636;
}

.bootbox-edit-title input:focus {
  outline: none;
}

.cvideo__wishlist,
.icon--sicon-heart,
.icon--sicon-heart-o {
  font-size: 24px;
  color: #f0f0f0;
  line-height: 24px;
  cursor: pointer;
}

.cannot-check {
  cursor: default !important;
}

.cvideo__wishlist:before {
  content: "";
  font-family: "soremoicons";
  /* stylelint-disable-line */
}

.cvideo__wishlist,
.icon-botton-cscene {
  margin-left: auto;
}

.icon-botton-cscene .icon.icon--sicon-heart {
  color: #009ace;
}

.icon--sicon-heart:not(.cannot-check):hover:before {
  content: "";
  color: #0076a5;
}

.icon--sicon-heart-o:not(.cannot-check):hover:before {
  content: "";
  color: #009ace;
}

@media (hover: hover) {
  .video-filter .icon--sicon-heart-o:not(.cannot-check):hover:before {
    content: "";
    color: #0076a5;
  }
}

.video-heart .icon--sicon-heart:hover:before,
.video-heart-o .icon--sicon-heart-o:hover:before {
  content: "";
  color: #0076a5;
}

.download_production_btn {
  content: "";
  background-image: url("../../images/icon-download.svg");
  width: 24px;
  height: 16px;
}

.cvideo__download {
  font-size: 20px;
  margin-left: 18px;
}

.cvideo__share {
  margin-left: auto;
}

.cvideo__time {
  display: flex;
  align-items: center;
  line-height: 1;
}

.cvideo__time .icon {
  font-size: 16px;
  color: #a7a8a9;
  margin-right: 8px;
}

.cvideo__tooltip {
  padding: 8px 12px;
}

.cvideo__tooltip a {
  color: #fff;
  display: block;
  text-align: left;
  margin-bottom: 8px;
}

.cvideo__tooltip a:last-child {
  margin-bottom: 0;
}

.cvideo:hover .cvideo__thumb:before {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

/*sbreadcrumb*/

.sbreadcrumb {
  padding: 0;
  margin: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
}

.sbreadcrumb-item {
  -ms-grid-row-align: center;
  align-self: center;
}

.sbreadcrumb-item:before {
  content: "\e914";
  font-family: "soremoicons";
  /* stylelint-disable-line */
  color: #000;
  margin: 0 17px;
  font-size: 16px;
  position: relative;
  bottom: -2px;
}

.sbreadcrumb-item:first-child {
  margin-left: 0;
}

.sbreadcrumb-item:first-child:before {
  display: none;
}

.sbreadcrumb-item:last-child {
  margin-right: 0;
}

.pdetail {
  margin-top: 104px;
}

.pdetail--has-breadcrumb {
  margin-top: 89px;
}


.pd-section__title {
  padding: 0 16px 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start; 

  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-size: 18px;
  letter-spacing: 2.5px;
  line-height: 100%;
}


.pd-section__title .icon {
  /* stylelint-disable-line */
  font-size: 24px;
  color: #009ace;
  margin-left: auto;
}

.pd-section__title-sub {
  padding: 8px 0 0;

  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-size: 13px;
  letter-spacing: 0;

  color: #a7a8a9;
}


@media (max-width: 992px) {
  /* .pd-section__content {
    padding: 15px;
  } */
  .pd-section .video-order-by {
    width: 90px;
  }

  .pdetail {
    margin-top: 65px;
    background-color: #fff;
  }

  .pd-section__header-left {
    /* min-width: 90%; */
  }
}

.pd-section__video {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 48px 16px 16px;
}

/*.pd-section .cvideo {*/
/*    flex: 0 0 320px;*/
/*    padding: 0 12px;*/
/*}*/

/*.pd-section .cvideo:first-child {*/
/*    margin-left: -12px;*/
/*}*/

/*.pd-section .cvideo:last-child {*/
/*    margin-right: -12px;*/
/*}*/

/* .pd-section__video .cvideo {
  flex: 0 0 200px;
  padding: 0 8px;
} */

.pd-section--delivery-video .pd-section__video .cvideo {
  flex: 0 0 160px;
  padding: 0 8px 0 0;
}

/* .pd-section__video .cvideo:first-child {
  margin-left: -8px;
}

.pd-section__video .cvideo:last-child {
  margin-right: -8px;
} */

.pd-section__video .cvideo__wishlist {
  font-size: 20px;
}

.pd-section__video .cvideo .stars {
  font-size: 14px;
}

.pd-section__header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 16px;
  gap: 16px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;

}

.hide-button-tutorial {
  display: none !important;
  pointer-events: none !important;
}

.tooltip-content .arrow-tutorial {
  position: absolute;
  border: 10px solid transparent;
  border-top-color: #fcfcfc;
  bottom: 0px;
  left: 50%;
  transform: translate(50%, 100%);
}

@media (max-width: 992px) {
  .pd-section__header {
    /*display: block;*/
    /*margin: 0 15px;*/
    /*border: 1px solid #f0f0f0;*/
    /*border-radius: 8px;*/
    /*padding: 24px 16px;*/
    /* padding: 0px 0px 0px 16px; */
  }

  .tooltip-content .arrow-tutorial {
    position: absolute;
    border: 10px solid transparent;
    border-bottom-color: #fcfcfc !important;
    border-top-color: transparent !important;
    top: 0px;
    bottom: auto !important;
    left: 50%;
    transform: translate(-50%, -100%) !important;
  }
}

.pd-section__header-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
}

@media (max-width: 992px) {
  .pd-section__header-left .switch-slider {
    transform: scale(0.9);
  }
}

@media (max-width: 992px) {
  .pd-section__header-left .custom-switch .switch-label {
    font-size: 13px;
    margin-left: 8px;
  }
}

@media (max-width: 992px) {
  .pd-section__header-left {
    display: block;
  }
}

.pd-section__header-left .video-filter {
  display: flex;
  align-items: center;
  margin-left: 100px;
  color: #000;
}

.pd-section__header-left .video-filter:hover {
  cursor: pointer;
}

.pd-section__header-left .video-filter .icon {
  /* stylelint-disable-line */
  font-size: 24px;
  margin-right: 4px;
  color: #f0f0f0;
}

.pd-section__header-left .video-filter.video-heart .icon {
  /* stylelint-disable-line */
  color: #009ace;
}

@media (max-width: 992px) {
  .pd-section__header-left .video-filter .icon {
    font-size: 24px;
  }
}

@media (max-width: 992px) {
  .pd-section__header-left .video-filter .icon-image {
    width: 24px;
    height: 24px;
    background-size: cover;
  }
}

.pd-section__header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

@media (max-width: 992px) {
  .pd-section__header-right {
    /* flex: 1; */
  }
}

.pd-section .video-order {
  display: flex;
  align-items: center;
  font-size: 24px;
  margin: 0 4px 0 16px;
}

@media (max-width: 992px) {
  .pd-section .video-order {
    /* font-size: 20px; */
  }
}

.pd-section .video-order-type {
  color: #f0f0f0;
}

.pd-section .video-order-type:hover {
  cursor: pointer;
}

.pd-section .video-order-type.active {
  color: #009ace;
}

.pd-section .video-order__asc {
  margin-right: 2px;
}

@media (max-width: 992px) {
  .pd-section .video-order-by {
    flex: 1;
  }
}

@media (max-width: 992px) {
  .pd-section .video-order-by .SumoSelect {
    width: 100%;
  }
}

.pd-section .video-order-by .sselect-wrapper select,
.pd-section .video-order-by .sselect-wrapper option {
  /* font-size: 11px; */
}

.pd-section .pd-add-chapter {
  padding: 32px 0px;
}

.pd-section .pd-add-chapter__content {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px 0px;
  text-align: center;
  display: block;
}

.pd-section .pd-add-chapter__content:hover {
  background-color: rgb(167, 168, 169, 0.1);
}

.pd-section .pd-add-chapter__icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(167, 168, 169, 0.1);
  border-radius: 50%;
  margin: 0 auto;
}

.pd-section .pd-add-chapter__icon .icon {
  /* stylelint-disable-line */
  font-size: 32px;
  color: #a7a8a9;
}

.pd-section .pd-add-chapter__text {
  margin-top: 4px;
  color: #a7a8a9;
}


@media (max-width: 992px) {
  .pd-section--all-video {
    margin-bottom: 0;
  }
}

.pd-section--all-video .pd-section__title {
  display: none;
}

/*@media (max-width: 992px) {*/
/*    .pd-section--all-video .pd-section__title {*/
/*        display: flex;*/
/*        margin-bottom: 12px;*/
/*    }*/
/*}*/

.pd-section--detail-1 .pd-section__content {
  display: flex;
}

.pd-section--settings {
  margin-top: 24px;
}

.pd-chapter {
  margin-bottom: 40px;
}

.pd-chapter:first-child {
  margin-top: 48px;
}

.pd-chapter__line {
  flex: 1;
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 24px;
}

.pd-chapter__toggle {
  margin-left: auto;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(83, 86, 90, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.pd-chapter__toggle:before {
  content: "\e922";
  font-family: "soremoicons" !important;
  /* stylelint-disable-line */
  line-height: 1;
}

.pd-chapter__title {
  /* font-size: 18px;
  letter-spacing: 2.5px; */

  color: #000;
  padding:0 16px 8px;
  display: flex;
  align-items: center;
}

.pd-chapter__title:hover {
  cursor: pointer;
}

.pd-chapter__title.active .pd-chapter__line {
  display: none;
}

.pd-chapter__title.active .pd-chapter__toggle:before {
  transform: rotate(180deg);
  transition: all 0.3s;
}

.pd-chapter__title .pd-chapter__action {
  display: none;
  align-items: center;
  /* font-size: 12px; */
  margin-left: 4px;
  transition: all 0.3s;
  flex-direction: row-reverse;
}

.pd-chapter__title .pd-chapter__action .pd-chapter-button {
  width: 24px;
  height: 24px;
  background-color: rgba(167, 168, 169, 0.2);
  border-radius: 50%;
  color: #a7a8a9;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.pd-chapter__title .pd-chapter__action .pd-chapter__delete {
  margin-right: 6px;
}
.pd-chapter__title .pd-chapter__action .pd-chapter__edit {
  margin: 0 10px;
}

.pd-chapter__title .pd-chapter__action.active {
  display: flex;
}

.pd-chapter__title .pd-chapter__action.active-hover {
  display: flex;
}

.pd-chapter__title .pd-chapter__action .pd-chapter__edit:hover {
  background-color: #009ace;
  color: #fff;
}

.pd-chapter__title .pd-chapter__action .pd-chapter__delete:hover {
  background-color: #009ace;
  color: #fff;
}

.pd-chapter__content {
  display: none;
}

.pd-chapter__content .cvideo {
  /* stylelint-disable-line */
  flex: 0 0 256px;
  padding: 0 8px 0 0;
}

@media (max-width: 992px) {
  .pd-chapter__content .cvideo {
    flex: 0 0 256px;
    padding: 0 8px 0 0;
  }
}

.pd-chapter__content .cvideo:last-child {
  margin-right: -12px;
}

@media (max-width: 992px) {
  .pd-chapter__content .cvideo:last-child {
    margin-right: -8px;
  }
}

@media (max-width: 992px) {
  .pd-chapter__content .cvideo .stars {
    font-size: 12px;
  }
}

/*@media (max-width: 992px) {*/
/*    .pd-chapter__content .cvideo__title {*/
/*        font-size: 12px;*/
/*    }*/
/*}*/

/*@media (max-width: 992px) {*/
/*    .pd-chapter__content .cvideo__date-time {*/
/*        font-size: 10px;*/
/*    }*/
/*}*/

@media (max-width: 992px) {
  .pd-chapter__content .cvideo__wishlist {
    font-size: 20px;
  }
}

.pd-chapter__add {
  flex: 0 0 96px;
  /* border: 1px dashed #f0f0f0; */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  /* border-radius: 12px; */
  min-height: 96px;
  cursor: pointer;
}

.pd-chapter__add-content {
  align-items: center;
  display: flex;
  flex-direction: column;
}

@media (max-width: 992px) {
  .pd-chapter__add {
    flex: 0 0 96px;
    min-height: 96px;
  }
}

.pd-chapter__add .icon {
  /* stylelint-disable-line */
  font-size: 32px;
  color: #a7a8a9;
}

.pd-chapter__add-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(167, 168, 169, 0.1);
  border-radius: 50%;
}

.pd-chapter__add-text {
  color: #a7a8a9;
  margin-top: 4px;
  text-align: center;
}

.pd-chapter__list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 8px 16px 16px
}

/*.pd-chapter .cvideo {*/
/*    !* stylelint-disable-line *!*/
/*    flex: 0 0 278px;*/
/*}*/

#pnew-chapter .modal-header {
  border-bottom: none;
  padding-bottom: 8px;
}

.new-chapter__action {
  margin-top: 24px;
  text-align: center;
}

.pd-video-list {
  display: flex;
  flex-wrap: wrap;
  /* margin: 16px -12px -12px -12px; */
}

.pd-video-list .cvideo {
  /* stylelint-disable-line */
  flex: 0 0 256px;
  max-width: none;
  padding: 0 8px 0 0;
  margin-bottom: 32px;
}

.pd-video-list .cvideo:first-child {
  margin-left: 0;
}

.pd-video-list .cvideo:last-child {
  margin-right: 0;
}

@media (max-width: 992px) {
  .pd-video-list .cvideo {
    flex: 0 0 160px;
  }
}

.pd-scene {
  flex: 0 0 50%;
  padding-right: 12px;
}

@media (max-width: 768px) {
  .pd-scene {
    padding-right: 0;
  }
}

.pd-comment {
  /* flex: 0 0 50%;*/
  /* padding-left: 12px; */
}

@media (max-width: 768px) {
  .pd-comment {
    padding-left: 0;
  }
}

.pd-comment__top {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 24px;
}

.pd-comment__title {
  font-size: 16px;
  color: #000;
  font-weight: 400;
}

.pd-comment__file {
  margin-left: 60px;
  display: flex;
  align-items: center;
}

.pd-comment__file .icon {
  /* stylelint-disable-line */
  font-size: 24px;
  margin-right: 12px;
  color: #000;
}

.pd-comment__attach {
  margin-left: 60px;
  line-height: 25px;
}

.pd-comment .mmessage {
  align-items: center;
}

.pd-comment .mmessage-info {
  margin-top: 0;
}

.pd-comment .maction {
  padding: 0 0 0 0;
  border: none;
}

.maction {
  padding: 16px;
  margin-bottom: 15px;
}

.pd-comment .mcomment-icon .mcomment-icon-board {
  left: auto;
  right: -59px;
  height: 310px;
  transform: none;
}

.pd-settings-default {
  display: flex;
}

.pd-preview {
  flex: 0 0 50%;
  padding-right: 12px;
}

@media (max-width: 992px) {
  .pd-preview {
    padding-right: 0;
  }

  .mcolumn--main .maction {
    margin-bottom: 0;
  }
}

.pd-tree {
  flex: 0 0 50%;
  padding-left: 12px;
}

@media (max-width: 992px) {
  .pd-tree {
    padding-left: 0;
  }

  #modal-upload-contract-plan,
  #modal-upload-contract,
  #modal-upload-plan,
  #modal-confirm-upload {
    position: fixed;
    top: 0;
    height: 100%;
  }

  #modal-upload-contract-plan .modal-dialog,
  #modal-upload-plan .modal-dialog,
  #modal-upload-contract .modal-dialog {
    height: 95%;
    width: 95%;
  }

  #modal-upload-contract-plan .modal-content,
  #modal-upload-contract .modal-content,
  #modal-upload-plan .modal-content {
    height: 100%;
    max-width: 100vw;
    max-height: 100vh;
  }

  #modal-upload-contract-plan .modal-content .popup-body,
  #modal-upload-contract .modal-content .popup-body,
  #modal-upload-plan .modal-content .popup-body {
    height: calc(100% - 166px);
    overflow-y: scroll;
    overflow-x: hidden;
  }

  /* #modal-upload-contract-plan .form-group {
        padding-right: 48px;
    } */

  .contract__form-action-content {
    right: -10px;
  }
}

.pd-preview-default {
  margin-top: 16px;
}

.pd-preview-default__title {
  margin-bottom: 24px;
  color: #a7a8a9;
}

.pd-preview-default__upload {
  background-color: #fcfcfc;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.pd-tree-default {
  background-color: #fcfcfc;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  margin-top: 60px;
}

#pnew-chapter-tree .modal-header {
  border-bottom: none;
  padding-bottom: 8px;
}

#pnew-chapter-tree .new-chapter__action {
  margin-top: 40px;
}

#pnew-chapter-tree .new-chapter__create {
  margin-left: 12px;
}

.pd-tree-preview .video-modal__left {
  width: 100%;
}

/* comment */
.mcomment-input-title {
  color: #009ace;
  margin-right: 8px;
  display: none;
}

.mcomment-input-title .icon--sicon-pin {
  margin-right: 8px;
}

.mcomment-input-title .icon {
  /* stylelint-disable-line */
  font-size: 16px;
  position: relative;
  bottom: -2px;
}

.mcomment-input-close {
  width: 21px;
  height: 21px;
  background-color: rgba(167, 168, 169, 0.2);
  color: #53565a;
  border-radius: 50%;
  display: none;
  text-align: center;
  line-height: 21px;
  flex: 0 0 16px;
  font-size: 12px;
  margin-left: 4px;
}

.mcomment-input-close:hover {
  cursor: pointer;
  color: #009ace;
}

.mcomment-input.is-reply,
.mcomment-input.is-pin {
  align-items: center;
}

.mcomment-input.is-reply .mcomment-input-title,
.mcomment-input.is-pin .mcomment-input-title {
  display: block;
}

.mcomment-input.is-reply .mcomment-input-close,
.mcomment-input.is-pin .mcomment-input-close {
  display: block;
}

.video-pin-time.gray:before {
  background: url(../../images/icon-play-circle2-gray.svg) no-repeat top center;
  background-size: contain;
}

.animated {
  animation-duration: 3s;
  animation-fill-mode: both;
}

.pd-scene-title-detail.show-comment-unresolved .mmessage.resolved,
.pd-product-comment.show-comment-unresolved .mmessage.resolved,
.pd-product-comment.only-show-file .mmessenger.mmessenger--text {
  display: none !important;
  transition: all 1s;
}

.pd-scene-title-detail.show-comment-all:not(.show-comment-unresolved)
  .mmessage.resolved,
.pd-product-comment.show-comment-all:not(.show-comment-unresolved)
  .mmessage.resolved {
  display: flex !important;
  transition: all 1s;
}

.scene-file--name {
  font-size: 13px;
  display: block;
  overflow: hidden;
  cursor: default;
  text-overflow: ellipsis;
  color:#000;
  padding-right: 40px;
}

.stars {
  position: relative;
  /* display: inline-block; */
  font-size: 13px;
  letter-spacing: 1px;
}

.stars:before {
  /* content: "\e940 \e940 \e940 \e940 \e940"; */
  color: #a7a8a9;
  font-family: "soremoicons";
}

/* .stars span {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  width: 100%;
} */

.stars span {
  width: 8px;
  font-size: 13px;
}

.stars:not(.average-star) {
  margin-right: 8px;
}

.average-star{
  padding-right: 8px;
  /* height: 18px; */
}

.stars:not(.cannot-check) :is(span):hover,
.stars:not(.cannot-check) :is(span):has(~ :is(span):hover) {
  color: #009ace;
}

.stars :is(span).active,
.stars :is(span):has(~ :is(span).active) {
  color: #009ace;
}

.stars.can-rate{
  cursor: pointer;
}

.stars a.active ~ a:before {
  color: #a7a8a9;
}

.stars:not(.cannot-check):hover a:before {
  color: #009ace;
  content: "\e93f";
}

.stars:not(.cannot-check):hover a:before ~ a:before {
  color: #a7a8a9;
}

.stars a {
  position: relative;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
  width: 20%;
  height: 1em;
  line-height: 1rem;
}

.stars.cannot-check a {
  cursor: default !important;
}

.ps-stars {
  margin: 0 15px;
}

.ps-stars a {
  padding: 0 !important;
  margin: 0 !important;
  width: 11px;
}

.stars a:before {
  content: "\e940";
  color: #a7a8a9;
  font-family: "soremoicons";
  display: block;
  position: absolute;
  width: 1em;
  height: 1em;
  left: 0;
  text-indent: 0;
}

.average-star a:before {
  color: #f0f0f0 !important;
}

.pd-chapter__rating {
  /* font-family: "A+mfCv-AXISラウンド 50 L StdN", "Noto Sans Japanese",
    "sans-serif"; */
  font-size: 11px;
  /* line-height: 100%; */

  letter-spacing: 0;
  color: #a7a8a9;
}

.stars:not(.cannot-check) a:hover ~ a:before {
  color: #a7a8a9 !important;
  content: "\e940" !important;
}

.stars.selected span:before {
  color: #009ace;
}

.stars.selected span:before ~ span:before {
  color: #a7a8a9;
}

.stars.selected a.active ~ a:before {
  content: "\e940";
}

.stars.selected:not(.cannot-check):hover a:before {
  color: #009ace;
  content: "\e93f";
}

.stars.selected:not(.cannot-check):hover a:before ~ a:before {
  color: #a7a8a9;
}

.stars.selected:not(.cannot-check):hover a.active ~ a:before {
  content: "\e93f";
}


.owner-top {
  margin: 0;
}

.bootbox-prompt-message label {
  color: #000000;
}

/* Modal */
/* .modal-body,
.modal-footer {
  padding: 24px;
} */

#edit-scene-title {
  margin: 0;
}

#edit-scene-title input {
  width: 100%;
}
/* End modal */

/* Floating button */
.button-add-DM,
.button-accept-DM {
  position: absolute;
  bottom: calc(100% + 32px);
  right: 0;
  z-index: 10;
  padding: 0 !important;
}

.button-add-DM .btn-content {
  position: relative;
}

.button-add-DM i {
  font-size: 63px !important;
  margin-bottom: 0 !important;
}

.button-accept-DM i {
  background: white;
  border-radius: 50%;
}
/* End floating button */

/* Modal create edit scene */
#modal-upload-scene .schedule-date-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px 8px 12px;
  background: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  position: relative;
}

#modal-upload-scene .schedule-date-container .form-row__mobile {
  width: 100%;
  padding: 0;
  margin: 0;
  margin-bottom: 8px !important;
}

#modal-upload-scene
  .schedule-date-container
  .component-datetime-container.calendar-time {
  width: auto;
}

#modal-upload-scene .schedule-date-container .text-count-day {
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-size: 13px;
  line-height: 200%;
  color: #000000;
}

#modal-upload-scene .modal-dialog,
#modal-take-scene .modal-dialog,
#modal-scene-thumbnail .modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#modal-upload-scene .popup-header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 152px;
  width: 100%;
  padding: 0 24px;
}

/* ---------------- */
#modal-upload-scene .modal-dialog {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0px !important;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}

#modal-upload-scene .modal-dialog .modal-content {
  width: 100%;
  margin-top: 152px;
}

#modal-upload-scene .modal-body {
  max-height: 100% !important;
  min-height: calc(100vh - 152px - 104px) !important;
  overflow: hidden;
  max-width: 756px;
  width: 100%;
  height: 100% !important;
}

#modal-upload-scene .custom-input-component {
  margin-bottom: 0;
}

#modal-upload-scene .custom-input-component input {
  margin: 0;
}

#modal-upload-scene .create-scene__action {
  position: fixed;
  bottom: 0;
  left: 0;
  margin-bottom: 0px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 16px;
  width: 100%;
  background-color: #fcfcfc;
  border: 1px solid #f0f0f0;
  z-index: 9999;
}

#modal-upload-scene .create-scene__action__container {
  width: min(100% - 32px, 1140px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

#modal-upload-scene .modal-dialog__header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 152px;
  width: 100%;
  padding: 0 24px;
}

#modal-upload-scene .modal-dialog__header__container {
  width: 100%;
  max-width: 1140px;
  background: #ffffff;
  padding: 32px 0px;
  height: 100%;
}

#modal-upload-scene .modal-dialog__header__container > hr {
  margin: 24px 0;
  width: 100%;
  border-bottom: none;
}

#modal-upload-scene
  .modal-dialog__header__container
  .modal-dialog__header__text {
  width: 100%;
  font-weight: 400;
  font-size: 40px;
  line-height: 100%;
  color: #000000;
  letter-spacing: 2.5px;
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  text-align: left;
}

#modal-upload-scene
  .modal-dialog__header__container
  .modal-dialog__header__text
  h1 {
  font-size: 40px;
  line-height: 100%;
}

#modal-upload-scene .heading--16 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

#modal-upload-scene .grey-label--8,
#modal-upload-scene .blue-label--8 {
  margin-left: 4px;
}

#modal-upload-scene .form-block-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background-color: transparent;
  margin-bottom: 16px;
}

#modal-upload-scene .form-block-container .form-block-title {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 200%;
  color: #000000;
  text-align: left;
}

#modal-upload-scene .form-block-container .form-block-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px 8px 16px;
  background: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

#modal-upload-scene .form-block-container .form-block-content .sform-row {
  width: 100%;
}

#modal-upload-scene
  .form-block-container
  .form-block-content
  .sform-row:last-child {
  margin-bottom: 0px;
}

#modal-upload-scene
  .form-block-container
  .form-block-content
  .input-block-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

#modal-upload-scene
  .form-block-container
  .form-block-content
  .input-block-bottom {
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
}

#modal-upload-scene .form-block-container .form-block-content .input-block-top {
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: flex-start;
}

#modal-upload-scene .form-row {
  margin: 0px 0 24px !important;
}

#modal-upload-scene .form-block-container .form-block-content .input-block-top {
  margin-bottom: 8px;
}

#modal-upload-scene
  .form-block-container
  .form-block-content
  .input-block-top
  .form-group {
  margin-bottom: 0px;
  position: relative;
}

#modal-upload-scene .form-block-content label {
  margin-bottom: 0;
}

#modal-upload-scene
  .form-block-content
  .sform-row--2-columns
  .sform-group:first-child {
  padding-right: 4px;
}

#modal-upload-scene
  .form-block-content
  .sform-row--2-columns
  .sform-group:last-child {
  padding-left: 4px;
}

@media (max-width: 576px) {
  #modal-upload-scene {
    top: 0;
    width: 100%;
    margin: 0;
  }

  #modal-upload-scene .modal-dialog__header {
    height: 132px !important;
  }

  #modal-upload-scene
    .modal-dialog__header__container
    .modal-dialog__header__text
    h1 {
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 2.5px;
  }

  #modal-upload-scene .create-scene {
    max-height: calc(100vh - 132px - 104px) !important;
  }

  /* #modal-upload-scene .create-scene__action__container {
    padding-left: 24px;
    padding-right: 24px;
  } */

  #modal-upload-scene
    .form-block-content
    .sform-row--2-columns
    .sform-group:last-child {
    padding-left: 4px;
  }

  #modal-upload-scene
    .form-block-content
    .sform-row--2-columns
    .sform-group:first-child {
    padding-right: 4px;
  }

  #modal-upload-scene .form-block-content .sform-row--2-columns .sform-group {
    flex: 0 0 50% !important;
  }

  #modal-upload-scene .modal-dialog .modal-content {
    width: 100%;
    margin-top: 132px;
  }
}

.create-scene {
  max-height: calc(100vh - 152px - 104px) !important;
  width: 100%;
  display: flex;
  justify-content: center;
  height: 100% !important;
  position: relative;
}

#modal-upload-scene .button-scroll-top {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 44px;
  padding-right: 8px;
  /* border-top: 1px solid #F0F0F0; */
}

#modal-upload-scene .button-scroll-top-container:hover svg path {
  fill: var(--soremo-deep-blue);
}

#modal-upload-scene .button-scroll-top svg:hover {
  cursor: pointer;
}

#modal-upload-scene .scene-list-take {
  width: 100%;
  padding: 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column-reverse;
  margin-bottom: 36px;
}

#modal-upload-scene .scene-list-take:empty {
  border: none !important;
}

#modal-upload-scene .scene-take-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}

#modal-upload-scene .scene-take-container:hover {
  background-color: #f0f0f0;
}

#modal-upload-scene .scene-take-container .scene-take-namne-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
  margin-left: 8px;
  padding-bottom: 16px;
  margin-top: 16px;
}

#modal-upload-scene .scene-take-name-left {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

#modal-upload-scene
  .scene-take-container:not(:first-child)
  .scene-take-namne-container {
  border-bottom: 1px solid #f0f0f0;
}

#modal-upload-scene .scene-take-name {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

#modal-upload-scene .scene-take-name .scene-take-name-content {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-size: 13px;
  line-height: 200%;
  color: #000000;
}

#modal-upload-scene .scene-take-name .scene-take-time-content {
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-size: 8px;
  line-height: 100%;
  color: #a7a8a9;
  margin-left: 16px;
}

#modal-upload-scene .scene-take-list-variant {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
}

#modal-upload-scene .scene-take-list-variant .variant-name-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

#modal-upload-scene
  .scene-take-list-variant
  .variant-name-container
  .scene-variant-name {
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-size: 11px;
  line-height: 200%;
  color: #000000;
  margin-right: 8px;
  /* flex: 1; */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  max-width: 50%;
}

#modal-upload-scene
  .scene-take-list-variant
  .variant-name-container
  .scene-variant-file {
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  font-size: 11px;
  line-height: 200%;
  color: #a7a8a9;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  max-width: 50%;
}

#modal-upload-scene .scene-take-video {
  position: relative;
  width: 86px;
  height: 48px;
}

#modal-upload-scene .btn-popup-close:hover {
  background: transparent !important;
}

#modal-upload-scene[action-type="edit"] .btn-popup-close {
  display: none !important;
}

#modal-upload-scene .scene-take-video img {
  width: 86px;
  background: #f0f0f0;
  border-radius: 4px;
  height: 48px;
  text-indent: -10000px;
  min-width: 86px;
  min-height: 48px;
  object-fit: cover;
}

#modal-upload-scene #create-scene-upload-dropzone .dz-button p {
  margin-top: -9px;
  line-height: 100%;
  font-weight: 300;
  font-size: 8px;
  color: #000000;
}

#modal-upload-scene #create-scene-upload-dropzone {
  height: 96px;
}

#modal-upload-scene .scene-take-video svg {
  position: absolute;
  top: calc(50% - 10px);
  left: calc(50% - 10px);
}

#modal-upload-scene .btn-upload-scene .btn-text {
  margin-right: 0;
}

#modal-upload-scene .modal-body:has(.scene-list-take:empty) .button-scroll-top {
  display: none;
}

/* ---------------- */
/* End modal create edit scene */

/* Modal take scene */
#modal-take-scene .take-scene {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 32px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  max-height: 80vh;
  height: 100%;
}

#modal-take-scene .modal-dialog {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#modal-take-scene .modal-body {
  max-height: calc(80vh - 130px - 116px) !important;
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px 8px !important;
  background: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

#modal-take-scene .modal-content {
  max-width: 756px;
  width: 100%;
}

#modal-take-scene .take-scene__action {
  width: 100%;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
  padding: 32px 0 0;
}

#modal-take-scene .btn-confirm-take-scene {
  width: 128px;
  min-width: 128px;
}

#modal-take-scene .take-scene-back {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.take-scene-back svg,
.scene-thumnail-back svg {
  cursor: pointer;
}

#modal-take-scene .take-scene-name-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 4px;
}

#modal-take-scene .take-scene-name {
  font-size: 16px;
  line-height: 200%;
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  color: #000000;
  font-weight: 400;
}

#modal-take-scene .take-scene-time {
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  color: #a7a8a9;
  font-weight: 300;
  margin-left: 16px;
}

#modal-take-scene .upload-container {
  width: 100%;
}

#modal-take-scene .contract__form-label {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 200%;
  color: #000000;
}

#modal-take-scene .version-container {
  min-height: 64px;
  max-height: calc(80vh - 132px - 180px);
  width: 100%;
}

#modal-take-scene .modal-dialog {
  background-color: transparent !important;
}

#modal-take-scene .version-item {
  width: 100%;
  height: 64px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

#modal-take-scene .version-item img {
  min-width: 114px;
  height: 64px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: #f0f0f0;
  text-indent: -10000px;
  border-radius: 4px 0px 0px 4px;
  object-fit: cover;
  max-width: 114px;
}

#modal-take-scene .version-item .content-container {
  width: 100%;
  height: 100%;
  padding: 8px 14px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 0px 4px 4px 0px;
  border-left: none;
}

#modal-take-scene .version-item .version-real-name {
  font-weight: 300;
  font-size: 13px;
  line-height: 200%;
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  color: #000000;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

#modal-take-scene .version-item .version-file-name {
  font-weight: 300;
  font-size: 11px;
  line-height: 200%;
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  color: #a7a8a9;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

#modal-take-scene .version-item .version-action-container {
  width: 24px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 24px;
}

#modal-take-scene .version-item .version-action__button-container {
  flex-direction: column;
  display: none;
}

#modal-take-scene .version-item:hover .version-action__button-container {
  display: flex;
}

#modal-take-scene .version-item .member-item__btn-move {
  cursor: pointer;
}

#modal-take-scene .version-item .member-item__btn-delete {
  cursor: pointer;
}

#modal-take-scene .version-item .member-item__btn-move:hover svg path {
  fill: #009ace;
}

#modal-take-scene .version-item .member-item__btn-delete:hover svg path {
  fill: #009ace;
}

#modal-take-scene #version-upload-dropzone .dz-button p {
  margin-top: -9px;
  line-height: 100%;
  font-weight: 300;
  font-size: 8px;
  color: #000000;
}

#modal-take-scene #version-upload-dropzone {
  height: 96px !important;
}
/* End modal take scene */

/* Modal thumbnail scene */
#modal-scene-thumbnail .modal-content {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  height: 100%;
}

#modal-scene-thumbnail .modal-dialog {
  width: 100% !important;
  height: auto !important;
  max-width: 50%;
  max-height: 80vh;
  border-radius: 0px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 32px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 12px !important;
}

#modal-scene-thumbnail .scene-thumnail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

#modal-scene-thumbnail .scene-thumnail-back {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: fit-content;
}

#modal-scene-thumbnail .scene-thumnail-name-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 4px;
}

#modal-scene-thumbnail .scene-thumnail-name {
  line-height: 200%;
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  color: #000000;
  font-weight: 400;
  font-size: 13px;
}

#modal-scene-thumbnail .take-scene__action {
  width: 100%;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
  padding: 32px 0 0;
}

#modal-scene-thumbnail .modal-body {
  width: 100%;
  height: 100%;
  padding: 0 !important;
  min-height: 100px;
  max-height: calc(80vh - 64px - 30px - 48px) !important;
  overflow-x: hidden;
}

#modal-scene-thumbnail .component-tab-container {
  margin-bottom: 16px;
}

#modal-scene-thumbnail .component-tab-container .tab-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px 8px 24px;
  background: #fcfcfc;
}

#modal-scene-thumbnail .component-tab-container .tab-content .tab-pane {
  width: 100%;
  padding: 0;
}

#modal-scene-thumbnail .component-tab-container video {
  width: fit-content;
  height: fit-content;
  background: #d9d9d9;
  border-radius: 2px;
  max-height: 100%;
  max-width: 100%;
}

#modal-scene-thumbnail .component-tab-container img,
#modal-scene-thumbnail .thumbnail-img-container-1 img {
  width: auto;
  height: fit-content;
  min-height: 170px;
  background: #d9d9d9;
  border-radius: 6px;
  text-indent: -10000px !important;
  max-width: 100%;
  max-height: 100%;
}

#modal-scene-thumbnail .reupload-version-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#modal-scene-thumbnail .version-name {
  font-weight: 300;
  font-size: 11px;
  line-height: 200%;
  color: #000000;
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
}

#modal-scene-thumbnail .btn-reupload {
  width: 128px;
  min-width: 128px;
  padding: 8px 9px !important;
}

#modal-scene-thumbnail .btn-reupload .btn-text {
  margin-left: 4px;
}

#modal-scene-thumbnail .take-scene__action .component-button {
  width: 128px;
  min-width: 128px;
}

#modal-scene-thumbnail .tab-content > .active {
  display: flex !important;
}

#modal-scene-thumbnail .tab-content .thumbnail-img-container,
#modal-scene-thumbnail .tab-content .thumbnail-video-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 400px;
}

#modal-scene-thumbnail .thumbnail-img-container-1 img[src=""] {
  width: 100% !important;
}

#modal-scene-thumbnail .thumbnail-img-container-1 {
  margin-bottom: 16px;
}

#modal-scene-thumbnail .thumbnail-img-container,
#modal-scene-thumbnail .thumbnail-img-container-1 {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

#modal-scene-thumbnail .thumbnail-img-container > svg,
#modal-scene-thumbnail .thumbnail-img-container-1 > svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.6;
}

@media (max-width: 365px) {
  .datepicker-dropdown {
    left: 50% !important;
    transform: translate(-50%, 0) !important;
    max-width: 100% !important;
  }

  .component-button {
    min-width: 50%;
  }
}
@media (max-width: 768px) {
  #modal-scene-thumbnail .component-tab-container video {
    max-height: 170px;
  }

  #modal-scene-thumbnail .component-tab-container img,
  #modal-scene-thumbnail .thumbnail-img-container-1 img {
    max-height: 170px;
  }

  #modal-scene-thumbnail .thumbnail-img-container,
  #modal-scene-thumbnail .thumbnail-img-container-1,
  #modal-scene-thumbnail .tab-content .thumbnail-video-container {
    height: 170px !important;
  }

  #modal-take-scene {
    margin: 0;
  }

  #modal-take-scene .modal-content {
    max-width: calc(100% - 10px);
    width: 100%;
    height: 90vh;
  }

  #modal-take-scene .take-scene {
    max-height: 100%;
    height: 100%;
  }

  #modal-take-scene .modal-body {
    height: 100%;
    max-height: 100% !important;
  }

  #modal-take-scene .version-container {
    min-height: 64px;
    max-height: 100% !important;
    width: 100%;
    height: 100%;
  }
}
@media (max-width: 992px) {
  #modal-scene-thumbnail .reupload-version-container {
    display: block;
  }

  #modal-scene-thumbnail .modal-dialog {
    max-width: calc(100% - 10px);
  }

  #modal-scene-thumbnail .smodal {
    margin: 0 !important;
  }

  #modal-scene-thumbnail .modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  #modal-take-scene .version-item .version-action__button-container {
    display: flex !important;
  }
}
/* End modal thumbnail scene */

/* Carousel to button */
.list-variation {
  display: flex;
  flex-direction: column-reverse;
  width: 100%;
  overflow-y: auto;
  padding-top: 8px;
}

.list-variation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.list-scene-horizontal {
  height: auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 16px 16px;
  flex-flow: wrap;
  overflow: hidden;
}

.variation-button-container {
  /* 高さ固定、幅は画像の元のアスペクト比に応じて自動調整 */
  /* height: 80px; 固定高さ */
  /* width: auto; 幅は画像に応じて自動調整 */
  /* min-width: 60px; 最小幅 */
  /* max-width: 160px; 最大幅 */
  
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* border: 1px solid #f0f0f0 !important; */
  border-radius: 6px;
  cursor: pointer !important;
  outline: none !important;
  position: relative;
  box-shadow: 2px 5px 8px 0px rgba(0, 154, 206, 0.1);
  overflow: hidden; /* 画像がコンテナからはみ出ないように */
}

.variation-button-name-tooltip-container {
  display: none;
  width: max-content;
  min-height: 16px;
  max-width: unset;
  min-width: 48px;
  flex-direction: row;
  align-items: flex-start;
  padding: 4px;
  background: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  position: fixed;
  top: -3px;
  left: 50%;
  /* transform: translate(-50%, -100%); */
  z-index: 99;
  justify-content: center;
}

.variation-button-name-tooltip-content {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-size: 11px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #000000;
  word-break: break-all;
}

.variation-button-container:hover .variation-button-name-tooltip-container {
  display: flex;
}

/* .variation-button-container:hover {
    background-image: none !important;
} */

.variation-button-container.active,
.variation-button-container:hover {
  outline: solid 2px #009ace !important;
  outline-offset: -1px;
  box-shadow: 2px 5px 8px 0px rgba(0, 154, 206, 0.10);

}

.variation-button-name {
  font-weight: 300;
  font-size: 13px;
  line-height: 200%;
  color: #000000;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  overflow: hidden;
  text-align: center;
}

.take-detail {
  height: 100%;
  width: 50px;
}

.take-detail-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 42px;
  height: 42px;
  border-radius: 19px;
  min-width: 42px;
  cursor: pointer;
  position: relative;
  margin-left: 8px;
}

.take-detail-text {
  font-style: normal;
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #ffffff;
  margin-bottom: 2px;
}

.take-detail-number {
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 2.5px;
  color: #ffffff;
}

.list-scene-horizontal::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.choose-take-overlay .input-radio input:checked ~ .check-mark {
  border: 1px solid var(--soremo-blue);
}

.choose-take-overlay .input-radio .check-mark {
  width: 16px;
  height: 16px;
  border: 1px solid var(--soremo-light-gray);
  top: 2px;
}

.choose-take-overlay .input-radio .check-mark:after {
  top: 2px;
  left: 2px;
  width: 10px;
  height: 10px;
}

.choose-take-overlay .input-radio {
  line-height: 200% !important;
  padding-left: 16px;
}

.choose-take-overlay{
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-start;
    padding: 8px 8px 16px 18px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    box-shadow: 2px 4px 8px 3px rgb(0 0 0 / 5%);
    border-radius: 4px;
    width: fit-content;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    /*max-height: 240px;*/
    overflow-y: auto;
}

.choose-take-overlay.choose-take-overlay-hidden {
    height: 0 !important;
    overflow: hidden !important;
    border: none !important;
    box-shadow: none !important;
    visibility: hidden !important;
}

.take-overlay-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin-bottom: 16px;
}

.take-overlay-container:first-child {
  margin-bottom: 0;
}

.take-detail-select-container {
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #a7a8a9;
  width: 40px;
  text-align: center;
  margin: 0 8px 0 18px;
}

.take-detail-select-container .take-dayless {
  font-style: normal;
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #a7a8a9;
}

.take-detail-select-container .take-day {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 100%;
  text-align: center;
  letter-spacing: 2.5px;
  color: #53565a;
}

.take-detail-select-container .take-time {
  font-style: normal;
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #a7a8a9;
}

.take-name-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  width: 42px;
  height: 42px;
  border-radius: 19px;
  position: relative;
}

.arrow-container {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translate(-50%, -100%);
  height: 16px;
  width: fit-content;
  display: flex;
}

.arrow-container svg path {
  fill: #a7a8a9;
}

.take-name-container .take-text {
  font-style: normal;
  font-weight: 300;
  font-size: 8px;
  line-height: 100%;
  font-feature-settings: "palt" on;
  color: #ffffff;
}

.take-name-container .take-number {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 2.5px;
  color: #ffffff;
}

.take-color-1 {
  background: #4abdac;
  border: 1px solid #4abdac;
}
.take-color-2 {
  background: #f7b733;
  border: 1px solid #f7b733;
}
.take-color-3 {
  background: #fc4a1a;
  border: 1px solid #fc4a1a;
}
.take-color-4 {
  background: #4abdac;
  border: 1px solid #4abdac;
}

.select-take-back {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: transparent;
  z-index: 999;
  display: none;
}
/* End carousel to button */

/* code fixed button preview plan approve

.open-preview-plan-approve-modal {
    position: fixed;
    bottom: 9%;
}

@media (min-width: 768px) and (max-width: 992px) {
  .open-preview-plan-approve-modal {
    bottom: 19.5%;
    left: 0;
    width: 100% !important;
  }
}


@media (max-width: 768px) {
  .open-preview-plan-approve-modal {
    bottom: 26%;
    width: 100% !important;
  }
}

 */

.border-editing .mcomment-top:has(.block-remove-msg-editing) {
  position: relative;
}

.border-editing .mcomment-top .block-remove-msg-editing {
  position: absolute;
  top: 4%;
  right: 5px;
}

.d-block {
  display: block;
}

.border-editing .mcomment-attached:not(:empty) {
  padding: 12px 14px;
}