/* COLLECTION */
:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --boder-color: #F0F0F0;
    --blue-color: #009ACE;
    --background-color: #FCFCFC;
}

.collection-wrap {

}

/* Tabs */

.tab-nav-wrap {
    margin: 30px 0;
}
.tab-nav-wrap {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.nav-tabs {
    border: 1px solid var(--boder-color);
    border-radius: 10px;
    width: fit-content;
    margin: 10px 0;
}

.nav-item {
    padding: 15px 24px;
    border-right: 1px solid var(--boder-color);
}

.nav-item:last-child {
    border-right: none;
}

.nav-item a {
    color: var(--grey1-color);
}

.nav-link {
    background-color: var(--white-color);
    font-size: 12px;
    line-height: 18px;
}

.nav-tabs li.active a, .nav-tabs li.active a:focus, .nav-tabs li.active a:hover {
    color: var(--blue-color);
}

.nav-tabs li a, .nav-tabs li.active a, .nav-tabs li.active a:focus, .nav-tabs li.active a:hover, .nav-tabs li a:hover {
    border: none;
    padding: 0;
    background-color: var(--white-color);
}

/* Content tabs */
.collection-content {

}

.collection-content__heading {
    padding: 8px 0;
    font-size: 18px;
    color: var(--black1-color);
}

.collection-content__wrap {

}

.collection-content__list {
    
}

.collection-content__item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--soremo-placeholder);
}

.cvideo__title {
    padding-left: 0;
}

/* シーン */
.pd-chapter__content {
    display: block;
}

.collection-content__wrap .pd-chapter__list {
    flex-wrap: wrap;
    margin: 0 -10px;
}

.cvideo__rating .collection-content__rating .icon {
    font-size: 13px;
    cursor: pointer;
}

.pd-chapter__list .cvideo {
    padding: 8px;
    width: 200px;
}

.project-delivery-item-content .cvideo__thumb video {
    object-fit: cover;
    cursor: pointer;
}

.cvideo__rating {
    line-height: 14px;
}

/* アルバム */
.collection-content__artist-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.collection-content__artist-item {
    padding: 10px;
}

.collection-content__artist-image {
    position: relative;
    margin-bottom: 8px;
    height: 170px;
    max-width: 170px;
}

.collection-content__artist-img {
    height: 100%;
    width: 100%;
    border-radius: 12px;
}

.collection-content__artist-image .icon-bookmark {
    position: absolute;
    top: 15px;
    left: 15px;
}

.icon-bookmark:before {
    content: '\e948';
    font-family: "soremoicons";
    color: var(--white-color);
    font-size: 20px;
}

.icon-bookmark.active:before {
    content: '\e949';
    font-family: "soremoicons";
    color: var(--white-color);
    font-size: 20px;
}

.collection-content__artist-title {
    font-size: 14px;
    font-weight: 400;
    color: var(--black1-color);
    margin-bottom: 4px;
}

.collection-content__artist-detail {
    font-size: 12px;
    color: var(--black2-color);
    margin-bottom: 4px;
}

.collection-content__artist-bg {
    height: 100%;
    width: 100%;
    border-radius: 12px;
    position: absolute;
    top: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
}

.collection-content__artist-view, .collection-content__artist-view:hover, .collection-content__artist-view:focus {
    color: var(--black1-color);
    padding: 8px 13px;
    background-color: var(--white-color);
    border-radius: 21px;
    font-size: 14px;
    font-weight: 400;
    width: fit-content;
    position: absolute;
    top: 34px;
    left: 50%;
    transform: translateX(-50%);
}

.collection-content__artist-action {
    display: flex;
    justify-content: center;
    margin-top: 100px;
}

.collection-content__artist-btn, .collection-content__artist-btn:hover, .collection-content__artist-btn:focus {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--white-color);
    height: 40px;
    width: 40px;
    margin: 8px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.collection-content__artist-btn i {
    font-size: 18px;
}

.collection-content__artist-image:hover .collection-content__artist-bg {
    display: block;
}

.collection-content__right {
    margin: 0 15px;
}

/* アーティスト */
.img-artist {
    height: 64px;
    width: 64px;
    border-radius: 50%;
}

.collection-content_total {
    font-size: 14px;
}

/* Modal create new list */
.btn-create-new, .btn-create-new:hover, .btn-create-new:focus {
    background: var(--blue-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--white-color);
    padding: 12px 24px;
    margin: 10px 0;
    max-height: 45px;
}

#create-new-modal {
    background-color: rgba(0, 0, 0, 0.6);
}

.create-modal__heading {
    position: relative;
    padding: 24px;
    border-bottom: none;
    margin-bottom: 15px;
}

.create-modal__heading h5 {
    color: var(--black1-color);
    font-size: 16px;
    font-weight: 400;
}

.create-modal__heading .close {
    color: var(--grey1-color);
    opacity: 1;
}

.create-modal__heading .close i {
    font-size: 20px;
    line-height: 20px;
    position: absolute;
    right: 24px;
    top: 25px;
    padding: 0;
}

.create-modal__wrap {
    padding: 6px 24px;
}

.create-modal__wrap .form-group {
    margin-bottom: 24px;
}

.create-modal__wrap .form-group:last-child {
    margin-bottom: 0;
}

.create-modal__action {
    text-align: right;
    border-top: none;
    padding: 34px 15px 40px;
}

.cvideo__heading {
    padding-left: 0px !important;
}

.cvideo__heading .cvideo__title {
    line-height: 24px;
}


.create-modal__action .btn {
    padding: 12px 24px;
    font-size: 13px;
    border-radius: 4px;
    margin: 0 8px;
}

.btn {
    cursor: pointer;
}

.btn-modal-cancel{
    background-color: var(--white-color);
    color: var(--blue-color);
    border: 1px solid var(--blue-color);
}

.modal-dialog {
    margin-top: 50vh;
    transform: translateY(-50%) !important;
}

.btn-modal-accept {
    background-color: var(--blue-color);
    color: var(--white-color);
}

/* Modal add item */
#add-item-modal {
    background-color: rgba(0, 0, 0, 0.6);
}

#add-new-modal .modal-content {
    border-radius: 12px;
}

#add-item-modal .cvideo__heading > div {
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%;
}

.add-modal__heading {
    position: relative;
    padding: 24px;
    border-bottom: none;
}

.add-modal__heading h5 {
    color: var(--black1-color);
    font-size: 16px;
    font-weight: 400;
}

.add-modal__heading .close {
    color: var(--grey1-color);
    opacity: 1;
}

.add-modal__heading .close i {
    font-size: 18px;
    line-height: 18px;
    position: absolute;
    right: 24px;
    top: 25px;
    padding: 0;
}

.add-modal__wrap {
    padding: 6px 24px;
}

.add-modal__wrap .form-group {
    margin-bottom: 24px;
}

.add-modal__wrap .form-group:last-child {
    margin-bottom: 0;
}

.add-modal__search{
    display: flex;
    align-items: center;
    position: relative;
}

.add-modal__search .icon {
    position: absolute;
    left: 16px;
}

#search-input {
    padding-left: 42px;
}

.add-modal__action {
    text-align: right;
    border-top: none;
    padding: 34px 15px 40px;
}

.modal-footer.add-modal__action .btn {
    border-radius: 4px;
    margin: 8px;
}

.cvideo__bookmark .icon--bookmark::before {
    content: '\e94d';
    color: var(--white-color);
    font-family: 'soremoicons';
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    background: #00000026;
    border-radius: 4px;
    border: 0px solid transparent;
    /* box-shadow: 0 2px 16px rgb(0 0 0 / 25%); */
}

.cvideo__bookmark .icon--bookmark.checked::before {
    content: '\e94e';
    color: var(--white-color);
    font-family: 'soremoicons';
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
}

.cvideo__not-found, .cvideo__existed {
    display: none;
    padding: 25px 10px;
    text-align: center;
}

.cvideo__not-found.active, .cvideo__existed.active {
    display: block;
}

/* List new */
.tab-section {

}
.tab-section__wrap {

}

.tab-section__header {
    display: flex;
    cursor: pointer;
    height: 64px;
    line-height: 64px;
    position: relative;
}

.tab-section__header-root {
    display: flex;
    height: 64px;
    line-height: 64px;
    position: relative;
}

.tab-section__title {
    word-break: break-all;
    margin-left: 24px;
    line-height: inherit;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.tab-section__header-root .tab-section__title {
    margin-top: 17px;
}

.tab-section__action {
    height: 18px;
    margin-left: 16px;
    display: none;
}

.tab-section__action.active {
    display: block;
    min-width: 80px;
}

.tab-section__action.active-hover {
    display: block;
    min-width: 80px;
}

.tab-section__action .btn--edit, .btn--delete {
    width: 24px;
    height: 24px;
    background-color: rgba(167, 168, 169, 0.2);
    border-radius: 50%;
    color: #a7a8a9;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    margin: 5px;
}

.tab-section__action .btn--edit:hover, .btn--delete:hover {
    background-color: #009ace;
    color: #fff;
}

.tab-section__action .btn--edit:focus, .btn--delete:focus {
    outline: none;
}

.tab-section__main {
    padding: 20px 0;
}

.container {
    margin-bottom: 80px;
}

.tab-section__list__root .tab-section__main {
    padding: 6px;
}

.scene-title__move {
    margin-left: 5px;
}

.pd-chapter__add {
    cursor: pointer;
}

.cvideo__thumb:hover .cvideo__bg {
    display: block;
}

.cvideo__thumb {
    height: 100px;
    width: 184px;
    margin-bottom: 10px;
}

.pd-chapter__list-root:first-child{
    margin-left: 15px;
}

.cvideo__bg {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 6px;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    display: none;
}

.cvideo__action {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
}

.cvideo__btn, .cvideo__btn:hover, .cvideo__btn:focus {
    color: var(--white-color);
    width: 40px;
    height: 40px;
    margin: 8px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.cvideo__btn .icon {
    font-size: 25px;
}

/* Tab header */
.tab-header {
    margin: 40px 0 24px 0;
}

.tab-header__heading {
    margin: 6px 0;
}

.tab-header__description {
    margin: 6px 0;
}

.pd-add-chapter__icon .icon {
    font-size: 32px;
    color: #a7a8a9;
}

.pd-add-chapter__icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(167, 168, 169, 0.1);
    border-radius: 50%;
    margin: 0 auto;
}

.pd-add-chapter__content {
    border: 1px dashed #f0f0f0;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    display: block;
    margin: 40px 24px 40px 24px;
}

.pd-chapter__line{
    margin-top: 33px !important;
    display: none;
}

.pd-chapter__list-root {
    padding-bottom: 0 !important;
    min-height: 180px;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
}

.pd-chapter__list-root .cvideo {
    padding: 8px;
    width: 200px;
}

.pd-add-chapter__text {
    margin-top: 16px;
    color: #53565a;
}

.tab-container {
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    background-color: #fff;
    margin-bottom: 24px;
}

.collapse-header {
    height: 64px;
    border-bottom: 1px solid #F0F0F0;
    position: relative;
}

.collapse-header .pd-chapter__toggle.active {
    transform: rotate(0deg);
}

.collapse-header .pd-chapter__toggle {
    font-size: 18px;
    color: #53565A;
    cursor: pointer;
    margin: 20px 24px 0 0;
    position: absolute;
    right: 0;
    transition: all .3s;
    transform: rotate(180deg);
}

.tab-section__header-root .pd-chapter__toggle.active {
    transform: rotate(0deg);
}

.tab-section__header-root .pd-chapter__toggle {
    margin-left: 20px;
    font-size: 18px;
    color: black;
    cursor: pointer;
    transform: rotate(180deg);
    margin: 20px 20px 0 0;
    position: absolute;
    right: 0;
    transition: all .3s;
}

.tab-section__header .pd-chapter__toggle:before {
    content: '\e922';
    font-family: 'soremoicons' !important;
    line-height: 1;
    font-size: 17px;
    color: black;
}

.modal-bookmark-delete .btn{
    width: 100px !important;
    height: 44px !important;
}

.modal-bookmark-delete .modal-title-top{
    font-size: 16px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 16px;
    color: black;
}

.modal-dialog .btn {
    font-size: 13px !important;
    padding: 10px 15px !important;
}

.modal-bookmark-delete .modal-title-down{
    font-size: 13px;
    text-align: left;
    color: black;
    margin-top: 15px;
}

.delete-modal__action {
    text-align: right;
    border-top: none;
    padding: 0 0 35px
}

.pd-chapter__add-text {
    font-size: 11px !important;
}
.pd-add-chapter__text {
    font-size: 13px;
}

.pd-chapter__list {
    padding-bottom: 0 !important;
    min-height: 180px;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    margin-left: 24px;
    margin-right: 24px;
}

.stars a {
    line-height: inherit;
}

/* Responsive */

@media (min-width: 992px) {
    #add-item-modal .modal-content, #add-item-modal .modal-dialog {
        width: 752px;
    }

    #add-item-modal .add-modal__heading {
        margin-bottom: 10px;
    }

    #create-new-modal .modal-content, #create-new-modal .modal-dialog  {
        border-radius: 12px;
        width: 558px;
        height: 248px;
    }

    #edit-list-name-modal .modal-content, #edit-list-name-modal .modal-dialog  {
        width: 558px;
        height: 248px;
    }

    #delete-list-bookmark .modal-content, #delete-list-bookmark .modal-dialog {
        width: 364px;
    }

    #unbookmark-scene .modal-body {
        width: 316px;
        margin: auto;
        margin-top: 10px;
    }

    .modal-bookmark-delete .modal-content, .modal-bookmark-delete .modal-dialog {
        width: 364px;
    }

}

@media (max-width: 992px) {
    .pd-chapter__content .cvideo, .collection-content__artist-item {
        flex: 0 0 160px;
    }

    .pd-chapter__content .cvideo {
        flex: 0 0 160px;
        padding: 0 8px;
    }

    video {
        width: 144px;
        height: 100px;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .tab-section__title {
        width: 200px;
    }

    .tab-section__title-active {
        width: 150px !important;
    }
    .modal {
        top: 0 !important;
    }
}

.list-new-works__title {
    display: flex;
    min-height: 22px;
    justify-content: space-between;
}

.list-new-works__title span {
    text-overflow: ellipsis;
    overflow: hidden;
}

.tab-section .list-new-works__title:hover span {
    max-width: calc(100% - 50px);
    text-overflow: ellipsis;
    overflow: hidden;
}

.list-new-works__title .scene-title__action {
    display: none;
    align-items: center;
    font-size: 12px;
    margin-left: 4px;
    transition: all .3s;
    flex-direction: row-reverse;
}

.list-new-works__title .scene-title__action.active {
    display: flex;
}

.list-new-works__title .scene-title__action.active-hover {
    display: flex;
}

.list-new-works__title .scene-title__action .scene-title-button{
    width: 22px;
    height: 22px;
    background-color: rgba(167, 168, 169, 0.2);
    border-radius: 50%;
    color: #a7a8a9;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.list-new-works__title .scene-title__action .scene-title__delete{
    margin-right: 6px;
}

.list-new-works__title .scene-title__action .scene-title__edit{
    margin: 0 7px;
}

.list-new-works__title .scene-title__action .scene-title-button:hover {
    background-color: #009ace;
    color: #fff;
}
