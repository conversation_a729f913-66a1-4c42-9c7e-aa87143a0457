#bg {
    background-color: var(--soremo-background);
}

body {
    visibility: hidden;
}


nav {
    height: 32px;
}

.widget {
    width: 100vw;
    height: calc(256px);
    background: var(--soremo-gray);
    color: #fff;

    font-size: 11px;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    gap: 16px;


    .logo {
        width: 80px;
        aspect-ratio: 1 / 1;
        background: url('/static/images/logo_soremo_white.svg') no-repeat center center / contain;
        color: #fff;

        margin-top: 64px;
    }

    .legal {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 16px;

        @media (width < 640px) {
            flex-direction: column;
            justify-content: flex-start;
        }
    }

    a {
        text-decoration: none;
        color: #fff;

        /* BodyText 11 設定 */
        font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
        font-weight: normal;
        font-size: 11px;
        line-height: 200%;
    }
}


.u-fullpage {
    height: 100dvh;
}

.u-relative {
    position: relative;
}

.tooltip {
    inset-area: right;

    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    /* background-color: var(--soremo-background); */
    padding: 4px 4px;

    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

}

.p-cards-os {
    width: 100%;
    padding: 32px 0px 0px;
    max-height: 80.9dvh;
    overflow-y: hidden;

    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
}

.p-cards-os2 {
    width: 100%;
    padding: 32px 0px 0px;
    height: 61.8dvh;
    overflow-y: hidden ;

    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    @media (width > 960px) {
        width: calc(100% / 2 - 16px / 2);
    }
}


.p-cards-os3 {
    width: 100%;
    padding: 32px 0px 0px;
    height: 38.2dvh;
    overflow-y: hidden ;

    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    background-color: #f0f0f0;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    @media (width > 960px) {
        width: calc(100% / 2 - 16px / 2);
    }
}


@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
  }
  
.animated-gradient-text {
    background: linear-gradient(to right, red, orange, yellow, green, blue, indigo, violet);
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
    animation: gradientAnimation 5s linear infinite;
}


.c-movie {
    width: min(100%, 1140px);
}

.c-movie:hover {
    cursor: pointer;
}

.c-counter-animated::before {
    padding:0 56px 0 0;
}

.c-counter-os {
    position:absolute;
    top: -4px;
    left: 8px;

    width: 48px;
    height: 48px;

    border-radius: 50%;
    border: 1px solid var(--soremo-border);

    display: flex;
    align-items: center;
    justify-content: center;

    background: var(--soremo-gray);
    color: var(--soremo-background);
}

.os-heroimage-container {
    height: 100dvh;

    background-image: url('../images/os/os_heroimage.png'); /* 画像のパスを指定 */
    background-size: auto 100%; /* 高さを要素の100%に設定し、幅は自動調整 */
    background-position: center center; /* 画像を中央に配置 */
    background-repeat: no-repeat; /* 画像の繰り返し無し */  

    overflow: hidden;
}

#navigate_top:hover {
    cursor: pointer;
    scale: 1.3;
    color: var(--soremo-blue);
    transition: 0.3s;
}

.c-hero-tagline {
    position:absolute;
    bottom: 32px;

    font-family: "din-2014-rounded-variable",sans-serif;
    font-variation-settings: 'wght' 200;

    /* font-family: "futura-pt", 'A+mfCv-AXISラウンド 50 L StdN','M PLUS 1p',sans-serif; */
    font-style: normal;

    font-size: clamp(4rem, 3.273rem + 3.64vw, 8rem);
    line-height: 100%;

    color: #fff;
}

.c-hero-message {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
    'M PLUS 1p',
    sans-serif;
    font-weight: normal;

    font-size: clamp(1.313rem, 0.849rem + 2.32vw, 2.5rem);
    line-height: 150%;
    letter-spacing: 0.75rem;
}

.os-offer-container {
    background-image: url('../images/os/os_offer.png'); /* 画像のパスを指定 */
    background-size: auto 100%; /* 高さを要素の100%に設定し、幅は自動調整 */
    background-position: center center; /* 画像を中央に配置 */
    background-repeat: no-repeat; /* 画像の繰り返し無し */  

    overflow: hidden;
}

.os-invite-container {
    background-image: url('../images/os/os_invite.png'); /* 画像のパスを指定 */
    background-size: auto 100%; /* 高さを要素の100%に設定し、幅は自動調整 */
    background-position: center bottom; /* 画像を中央に配置 */
    background-repeat: no-repeat; /* 画像の繰り返し無し */  

    overflow: hidden;
}


.fade-in {
    opacity: 0; /* 初期状態では透明 */
    transition: opacity 1s; /* 透明度の変化に1秒かける */
  }
