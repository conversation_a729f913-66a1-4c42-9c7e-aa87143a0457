/* ==========================================================================
   Foundation
   ========================================================================== */
@charset "utf-8";

/* ローカルのフォントを読み込む */
@font-face {
    font-family: "A+mfCv-AXISラウンド 50 L StdN";
    src: url('../fonts/AxisRound50StdN-L.otf') format('opentype');
    font-display: block;
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 R StdN";
    src: url('../fonts/AxisRound50StdN-R.otf') format('opentype');
    font-display: block;
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 M StdN";
    src: url('../fonts/AxisRound50StdN-M.otf') format('opentype');
    font-display: block;
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 B StdN";
    src: url('../fonts/AxisRound50StdN-B.otf') format('opentype');
    font-display: block;
}


/* Basic Font Setting */
/*                    */

/* material icon */
/*               */
.material-symbols-rounded {
    font-variation-settings:
        'FILL' 1,
        'wght' 600,
        'GRAD' 0,
        'opsz' 24;
    color: var(--soremo-light-gray);
}

/* heading-spacing */
.heading-spacing-2448 {
    font-family: 'A+mfCv-AXISラウンド 50 M StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: clamp(1.5rem, 0.915rem + 2.93vw, 3rem);
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-spacing-2140 {
    font-family: 'A+mfCv-AXISラウンド 50 M StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: clamp(1.313rem, 0.849rem + 2.32vw, 2.5rem);
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-spacing-1824 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: clamp(1.125rem, 0.75rem + 1.88vw, 1.5rem);
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-18-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 18px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-13-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 13px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 100%;
    letter-spacing: 2.5px;
}

/* heading (R) */
.heading-32 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 32px;
}

.heading-24 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size:24px;
}

.heading-13 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 13px;
}

h1,
h2,
h3,
h4,
h5,
h6,
label,
button,
.heading {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-feature-settings: 'clig' off, 'liga' off;

    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
}


/* bodytext (L) */
.bodytext-16 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 16px;
    line-height: 200%;
}

.bodytext-13 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 13px;
    line-height: 200%;
}

.bodytext-11 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 200%;
}

.label8 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
}

.bodytext-1113 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    /* 11px - 13px viewport 320px - 640px */
    font-size: clamp(0.688rem, 0.563rem + 0.63vw, 0.813rem);
    line-height: 200%;
}

p,
a,
li,
dt,
dd,
input,
textarea,
body,
.bodytext {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
    'M PLUS 1p',
    sans-serif;
    font-weight: normal;
    /* 13px - 16px viewport 320px - 640px */
    /* font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem); */
    /* BodyText 13 - 16 */
    font-size: 13px;
    line-height: 200%;
}

.bodytext-quote {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    /* font-size: clamp(0.5rem, -0.476rem + 4.88vw, 3rem); */
    /* font-size: clamp(0.5rem, 0.11rem + 1.95vw, 1.5rem); */
    font-size: clamp(0.5rem, -0.5rem + 5vw, 1.5rem);
    line-height: 200%;
    letter-spacing: 2.5px;
}

/* <hr> */
hr {
    /* 既存のボーダーを削除 */
    border: none;
    /* 線の高さ（太さ） */
    height: 1px;
    /* 線の色 */
    background-color: var(--soremo-border);
    /* 上下のマージン */
    margin: 8px 0;
}


/* Reset
   ----------------------------------------------------------------- */

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;

    /* Google Chrome のスワイプによる戻りを無効に */
    overscroll-behavior-x: none ! important;
}

/* リセット */
h1,
h2,
h3,
h4,
h5,
h6,
p {
    margin: 0;
    padding: 0;
}


a,
a:visited,
a:hover,
a:active {
    text-decoration: none;
    /* 親要素から色を継承します */
    color: inherit;
}

a:focus {
    outline: none;
}

ul,
ol,
li {
    list-style: none;
}

/* スクロールバーの調整 */
/* スクロールバー全体のスタイル設定 */
::-webkit-scrollbar {
    /* スクロールバーの幅 */
    width: 4px;
    height: 4px;
}

/* スクロールバーのトラック（背景部分）のスタイル設定 */
::-webkit-scrollbar-track {
    /* トラックの背景色 */
    background: var(--soremo-background);
}

/* スクロールバーのつまみのスタイル設定 */
::-webkit-scrollbar-thumb {
    /* つまみの背景色 */
    background: var(--soremo-placeholder);
    /* つまみの丸み */
    border-radius: 6px;
}

/* スクロールバーのつまみにホバーした時のスタイル設定 */
::-webkit-scrollbar-thumb:hover {
    /* ホバー時の背景色 */
    background: var(--soremo-light-gray);
}

/* モーダル　　　  */
/* モーダルの背景  */
dialog::backdrop {
    background: rgba(0, 0, 0, 0.05);
}

dialog {
    display: none;
}

dialog[open] {
    position: fixed;
    width: clamp(320px, 80vw, 756px);
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 32px 16px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    /* 画面の中央に垂直方向で配置 */
    top: 50%;
    /* 画面の中央に水平方向で配置 */
    left: 50%;
    /* モーダルの左上の角を中央から左上に移動 */
    transform: translate(-50%, -50%);

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;

    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}

/* PCでのリサイズハンドルの設定（p-left-column） */
.resize-handle {
    width: 4px;
    height: 100%;

    position: absolute;
    top: 0;
    right: 0;

    background: var(--soremo-border);
}


/* Base
   ----------------------------------------------------------------- */
:root {
    --soremo-blue: #009ace;
    --soremo-deep-blue: #0076A5;
    --soremo-light-gray: #a7a8a9;
    --soremo-gray: #53565A;
    
    --soremo-border: #f0f0f0;
    --soremo-background: #fcfcfc;
    --soremo-green: #2CC84D;

    --soremo-placeholder: #d3d3d3;

    /* --soremo-bg-white: radial-gradient(50% 50% at 50% 50%, #FFF 0%, #FCFCFC 100%); */
    --soremo-bg-white: radial-gradient(50% 50% at 50% 50%, rgba(252, 252, 252, 0.05) 0%, rgba(240, 240, 240, 0.05) 100%);
    --soremo-bg-blue: radial-gradient(50% 50% at 50% 50%, rgba(252, 252, 252, 0.05) 0%, rgba(0, 154, 206, 0.05) 100%);
}


html {
    font-size: 16px;
}

/* ==========================================================================
   Layout
   ========================================================================== */
/* #bg {
    background: linear-gradient(rgba(253, 253, 253, 0.5), rgba(247, 247, 246, 0.5), rgba(242, 242, 239, 0.5), rgba(236, 236, 232, 0.5), rgba(231, 231, 225, 0.5));
    z-index: -999;
} */

body {
    overflow-x: hidden;
}

/* Header
   ----------------------------------------------------------------- */

/* グローバルヘッダー */
#grobal-header {
    width: 100%;
    height: 64px;

    position: sticky;
    top: -139px;
    transition: top 0.2s;

    background-color: var(--soremo-background);
    border-bottom: 1px solid var(--soremo-border);

    z-index: 6;
}

#grobal-header ul {
    padding: 16px 16px;

    /* flex親設定 */
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    gap: 48px;

    color: var(--soremo-light-gray);
}

/* プロジェクトバナー */
#project-banner {
    width: 100%;
    height: 75px;
    padding: 0px 16px 0px 16px;

    position: sticky;
    top: -75px;
    transition: top 0.2s;

    z-index: 4;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;

}

#top-app-bar {
    width: 100%;
    height: 40px;

    position: sticky;
    top: 0px;
    transition: top 0.2s;

    z-index: 3;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 8px;
}

/* top-app-barの子要素が一つの場合は、中央添え */

#top-app-bar:has(>:only-child) {
    justify-content: center;
}

/* top-app-bar内のスタイル設定 */
/*  */
#left-sidebar-open:hover,
#left-sidebar-close:hover,
#right-sidebar-open:hover,
#right-sidebar-close:hover {
    cursor: pointer;
    color: var(--soremo-blue);
    scale: 1.2;
}

.left-sidebar {
    width: min(240px, 100vw - 48px);
    height: calc(100dvh - 75px);
    overflow-y: auto;

    background-color: var(--soremo-background);
    border-right: 1px solid var(--soremo-border);

    position: absolute;
    top: 0;
    left: 0;

    z-index: 3;
    padding: 8px 16px 80px 16px;

    /* flex親設定（上下で分割） */
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
}

.left-sidebar div {
    width: 100%;
}

.left-sidebar hr {
    width: 100%;
    /* 既存のボーダーを削除 */
    border: none;
    /* 線の高さ（太さ） */
    height: 1px;
    /* 線の色 */
    background-color: var(--soremo-border);
    /* 上下のマージン */
    margin: 8px 0;
}

.left-sidebar ul {
    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    /* gap: 8px; */

    width: 100%;
    padding: 8px 0;
    list-style: none;

    font-size: 13px;
}

.left-sidebar .sidebar-label {
    font-size: 11px;
    color: var(--soremo-light-gray);
}

.left-sidebar ul>a {
    width: 100%;
}

.left-sidebar ul li {
    width: 100%;
    padding: 8px 8px;

    display: flex;
    align-items: center;

    span {
        padding-right: 4px;
    }
}

.left-sidebar ul li:hover,
.left-sidebar ul a:hover {
    cursor: pointer;
    background-color: var(--soremo-border);
    border-radius: 6px;
    /* color: #fff; */
}

@media (width >=768px) {
    .left-sidebar ul {
        font-size: 16px;
    }

    .left-sidebar .sidebar-label {
        font-size: 13px;
    }
}


.right-sidebar {
    width: clamp(320px, 23.6vw, 1140px * 0.236);
    height: calc(100dvh - 75px);
    overflow-y: auto;
    overflow-x: hidden;

    background-color: var(--soremo-background);
    border-left: 1px solid var(--soremo-border);

    position: absolute;
    top: 0;
    right: 0;

    z-index: 3;
    padding: 8px 16px 32px 8px;

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 16px;
}







/* DMの設定 */

.c-message-container-their {
    padding: 0px 0px 8px 0px;

    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 4px;
}

.c-message-container-ours {
    padding: 0px 0px 8px 0px;

    flex-grow: 1;

    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    gap: 4px;
}

.c-message-system {
    padding: 8px 0px 8px 0px;

    display: flex;
    align-items: center;
    justify-content: center;

    color: var(--soremo-blue);

    border-top: 1px solid var(--soremo-blue);
    border-bottom: 1px solid var(--soremo-blue);
}

.c-message-their {
    padding: 8px 8px;

    border-radius: 6px;
    background: var(--soremo-bg-white);
    border: 1px solid var(--soremo-border);
}


.c-message-ours {
    padding: 8px 8px;

    border-radius: 6px;
    background: var(--soremo-bg-blue);
    border: 1px solid rgba(0, 154, 206, 0.1);

    color: black;
}

.c-tag {
    padding: 4px 8px;
    border-radius: 4px;
    background-color: var(--soremo-bg-white);
    border: 1px solid var(--soremo-border);
}


/* アクションパネルの設定  */
#action-panel {
    padding: 0px 0px 0px;

    position: fixed;
    bottom: 80px;
    transition: bottom 0.2s;

}

#action-panel .p-right-column {

    border-radius: 6px 6px 6px 6px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);
}

.approval-actions {
    width: 100%;
    padding: 8px 8px 0px 8px;
}

.comment-field {
    width: 100%;
    padding: 8px 8px 8px 8px;

    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    position: relative;
}

.comment-field textarea {
    padding-left: 40px;
    padding-right: 40px;
}



/* Main
   ----------------------------------------------------------------- */
main {
    /* width: min(100% - 32px); */
    width: 100%;
    margin-inline: auto;

    /* padding-bottom: 88px; */
    transition: all 0.2s;
}

#project-setting {
    min-height: calc(100dvh - 75px - 40px);
}

#dm {
    min-height: calc(100dvh - 75px - 40px);
}


/* Footer
   ----------------------------------------------------------------- */
footer {
    width: 100%;
    margin-inline: auto;
}



/* bottom-app-bar内の設定 */
/*  */
#bottom-app-bar {
    height: 80px;
    padding: 16px 16px 0px;

    position: fixed;
    bottom: 0;
    transition: bottom 0.2s;

    /* サイドバーより上に設定 */
    z-index: 4;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

}

/* todo-list
   ----------------------------------------------------------------- */

.todo-list {
    background: linear-gradient(rgb(247, 246, 247), rgb(232, 239, 239), rgb(217, 232, 231), rgb(201, 224, 224), rgb(186, 217, 216));
}


.todo-list-container {
    width: min(100% - 32px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;


    @media (width > 640px) {

        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: space-between;

        .c-thumbnail-video {
            width: 100%;
        }
    }
}



/* ==========================================================================
   Object
   ========================================================================== */

/* Component
   ----------------------------------------------------------------- */
input[type="text"],
input[type="tel"],
input[type="email"],
input[type="date"],
input[type="number"],
input[type="search"],
textarea {
    /* テキストボックスの幅 */
    width: 100%;
    /* 内側の余白 */
    padding: 12px 4px 12px 12px;
    /* 外側の余白 */
    margin: 0;
    /* ボーダーのスタイル */
    border: 1px solid var(--soremo-border);
    /* 角の丸み */
    border-radius: 4px;
}

input[type="tel"] {
    width: 144px;
}

input[type="date"] {
    /* 元のカレンダーアイコンを非表示にする */
    -webkit-appearance: none;
    appearance: none;
    /* パディングを追加してアイコンとテキストが重ならないようにする */
    padding-right: 24px;
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
}

input[type="number"] {
    /* 数値のみ入力可能 */
    -moz-appearance: textfield;
    appearance: textfield;
    /* 右寄せにする */
    text-align: right;
}

/* スピンボタンの非表示 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="search"] {
    padding-left: 36px;
}

input[type="search"]::-webkit-search-cancel-button {
    display: none;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
textarea:focus {
    /* フォーカス時のボーダーカラー */
    border-color: var(--soremo-blue);
    /* デフォルトのアウトラインを削除 */
    outline: none;
    /* ボックスシャドウ */
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

/* オートフィルされたフォームの背景色を変更  */
input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px var(--soremo-background) inset !important;
}

textarea {
    resize: none;
}

/* textarea::-webkit-scrollbar {
    display: none;
} */

/* プレイスホルダーの色 */
input::placeholder,
textarea::placeholder {
    color: var(--soremo-placeholder);
    /* ここに希望の色を指定 */
}

/* フォームの設定 */
form {
    width: 100%;
}

/* レンジの設定 */
input[type="range"] {
    /* Chrome、Safari、Edge、Opera用 */
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    /* トラックの高さ */
    height: 12px;
    /* トラックの背景色を透明に */
    background: transparent;
    /* フォーカス時のアウトラインを削除 */
    outline: none;
}

input[type="range"]::-webkit-slider-runnable-track {
    background: var(--soremo-border);
    /* トラックの角の丸み */
    border-radius: 6px;
    width: 100%;
    /* トラックの高さ */
    height: 12px;
}

input[type="range"]::-webkit-slider-thumb {
    /* Chrome、Safari用 */
    -webkit-appearance: none;
    appearance: none;
    /* サムの幅 */
    width: 12px;
    /* サムの高さ */
    height: 12px;
    background: var(--soremo-light-gray);
    cursor: pointer;
    /* サムを円形に */
    border-radius: 50%;
    /* トラック中央に配置するための調整 */
    margin-top: 0px;
}

input[type="range"]:hover::-webkit-slider-thumb {
    /* ホバー時のサムの色 */
    background: var(--soremo-blue);
}



/* プログレスバー */
/* 全体のスタイル */
progress {
    /* プログレスバーの幅 */
    width: 100%;
    /* プログレスバーの高さ */
    height: 6px;

    -webkit-appearance: none;
    appearance: none;
}

/* プログレスバーの未完了部分のスタイル */
progress::-webkit-progress-bar {
    /* 背景色 */
    background-color: var(--soremo-border);
    /* 角の丸み */
    border-radius: 6px;
    /* 内側の影 */
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25) inset; */
}

/* プログレスバーの完了部分のスタイル */
progress::-webkit-progress-value {
    /* バーの色 */
    background-color: var(--soremo-blue);
    /* 角の丸み */
    border-radius: 6px;
    /* 影 */
    box-shadow: 2px 5px 8px 0px rgba(0, 154, 206, 0.10);
}

.progressbar {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    width: min(100% - 16px, 640px);
}

.progressbar span {
    font-size: 11px;
    color: var(--soremo-light-gray);
    width: 42px;
    text-align: right;
}


/* ドロップダウンメニュー */
/*                    */
.c-dropdown {
    display: none;

    position: absolute;
    width: clamp(192px, 61.8vw, 256px);
    padding: 0px 8px;
    border-radius: 6px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;
    color: #000;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    top: 24px;
    right: 0;

    z-index: calc(infinity);
}

.c-dropdown li {
    padding: 8px 0px;
    border-bottom: 1px solid #f0f0f0;
}

.c-dropdown li:last-child {
    border-bottom: none;
}

.c-dropdown li:hover {
    cursor: pointer;
    background-color: #fcfcfc;
}


/* フォーム内のアイコン */
/*            */
.c-icon-search {
    color: var(--soremo-light-gray);
    position: absolute;
    left: 16px;
    top: 24px;
}

.c-icon-close-small {
    color: var(--soremo-light-gray);
    position: absolute;
    right: 12px;
    top: 18px;

    display: none;
    transition: 0.2s;
}

.c-icon-close-small:hover {
    cursor: pointer;
    color: var(--soremo-blue);
    scale: 1.3;
}

.c-icon-attach {
    color: var(--soremo-light-gray);
    position: absolute;
    left: 16px;
    bottom: 26px;
    transition: 0.2s;
}

.c-icon-attach-safari {
    bottom: 34px;
}

.c-icon-attach:hover {
    cursor: pointer;
    color: var(--soremo-blue);
    scale: 1.3;
}

.c-icon-send {
    color: var(--soremo-border);

    font-size: 32px;
    position: absolute;
    right: 16px;
    bottom: 22px;

    transition: 0.2s;
}

.c-icon-send-safari {
    bottom: 29px;
}

.c-icon-send.is-icon-active:hover {
    cursor: pointer;
    color: var(--soremo-deep-blue);

    scale: 1.2;
}

.c-icon-date-range,
.c-icon-event {
    color: var(--soremo-light-gray);
    position: absolute;
    right: 8px;
    top: 16px;

    transition: 0.2s;
}

.c-icon-date-range:hover,
.c-icon-event:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    scale: 1.3;
}

.c-icon-play-audio,
.c-icon-pause-audio {
    color: var(--soremo-light-gray);
    transition: 0.2s;
}

.s-audio-control .c-icon-play-audio:not(.u-fontsize-32),
.s-audio-control .c-icon-pause-audio:not(.u-fontsize-32) {
    font-size: 64px;
    color: var(--soremo-light-gray);
    transition: 0.2s;
}

.audio-control-custom a span {
    color: var(--soremo-light-gray);
}

.c-icon-play-audio:hover,
.c-icon-pause-audio:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    scale: 1.2;
}

.c-icon-more-horiz {
    transition: 0.2s;
}

.c-icon-more-horiz:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    scale: 1.2;
}

.c-icon-download {
    transition: 0.2s;
}

.c-icon-download:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    scale: 1.2;
}

.c-icon-quote {
    color: var(--soremo-light-gray);

    /* font-size: clamp(3rem, 1.439rem + 7.8vw, 7rem); */
    /* font-size: clamp(1rem, -1rem + 10vw, 3rem); */
    font-size: clamp(0.5rem, -0.5rem + 5vw, 1.5rem);
    line-height: 100%;
}




/* .list-input-select {
    padding: 8px 12px;
} */

.list-input-select:hover {
    cursor: pointer;
    background-color: var(--soremo-background);
}


/* ボタンの設定 */
/*            */

.c-btn-contract {
    width: 100%;
    padding: 16px 8px;
    color: white;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 6px;

    transition: 0.2s;
}

.c-btn-contract-done {
    width: 100%;
    padding: 16px 8px;
    color: var(--soremo-blue);
    background: #fff;
    border: 1px solid var(--soremo-blue);
    border-radius: 6px;

    transition: 0.2s;
}


.c-btn-primary,
.c-btn-primary:active {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    border-radius: 4px;

    transition: 0.2s;

    @media (width < 756px) {
        min-width: 144px;
    }
}

.c-btn-primary:hover,
.c-btn-primary:focus,
.c-btn-contract:hover,
.c-btn-contract:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);

    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.25);

    cursor: pointer;
    /* scale: 1.005; */
}

.c-btn-primary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}

.c-btn-secondary {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;

    transition: 0.2s;
}

.c-btn-secondary:hover,
.c-btn-secondary:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.25);
    cursor: pointer;
}

.c-btn-secondary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}


.c-btn-tertiary {
    min-width: 128px;
    padding: 8px 24px;
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    transition: 0.2s;
}

.c-btn-tertiary:hover {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

.c-btn-tertiary:focus {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    outline: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}


.c-btn-modal {
    padding: 8px 12px;
    min-width: 128px;
}

.c-btn-text {
    padding: 8px 0px;
    color: var(--soremo-light-gray);
}

.c-btn-text-white {
    padding: 8px 0px;
    color: #fff;
    border-radius: 4px;

    .material-symbols-rounded {
        color: white;
    }
}

.c-btn-text:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

.c-btn-text-white:hover {
    cursor: pointer;
    background: rgba(255, 255, 255, 0.1);

    .material-symbols-rounded {
        color: white;
    }
}


.c-btn-small-primary {
    padding: 4px 12px;
    color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;
    transition: 0.2s;
}

.c-btn-small-primary:hover {
    cursor: pointer;
    color: #fff;
    background: var(--soremo-blue);
    transition: 0.2s;
}



/* セグメントコントロール */
.c-segment-control {
    width: clamp(320px - 24px, 100% - 80px, 640px);
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    color: var(--soremo-light-gray)
}

.c-segment-control li,
.c-segment-control label {
    width: 50%;
    text-align: center;
    border-radius: 6px;
    padding: 4px 0;
    margin: 1px 1px;

    line-height: 100%;
    transition: 0.2s;
}

.c-segment-control li:hover,
.c-segment-control label:hover {
    cursor: pointer;
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-segment-control label:has(input:checked) {
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-segment-control input[type="radio"] {
    display: none;
}


.c-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px 6px 0px 0px;


    list-style: none;
    font-size: 13px;
    color: var(--soremo-light-gray);
    transition: 0.2s;
}

.c-tabs li,
.c-tabs label {
    width: 50%;
    text-align: center;
    border-radius: 6px 6px 0px 0px;
    padding: 1px 0;
    margin: 1px 1px;
}

.c-tabs li:hover,
.c-tabs label:hover {
    cursor: pointer;
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-tabs input:checked+label {
    background-color: var(--soremo-light-gray);
    color: #fff;
}


/* c-radio ラジオボタンのコンポーネント */
[class^="c-radio"] input[type="radio"] {
    display: none;
}

[class^="c-radio"]:hover {
    border: 1px solid var(--soremo-blue);

    background-color: #FFF;

    /* card drop shadow blue */
    box-shadow: 2px 4px 8px 0px rgba(0, 154, 206, 0.05);
}

[class^="c-radio"]:hover:has(input[type="radio"]:checked) {
    border: 1px solid var(--soremo-deep-blue);

    background-color: #FFF;

    /* card drop shadow blue */
    box-shadow: 2px 4px 8px 0px rgba(0, 154, 206, 0.05);
}

[class^="c-radio"]:has(input[type="radio"]:checked) {

    border: 1px solid var(--soremo-blue);

    background-color: #FFF;

    /* card drop shadow blue */
    box-shadow: 2px 4px 8px 0px rgba(0, 154, 206, 0.05);
}


.c-radio-icon {
    width: 100%;
    padding: 16px 12px 16px;

    border-radius: 6px;
    border: 1px solid var(--soremo-border);
    background-color: var(--soremo-background);

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    transition: 0.2s;
}


/* add-blockの設定 */
.c-add-block {
    width: 100%;
    height: 64px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0px;

    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    background-color: #FFF;

    color: var(--soremo-light-gray);

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    transition: 0.2s;

    span {
        font-size: 32px;
    }

    a {
        font-size: 8px;
    }

}

.c-add-block:hover {
    cursor: pointer;
    color: #fff;
    background-color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);

    .material-symbols-rounded {
        color: #fff;
    }
}


/* グループ設定 */

.c-group {
    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    background: rgba(255, 255, 255, 0.5);
    padding: 16px 8px 16px 8px;
}

/* 引用 */

.c-quote {
    border-radius: 4px;
    background: #ffffff;
    padding: 16px 16px 24px 16px;
    border: 1px solid var(--soremo-border);
}

.c-quote-line {
    position: relative;
}

.c-quote-line::before {
    content: "";
    position: absolute;
    left: -32px;
    top: 50%;
    transform: translateX(-50%);
    width: 40px;
    /* 引用線の幅 */
    height: 1px;
    /* 引用線の高さ */
    background-color: #000;
    /* 引用線の色 */
}


/* キーワード */
.c-keyword {
    margin: 4px;
    padding: 4px 4px;
    /* border: 1px solid #fcfcfc; */
    border-radius: 4px;

    color: #a7a8a9;
    user-select: none;
}

.c-keyword:hover {
    cursor: pointer;
    outline: 1px solid var(--soremo-light-gray);
    color: #FFF;
    background-color: var(--soremo-light-gray);
}

/* 画像をアップロード */
.c-upload-image {
    width: 100%;
    aspect-ratio: 5/1;
    margin-inline: auto;
    background-image: url('images/banner-image.jpg');
    background-size: cover;
    background-position: left center;

    display: flex;
    align-items: center;
    justify-content: center;

    .material-symbols-rounded {
        font-size: 128px;
        color: var(--soremo-light-gray);
        mix-blend-mode: screen;
    }
}

.c-upload-image:hover {
    cursor: pointer;
    filter: brightness(80%);

    .material-symbols-rounded {
        font-size: 128px;
        color: var(--soremo-light-gray);
        mix-blend-mode: screen;
    }
}

.c-icon-navigate-before {
    color: var(--soremo-blue);

    transition: 0.2s;

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: flex-start;

    @media (width > 640px) {
        visibility: hidden;
    }
}

.c-icon-navigate-before:hover {
    cursor: pointer;
    transform: scale(1.1);

    color: var(--soremo-deep-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

.c-status-label {
    width: 4px;
    height: 100%;

    position: absolute;
    left: 0px;
    top: 0px;
}


.c-budge {
    width: 16px;
    height: 16px;

    background-color: var(--soremo-blue);
    color: #FFF;
    padding: 2px 0;
    border-radius: 50%;
    border: 1px solid #fff;

    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 8px;
    line-height: 100%;
}

[class^="c-take-budge"] {
    width: 18px;
    height: 18px;

    background-color: var(--soremo-blue);
    color: #FFF;
    border-radius: 8px;

    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;

    font-size: 8px;
    line-height: 100%;
}

.c-take-budge1 {
    background-color: #4ABDAC;
}

.c-take-budge2 {
    background-color: #F7B733;
}

.c-take-budge3 {
    background-color: #FC4A1A;
}

.all-medias {
    width: min(100% - 32px);
    margin-inline: auto;
}


.all-medias-container {
    flex-wrap: wrap;
    overflow-x: auto;
    overflow-y: hidden;

    padding: 32px 0 2px 0;

    gap: clamp(0.75rem, 0.5rem + 1.25vw, 1rem);

    & [class^="c-thumbnail-video"],
    [class^="c-thumbnail-image"] {
        height: 192px;
    }

    @media (width > 640px) {

        & [class^="c-thumbnail-video"],
        [class^="c-thumbnail-image"] {
            height: 256px;
        }
    }

}

.pending-response {
    background: linear-gradient(rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));
}

.pending-response-container {
    width: min(100% - 32px);
    margin-inline: auto;
    flex-wrap: wrap;
    gap: 6px;
    padding: 8px 0 16px;

    & [class^="c-thumbnail-video"]~div,
    [class^="c-thumbnail-image"]~div {
        display: none;
    }

    & [class^="c-thumbnail-video"],
    [class^="c-thumbnail-image"] {
        height: 64px;
    }

    @media (width > 640px) {
        gap: 8px 12px;

        & [class^="c-thumbnail-video"],
        [class^="c-thumbnail-image"] {
            height: 96px;
        }
    }
}


[class^="c-thumbnail-video"] {

    border-radius: 6px;
    background-color: var(--soremo-background);

    transition: 0.2s;

}

@media (width > 640px) {
    [class^="c-thumbnail-video"]:hover {
        scale: 1.05;
    }
}


[class^="c-thumbnail-image"] {
    border-radius: 6px;
    background-color: var(--soremo-background);

    background-size: cover;
    background-position: center;

}



.c-thumbnail-image1 {
    background-image: url('medias/image1.jpg');
}

.c-thumbnail-image2 {
    background-image: url('medias/image2.jpg');
}

.c-thumbnail-image3 {
    background-image: url('medias/image3.jpg');
}

.c-thumbnail-image4 {
    background-image: url('medias/image4.jpg');
}

.c-thumbnail-image5 {
    background-image: url('medias/image5.jpg');
}

.c-thumbnail-image6 {
    background-image: url('medias/image6.jpg');
}

.c-thumbnail-image7 {
    background-image: url('medias/image7.jpg');
}

.c-thumbnail-image8 {
    background-image: url('medias/image8.jpg');
}

.c-thumbnail-image9 {
    background-image: url('medias/image9.jpg');
}



.project-avatars {
    border-radius: 11px;
    border: 1px solid var(--soremo-border);
    padding: 4px calc(4px + 4px) 4px 4px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.project-avatars:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

[class^="c-avatar"] {
    background-color: var(--soremo-background);
    border: 1px solid #fff;

    box-shadow: 2px 4px 10px 0px #E5E5E5;

    background-size: cover;
    background-position: center center;
    overflow: hidden;
    flex: 0 0 auto;
}

.c-avatar48 {
    width: 48px;
    height: 48px;

    border-radius: 18px;
}

.c-avatar40 {
    width: 40px;
    height: 40px;

    border-radius: 16px;
}

.c-avatar32 {
    width: 32px;
    height: 32px;

    border-radius: 13px;
}

.c-avatar24 {
    width: 24px;
    height: 24px;

    border-radius: 10px;
}

.c-avatar16 {
    width: 16px;
    height: 16px;
    margin-right: -4px;

    border-radius: 6px;
}

.c-avatar-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 1px solid #fff;

    position: absolute;
    right: 0px;
    bottom: 0px;
    background: #2CC84D;
}


.c-avatar-producer {
    background-image: url('images/avatar-producer.png');
    background-size: cover;
}

.c-avatar-director1 {
    background-image: url('images/avatar-director1.png');
    background-size: cover;
}

.c-avatar-composer {
    background-image: url('images/avatar-musician-composer.png');
    background-size: cover;
}

.c-avatar-lyricist {
    background-image: url('images/avatar-musician-lyricist.png');
    background-size: cover;
}

.c-avatar-vocalist {
    background-image: url('images/avatar-musician-vocalist.png');
    background-size: cover;
}

.c-avatar-guitarist {
    background-image: url('images/avatar-musician-guitar.png');
    background-size: cover;
}

.c-avatar-drummer {
    background-image: url('images/avatar-musician-drum.png');
    background-size: cover;
}

.c-thumbnail-small {
    width: 42px;
    aspect-ratio: 16/9;

    background: var(--soremo-border);
    border-radius: 4px;

    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}



.c-vertical-line {
    width: 3px;
    height: 100%;
    background-color: #fff;
    margin: 0 4px;
}

.c-icon-btn {
    width: 128px;
    color: var(--soremo-light-gray);

    transition: 0.2s;

    .material-symbols-rounded {
        color: var(--soremo-light-gray);
    }
}

.c-icon-btn:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}


/**
 * Media
 */
/* Project
   ----------------------------------------------------------------- */

.p-left-column {
    width: 100%;
    position: relative;

    @media (width > 640px) {
        width: clamp(320px, 38.2vw, 1140px * 0.382);
        flex: 0 0 auto;
    }
}

.p-right-column {
    margin-inline: auto;
}

/* プロジェクトメンバーモーダル */
/* 　　　　　　　　　　　　　　 */
.p-project-members {
    width: clamp(375px - 64px, 100vw - 64px, 1140px * 0.382);
    max-height: calc(100vh - 80px);

    position: absolute;
    top: -24px;
    right: 28px;
    transition: top 0.2s;

    border-radius: 0 0 12px 12px;
    background-color: var(--soremo-background);
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    z-index: 5;
    padding: 64px 8px 32px;
}

.p-project-members-mask,
.p-schedule-mask {
    width: 100vw;
    height: 100dvh;

    position: fixed;
    inset: 0;

    /* background: rgba(0, 0, 0, 0.05); */
}







/* スケジュールモーダル */
/* 　　　　　　　　　　 */
.p-schedule {
    width: clamp(332px, 19.1vw, 640px);
    max-height: calc(100dvh);

    position: absolute;
    top: -16px;
    right: -8px;
    transition: top 0.2s;

    border-radius: 12px 12px 12px 12px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.84);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    z-index: 5;
    padding: 16px 8px 32px;

    display: flex;
    flex-direction: column;
    align-items: center;

}

#p-schedule-open {
    color: #FFF;
}

#p-schedule-open:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}

#p-schedule-close {
    color: var(--soremo-light-gray);
}

#p-schedule-close:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}

.p-lists-schedule {
    width: 100%;
    height: 32px;
    padding: 2px 0px;

    border-radius: 4px;
}

.p-lists-task {
    width: 100%;
    height: 32px;
    padding: 0px 0px 0px 18.5px;

    border-radius: 4px;

    display: flex;
    align-items: center;
    justify-content: space-between;
}

.p-lists-user {
    width: clamp(240px, 100%, 1140px);
    padding: 8px 8px 8px 8px;

    border-radius: 4px;
    background-color: #fff;
}


/* PL画面 */
/* 　     */
.financial-summary-header-container {
    width: 100%;

    display: flex;
    flex-direction: column;
    gap: 8px;
}

.financial-summary-header {
    width: 100%;
    padding: 12px 24px;

    border-radius: 4px;
    background-color: var(--soremo-blue);
    color: #fff;

    p {
        font-size: 13px;
        line-height: 200%;
    }

    h2 {
        font-size: 32px;
    }

    h2>span {
        font-size: 11px;
    }
}

.transaction-list-container {
    width: 100%;

}

.transaction-list-header {
    width: 100%;
    padding-left: 36px;
    padding-right: 32px;

    display: flex;
    align-items: center;
    justify-content: space-between;

    font-size: 11px;
    color: var(--soremo-light-gray);
}

.transaction-list {
    width: 100%;
    padding: 8px 0;

    display: flex;
    align-items: center;
    justify-content: space-between;
}

.transaction-list .artist-name {
    display: flex;
    align-items: center;
    gap: 8px;
}

.transaction-list li .financial-ratio {
    padding-left: 16px;
    text-align: right;
}


/* プロジェクト設定画面 */

.p-lists-headline {
    width: 100%;
    border-bottom: 1px solid var(--soremo-border);

    transition: 0.1s;
}

.p-lists-headline label {
    width: 100%;
    height: 100%;

    padding: 8px 0px 8px 8px;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;
}


[class^="p-lists"] input[type="radio"] {
    display: none;
}

/* [class^="p-lists"] span.material-symbols-rounded { */
    /* visibility: hidden; */
    /* color: var(--soremo-border); */
/* } */

[class^="p-lists"]:hover {
    cursor: pointer;
    background-color: var(--soremo-background);

    /* span.material-symbols-rounded { */
        /* visibility: visible; */
        /* color: var(--soremo-light-gray); */
    /* } */
}

[class^="p-lists"]:has(input[type="radio"]:checked+label) {
    background-color: var(--soremo-border);

    span.material-symbols-rounded {
        visibility: visible;
        color: var(--soremo-light-gray);
    }
}


.p-right-column img {
    display: block;
    margin-inline: auto;
}

.p-right-column .indicator-icon {
    color: var(--soremo-light-gray);
    text-align: center;

    .material-symbols-rounded {
        font-size: 128px;
    }
}


/* バナーの設定 */

.c-tab1 {
    width: 100%;
    padding: 16px 8px 24px;
    border-left: 1px solid var(--soremo-border);
    border-right: 1px solid var(--soremo-border);
    border-bottom: 1px solid var(--soremo-border);
    border-radius: 0px 0px 6px 6px;
}

.c-tab2 {
    width: 100%;
    border-left: 1px solid var(--soremo-border);
    border-right: 1px solid var(--soremo-border);
    border-bottom: 1px solid var(--soremo-border);
    border-radius: 0px 0px 6px 6px;
    overflow: hidden;
}


.p-option-font {
    width: 100%;
    height: 75px;

    padding: 8px 16px;
    margin-bottom: 8px;

    border-radius: 4px;
    border: 1px solid var(--soremo-border);

    transition: 0.2s;
}

.p-option-font:hover {
    cursor: pointer;
    border: 2px solid var(--soremo-blue);
    /* outline: 2px solid var(--soremo-blue); */

}

/* 選択された li 要素を強調表示 */
.p-option-font:has(input[type="radio"]:checked) {
    border: 2px solid var(--soremo-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

[class^="code-font"] {
    width: 100%;
    line-height: 100%;

    /* テキストを改行させません */
    white-space: nowrap;
    /* はみ出したテキストを...で表現します */
    text-overflow: ellipsis;
}

.code-font1 {
    font-family: "corporate-logo-ver2", sans-serif;
    font-weight: 700;
    font-style: normal;

    /* font-size: 30px; */
    /* font-size: 38px; */
    font-size: clamp(1.875rem, 1.375rem + 2.5vw, 2.375rem);

    white-space: nowrap;
}

.code-font2 {

    font-family: "dnp-shuei-mincho-pr6n", sans-serif;
    font-weight: 400;
    font-style: normal;

    /* font-size: 24px; */
    /* font-size: 36px; */
    font-size: clamp(1.5rem, 0.75rem + 3.75vw, 2.25rem);

    white-space: nowrap;
}

.code-font3 {
    font-family: "din-2014", sans-serif;
    font-weight: 800;
    font-style: normal;

    /* font-size: 40px; */
    /* font-size: 44px; */

    font-size: clamp(2.5rem, 2.25rem + 1.25vw, 2.75rem);
    text-transform: uppercase;
}

.code-font4 {
    font-family: "futura-pt", sans-serif;
    font-weight: 300;
    font-style: italic;

    /* font-size: 38px; */
    /* font-size: 42px; */
    font-size: clamp(2.375rem, 2.125rem + 1.25vw, 2.625rem);
}

.code-font5 {
    font-family: "trajan-pro-3", serif;
    font-weight: 300;
    font-style: normal;

    /* font-size: 32px; */
    /* font-size: 34px; */
    font-size: clamp(2rem, 1.875rem + 0.63vw, 2.125rem);

    text-transform: uppercase;
}

.code-font6 {
    font-family: "lindsey-signature", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 36px;
}



.color-container {
    width: 100%;
    padding: 16px 16px;

    overflow-y: auto;
    scroll-snap-align: start;

}

.color-container [class^="banner-color"] {
    width: 32px;
    height: 32px;
    border-radius: 50%;

    flex-shrink: 0;

    filter: drop-shadow(2px 5px 8px rgba(0, 154, 206, 0.10));

    transition: transform 0.2s ease-in-out;

}

.color-container [class^="banner-color"]:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-blue);

    transform: scale(1.3);
}

/* 選択された li 要素を強調表示 */
[class^="banner-color"]:has(input[type="radio"]:checked) {
    border: 1px solid var(--soremo-blue);
}

.banner-color1 {
    background: var(--soremo-gray);

    >div:first-child {
        color: white;
    }
}

.banner-color2 {
    background: #3B5375;

    >div:first-child {
        color: white;
    }
}

.banner-color3 {
    background: linear-gradient(to right, #3b5375, #33578b, #2a5aa1, #255db6, #2a5ecb);

    >div:first-child {
        color: white;
    }
}

.banner-color4 {
    background: linear-gradient(to left, #6fa8ed, #5a96e6, #4783de, #3771d5, #2a5ecb);

    >div:first-child {
        color: white;
    }
}

.banner-color5 {
    background: #6EB370;
    background: linear-gradient(135deg, #6eb370, #26b89b, #00b8c5, #00b2e3, #6fa8ed);

    >div:first-child {
        color: white;
    }
}

.banner-color6 {
    background-color: var(--soremo-background);
    background: linear-gradient(to right, #6eb370, #94be72, #b8c979, #dad384, #fadd94);
    ;

    >div:first-child {
        color: white;
    }
}

.banner-color7 {
    background: linear-gradient(to right, #ffa254, #ffac83, #ffbbac, #ffcecf, #ffe3e8);

    >div:first-child {
        color: white;
    }
}

.banner-color8 {
    /* background: #EA6586; */
    background: linear-gradient(135deg, #ea6586, #f2879e, #f8a6b6, #fdc5cf, #ffe3e8);

    >div:first-child {
        color: white;
    }
}

.banner-color9 {
    /* background: #C68ED5; */
    background: linear-gradient(135deg, #c68ed5, #d483c6, #df77b3, #e66d9e, #ea6586);

    >div:first-child {
        color: white;
    }
}

.banner-color10 {
    background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));

    >div:first-child {
        color: white;
    }

}

.banner-color11 {
    background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));

    >div:first-child {
        color: white;
    }
}

.banner-color12 {
    background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));

    >div:first-child {
        color: white;
    }
}

.banner-color13 {
    background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));

    >div:first-child {
        color: white;
    }
}

.banner-color15 {
    background: linear-gradient(135deg, rgb(254, 254, 254), rgb(246, 248, 243), rgb(238, 241, 232), rgb(231, 235, 222), rgb(223, 229, 211));

    >div:first-child {
        color: black;
    }
}

.banner-color14 {
    background: linear-gradient(135deg, rgb(247, 246, 247), rgb(232, 239, 239), rgb(217, 232, 231), rgb(201, 224, 224), rgb(186, 217, 216));

    >div:first-child {
        color: black;
    }
}

.banner-color16 {
    background: linear-gradient(135deg, rgb(251, 251, 251), rgb(249, 246, 238), rgba(247, 241, 225, 0.5), rgba(245, 236, 213, 0.6), rgba(242, 231, 200, 0.7));

    >div:first-child {
        color: black;
    }
}

.banner-color17 {
    background: linear-gradient(135deg, rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));

    >div:first-child {
        color: black;
    }
}

.banner-color18 {
    background: linear-gradient(315deg, #f0f0f0, #f3f3f3, #f6f6f6, #f9f9f9, #fcfcfc);

    >div:first-child {
        color: black;
    }
}









.th-milestone-date {
    padding: 0px 8px;
    width: 154px;
}

#milestone-date {
    width: 184px;
}

.indicator-image {
    height: 160px;
    aspect-ratio: 16/9;

    display: block;
    margin-inline: auto;

    border-radius: 12px;
}

.hover-menu {
    width: 32px;
    height: 64px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    transition: 0.2s;

    visibility: hidden;
}

.hover-menu:hover {
    cursor: pointer;
    scale: 1.3;

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

/* 注文ページ（未使用） */

.p-plan {
    width: 100%;
    min-height: 288px;

    /* ボーダー設定 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    /* 後ろをぼかす */
    backdrop-filter: bler(4px) brightness(0.5);
    transition: 0.1s;

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 16px 12px;

    position: relative;

    @media (width < 756px) {
        width: 100%;
    }
}


#plan1+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));
}

#plan2+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(212, 227, 232), rgb(157, 168, 172), rgb(106, 114, 116), rgb(59, 63, 65), rgb(16, 18, 19));
    color: #fff;

    .material-symbols-rounded {
        color: #fff;
    }
}

.p-plan:hover {
    cursor: pointer;
    /* アウトライン */
    outline: 1px solid #fff;

    /* 影の設定 */
    box-shadow: 4px 4px 8px 4px rgba(0, 0, 0, 0.1);
}

input[type="radio"]:checked+.p-plan {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}



.p-option {
    width: 100%;
    height: 160px;

    padding: 0px 24px 0px 172px;
    margin-bottom: 12px;

    background-color: #fff;
    border: 1px solid var(--soremo-border);
    border-radius: 12px;

    /* flex親設定（縦） */
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;

    transition: ocacity 0.5s;

    position: relative;

    h4 {
        font-family: "A+mfCv-AXISラウンド 50 R StdN";
        font-weight: 400;
        font-size: 18px;
        letter-spacing: 2.5px;
    }


    a {
        font-size: 11px;
        color: var(--soremo-blue);
    }

    @media (width < 756px) {
        height: 128px;
        padding: 0px 0px 0px 136px;

        h4 {
            font-size: 16px;
        }

        p {
            font-size: 11px;
            line-height: 187.5%;
            font-feature-settings: 'palt' on;
        }
    }

}

.p-option-image1 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option1.png') no-repeat center / cover;

}

.p-option-image2 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option2.png') no-repeat center / cover;
}

.p-option-image3 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option3.png') no-repeat center / cover;
}

.p-option:hover {
    border: 1px solid var(--soremo-border);
    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

input[type="checkbox"]:checked+.p-option {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}


/* DM */
/* 　　*/

/* スレッドリスト */
.p-lists-order {
    width: 100%;
    padding: 8px 0px 8px 12px;
    border-bottom: 1px solid var(--soremo-border);

    position: relative;
}



.p-lists-recieved-offer {
    width: 100%;
    padding: 8px 0px 8px 12px;
    border-bottom: 1px solid var(--soremo-border);

    position: relative;
}

.p-sender {
    padding: 0px 0px 0px 26px;

}

.p-lists-sent-offer {
    width: 100%;
    padding: 8px 0px 8px 12px;
    border-bottom: 1px solid var(--soremo-border);

    position: relative;
}



/**
 * Articles
 */



/* Utility
   ----------------------------------------------------------------- */
/* align
*/
.u-block-wrapper {
    width: 100%;
    padding: 4px 8px;

    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    border-radius: 4px;
    border: 1px solid var(--soremo-background);
    background: var(--soremo-background);
}

.u-block-wrapper:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-border);
    background: var(--soremo-border);
}



.u-block-label {
    width: 100%;
    padding: 8px 8px 2px;

    display: flex;
    align-items: center;
    justify-content: flex-start;

    color: var(--soremo-light-gray);
}


.u-block-label>li:nth-child(1) {
    flex: 1;
    /* 最初のアイテムは残りのスペースの2倍の幅を持つ */
}

.u-block-label>li:nth-child(2) {
    flex: 1.382;
    /* 2番目のアイテムは残りのスペースの1倍の幅を持つ */
}


/* 2列構成。SPとPCで表示を切り替えるパターン */

.u-col-to-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    @media (width > 640px) {
        flex-direction: row;
        align-items: stretch;
        justify-content: flex-start;
    }
}

.u-wrapper {
    width: min(100% - 32px, 1140px);
}

.u-wrapper-reading {
    width: clamp(320px, 100% - 32px, 740px);
}

.u-wrapper-reading-paragraph {
    width: clamp(320px, 100% - 32px, 640px);
}

.u-wrapper-btn {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    @media (width > 756px) {
        flex-direction: row;
        justify-content: space-between;
    }
}

.u-wrapper-btn:has(>:only-child) {
    justify-content: flex-end;
}

.u-col {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.u-col-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.u-col-between {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
}



.u-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.u-row-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.u-row-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.u-row-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.u-row-btn {
    /* SPは中央寄せ */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    /* PCは右寄せ */
    @media (width > 640px) {
        justify-content: flex-end;
        gap: 16px;
    }
}


.u-items-start {
    align-items: flex-start;
}

.u-items-center {
    align-items: center;
}

.u-items-stretch {
    align-items: stretch;
}

.u-items-end {
    align-items: flex-end;
}

.u-justify-start {
    justify-content: flex-start;
}

.u-justify-center {
    justify-content: center;
}

.u-justify-end {
    justify-content: flex-end;
}


.u-wrap {
    flex-wrap: wrap;
}


.u-gap2 {
    gap: 2px;
}

.u-gap4 {
    gap: 4px;
}

.u-gap6 {
    gap: 6px;
}

.u-gap8 {
    gap: 8px;
}

.u-gap12 {
    gap: 12px;
}

.u-gap16 {
    gap: 16px;
}

.u-gap24 {
    gap: 24px;
}

.u-fill {
    flex: 1;
}

.u-flex-none {
    flex: 0 0 auto;
}

.u-overflow-y-auto {
    overflow-y: auto;
}

.u-w100 {
    width: 100%;
}

.u-h100 {
    height: 100%;
}

.u-border-top {
    border-top: 1px solid var(--soremo-border);
}

.u-border-right {
    border-right: 1px solid var(--soremo-border);
}

.u-border-bottom {
    border-bottom: 1px solid var(--soremo-border);
}

.u-relative {
    position: relative;
}

.u-absolute {
    position: absolute;
}

.u-fixed {
    position: fixed;
}


/* パディング */

u-p0 {
    padding: 0px;
}

.u-pt256 {
    padding-top: 256px;
}

.u-pt128 {
    padding-top: 128px;
}

.u-pt96 {
    padding-top: 96px;
}

.u-pt64 {
    padding-top: 64px;
}

.u-pt32 {
    padding-top: 32px;
}

.u-pt24 {
    padding-top: 24px;
}

.u-pt16 {
    padding-top: 16px;
}

.u-pt8 {
    padding-top: 8px;
}


.u-ptb16 {
    padding-top: 16px;
    padding-bottom: 16px;
}

.u-ptb8 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.u-ptb4 {
    padding-top: 4px;
    padding-bottom: 4px;
}



.u-pr8 {
    padding-right: 8px;
}

.u-pr16 {
    padding-right: 16px;
}

.u-pb4 {
    padding-bottom: 4px;
}

.u-pb8 {
    padding-bottom: 8px;
}

.u-pb16 {
    padding-bottom: 16px;
}

.u-pb32 {
    padding-bottom: 32px;
}

.u-pl8 {
    padding-left: 8px;
}

.u-pl16 {
    padding-left: 16px;
}

.u-pl24 {
    padding-left: 24px;
}


/* マージン */
.u-m0 {
    margin: 0px;
}

.u-mt0 {
    margin-top: 0px;
}

.u-mt8 {
    margin-top: 8px;
}

.u-mt16 {
    margin-top: 16px;
}

.u-mt24 {
    margin-top: 24px;
}

.u-mt32 {
    margin-top: 32px;
}

.u-mt48 {
    margin-top: 48px;
}

.u-mt64 {
    margin-top: 64px;
}

.u-mt96 {
    margin-top: 96px;
}

.u-mt128 {
    margin-top: 128px;
}

.u-mt192 {
    margin-top: 192px;
}



.u-mr4 {
    margin-right: -4px;
}

.u-mr16 {
    margin-right: 16px;
}

.u-mb0 {
    margin-bottom: 0px;
}

.u-mb8 {
    margin-bottom: 8px;
}

.u-mb16 {
    margin-bottom: 16px;
}

.u-mb24 {
    margin-bottom: 24px;
}

.u-mb32 {
    margin-bottom: 32px;
}

.u-mb64 {
    margin-bottom: 64px;
}

.u-ml-8 {
    margin-left: -8px;
}

.u-ml8 {
    margin-left: 8px;
}

.u-ml16 {
    margin-left: 16px;
}

.u-ml64 {
    margin-left: 64px;
}

.u-mx-auto {
    margin-inline: auto;
}

.u-text-center {
    text-align: center;
}

.u-text-justify {
    text-align: justify;
}

.u-text-right {
    text-align: right;
}


/* color設定 */
.u-text-white {
    color: #fff;
}

.u-text-blue {
    color: var(--soremo-blue);
}

.u-text-light-gray {
    color: var(--soremo-light-gray);
}

.u-text-black {
    color: #000;
}

.u-text-green,
.text-danger {
    color: var(--soremo-green);
}

.u-text-border {
    color: var(--soremo-border);
}


/* フォントサイズ */

.u-fontsize-32 {
    font-size: 32px !important;
}

.u-fontsize-40 {
    font-size: 40px;
}

.u-fontsize-48 {
    font-size: 48px;
}

.u-fontsize-64 {
    font-size: 64px;
}



.u-line-height-100 {
    line-height: 100%;
}

.u-line-height-150 {
    line-height: 150%;
}

.u-line-height-200 {
    line-height: 200%;
}

.u-white-space {
    white-space: nowrap;
}

.u-text-ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
}

.u-keep-all-words {
    word-break: keep-all;
}

.u-bg-white {
    background-color: #fff;
}

.u-bg-background {
    background-color: var(--soremo-background);
}

.u-bg-border {
    background-color: var(--soremo-border) !important;
}

.u-bg-light-gray {
    background-color: var(--soremo-light-gray) !important;
}

.u-bg-gray {
    background-color: var(--soremo-gray) !important;
}

.u-bg-blue {
    background-color: var(--soremo-blue) !important;
}

.u-bg-green {
    background-color: var(--soremo-green) !important;
}




/**
 * Clearfix
 */

/**
 * Display
 */

/* step毎のセクションを隠すためのクラス */
.is-hidden {
    display: none;
}

.is-active {
    display: block;
}

.is-active-flex {
    display: flex;
}

.is-invisible {
    visibility: hidden;
}

.is-visible {
    visibility: visible;
}

.is-icon-inactive {
    color: var(--soremo-border);
}

.is-icon-active {
    cursor: pointer;
    color: var(--soremo-blue);
}

.is-font-loading {
    display: none;
}


/* アニメーションの設定 */
/*                   */
@keyframes fadeIn {

    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes blink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}



.banner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    list-style: none;

    gap: clamp(8px, 1.5vw, 24px);
    margin: 64px 0 128px;
}

.p-fixed-banner {
    width: 100%;
    aspect-ratio: 5 / 1;
    padding: 0px 8px 0px 16px;
    border-radius: 6px;

    display: flex;
    /* 水平方向の中央揃え */
    justify-content: flex-start;
    /* 垂直方向の中央揃え */
    align-items: center;

    @media (width < 720px) {
        height: calc(720px / 5);
        background-size: auto 100%;
        aspect-ratio: auto;
    }
}

.p-fixed-banner .lindsey {
    font-family: "lindsey-signature", sans-serif;
    font-weight: 400;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(64px, 5vw, 96px);
    color: #fff;
}

.p-fixed-banner .din-2014 {
    font-family: "din-2014", sans-serif;
    font-weight: 800;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(32px, 5vw, 64px);
    color: #fff;
    text-transform: uppercase;
}

.p-fixed-banner .trajan-pro-3 {
    font-family: "trajan-pro-3", serif;
    font-weight: 400;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(32px, 5vw, 64px);
    color: #fff;
    text-transform: uppercase;
}


.p-fixed-banner .corporate-logo-ver2 {
    font-family: "corporate-logo-ver2",
        sans-serif;
    font-weight: 700;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(24px, 5vw, 48px);
    color: #fff;
}

.p-fixed-banner[data-color="color1"] {
    /* color1のスタイル */
    /* background-color: black; */
    background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));
}

.p-fixed-banner[data-color="color2"] {
    /* color2のスタイル */
    background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));
}

.p-fixed-banner[data-color="color3"] {
    /* color3のスタイル */
    background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));
}

.p-fixed-banner[data-color="color4"] {
    /* color4のスタイル */
    background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));
}

.p-fixed-banner[data-color="color5"] {
    /* color5のスタイル */
    background: url('images/studio.jpg') no-repeat top left / cover;

}

.p-fixed-banner:hover {
    cursor: pointer;

    transform: scale(1.02);
    transform-origin: center;

    transition: transform 0.3s ease-in-out;
    outline: 1px solid var(--soremo-border);

    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}

.c-fab {
    width: auto;;
    padding: 8px 16px 8px 8px;

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;

    background-color: #fff;
    color: var(--soremo-light-gray);

    border-radius: 6px;
    border: 1px solid var(--soremo-border);

    box-shadow: 2px 4px 10px 0px rgba(167, 168, 169, 0.15);

    transition: 0.2s;

    & span {
        font-size: 48px;
    }
}

.c-fab:hover {
    cursor: pointer;
    transform: scale(1.02);
    transform-origin: center;
    background-color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);

    color: #fff;

    box-shadow: 0px 0px 8px 0px var(--soremo-blue);

    & span {
        color: #fff;
    }
}

#fab-review {
    position: absolute;
    right: 0px;
    bottom: 0px;
}


.p-cards {
    width: 100%;
    padding: 16px 12px 16px;

    border-radius: 6px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
}

.p-cards-todo {
    width: 100%;
    padding: 16px 12px 16px;

    border-radius: 6px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    /* ！！兄弟要素の数に応じた上限設定が必要 */

    @media (width > 960px) {
        width: calc(50% - 16px / 2);
    }

    @media (width > 960px) {
        width: calc(100% / 2 - 16px / 2);
    }

    @media (width > 1440px) {
        width: calc(100% / 3 - 32px / 3);
    }
}


.c-counter {
    display: block;
    width: 32px;
    height: 32px;

    border-radius: 50%;
    background-color: var(--soremo-gray);
    color: #fff;

    display: flex;
    align-items: center;
    justify-content: center;
}


/* wavesurfer.js styling */

#waveform {
    cursor: pointer;
    position: relative;
}

#hover {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    pointer-events: none;
    height: 100%;
    width: 0;
    mix-blend-mode: overlay;
    background: rgba(255, 255, 255, 0.15);
    opacity: 0;
    transition: opacity 0.2s ease;
}

#waveform:hover #hover {
    opacity: 1;
}

#time,
#duration {
    position: absolute;
    /* z-index: 11; */
    top: 64px;
    margin-top: -1px;
    transform: translateY(-50%);
    font-size: 11px;
    /* background: #fff; */
    padding: 4px 0px;
    color: var(--soremo-light-gray);
}

#time {
    left: 0;
}

#duration {
    right: 0;
}

.position-sticky {
    position: sticky;
}

.position-absolute {
    position: absolute;
    bottom: 0;
}

.top-0 {
    top: 0;
}

.top-75 {
    top: 75px;
}

.sheader-scroll-up {
    transition: top 0.2s ease 0s;
    top: 0;
}

.sheader-scroll-down {
    transition: top 0.2s ease 0s;
    top: -64px;
}

/*Create refactored placeholder class*/
.sheader-pc-placeholder {
    display: flex;
    width: 100%;
    height: 64px;
    transition: height 0.2s ease 0s;
}

.sheader-pc-placeholder.up {
    transition: height 0.2s ease 0s;
    height: 0px;
}

.sheader-pc-placeholder.down {
    transition: height 0.2s ease 0s;
    height: 64px;
}

.refactor .mcolumn.mcolumn--left.column-list-offer.resize-drag {
    margin-top: 10px;
}

.action-panel-head.action-panel-head-custom.action-add-offer.refactor {
    position: absolute;
}

.martist.role_admin {
    height: 100%;
}

.refactor .mrow.mrow-custom.content-block-up {
    height: 100%;
}

.menu-offer-block-up {
    transition: height 0.2s ease 0s;
    height: calc(100% - 44px);
}

/* .menu-offer-block-up .action-panel-head-custom {
    bottom: 80px;
} */

.menu-offer-block-down {
    transition: height 0.2s ease 0s;
    height: calc(100% - 44px);
}

.download-block-up {
    height: calc(100dvh - 64px - 75px - 80px) !important;
}

.minfo-section.minfor-document {
    height: 100%;
}

.minfo-wrap.mscrollbar.mscrollbar--vertical {
    height: 100%;
}

.project-item__content.content-block-up.refactor {
    transition: height 0.2s ease 0s;
    height: calc(100dvh - 64px - 75px);
}

.offer-content-message {
    height: 100%;
}

.content-block-down {
    transition: height 0.2s ease 0s;
    height: calc(100dvh - 75px) !important;
}

.mcolumn.mcolumn--main.DM-box-container.dm-block-message {
    max-height: 100%;
}

.mcontent.refactor {
    width: clamp(320px, 100%, 740px);
}

.banner-project-backgroud {
    transition: top 0.2s ease 0s;
}

/* .banner-project-down { */
/* top: 0px; */
/* } */

/* .banner-project-up { */
/* top: 64.9006px; */
/* } */

.bottom-navigation-down {
    bottom: -80px !important;
}

.bottom-navigation-up {
    bottom: 0 !important;
}

.mmessage--sent:hover>.mmessage-info>.dropdown-comment>.show-more-action-message {
    display: flex !important;
}

.mmessage--received:hover>.mmessage-info>.dropdown-comment>.show-more-action-message {
    display: flex !important;
}

/* =====reply===== */

.li-reply-message>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply.svg');
}

.li-reply-message:hover>.mmessage-reply>.txt-reply-comment,
.li-reply-message:hover>.mmessage-reply>.img-reply-comment {
    color: #009ACE !important;
}

.li-reply-message:hover>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply-active.svg');
}

/* =====edit===== */

.li-edit-message>.mmessage-edit>.img-edit-comment {
    background: url('/static/images/scene-detail/icon-edit.svg');
}

.li-edit-message:hover>.mmessage-edit>.txt-edit-comment,
.li-edit-message:hover>.mmessage-edit>.img-edit-comment {
    color: #009ACE !important;
}

.li-edit-message:hover>.mmessage-edit>.img-edit-comment {
    background: url('/static/images/scene-detail/icon-edit-active.svg');
}

/* =====resolve===== */

/* .li-resolve-message>.mmessage-resolve>.img-resolve-comment {
    background: url('/static/images/scene-detail/icon-resolve.svg');
} */

.li-resolve-message:hover>.mmessage-resolve>.txt-item-comment, 
.li-resolve-message:hover>.mmessage-resolve>.img-resolve-comment  {
    color: #009ACE !important;
}

.li-resolve-message:hover>.mmessage-resolve>.img-resolve-comment {
    background: url('/static/images/scene-detail/icon-resolve-active.svg');
}

/* =====last-action-comment===== */
.last-action-comment>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply.svg');
}

.last-action-comment:hover>.mmessage-reply>.txt-reply-comment {
    color: #009ACE !important;
}

.last-action-comment:hover>.mmessage-reply>.img-reply-comment {
    background: url('/static/images/scene-detail/icon-reply-active.svg');
}

.mmessage-component.refactor {
    max-height: 611.491px;
}

.pd-section .mmessage-list.refactor {
    padding: 16px 0 92px 8px;
}

.max-height-mmessage-component-up.refactor {
    max-height: 100%;
}

.max-height-mmessage-component-down {
    max-height: 91dvh !important;
}

.max-height-pd-section-file-up {
    max-height: 100% !important;
    height: calc(100dvh - 64px - 75px - 80px) !important;
}

.max-height-pd-section-file-down {
    max-height: 100% !important;
    height: calc(100dvh - 75px) !important;
}


@media (max-width: 576px) {
    .refactor .mcolumn.mcolumn--left.column-list-offer.resize-drag {
        margin-top: 0px;
        overflow: auto;
    }

    .mcolumn.mcolumn--right {
        padding-top: 0px !important;
    }

    .sc-content-block-2 {
        max-height: 108px;
    }

    .tab--messenger-artist.refactor {
        overflow: hidden;
    }
}

.sc-content-block-2 {
    max-height: 30dvh;
}

.sidebar.refactor {
    position: absolute !important;
    padding: 16px 12px 12px 12px !important;
    top: 0px;
}

.refactor .project-tab.project-tab-product-comment {
    position: relative;
    height: 100%;
}

.refactor .pd-section.pd-section--detail {
    position: relative;
    height: 100%;
}

.refactor .pd-section__content.main-talk-room {
    position: relative;
    height: 100%;
}

.refactor .pd-comment {
    position: relative;
    height: 100%;
}

.refactor .pd-comment__content {
    position: relative;
    height: 100%;
}

.refactor .mmessage-component {
    position: relative;
    height: 100%;
}

.refactor .pd-comment__main {
    position: relative;
    height: 100%;
}

.mmessage-component.refactor.mmessage-component-refactor {
    max-height: 100%;
}

.project-item__content.refactor {
    height: calc(100dvh - 64px - 75px);
}

.project-item__content.refactor:has(.custom-switch-new.switch-homepage:not(.hide)) {
    /* background: linear-gradient(rgb(247, 246, 247), rgb(232, 239, 239), rgb(217, 232, 231), rgb(201, 224, 224), rgb(186, 217, 216)); */
    background: var(--soremo-background);
}

.project-tab.project-tab-progress.active {
    position: relative;
    /* padding-top: 45px; */
    overflow-y: auto;
}

.mcolumn.mcolumn--right {
    padding-top: 40px;
}

.mcolumn--main.DM-box-container.dm-block-message.refactor {
    display: block;
}

.offer-content-message.refactor {
    width: 100%;
    max-width: 100%;
}

.mcontent.refactor {
    width: 100%;
    max-width: 100%;
}

.prdt .pd-product-comment .pd-comment.refactor {
    max-width: 100%;
}

.mmessage-container.refactor {
    /* max-width: clamp(320px, 100%, 740px); */
    /* width: clamp(320px, 100%, 740px); */
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 4px;
}

.mmessage-list-container.refactor {
    max-width: clamp(320px, 100%, 756px);
    margin-left: auto;
    margin-right: auto;
}

.footer-comment-block.refactor {
    position: absolute;
    bottom: 80px;
    left: 0;
    right: 0;
}

.content-block-down .footer-comment-block.refactor {
    bottom: 0px;
}

.messenger-detail>.mcontent.refactor>.mmessage-list.mscrollbar>.mmessage-container.refactor:first-child,
.pd-comment__content>.mmessage-component.refactor>.mmessage-list.mscrollbar>.mmessage-list-container.refactor>.mmessage-container.refactor:first-child {
    margin-top: 28px;
}

.refactor .mcolumn-content-container {
    height: 100%;
    overflow: auto;
}

.refactor .dm-block-message .messenger-detail.have-file .mcontent .mmessage-list.mscrollbar{
    padding-bottom: 220px;
    transition: padding-bottom 0.2s ease 0s;
}

.refactor .dm-block-message .messenger-detail.have-file.have-text-offer .mcontent .mmessage-list.mscrollbar {
    padding-bottom: 230px;
    transition: padding-bottom 0.2s ease 0s;
}


.content-block-up .dm-block-message.refactor .messenger-detail.have-file .mcontent .mmessage-list.mscrollbar{
    padding-bottom: 300px;
    transition: padding-bottom 0.2s ease 0s;
}

.content-block-up .dm-block-message.refactor .messenger-detail.have-file.have-text-offer .mcontent .mmessage-list.mscrollbar {
    padding-bottom: 330px;
    transition: padding-bottom 0.2s ease 0s;
}

.refactor .dm-block-message .messenger-detail .mcontent .mmessage-list.mscrollbar {
    padding-bottom: 120px;
    transition: padding-bottom 0.2s ease 0s;
}

.messenger-detail:has(#footerCommentBlockOffer .button-confirm-offer-quick, #footerCommentBlockOffer .rate-review) .mcontent.refactor {
    height: calc(100% - 60px)
}

.content-block-up .dm-block-message.refactor .messenger-detail .mcontent .mmessage-list.mscrollbar {
    padding-bottom: 180px;
    transition: padding-bottom 0.2s ease 0s;
}

.modal-open .block-navigation-bar.bottom-navigation-down,
.modal-open .block-navigation-bar.bottom-navigation-up {
    z-index: 100 !important;
}

.content-block-down .menu-offer-block-up .action-panel-head-custom {
    bottom: 0px;
}

.switch-dm.refactor-ui {
    display: block;
    margin-left: 8px;
    margin-right: 8px;
    height: 30px;
    padding: 0px;
    position: relative;
    width: 95%;
    margin-bottom: 8px;
}

@media (max-width: 695px) {
    .switch-dm.refactor-ui {
        display: block;
        margin-left: auto;
        margin-right: auto;
        height: auto;
        padding: 0;
        position: relative;
        width: clamp(320px - 24px, 100% - 80px, 640px);
        margin-bottom: 8px;
        margin-top: 8px;
    }

    .footer-comment-block.refactor {
        margin-bottom: 0px;
        bottom: 80px;
        width: 100%;
        /* left: 50%; */
    }

    .content-block-down.footer-comment-block.refactor {
        bottom: 0;
    }


    .messenger-detail>.mcontent.refactor>.mmessage-list.mscrollbar>.mmessage-container.refactor:first-child {
        margin-top: 0px;
    }

    .refactor .martist.hide-banner {
        height: 100%;
    }

    .mrow.mrow-custom.content-block-up {
        height: 100%;
    }

    .mmessage-list.mscrollbar {
        height: 100%;
    }

    .refactor .dm-block-message .messenger-detail.have-file .mcontent .mmessage-list.mscrollbar{
        padding-bottom: 220px;
        transition: padding-bottom 0.2s ease 0s;
    }

    .refactor .dm-block-message .messenger-detail.have-file.have-text-offer .mcontent .mmessage-list.mscrollbar {
        padding-bottom: 230px;
        transition: padding-bottom 0.2s ease 0s;
    }
    
    .content-block-up .dm-block-message.refactor .messenger-detail.have-file .mcontent .mmessage-list.mscrollbar{
        padding-bottom: 312px;
        transition: padding-bottom 0.2s ease 0s;
    }

    .content-block-up .dm-block-message.refactor .messenger-detail.have-file.have-text-offer .mcontent .mmessage-list.mscrollbar {
        padding-bottom: 330px;
        transition: padding-bottom 0.2s ease 0s;
    }

    .refactor .dm-block-message .messenger-detail .mcontent .mmessage-list.mscrollbar {
        padding-bottom: 120px;
        transition: padding-bottom 0.2s ease 0s;
    }

    .content-block-up .dm-block-message.refactor .messenger-detail .mcontent .mmessage-list.mscrollbar {
        padding-bottom: 180px;
        transition: padding-bottom 0.2s ease 0s;
    }

    .space-first-message {
        height: 46px;
    }

    .prdt .messenger-detail .mcontent.refactor {
        /* height: calc(100% + 65px); */
        margin-top: -65px;
    }

    .refactor .tfile {
        padding-bottom: 120px;
    }

    .refactor .mcolumn.mcolumn--left.column-list-offer.menu-offer-block-down {
        height: calc(100% - 88px);
        position: relative;
    }

    .refactor .mcolumn.mcolumn--left.column-list-offer.menu-offer-block-up {
        height: calc(100% - 88px);
        position: relative;
    }
}

.tab--messenger-artist,
.psearch-parant-refactor {
    overflow: auto;
    height: 100%;
}

/* .project-tab.project-tab-progress {
    overflow-y: scroll;
} */

.pd-chapter__add.pd-chapter-new.refactor {
    position: absolute;
    bottom: 0px;
}

.mmessage.reply>.mmessage-main>.mmessage-content>.s-filedisable-wrap>.s-filetext {
    color: #000;
}

.sidebar-refactor-down {
    height: calc(100dvh - 75px);
}

.sidebar-refactor-up {
    height: calc(100dvh - 75px - 80px - 64px);
}

.budget-list-item.tab-2.refactor {
    height: 100% !important;
    max-height: 60% !important;
}

.refactor .mcolumn-content {
    height: auto;
    max-height: 100%;
}

.sidebar-relative {
    position: relative;
    height: 100%;
}

/* #left-sidebar.position-absolute {
    top: 0;
} */


body.srm3 {
    height: 100dvh;
}

.block-content-scene.refactor {
    place-content: center;
}

.project-tab.project-tab-progress.active.project-tab-scene-detail {
    margin-top: 0;
    overflow-y: hidden;
}

.cscene__bookmark.bookmark-button.save-bookmark-pc {
    font-size: 1rem;
    text-align: center;
}

/* .navigation-top-app-bar.refactor.display-flex {
    display: flex;
    justify-content: center;
} */

/* .navigation-top-app-bar:has(.switch-homepage.hide) {
    justify-content: end;
    width: 50px;
    right: 0px;
} */

.pd-section--delivery-video .pd-section__video .cvideo.refactor {
    flex: 0 0;
    padding: 0 8px 0 0;
}

/* .pd-section--delivery-video .pd-section__video .scene-home.refactor{
    margin-bottom: 8px;
} */
  
.refactor .cvideo__thumb video{
    width: auto;
}

.refactor .cvideo__thumb .thumb-schedule-video.view_only {
    height: 256px;
    min-width: 48px;
}

.pd-chapter__content .cvideo.refactor {
    flex: 0 0;
    padding: 0 8px 0 0;
}

.cvideo.refactor .project-delivery-item-content {
    min-width: 70px;
}

.cvideo.refactor .project-delivery-item-content .thumb-schedule-video {
    height: 256px;
    min-width: 128px;
}

#loading_animation.loading_animation_container {
    background-color: rgba(0, 0, 0, 0);
}

.scene-detail.upload-button-wrapper {
    z-index: 9999;
    display: none;
    width: min(100% - 16px, 640px);
    /* height: 100%; */
    max-height: 196px;
    align-items: center;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 1.0);
    padding: 16px 24px;
    justify-content: center;
}

.scene-detail.upload-button-wrapper.fullscreen {
    position: fixed;
    width: 100vw;
    height: 100vh;
}

.scene-detail.upload-button-wrapper:not(success) p {
    display: block;
}

.scene-detail.upload-button-wrapper.success p,
.scene-detail.upload-button-wrapper.success .fill {
    display: none;
}

.scene-detail.upload-button-wrapper .fill {
    background: #fcfcfc;
    width: 100%;
    height: 6px;
    position: relative;
    border: 1px solid #f0f0f0;
    border-radius: 6px;

}

.scene-detail.upload-button-wrapper .fill .process {
    background: #009ace;
    border-radius: 6px;
    width: 0;
    height: 6px;
    position: absolute;
    top: 0;
    left: 0;
    transition: .5s all ease .5s;
}

.scene-detail.upload-button-wrapper .fa {
    font-size: 25px;
    color: white;
    line-height: 50px;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
}

.scene-detail.upload-button-wrapper.clicked .fill .process {
    width: 0;
}

.scene-detail.upload-button-wrapper.success .fa {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    background: #009ace;
    padding: .5em 1em;
    border-radius: 50%;
    -webkit-transition: .3s all ease .3s;
    transition: .3s all ease .3s;
}

.scene-detail.upload-final-product-file {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    width: clamp(320px, 80vw, 756px);
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 48px 16px 48px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}

.scene-detail.upload-final-product-file p {
    font-size: 13px;
    color: #000;
    padding: 8px 0;
    margin: 0;
}


.pd-chapter.active.chapter-block .pd-chapter__content {
    overflow: auto;
}

.pd-chapter.active.chapter-block .pd-chapter__content .pd-chapter__list.mscrollbar.ui-sortable {
    flex-wrap: nowrap;
}

.pd-section.pd-section--delivery-video.refactor {
    padding: 40px 0px 32px;
    width: 100%;
    transition: .3s all ease .3s;
    /* background: linear-gradient(rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225)); */
    background: rgba(240, 240, 240,0.5);
    /* z-index: 10; */
}

/* .pd-section.scene-home-pd.refactor{
    padding-bottom: 16px;
} */

.content-block-down .pd-section.pd-section--delivery-video.refactor {
    transition: .3s all ease .3s;
    bottom: 0px;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__content {
    width: 100%;
    overflow: auto;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__content>.pd-section__video.mscrollbar {
    gap: 4px 4px;
    padding: 8px 16px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__content .cvideo__thumb-item-delivery .cvideo__thumb>video {
    height: 96px;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__content .cvideo__thumb-item-delivery .cvideo__heading,
.pd-section.pd-section--delivery-video.refactor>.pd-section__content .cvideo__thumb-item-delivery .cvideo__meta {
    display: none;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__title.sheading {
    font-size: 24px;
}

.pd-section.pd-section--delivery-video.refactor>.pd-section__content .thumb-schedule-video.view_only.refactor,
.pd-section.pd-section--delivery-video.refactor>.pd-section__content .cvideo__thumb {
    height: 96px;
    min-height: 96px;
}

.tab--messenger-artist.refactor .martist.role_master_admin,
.tab--messenger-artist.refactor .martist {
    height: 100%;
}

#loading_animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    z-index: 9999;
    /* background-image: url('/static/images/icon-loading-b.svg'); */
    background-size: 32px;
    background-position: center;
    background-repeat: no-repeat;
}

#loading_animation svg {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    height: 64px;
    width: 64x;
}

.mcolumn.mcolumn--left .switch-dm.show {
    z-index: 550;
}

.todo-list-item .cvideo__thumb.cvideo__thumb-list-update {
    width: 100%;
    position: relative;
}

.todo-list-item .cvideo__thumb.cvideo__thumb-list-update video {
    width: 100%;
}

.owner-top.has-variations.scene-detail-page.scene-style,
.owner-top.scene-detail-page.scene-style {
    margin-top: 60px;
}


/* input-select component */
.click-open-menu-select-input {
    color: var(--soremo-light-gray);
    position: absolute;
    right: 8px;
    top: 14px;
  
    transition: 0.2s;
  }
  
.click-open-menu-select-input:hover {
    cursor: pointer;
    color: var(--soremo-blue);
  
    scale: 1.2;
}

/* ==========================================================================
   New Display Offer For DM Block
   ========================================================================== */
/* .contract-block{
    padding: 5px;
    border-radius: 8px;
    position: relative;
    position: relative;
} */

/* .contract-block-title{
    font-weight: bold;
    font-size: 16px;
} */

/* .contract-block-detail{
    border: 2px solid #eee;
    border-radius: 5px;
    padding: 5px;
} */

.contract-block-detail-line-content{
    font-size: 13px;
}

.contract-block-detail-line-value{
    color: black;
    overflow-wrap: anywhere;
}

.contract-block-divider{
    margin-top: 5px;
    margin-bottom: 5px;
    border: 1px solid #f0f0f0;
}

.contract-block-contract{
    cursor: pointer;
}

.contract-block-contract-download-button, .contract-block-action-trigger-icon{
    margin-left:auto;
}

.contract-block-action-button{
    width: 20px;
}

.contract-block-action{
    right:10px;
    position: absolute;
    top: 0;
    display: none;
    width: 20px;
    cursor: pointer;
}

.contract-block:hover .contract-block-action,.mmessage-list.mscrollbar:hover .mmessage-container.refactor .contract-block .contract-block-action{
    display: block;
}

.contract-block-action-dropdown{
    width: 180px;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 5px;
    background: #fff;
}

.contract-block-action-dropdown .contract-block-action-trigger:hover  .contract-block-action-trigger-text {
    cursor: pointer;
    font-weight: bold;
}

.contract-block-action-trigger{
    padding: 6px 6px 0px 6px;
    cursor: pointer;
}

.contract-block-action-trigger:hover{
    font-weight: bold;
}

.contract-block-action-trigger.top-border{
    border-top: 1px solid #eee;
}

.contract-block-button{
    justify-content: center;
    cursor: pointer;
    align-items: center;
}

/* .contract-block-button-child{
    margin: 10px;
} */

.contract-block-button-child-top .material-symbols-rounded{
    font-size: 24px;
    line-height: 100%;
}

.contract-block-button-child-bottom .contract-block-button-child-small-text{
    font-size: 8px;
}

.contract-block-button-child-top .contract-block-button-child-text{ 
    font-size: 18px;
    letter-spacing: 2.5px;
    line-height: 100%;
}

.contract-block-button-child-top .material-symbols-rounded, .contract-block-button-child-top .material-symbols-rounded, .contract-block-button-child-bottom .contract-block-button-child-small-text, .contract-block-button-child-top .contract-block-button-child-text{
    color: #009ace;
}

.contract-block-blue-soremo{
    border-color: #009ace;
}

.contract-block-button.justify-between{
    justify-content: space-between;
}

.contract-block-action.u-absolute{
    right: 16px;
    top: unset;
}

.contract-block-action.u-absolute .contract-block-action-dropdown{
    bottom: 30px;
    top: unset;
    right: 0;
    left: unset;
}

.mcolumn--wrap.refactor{
    width: 100%;
}

@media (max-width: 695px) {
    .contract-block-action{
        display: block;
    }

    .contract-block-action-dropdown{
        right: unset;
        left: 0;
    }

    .contract-block-action.u-absolute .contract-block-action-dropdown{
        right: 0;
        left: unset;
    }

    .action-panel-head .button-confirm-offer-quick{
        right: 10px;
    }
}

.flex-direction-column{
    flex-direction: column;
}

.flex-direction-row{
    flex-direction: row;
}

.s-audio-time.new-position{
    position: absolute;
    font-size: 8px;
    color: var(--soremo-blue);

    bottom: 0px;
    left: 6px;
}

.s-audio-time-total{
    position: absolute;
    font-size: 8px;
    color: var(--soremo-light-gray);

    bottom: 0px;
    right: 0px;
}

.s-audio-time.new-position.is-single, .s-audio-time-total.is-single{
    padding-top: 20px;
}

/* .project-delivery-item-content .s-audio-time.new-position{
    left: 0px;
} */

.project-delivery-item-content .s-audio-time-total{
    right: 0px;
}

.s-audio-control.s-audio-control-progress-tab{
    flex-direction: row;
}

.s-audio-control.active .material-symbol-pause{
    display: block;
}

.s-audio-control.active .material-symbol-play{
    display: none;
}

.s-audio-control .material-symbol-pause{
    display: none;
}

.s-audio-wave-zoom{
    width: 100%;
    /* padding-top: 15px; */
    /* margin-left: 10px; */
}

.project-tab-progress .project-delivery-item-content canvas{
    width: 100%;
}

.project-tab-progress .pd-chapter__list .project-delivery-item-content canvas{
    height: 256px;
    width: auto;
}

.s-audio.s-audio--audio-wave.s-audio--black.active .s-audio-control .material-symbols-rounded{
    color: #fff;
}

.navigation-scene{
    cursor: pointer;
}

.editing .s-audio-control .material-symbols-rounded{
    color: #fff;
}

.modal-content .create-offer.mscrollbar .modal-body{
    height: 100%;
    max-height: unset;
}

.action-panel-head .button-confirm-offer-quick{
    position: absolute;
    right: 0px;
    top: -80px;
}

.action-panel-head .rate-review {
    margin-bottom: 80px;
}

.contract-block-soremo-border{
    border: 1px solid #009ace;
    border-radius: 6px;
    padding: 16px 24px 16px 8px;
}

#review-dialog {
    z-index: 9999;
}

#review-dialog #review-section-1 .c-radio-icon a {
    color: inherit;
}

dialog[open] {
    position: fixed;
    width: clamp(320px, 80vw, 756px);
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 32px 16px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}

.u-col-review {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.btn-prev-tertiary {
    min-width: 128px;
    padding: 8px 24px;
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    transition: 0.2s;
}

.btn-next-primary, .btn-next-primary:active {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    border-radius: 4px;
    transition: 0.2s;
}

.btn-next-primary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}


/* .c-keyword, .c-keyword:visited, .c-keyword:hover, .c-keyword:active {
    text-decoration: none;
    color: inherit;
} */

input[type="text"], input[type="tel"], input[type="email"], input[type="date"], input[type="number"], input[type="search"], textarea {
    width: 100%;
    padding: 12px 4px 12px 12px;
    margin: 0;
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
}

.title-review {
    margin-bottom: 0px !important;
}

.container-review{
    position: absolute;
    z-index: 999;
    height: 100vh;
    left: 0px;
    width: 100vw;
    top: 0px;
}

.my-review {
    display: flex !important;
    /* justify-content: end; */
}

.btn-next-primary,
.btn-next-primary:active {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    border-radius: 4px;

    transition: 0.2s;

    @media (width < 756px) {
        min-width: 144px;
    }
}

.btn-prev-tertiary {
    min-width: 128px;
    padding: 8px 24px;
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    transition: 0.2s;
}

.rate-review {
    width: 194px;
}

#review-dialog .c-quote{
    overflow: hidden;
    background: radial-gradient(50% 50% at 50% 50%, rgba(252, 252, 252, 0.05) 0%, rgba(0, 154, 206, 0.05) 100%);
    overflow-wrap: anywhere;
}

.u-col-review.is-hidden {
    display: none;
}

#review-section-2 .c-segment-control {
    width: 100%;
}


.u-col-review.is-hidden {
    display: none;
}

#review-section-2 .c-segment-control {
    width: 100%;
}

.mmessage-container .u-col {
    width: 100%;
}

.mmessage-container .c-message-their, 
.mmessage-container .c-message-ours, 
#review-section-2 .c-message-ours {
    width: 100%;
}
