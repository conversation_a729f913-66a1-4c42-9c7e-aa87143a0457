@charset "UTF-8";

#overlayBg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100;
}

.project-list .project-item .project-item__content {
    display: flex;
}

.project-item__content .project-tab.project-tab-messenger.active {
    width: 100%;
    transition: margin-right 0.5s;
}

.sidebar {
    height: 100dvh;
    width: 0;
    position: fixed;
    z-index: 300;
    right: 0;
    background-color: #FFF;
    overflow-x: hidden;
    transition: 0.5s;
    top: 140px;
}

.sidebar a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sidebar a:hover {
    color: #f1f1f1;
}

.sidebar .closebtn {
    display: flex;
    justify-content: flex-end;
}

.openbtn {
    font-size: 20px;
    cursor: pointer;
    background-color: #111;
    color: white;
    padding: 10px 15px;
    border: none;
}

.openbtn:hover {
    background-color: #444;
}

#mainSidebar {
    transition: margin-right .5s;
    padding: 16px;
}

.d-none-sb {
    display: none;
}

.modal-none {
    display: none !important;
}

/* .material-symbols-rounded {
    font-variation-settings: 'FILL' 1,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24
} */

.icon-balance-wallet {
    color: #a7a8a9;
}

.icon-balance-wallet:hover {
    color: #009ace;
}

@media screen and (max-height: 450px) {
    .sidebar {
        padding-top: 15px;
    }

    .sidebar a {
        font-size: 18px;
    }
}

@media (min-width: 767px) {
    #budgetLogSidebar {
        /*margin-right: -15px;*/
        width: clamp(320px, 19.1vw, 1140px - 16px);
        /*padding: 8px 12px 32px;*/
        padding: 8px 16px 32px 8px;
        /* height: calc(100dvh - 57px); */
        overflow-y: hidden;
    }

    #budgetLogSidebar #modalBudgetDetail {
        /*transform: translate(-50%, 0);*/
        /*top: 30%;*/
        top: 0;
    }
}


@media (max-width: 767px) {
    #budgetLogSidebar {
        position: fixed;
        padding: 16px 12px 32px 12px;
    }

    .sidebar {
        height: calc(100dvh - 115px);
    }

    #budgetLogSidebar #modalBudgetDetail {
        height: calc(100dvh - 115px);
    }

    .custom-loader.loader-bg.loader-active {
        top: 75%;
        left: calc(50% - 30px);
    }
}

.sidebar .closebtn {
    padding: 0;
    margin-bottom: 16px;
}

.total-budget-block .total-budget {
    display: flex;
    align-items: end;
}

.total-budget-block {
    background-color: #009ACE;
    padding: 24px 12px;
    border-radius: 4px;
}

.total-budget-block .title-text {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 200%;
    margin-bottom: 8px;
    display: flex;
    max-width: 157px;
    height: 26px;
}

.total-budget-block .total-budget .budget-number {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', serif;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    width: auto;
    max-width: 200px;
}

.total-budget-block .total-budget .budget-unit {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 150%;
    margin-left: 0px;
    height: 32px;
    display: flex;
    align-items: end;
    min-width: 55px;
}

.budget-progress {
    padding: 8px 0;
}

.budget-progress .line-progress {
    padding-bottom: 4px;
    display: flex;
    position: relative;
}


.budget-progress .line-progress .progress-item {
    height: 3px;
    border-radius: 4px;
    position: absolute;
    left: 0;
}

.budget-progress .line-progress .line-blue {
    background-color: #009ACE;
    z-index: 4;
}

.budget-progress .line-progress .line-white {
    background-color: #F0F0F0;
    z-index: 3;
}

.budget-progress .line-progress .line-grey {
    background-color: #A7A8A9;
    z-index: 2;
}

.budget-progress .line-progress .line-dark-grey {
    background-color: #53565A;
}

.budget-progress .percent-progress {
    color: #009ACE;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%; /* 22px */
    margin-bottom: 0;
    display: flex;
    padding-top: 4px;
}

.tab-filter-budget {
    padding: 16px 0
}

.tab-filter-budget .switch-btn-group {
    display: flex;
    /* overflow: hidden; */
    width: 100%;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
}

.switch-btn-group .switch-btn-item {
    flex: 1;
    margin: 1px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 300;
    line-height: 200%;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    /* min-width: 159px; */

}

.switch-btn-group .switch-btn-item:not(.active):hover {
    background-color: #A7A8A9;
    color: #FFF;
}

.switch-btn-group .switch-btn-item.active {
    background-color: #A7A8A9;
    color: #FFF;
}

.switch-btn-group .switch-btn-item:not(.active) {
    background-color: #FCFCFC;
    color: #A7A8A9;
}

.title-list-item {
    padding-left: 40px;
    padding-right: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-list-item .title-item {
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%; /* 22px */
    color: #A7A8A9
}

.budget-item:not(:first-child) {
    padding-top: 16px;
}

.budget-list-item .budget-item {
    cursor: pointer;
}

.budget-item {
    display: flex;
    padding-left: 1.5px;
}

.budget-list-item {
    overflow-y: auto;
    max-height: 215px;
}

.budget-item .user-avatar-info {
    position: relative;
    padding-right: 8px;
}

.budget-item .user-avatar-info .user-avatar {
    min-width: 32px;
    min-height: 32px;
    max-width: 32px;
    max-height: 32px;
    border-radius: 13px;
    border: 1px solid #FFF;
    object-fit: unset;
}

.budget-item .user-avatar-info .block-user-status {
    position: absolute;
    bottom: 0;
    right: 8px;
    width: 8px;
    height: 8px;
    max-width: 8px;
    max-height: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #FFF;
    border-radius: 8px;
}

.budget-item .user-avatar-info .block-user-status .user-status {
    color: #2CC84D;
    font-size: 8px;
}

.budget-item .budget-item-right {
    width: 100%;
}

.budget-item .budget-item-right .budget-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.budget-item .budget-item-right .budget-info-item .user-name {
    color: #000;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 200%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 131px;
}

.budget-item .budget-item-right .budget-info-item .budget-number-item  {
    display: flex;
}

.budget-item .budget-item-right .budget-info-item .budget-number-item .budget-amount {
    color: #009ACE;
    text-align: right;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    display: inline-block;
    height: 22px;
    min-width: 62px;
}

.budget-item.grey-item .budget-item-right .budget-info-item .budget-number-item .budget-amount, .budget-item.grey-item .budget-item-right .budget-info-item .budget-number-item .budget-percent {
    color: #53565A;
}

.budget-item .budget-item-right .budget-info-item .budget-number-item .budget-percent {
    color: #009ACE;
    text-align: right;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    width: 48px;
    height: 22px;
    display: inline-block;
    margin-left: 8px;
}

.budget-item .budget-item-right .budget-progress-item {
    width: 100%;
    height: 1px;
    background-color: #F0F0F0;
    display: flex;
}

.budget-item .budget-item-right .budget-progress-item:has(.progress-pay) {
    flex-direction: row-reverse;
}

.budget-item .budget-item-right .budget-progress-item .progress-content {
    height: 1px;
    width: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}

.budget-item .budget-item-right .budget-progress-item .progress-receive {
    background-color: #009ACE;
}

.budget-item .budget-item-right .budget-progress-item .progress-pay {
    background-color: #53565A;
}

.modal-backdrop.in {
    display: none;
}

.modal-budget-detail:not(.modal-none) {

}

#modalBudgetDetail {
    position: absolute;
    left: 50%;
    top: 0;
    /*top: 50%;*/
    transform: translate(-50%, 0);
    background-color: #FFFFFF;
    z-index: 101;
    padding: 32px 12px;
    border-radius: 12px;
    border: 1px solid #F0F0F0;
    display: block;
    width: calc(100% + 24px);
    /*height: calc(100dvh - 75px);*/
    height: calc(100dvh - 46px);
}

#modalBudgetDetail .modal-bd-header {
    padding-bottom: 16px;
    min-width: 300px;
}

.list-item-detail {
    overflow-y: auto;
}

.list-item-detail .item-detail:first-child {
    margin-top: 0;
}

.list-item-detail .item-detail {
    margin-top: 4px;
}

.list-item-detail .item-detail {
    border-radius: 6px;
    background-color: #F0F0F0;
}

#modalBudgetDetail .modal-bd-body .bg-detail {
    padding-bottom: 16px;
}

.bg-detail .budget-item {
    padding-bottom: 16px;
}

.bg-detail .title-item-detail {
    padding: 0 8px 4px 8px;
    display: flex;
    align-items: center;
}

.bg-detail .title-item-detail:last-child {
    margin-bottom: 0;
}

.bg-detail .title-item-detail .title-dt {
    color: #A7A8A9;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    display: inline-block;
}

.bg-detail .title-item-detail .title-1 {
    min-width: 56px;
    width: 56px;
}

.bg-detail .title-item-detail .title-2 {
    margin: 0 16px;
    min-width: 72px;
    text-align: right;
}

.bg-detail .title-item-detail .title-3 {
    width: 143px;
}

.bg-detail .item-detail {
    display: flex;
    align-items: center;
    padding: 8px;
    width: auto;
}

.bg-detail .item-detail .item-dt-date {
    min-width: 56px;
    display: inline-block;
}

.bg-detail .item-detail .item-dt-amount {
    min-width: 72px;
    margin: 0 16px;
    display: flex;
    justify-content: end;
}

.bg-detail .item-detail .item-dt-text {
    width: 100%;
}

.bg-detail .item-detail .item-dt-date {
    min-width: 56px;
}

.bg-detail .item-detail .item-dt {
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.bg-detail .item-detail.bg-grey .item-dt {
    color: #000000;
}

.btn-close-bgdt {
    cursor: pointer;
    min-height: 32px;
    min-width: 32px;
    max-width: 32px;
    max-height: 32px;
    color: #A7A8A9;
    justify-content: center;
    display: flex;
    align-items: center;
}

.custom-loader.loader-bg.loader-active {
    display: inline-block;
    position: absolute;
    top: 44%;
    left: 50%;
}

.custom-loader.loader-bg.loader-active.first-loading {
    top: auto;
    bottom: -45%;
}

.custom-loader {
    display: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: conic-gradient(#0000 10%, #8A9490);
    -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 4px), #000 0);
    animation: s3 1s infinite linear;
}

@keyframes s3 {
    to {
        transform: rotate(1turn)
    }
}

.bg-blue {
    background-color: #009ACE !important;
}

.item-detail.bg-blue .item-dt, .item-detail.bg-dark-grey .item-dt {
    color: #FFF;
}

.bg-dark-grey {
    background-color: #53565a !important;
}

.bg-grey {
    background-color: #f0f0f0 !important;
}

.bg-white {
    background-color: #ffffff !important;
}

.grey-background {
   background-color: #53565A !important;
}

.progress-content .progress-item-detail {
    height: 1px;
    position: absolute;
    right: 0;
}

.progress-grey {
    z-index: 2;
    background-color: #A7A8A9 !important;
}

.progress-dark-grey {
    z-index: 3;
    background-color: #53565A !important;
}

.progress-blue {
    z-index: 1;
    background-color: #009ACE !important;
}