/* ==========================================================================
   Foundation
   ========================================================================== */
@charset "utf-8";

/* ローカルのフォントを読み込む */
@font-face {
    font-family: "A+mfCv-AXISラウンド 50 L StdN";
    src: url('../fonts/AxisRound50StdN-L.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 R StdN";
    src: url('../fonts/AxisRound50StdN-R.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 M StdN";
    src: url('../fonts/AxisRound50StdN-M.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 B StdN";
    src: url('../fonts/AxisRound50StdN-B.otf') format('opentype');
}

/* material icon */
.material-symbols-rounded {
    font-variation-settings:
        'FILL' 1,
        'wght' 600,
        'GRAD' 0,
        'opsz' 24,
}

.material-symbols-rounded {
    color: var(--soremo-light-gray);
}

/* Basic Font Setting */
/*  */

/* Heading 18 Spacing */
/*  */
h4,
.heading-18-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: 18px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

/* heading */
/*  */
label,
.heading {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;

    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 200%;
}

/* bodytext */
/*  */
p,
a,
span,
li,
dt,
dd,
.bodytext {
    /* BodyText 13 - 16 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    /* 13px - 16px viewport 320px - 640px */
    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 200%;
}

input,
textarea,
.bodytext-16 {
    /* BodyText 16 設定 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 16px;
    line-height: 200%;
}

.bodytext-11 {
    /* BodyText 11 設定 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 200%;
}

.label-8 {
    /* BodyText 8 設定 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
}

/* <hr>Lineの設定 */
hr {
    /* 既存のボーダーを削除 */
    border: none;
    /* 線の高さ（太さ） */
    height: 1px;
    /* 線の色 */
    background-color: var(--soremo-border);
    /* 上下のマージン */
    margin: 8px 0;
}

/* 個別設定 */
/*  */

h3 {
    /* FUTURAのフォント設定 */
    font-family: "futura-pt",
        sans-serif;
    font-weight: 300;
    font-style: normal;

    font-size: clamp(1.75rem, 1.504rem + 1.05vw, 2rem);
    margin: 8px 0 24px;

    width: 100%;
}



/* Reset
   ----------------------------------------------------------------- */

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;

    /* Google Chrome のスワイプによる戻りを無効に */
    overscroll-behavior-x: none ! important;
}

html {
    width: 100%;

    /* コンテナクエリ親 */
    container-type: inline-size;
}

a,
a:visited,
a:hover,
a:active {
    text-decoration: none;
    color: inherit;
    /* 親要素から色を継承します */
}

li {
    list-style: none;
}

/* スクロールバーの調整 */
/* スクロールバー全体のスタイル設定 */
::-webkit-scrollbar {
    /* スクロールバーの幅 */
    width: 4px;
}

/* スクロールバーのトラック（背景部分）のスタイル設定 */
::-webkit-scrollbar-track {
    /* トラックの背景色 */
    background: var(--soremo-background);
}

/* スクロールバーのつまみのスタイル設定 */
::-webkit-scrollbar-thumb {
    /* つまみの背景色 */
    background: var(--soremo-border);
    /* つまみの丸み */
    border-radius: 6px;
}

/* スクロールバーのつまみにホバーした時のスタイル設定 */
::-webkit-scrollbar-thumb:hover {
    /* ホバー時の背景色 */
    background: var(--soremo-light-gray);
}


/* Base
   ----------------------------------------------------------------- */
:root {
    --soremo-blue: #009ace;
    --soremo-deep-blue: #0076A5;
    --soremo-grey3-color: #f0f0f0;
    --soremo-light-gray: #a7a8a9;
    --soremo-gray: #53565A;
    --soremo-border: #f0f0f0;
    --soremo-background: #fcfcfc;
}

body {
    width: 100%;
    height: 100dvh;

    overflow-y: auto;
    overflow-x: hidden;

    position: relative;
}



/* ==========================================================================
   Layout
   ========================================================================== */
#bg {
    /* background: #fbfbfb; */
    /* background: linear-gradient(rgb(251, 251, 251), rgb(249, 246, 238), rgb(247, 241, 225), rgb(245, 236, 213), rgb(242, 231, 200)); */
    background: linear-gradient(rgba(253, 253, 253, 0.5), rgba(247, 247, 246, 0.5), rgba(242, 242, 239, 0.5), rgba(236, 236, 232, 0.5), rgba(231, 231, 225, 0.5));
    z-index: -999;
}


/* Header
   ----------------------------------------------------------------- */

/* ナビゲーションバーのスタイル */
nav {
    width: 100%;
    height: 112px;
    margin-inline: auto;
    padding: 16px 0px 0px 0px;

    position: fixed;
    top: 0;
    transition: top 0.8s;

    text-shadow: 0px 0px 4px rgba(255, 255, 255, 0.70);

    z-index: 2;
}

nav h1 {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;

    /* FUTURAのフォント設定 */
    font-family: "futura-pt",
        sans-serif;
    font-weight: 300;
    font-style: normal;

    font-size: clamp(2.5rem, 2.008rem + 2.1vw, 3rem);
}

nav p {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;
}


/* Main
   ----------------------------------------------------------------- */
main {
    width: 100%;
    margin-inline: auto;
    margin: 112px auto 0px;
    padding: 0;
    min-height: 100dvh;
}

/* Footer
   ----------------------------------------------------------------- */
footer {
    width: 100%;
    padding: 16px 0 32px;

    border-top: 1px solid rgba(255, 255, 255, 0.16);

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    position: fixed;
    bottom: 0;
    transition: bottom 0.3s;

    /* flex親設定 */
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    @media (width < 756px) {
        /* flex親設定 */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}

/* ==========================================================================
   Object
   ========================================================================== */

/* Component
   ----------------------------------------------------------------- */
input[type="text"],
input[type="tel"],
input[type="email"],
input[type="date"],
input[type="number"],
textarea {
    /* テキストボックスの幅 */
    width: 100%;
    /* 内側の余白 */
    padding: 12px 4px 12px 12px;
    /* 外側の余白 */
    margin: 0;
    /* ボーダーのスタイル */
    border: 1px solid var(--soremo-border);
    /* 角の丸み */
    border-radius: 4px;
}

input[type="tel"] {
    width: 144px;
}

input[type="date"] {
    /* 元のカレンダーアイコンを非表示にする */
    -webkit-appearance: none;
    appearance: none;
    /* パディングを追加してアイコンとテキストが重ならないようにする */
    padding-right: 20px;
    position: relative;
}

input[type="date"]::after {
    /* Material Iconsのアイコンを使用 */
    content: "event";
    font-family: "Material Icons";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}


input[type="number"] {
    /* 数値のみ入力可能 */
    -moz-appearance: textfield;
    appearance: textfield;
    /* 右寄せにする */
    text-align: right;
}

/* スピンボタンの非表示 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
textarea:focus {
    /* フォーカス時のボーダーカラー */
    border-color: var(--soremo-blue);
    /* デフォルトのアウトラインを削除 */
    outline: none;
    /* ボックスシャドウ */
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

/* オートフィルされたフォームの背景色を変更  */
input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px var(--soremo-background) inset !important;
}

textarea {
    resize: none;

    /* body内のフォントを継承されないので個別に設定 */
    font-family: "A+mfCv-AXISラウンド 50 L StdN", 'M PLUS 1p', sans-serif;
    font-size: 16px;

    line-height: 200%;
}

textarea::-webkit-scrollbar {
    display: none;
}

/* プレイスホルダーの色 */
input::placeholder,
textarea::placeholder {
    color: var(--soremo-light-gray);
    /* ここに希望の色を指定 */
}

/* フォームの設定 */
form {
    width: 100%;
}

/* プログレスバー */
/* 全体のスタイル */
progress {
    /* プログレスバーの幅 */
    width: 100%;
    /* プログレスバーの高さ */
    height: 6px;

    -webkit-appearance: none;
    appearance: none;
}

/* プログレスバーの未完了部分のスタイル */
progress::-webkit-progress-bar {
    /* 背景色 */
    background-color: var(--soremo-border);
    /* 角の丸み */
    border-radius: 6px;
    /* 内側の影 */
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25) inset; */
}

/* プログレスバーの完了部分のスタイル */
progress::-webkit-progress-value {
    /* バーの色 */
    background-color: var(--soremo-blue);
    /* 角の丸み */
    border-radius: 6px;
    /* 影 */
    box-shadow: 2px 5px 8px 0px rgba(0, 154, 206, 0.10);
}

.progressbar {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    width: min(100% - 16px, 640px);
}

.progressbar span {
    font-size: 11px;
    color: var(--soremo-light-gray);
    width: 42px;
    text-align: right;
}


/* モーダル表示 */
/*            */
/* モーダルの背景  */
dialog::backdrop {
    background: rgba(0, 0, 0, 0.05);
}

dialog {
    display: none;
}

dialog[open] {
    display: block;
    position: fixed;
    width: clamp(320px, 80vw, 756px);
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 32px 16px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    /* 画面の中央に垂直方向で配置 */
    top: 50%;
    /* 画面の中央に水平方向で配置 */
    left: 50%;
    /* モーダルの左上の角を中央から左上に移動 */
    transform: translate(-50%, -50%);

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;

    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}

/* ボタンの設定 */
/*            */
.c-btn-primary {
    min-width: 192px;
    padding: 12px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;
}

.c-btn-primary:hover,
.c-btn-primary:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
    cursor: pointer;
}

.c-btn-primary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}

.c-btn-secondary {
    min-width: 176px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;
}

.c-btn-secondary:hover,
.c-btn-secondary:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
    cursor: pointer;
}

.c-btn-secondary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}


.c-btn-tertiary {
    min-width: 144px;
    padding: 8px 24px;
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    transition: 0.2s;
}

.c-btn-tertiary:hover {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

.c-btn-tertiary:focus {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    outline: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}


.c-btn-download {
    display: flex;
    align-items: center;
    justify-content: center;

    width: min(100%, 756px);
    margin-inline: auto;
    padding: 12px 24px;

    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    border-radius: 4px;
}
    
.c-btn-download:hover,
.c-btn-download:focus {
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
    cursor: pointer;
}
    


/* セグメントコントロールのスタイル設定 */
.c-segment-control {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    list-style: none;
    font-size: 13px;
    color: var(--soremo-light-gray)
}

.c-segment-control li,
.c-segment-control label {
    width: 50%;
    text-align: center;
    border-radius: 6px;
    padding: 1px 0;
    margin: 1px 1px;

}

.c-segment-control li:hover,
.c-segment-control label:hover {
    cursor: pointer;
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-segment-control label:has(input:checked) {
    background-color: var(--soremo-light-gray);
    color: #fff;
}



.c-group {
    border-radius: 4px;
    border: 1px solid var(--soremo-border, #F0F0F0);
    background: rgba(255, 255, 255, 0.5);
    padding: 16px 8px 16px 8px;
}

.c-group dl {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 8px;
}

dt {
    width: 144px;
    flex: 0 0 auto;
}



/**
 * Media
 */
/* Project
   ----------------------------------------------------------------- */

.p-plan {
    width: 100%;
    min-height: 288px;

    /* ボーダー設定 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    /* 後ろをぼかす */
    backdrop-filter: bler(4px) brightness(0.5);
    transition: 0.1s;

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 16px 12px;

    position: relative;

    @media (width < 756px) {
        width: 100%;
    }
}


#plan1+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));
}

#plan2+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(212, 227, 232), rgb(157, 168, 172), rgb(106, 114, 116), rgb(59, 63, 65), rgb(16, 18, 19));
    color: #fff;

    .material-symbols-rounded {
        color: #fff;
    }
}

.p-plan:hover {
    cursor: pointer;
    /* アウトライン */
    outline: 1px solid #fff;

    /* 影の設定 */
    box-shadow: 4px 4px 8px 4px rgba(0, 0, 0, 0.1);
}

input[type="radio"]:checked+.p-plan {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

/* 要素内の個別設定 */
.p-plan .price {
    position: absolute;
    bottom: 16px;

    /* FUTURAのフォント設定 */
    font-family: "futura-pt",
        sans-serif;
    font-weight: 300;
    font-style: normal;

    font-size: 32px;
}

.p-plan li {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px
}


.p-option {
    width: 100%;
    height: 160px;

    padding: 0px 24px 0px 172px;
    margin-bottom: 12px;

    background-color: #fff;
    border: 1px solid var(--soremo-border);
    border-radius: 12px;

    /* flex親設定（縦） */
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;

    transition: ocacity 0.5s;

    position: relative;

    h4 {
        font-family: "A+mfCv-AXISラウンド 50 R StdN";
        font-weight: 400;
        font-size: 18px;
        letter-spacing: 2.5px;
    }


    a {
        font-size: 11px;
        color: var(--soremo-blue);
    }

    @media (width < 756px) {
        height: 128px;
        padding: 0px 0px 0px 136px;

        h4 {
            font-size: 16px;
        }

        p {
            font-size: 11px;
            line-height: 187.5%;
            font-feature-settings: 'palt' on;
        }
    }

}

.p-option-image1 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('/static/images/mgk_option1.png') no-repeat center / cover;
}

.p-option-image2 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('/static/images/mgk_option2.png') no-repeat center / cover;
}

.p-option-image3 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('/static/images/mgk_option3.png') no-repeat center / cover;
}

.p-option:hover {
    border: 1px solid var(--soremo-border);
    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

input[type="checkbox"]:checked+.p-option {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

/**
 * Articles
 */



/* Utility
   ----------------------------------------------------------------- */
/* align
*/
.u-wrapper {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 16px;

}

.u-wrapper-reading {
    width: min(100%, 756px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;

}

.u-wrapper-btn {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* gap: 16px; */

    @media (width > 756px) {
        flex-direction: row;
        justify-content: space-between;
    }
}

.u-wrapper-btn:has(>:only-child) {
    justify-content: flex-end;
}


.u-row8 {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.u-row16 {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
}

.u-border-top {
    border-top: 1px solid var(--soremo-border);
}



/* マージン */

.u-mt8 {
    margin-top: 8px;
}

.u-mt16 {
    margin-top: 16px;
}

.u-mt24 {
    margin-top: 24px;
}

.u-mb8 {
    margin-bottom: 8px;
}

.u-mb16 {
    margin-bottom: 16px;
}

.u-mb24 {
    margin-bottom: 24px;
}


.u-mb-footer {
    margin-bottom: 128px;
}

/* color設定 */
.u-text-blue {
    color: var(--soremo-blue);
}

.u-text-gray {
    color: var(--soremo-grey3-color);
}

.u-text-light-gray {
    color: var(--soremo-light-gray);
}


/**
 * Clearfix
 */

/**
 * Display
 */

/* step毎のセクションを隠すためのクラス */
.is-hidden {
    display: none;
}

.is-active {
    display: block;
}

.is-active-flex {
    display: flex;
}

/* アニメーションの設定 */
/*                   */
@keyframes fadeIn {

    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes blink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}

/* topicのスタイル */
.catch {
    width: min(100%, 1140px);
    aspect-ratio: 1 / 1;
    margin: 0px auto 64px;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    position: relative;

    img {
        width: clamp(100%, 61.8vw, 1140px);
        position: absolute;
        z-index: -1;
    }
}

.catchphrase {
    color: #FFF;
    font-feature-settings: 'clig' off, 'liga' off;

    /* DNPShueiMinPr6Nフォント設定 */
    font-family: "dnp-shuei-mincho-pr6n", sans-serif;
    font-weight: 500;
    font-style: normal;

    /* font-size: clamp(1rem, 0.265rem + 3.14vw, 2.5rem); */
    font-size: clamp(0.813rem, -0.015rem + 3.53vw, 2.5rem);
    padding-top: min(61.8vw, 1140px * 0.618);

    text-shadow: 3px 2px 8px rgba(0, 0, 0, 0.75);

    @media (width < 756px) {
        text-shadow: 0px 0px 3px rgba(0, 0, 0, 1.0);
    }
}

.description {
    color: #FFF;
    font-size: clamp(0.688rem, 0.38rem + 1.31vw, 1rem);
    padding-top: min(4.775vw, 1140px * 0.04775);
    text-align: center;

    text-shadow: 3px 2px 8px rgba(0, 0, 0, 0.75);

    @media (width < 756px) {
        padding-left: 24px;
        padding-right: 16px;
        text-align: left;

        font-feature-settings: 'palt' on;
    }
}

.movie {
    width: min(100% - 32px, 1140px);
    margin: 0 auto 96px;


    video {
        width: 100%;
        border-radius: 16px 16px 16px 16px;
        box-shadow: 16px 12px 48px 0px var(--soremo-light-gray);
    }
}


.workflow,
.pamphlet {
    width: min(100% - 32px, 1140px);
    margin: 0 auto 96px;
}

.workflow .workflow-flex-container {
    width: min(100%, 756px);
    margin: 0px auto;

    .counter {
        width: clamp(2rem, 1.016rem + 4.2vw, 3rem);
        aspect-ratio: 1 / 1;

        border-radius: 50%;
        background-color: var(--soremo-gray);
        color: #fff;

        /* FUTURAのフォント設定 */
        font-family: "futura-pt",
            sans-serif;
        font-weight: 300;
        font-style: normal;
        font-size: clamp(1.5rem, 1.008rem + 2.1vw, 2rem);

        display: flex;
        justify-content: center;
        align-items: center;
    }

    h4 {
        /* Heading 18 Spacing */
        font-family: 'A+mfCv-AXISラウンド 50 R StdN';
        font-style: normal;
        line-height: 100%;

        font-size: 18px;
        letter-spacing: 2.5px;
    }

    .workflow-title-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;
    }

    img {
        width: min(100%, 756px);
        aspect-ratio: 18 / 9;
        object-fit: cover;

        border-radius: 16px;
        margin: 16px 0;
        border: 3px solid var(--soremo-border);
        box-shadow: 2px 6px 8px 0px #f0f0f0;
    }

    p {
        /* BodyText 16 設定 */
        font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
        font-weight: normal;
        font-size: 16px;
        line-height: 200%;
    }

    a {
        /* BodyText 11 設定 */
        font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
        font-weight: normal;
        font-size: 11px;
        line-height: 200%;
    }

    .workflow-flex-item {
        margin: 32px 0 144px;
    }
}

.plans {
    width: min(100% - 32px, 1140px);
    margin: 16px auto 48px;

    /* flex親（横設定） */
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    gap: 16px;

    @media (width < 756px) {
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
    }
}

.plans label {
    width: 100%;
}

.options {
    width: min(100% - 32px, 1140px);
    margin: 16px auto 96px;

    /* flex親設定（縦） */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
}

.estimate-area {
    @media (width > 756px) {
        width: 100%;
    }
}

.widget {
    width: 100vw;
    height: calc(288px + 32px);
    background: var(--soremo-gray);
    color: #fff;

    font-size: 11px;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    gap: 16px;


    .logo {
        width: 80px;
        aspect-ratio: 1 / 1;
        background: url('/static/images/logo_soremo_white.svg') no-repeat center center / contain;
        color: #fff;

        margin-top: 64px;
    }

    .legal {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 16px;

        @media (width < 640px) {
            flex-direction: column;
            justify-content: flex-start;
        }
    }

    a {
        text-decoration: none;
        color: #fff;

        /* BodyText 11 設定 */
        font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
        font-weight: normal;
        font-size: 11px;
        line-height: 200%;
    }
}