/* :root {
  --black2-color: #53565a;
  --white-color: #ffffff;
  --blue-color: #009ace;
} */

/* .pd-section {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  background-color: #fff;
  margin-bottom: 24px;
} */

@media (max-width: 992px) {
  .pd-section {
    border: none;
  }
}

.owner-top {
  position: relative;
}

.pd-section-file {
  position: relative;
}

.daterangepicker {
  z-index: 9999 !important;
}

.btn-tutorial-sp {
  position: fixed;
  bottom: 2vw;
  right: 2vw;
  z-index: 10;
  width: 32px;
  height: 32px;
  /* display: flex; */
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  color: #a7a8a9;
  border: none;
  transition: all 0.3s ease-in-out;
  background: #ffffff;
  display: none;
}

.btn-tutorial-pc {
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
  position: absolute;
  bottom: 1vh;
  z-index: 10;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  color: #a7a8a9;
  border: none;
  transition: all 0.1s ease-in-out;
  background: #ffffff;
}

.btn-tutorial-pc:hover,
.tooltip-content .tooltip-button .btn-next-tutorial:hover,
.tooltip-content .tooltip-button .btn-prev-tutorial:hover {
  color: var(--white-color) !important;
  background-color: var(--blue-color) !important;
}

.btn-tutorial-pc:focus,
.btn-tutorial-sp:focus,
.tooltip-content .tooltip-button .btn-next-tutorial:focus,
.tooltip-content .tooltip-button .btn-prev-tutorial:focus {
  color: var(--black2-color);
  background-color: var(--white-color) !important;
}

.tutorial-container-background {
  position: fixed;
  width: 100%;
  height: calc(100% + 105px);
  top: -75px;
  left: 0;
  z-index: 999;
  background-color: #000;
  opacity: 0;
  pointer-events: none;
}

.active-background {
  opacity: 0.5;
  pointer-events: all;
}

.active-tooltip {
  opacity: 1 !important;
  pointer-events: all !important;
  scale: 1 !important;
}

.all-tooltip {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  opacity: 0;
  pointer-events: none;
}

.tooltip-content {
  position: absolute;
  background-color: #fcfcfc;
  border-radius: 12px;
  padding: 16px;
  width: 90vw;
  max-width: 207px;
  text-align: center;
  z-index: 1000;
  opacity: 0;
  scale: 0;
  pointer-events: none;
  transition: all 0.3s ease-in-out;
}

.tooltip-content .arrow {
  position: absolute;
  border: 10px solid transparent;
  border-top-color: #fcfcfc;
  bottom: 0px;
  left: 50%;
  transform: translate(50%, 100%);
}

.tooltip-content .tooltip-title {
  font-size: 13px;
  line-height: 150%;
  color: #000000;
  font-family: "A+mfCv-AXISラウンド 50 R StdN", "Noto Sans Japanese", sans-serif;
}

.tooltip-content .tooltip-text {
  font-weight: 300;
  font-size: 13px;
  line-height: 150%;
  margin: 16px 0px;
}

.tooltip-content .tooltip-count {
  font-weight: 300;
  font-size: 11px;
  line-height: 150%;
  margin: 16px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tooltip-content .tooltip-count p {
  padding: 0px 2px;
}

.tooltip-content .tooltip-button {
  display: flex;
  justify-content: center;
}

.tooltip-content .tooltip-button .btn-prev-tutorial {
  width: 24px;
  height: 24px;
  background: #f0f0f0;
  margin: 0px 11px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: 4px;
  align-items: center;
}

.tooltip-content .tooltip-button .btn-next-tutorial {
  width: 24px;
  height: 24px;
  background: #f0f0f0;
  margin: 0px 11px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: 4px;
  align-items: center;
}

.pd-section__title {
  /* padding: 18px 16px;
  border-bottom: 1px solid #f0f0f0; */
  display: flex;
  align-items: center;
}

@media (max-width: 992px) {
  .pd-section__title {
    /* border: none; */
    font-size: 24px;
    padding: 9px 15px;
    /* flex-wrap: wrap; */
  }

  .pd-section__title-sub {
    margin-left: 0;
    margin-top: 0;
  }
}

.pd-section__title .icon {
  /* stylelint-disable-line */
  font-size: 24px;
  color: #009ace;
  margin-left: auto;
}

/* .pd-section__content {
  padding: 24px;
} */

/* @media (max-width: 992px) {
  .pd-section__content {
    padding: 15px;
  }
} */

/* .pd-section__video {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 24px;
} */

/* .pd-section__video .cvideo {
  flex: 0 0 200px;
  padding: 0 8px;
} */

/*.pd-section .cvideo {*/
/*    flex: 0 0 320px;*/
/*    padding: 0 12px;*/
/*}*/

.pd-section .cvideo:first-child {
  /*margin-left: -12px;*/
}

.pd-section .cvideo:last-child {
  margin-right: -12px;
}

/* .pd-section__header {
  display: flex;
  align-items: center;
  padding: 10px 24px;
  border-bottom: 1px solid #f0f0f0;
} */

/* @media (max-width: 992px) {
  .pd-section__header {
    padding: 10px 15px 0 15px;
  }
} */

.pd-section__header-left {
  display: flex;
  align-items: center;
}

@media (max-width: 992px) {
  .pd-section__header-left {
    display: flex;
    align-items: center;
  }
}

.pd-section__header-left .custom-switch .switch-label {
  color: #000;
  font-size: 11px;
}

.pd-section__header-left .video-filter {
  display: flex;
  align-items: center;
  margin-left: 100px;
  font-size: 13px;
}

.pd-section__header-left .video-filter:hover {
  cursor: pointer;
}

/* @media (max-width: 992px) {
  .pd-section__header-left .video-filter {
    margin: 24px 0;
  }
} */

.pd-section__header-left .video-filter.video-heart .icon {
  /* stylelint-disable-line */
  color: #009ace;
}

.pd-section__header-left .video-filter.video-heart-o .icon--sicon-heart-o {
  color: #009ace;
}

.pd-section__header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.pd-section .video-order-type {
  color: #f0f0f0;
}

.pd-section .video-order-type:hover {
  cursor: pointer;
}

.pd-section .video-order-type.active {
  color: #009ace;
}

.pd-section .video-order__asc {
  margin-right: 2px;
}

@media (max-width: 992px) {
  .pd-section .video-order-by {
    flex: 1;
  }
}

@media (max-width: 992px) {
  .pd-section .video-order-by .SumoSelect {
    width: 100%;
  }
}

.pd-section .pd-add-chapter {
  padding: 40px 0 16px 0 !important;
}

.pd-section .pd-add-chapter__content {
  border: 1px dashed #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  display: block;
}

.pd-section .pd-add-chapter__icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(167, 168, 169, 0.1);
  border-radius: 50%;
  margin: 0 auto;
}

.pd-section .pd-add-chapter__icon .icon {
  /* stylelint-disable-line */
  font-size: 32px;
  color: #a7a8a9;
}

.pd-section .pd-add-chapter__text {
  margin-top: 4px;
  color: #a7a8a9;
}

@media (max-width: 992px) {
  .pd-section--all-video {
    margin-bottom: 0;
  }
}

.pd-section--all-video .pd-section__title {
  display: none;
}

@media (max-width: 992px) {
  .pd-section--all-video .pd-section__title {
    display: flex;
    margin-bottom: 12px;
  }
}

.pd-section--detail-1 .pd-section__content {
  display: flex;
}

.pd-section--settings {
  margin-top: 24px;
}

.pd-chapter {
  margin-bottom: 40px;
}

.pd-chapter:first-child {
  margin-top: 16px;
}

.pd-chapter__toggle {
  margin-left: auto;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(83, 86, 90, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.pd-chapter__toggle:before {
  content: "\e922";
  font-family: "soremoicons" !important;
  /* stylelint-disable-line */
  line-height: 1;
}

.bootbox-body .edit-chapter-title {
  font-family: "A+mfCv-AXISラウンド 50 L StdN", "Noto Sans Japanese", sans-serif;
  color: black;
  font-weight: 400;
  margin-bottom: 30px;
  display: inline-block;
  margin-left: 20px;
}

.bootbox-edit-title {
  margin-left: 20px;
}

.bootbox-edit-title label {
  font-weight: 400;
  font-size: 17px;
  display: inline-block;
  margin-right: 15px;
}

.bootbox-body h3.edit-title {
  font-size: 20px;
}

.bootbox-edit-title input {
  border-radius: 3px;
  font-size: 17px;
  padding: 5px;
  border: 1px solid #383636;
  width: 80%;
}

.bootbox-edit-title input:focus {
  outline: none;
}

.pd-chapter__content {
  display: none;
}

.pd-chapter__add .icon {
  /* stylelint-disable-line */
  font-size: 32px;
  color: #a7a8a9;
}

.pd-chapter__add-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(167, 168, 169, 0.1);
  border-radius: 50%;
}

.pd-chapter__list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  /* padding-bottom: 24px; */
}

.pd-section__header .pd-chapter__toggle {
  margin-left: 20px;
  font-size: 18px;
  color: black;
  cursor: pointer;
  transform: rotate(180deg);
}

.pd-section__header .pd-chapter__toggle.active {
  transform: rotate(0deg);
}

/*.pd-chapter .cvideo {*/
/*    !* stylelint-disable-line *!*/
/*    flex: 0 0 278px;*/
/*}*/

#pnew-chapter .modal-header {
  border-bottom: none;
  padding-bottom: 8px;
}

.new-chapter__action {
  margin-top: 24px;
  text-align: center;
}

.pd-video-list {
  display: flex;
  flex-wrap: wrap;
  /* margin: 16px -12px -12px -12px; */
}

.pd-video-list .cvideo:first-child {
  margin-left: 0;
}

.pd-video-list .cvideo:last-child {
  margin-right: 0;
}

.pd-settings-default {
  display: flex;
}

.pd-preview {
  flex: 0 0 50%;
  padding-right: 12px;
}

@media (max-width: 992px) {
  .pd-preview {
    padding-right: 0;
  }
}

.pd-tree {
  flex: 0 0 50%;
  padding-left: 12px;
}

@media (max-width: 992px) {
  .pd-tree {
    padding-left: 0;
  }
}

.pd-preview-default {
  margin-top: 16px;
}

.pd-preview-default__title {
  margin-bottom: 24px;
  color: #a7a8a9;
}

.pd-preview-default__upload {
  background-color: #fcfcfc;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.pd-tree-default {
  background-color: #fcfcfc;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  margin-top: 60px;
}

#pnew-chapter-tree .modal-header {
  border-bottom: none;
  padding-bottom: 8px;
}

#pnew-chapter-tree .new-chapter__action {
  margin-top: 40px;
}

#pnew-chapter-tree .new-chapter__create {
  margin-left: 12px;
}

.pd-tree-preview .video-modal__left {
  width: 100%;
}

.pd-section--detail {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 992px) {
  .pd-section--detail {
    border-bottom: 1px solid #f0f0f0;
    border-radius: 0;
  }
}

.pd-section--detail .pd-section__content {
  display: flex;
  flex-direction: column;
  /* width: calc(100% - 267px); */
  /* flex-wrap: wrap; */
  /* transition: all 0.3s; */
}

.pd-section--detail .pd-section__content-not-share {
  width: 100% !important;
}

.pd-section__content-not-share .pd-scene {
  width: 100% !important;
  flex: auto;
}
.pd-section__content-not-share .cscene {
  width: 100% !important;
}
.pd-section__content-not-share .cscene-vertical {
  width: 100% !important;
}

.pd-section--detail.pd-product-comment .pd-section__content {
  width: calc(100% - 70px);
}

.pd-section--detail.pd-product-comment .pd-section__content.active {
  width: calc(100% - 280px);
}

@media (max-width: 992px) {
  .pd-section--detail .pd-section__content,
  .pd-section.pd-section--detail .pd-section__content.active {
    width: 100%;
  }

  .pd-section--detail.pd-product-comment .pd-section__content.active {
    width: calc(100% - 0px);
  }
}

.pd-section--detail .pd-section__content.active {
  width: calc(100% - 55px);
  transition: all 0.3s;
}

.pd-section--detail.drag-active {
  position: relative;
  z-index: 10;
}

.pd-section .mmessage-component {
  display: flex;
  flex-direction: column;
  /* max-height: 75vh; */
  position: relative;
  z-index: 0;
}

.pd-section .mmessage-list {
  padding: 16px 0 32px 8px;
}

.pd-scene {
  width: 50%;
  padding-right: 12px;
}

@media (max-width: 992px) {
  .pd-scene {
    padding-right: 0;
    width: 100%;
  }
}

.pd-product-comment .pd-comment {
  width: 100%;
}

.pd-comment {
  width: 50%;
  /*  padding-left: 12px; */
  position: relative;
  z-index: 9;
  /* height: 80vh; */
}

.pd-file {
  display: none;
}

.popover .pd-comment {
  width: 100%;
}

@media (max-width: 992px) {
  .pd-comment {
    padding-left: 0;
    width: 100%;
    transition: all 0.3s;
  }
}

.pd-comment__heading {
  color: #000;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 0;
  margin: 16px 0;
  position: relative;
  display: none;
}

.pd-comment__heading:hover {
  cursor: pointer;
}

.pd-comment__heading:after {
  content: "\e922";
  font-family: "soremoicons" !important;
  /* stylelint-disable-line */
  font-size: 16px;
  position: absolute;
  right: 0;
  top: calc(50% - 12px);
  transform: rotate(180deg);
  transition: all 0.3s;
}

.pd-comment__heading.active:after {
  transform: rotate(0deg);
  transition: all 0.3s;
}

.pd-comment__top {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
  justify-content: space-between;
}

@media (max-width: 992px) {
  .pd-comment__top {
    padding-bottom: 16px;
  }
}

.pd-comment__title {
  font-size: 16px;
  color: #000;
  font-weight: 400;
}

.pd-comment__file {
  margin-left: 60px;
  display: flex;
  align-items: center;
}

.pd-comment__file .icon {
  /* stylelint-disable-line */
  font-size: 24px;
  margin-right: 12px;
  color: #000;
}

.pd-comment__attach {
  margin-left: 0;
}

.pd-comment .mmessage,
.messenger-detail .mmessage {
  align-items: center;
}

.mmessage.clicked {
  align-items: flex-end;
}

.pd-comment .mmessage-info {
  margin-top: 0;
}

.pd-comment .mcomment-icon .mcomment-icon-board {
  left: auto;
  right: -59px;
  height: 310px;
  transform: none;
}

.pd-scene-title-detail
  .cscene-variation
  .SumoSelect
  > .CaptionCont
  > span:hover,
.pd-scene-title-detail
  .cscene-variation
  .SumoSelect
  > .CaptionCont
  > label:hover {
  cursor: pointer !important;
}

.pd-scene-title-detail .cvideo__rating {
  margin-left: 8px;
}

.pd-scene-title-detail .stars {
  font-size: 14px;
  margin-bottom: 6px;
}

.message-actions-container,
.message-info-container {
  cursor: default !important;
}

.martist .mmessage.mmessage--received {
  cursor: default !important;
}

.pd-section--detail .cscene--video .cscene__version {
  background: transparent;
}

@media (max-width: 992px) {
  .pd-scene-title-detail .stars {
    font-size: 12px;
  }

  .btn-tutorial-sp {
    display: flex;
  }

  .btn-tutorial-pc {
    display: none;
  }

  .pd-scene-title-detail .cvideo__rating {
    margin-left: 5px;
  }
}
