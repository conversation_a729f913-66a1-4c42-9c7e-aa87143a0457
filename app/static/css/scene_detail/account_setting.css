:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --grey3-color: #F0F0F0;
    --blue-color: #009ACE;
    --background-color: #FCFCFC;
}

.has-success .form-control {
    border-color: #ccc !important;
}

.has-success .control-label {
    color: #333 !important;
}

.has-success .help-block {
    color: #737373 !important;
}

.user-info__notifi ul {
    display: inline-flex;
    list-style: none;
    padding-left: 0px;
}

/* Content tabs */
.user-info__main.account__info {
    margin-top: 0;
    padding-top: 0;
    width: 100%;
}

.user-info {
    position: relative;
    margin-top: 65px;
    background-color: var(--background-color);
}

.h3-title {
    font-size: 1.1em;
    color: #1a1a1a;
}

.user-info__upload label {
    color: white;
}

.user-info__form label {
    margin-left: 0;
}

.user-info__upload {
    margin-top: 10px;
}

.user-info__wrap {
    float: left;
    position: fixed;
    top: 165px;
    background-color: var(--background-color);
    z-index: 2;
    width: 200px;
}

.user-info__heading {
}

.user-info__heading h3 {
    padding: 40px 0 24px;
    margin: 0;
}

.user-info__content {
    border: 1px solid var(--soremo-border);
    border-radius: 12px;
    padding: 32px 17px;
    background-color: var(--white-color);
}

.user-info__content .user-info__form {
    max-width: 100%;
}

.account__form-group.form-group {
    margin-bottom: 0;
}

.account__form-group .form-group {
    margin: 0;
    padding: 0;
}

.account__form-group .form-group:last-child {
    margin-bottom: 40px;
}

.account__form-group .form-group label {
    margin: 0;
    padding: 0;
}

.account__field-label {
    display: block;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    color: var(--black1-color);
    margin-bottom: 4px;
}

.account__field-hint {
    display: block;
    margin-bottom: 8px
}

.account__form-heading {
    margin: 0;
    padding: 40px 0 4px;
    border-top: 1px solid var(--soremo-border);
}

.account__field_input_text {
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    color: var(--black2-color);
}

.acc_action {
    text-align: left;
}

.acc_action .form-group {
    margin-bottom: 0;
}

.acc_action .button {
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    min-width: 100px;
    text-transform: uppercase;
    background-color: var(--blue-color);
    padding: 12px 39px;
    border-radius: 4px;
    border: none;
    color: var(--white-color);
}

.account__field-text {
    font-size: 11px;
    line-height: 17px;
    color: var(--black1-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}

.account__form-group-link .input-group {
    margin-top: 9px;
    margin-right: 24px;
}

.account__form-group .account__form-multi {
    display: block;
}

.account__form-group .account__form-multi:last-child {
    margin-bottom: 0;
}

.account__form-group .account__form-multi.account__form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.account__form-group .account__form-multi .input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 400;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
}

.account__form-group .input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.account__form-group .input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.account__form-group .input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}

.notification-time {
    position: relative;
    display: flex;
    align-items: center;
}

.notification-time .input-time {
    border: 1px solid var(--soremo-border);
    box-sizing: border-box;
    border-radius: 4px;
    height: 45px;
    margin-left: 42px;
    margin-bottom: 8px;
    padding: 10px 40px 10px 16px;
    color: var(--black2-color);
}

.notification-time:after {
    content: '\e925';
    font-family: 'soremoicons';
    pointer-events: none;
    position: absolute;
    bottom: 20px;
    right: 16px;
    font-size: 16px;
    color: var(--grey1-color);
}

.notification-time select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

.notification-time .input-time:focus-visible {
    outline: none;
}

.notification-time .input-button {
    background: none;
    border: none;
    color: #a7a8a9;
    padding: 0;
    font-size: 8px;
}

.form-group-content {
    width: 100%;
    margin: 0 -15px;
    display: flex;
    align-items: center;
}

.account__form-group .form-group .form-group-content label {
    margin-bottom: 8px;
    width: 100%;
}

.select-time {
    display: none;
}

.account__form-heading.account__form-title {
    border: none;
    padding-top: 0;
}

/* End content tabs */

@media (max-width: 992px) {
    .user-info {
        background-color: var(--white-color);
    }

    .user-info__content {
        border: none;
        padding: 0;
        margin: 0 -15px;
    }

    .user-info__heading h3 {
        font-size: 18px;
        line-height: 27px;
        padding: 12px 0 40px;
    }

    .user-info__main.account__info {
        width: 100%;
        margin: 0;
        padding: 0;
    }

    .account__form-group-link .input-group {
        width: 100%;
        margin-right: 0;
    }

    .account__submit-op {
        width: 100%;
    }

    .form-group-content {
        display: block;
        border: 1px solid var(--soremo-border);
        border-radius: 8px;
        margin: 0;
        margin-bottom: 16px;
    }

    .notification-time .input-time {
        margin: 8px 0 8px 27px;
    }

    .account__field-text {
        display: none;
    }

    .acc_action {
        text-align: center;
    }

    .account__form-heading.account__form-title {
        font-size: 18px;
        font-weight: 400;
        line-height: 27px;
        margin: 0;
        padding: 40px 0 4px;
        color: var(--black1-color);
        border-top: 1px solid var(--soremo-border);
    }
}
