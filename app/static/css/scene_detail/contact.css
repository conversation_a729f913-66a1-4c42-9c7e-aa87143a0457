:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --grey3-color: #F0F0F0;
    --blue-color: #009ACE;
    --background-color: #FFFFFF;
}

/* Content */
.srm3 {
    background: #FFFFFF !important;
}

.contact-info {
    position: relative;
    margin-top: 65px;
    background-color: #FFFFFF;
}

.h3-title {
    font-size: 1.1em;
    color: #1a1a1a;
}

.contact-info__upload label {
    color: white;
}


.contact-info__upload {
    margin-top: 10px;
}

.contact-info__heading {
}

.contact-info__heading h3 {
    padding: 40px 0 24px;
    margin: 0;
}

.contact-info__content {
    border: 1px solid var(--soremo-border);
    border-radius: 12px;
    padding: 32px 17px;
    background-color: var(--white-color);
}

.contact-info__form {
    margin: 0;
}

.contact-info__form .form-control {
    border-radius: 0 !important;
}

.contact-info__form label {
    margin-left: 0;
}

.account__field-label {
    display: block;
    margin-bottom: 4px;
}

.contact-info__content .contact-info__form {
    max-width: 100%;
}

.account__form-group.form-group {
    margin-bottom: 0;
}

.account__form-group .form-group {
    margin: 0 -12px 24px;
    padding: 0;
}

.account__form-group .form-group:last-child {
    margin-bottom: 40px;
}

.account__form-group .form-group label {
    margin: 0;
    padding: 0 12px;
}

input:required {
    border: 1px solid #2CC84D;
    border-radius: 4px;
  }

.account__form-heading {
    margin: 0;
    padding: 24px 0 16px;
    border-top: 1px solid var(--soremo-border);
}

.contact-info__submit.account__action {
    text-align: left;
    margin: 0;
}

.contact-info__submit.account__action .form-group {
    margin-bottom: 0;
}

.contact-info__submit.account__action .form-group .button {
    min-width: 100px;
    text-transform: uppercase;
    background-color: var(--blue-color);
    padding: 12px 39px;
    border-radius: 4px;
    border: none;
    color: var(--white-color);
}

.form-textarea textarea {
    width: 70%;
}

.account__field-text {
    font-size: 11px;
    line-height: 17px;
    color: var(--grey1-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.account__field-text-link {
    font-size: 11px;
    line-height: 17px;
    color: var(--grey1-color);
    padding-top: 8px;
    padding-bottom: 3px;
}

.account__field-text-blue {
    font-size: 11px;
    line-height: 17px;
    color: var(--blue-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.account__jp-astarisk, .account__jp-astarisk-op {
    margin-left: 8px;
}
/* End content */

/* Drag & drop file */
.account__upload-file {
    display: flex;
    flex-wrap: wrap;
}

.account__file {
    position: relative;
    max-width: 170px;
    display: flex;
    align-items: center;
    padding: 8px 25px 8px 16px;
    background-color: var(--soremo-border);
    border-radius: 6px;
    margin: 4px;
}

.account__file:first-child {
    margin-left: 0;
}

.account__file .icon {
    font-size: 15px;
    color: var(--grey1-color);
}

.account__file .icon--sicon-close {
    position: absolute;
    right: 8px;
    cursor: pointer;
}

.mattach-preview-container .mcommment-file {
    background-color: var(--soremo-border);
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--black1-color);
    max-width: 100px;
}

.account__file-name {
    font-size: 11px;
    line-height: 17px;
    display: block;
    margin: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--black1-color);
    max-width: 100px;
}

.mcommment-file__delete .icon, .mcommment-file__name .icon {
    color: var(--black2-color);
}

.account__form-group .form-group .account_upload-file {
    margin-right: -25px;
}

.account__form-group .form-group .account_upload-file #myDropZone {
    width: 100%;
    padding: 24px;
    cursor: pointer;
    background: var(--white-color);
    border: 1px dashed var(--soremo-placeholder);
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 8px;
}

.account__form-group .form-group .account_upload-file .dz-button .icon {
    font-size: 20px;
    color: var(--black2-color);
}

.account__form-group .form-group .account_upload-file .dz-button p {
    font-size: 13px;
    line-height: 20px;
    color: var(--black2-color);
    margin-top: 10px;
    margin-bottom: 0;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.account_upload-file .account_file {
    display: none;
}

.mattach-preview-container .mattach-previews {
    margin: 4px 0 0;
    padding: 0;
    overflow-y: hidden !important;
}

.dropzone.dz-drag-hover {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.dropzone.dz-drag-hover .icon, .dropzone.dz-drag-hover p {
    color: white !important;
}

.account_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
}

.account_upload-file .fallback:hover .icon, .account_upload-file .fallback:hover p{
    color: white !important;
}

.upload-final-product-file.upload-button-wrapper {
    z-index: 9999;
    /* background-color: rgba(255, 255, 255, 0.7); */
}
/* End drag & drop */

/* Modal confirm */
.popup-confirm .modal-dialog {
    width: 1140px;
}

.popup-content {
    padding: 32px;
}

.popup-content .popup-header {
    position: relative;
    border: none;
}

.popup-header hr {
    margin-top: 0px;
    margin-bottom: 0px;
}

.popup-title {
    font-size: 13px;
    line-height: 27px;
    font-weight: 400;
    color: var(--black1-color);
    margin: 11px 8px 23px 0;
}

.popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0;
    background-color: var(--white-color);
}

.popup-close .icon--sicon-close {
    font-size: 18px;
    color: var(--black1-color);
}

.popup-body {

}

.popup-body__wrap {
    border-bottom: 1px solid var(--soremo-border);
}

.popup-body__item {
    display: flex;
    padding: 12px 0;
    align-items: center;
}

#user-file {
    flex-wrap: wrap;
}

.popup-body__item:last-child {
    margin-bottom: 12px;
}

.popup-body__item .account__field-text-link {
    padding: 12px 0;
    width: 100%;
}

.popup-body__heading {
    font-size: 18px;
    line-height: 27px;
    font-weight: 400;
    color: var(--black1-color);
    padding: 14px 0;
}

.first-item-head {
    display: block;
}

.first-item-head .text-content{
   margin-left: 0px;
}

.popup-body__text {
    font-size: 13px;
    line-height: 20px;
}

.text-title {
    color: var(--black1-color);
    font-weight: 400;
    min-width: fit-content;
}

.text-content {
    color: var(--black1-color);
    margin-left: 8px;
}

.popup-footer {
    text-align: right;
    padding: 24px 0 8px;
}

.popup-footer .btn {
    cursor: pointer;
    border-radius: 4px;
}

.popup-footer .btn.btn-popup-send, .popup-footer .btn.btn-popup-send:hover {
    margin-left: 24px;
    padding: 12px 46px !important;
}
/* End modal confirm */

/* Modal ok */
.popup-container {
    padding: 0;
}
#modalOk.modal:before {
    height: 0;
}

#modalOk .modal-dialog {
    width: 370px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    height: 100%;
    border-radius: 12px;
}

.account__popup-container {
    top: 0;
    background: rgba(0, 0, 0, 0.8);
}

#modalOk .popup-title {
    font-size: 16px;
    line-height: 24px;
    color: var(--black1-color);
    margin: 0;
    text-align: center;
}

.popup-text {
    font-size: 13px;
    line-height: 20px;
    color: var(--black1-color);
    padding: 16px 0;
    margin: 0;
    text-align: center;
}

#modalOk .popup-content {
    padding: 40px 24px;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

#modalOk .popup-footer {
    text-align: right !important;
    padding: 0;
}

#modalOk .popup-footer .btn {
    margin: 0;
}
/* End modal ok */

@media (max-width: 992px) {
    .contact-info__heading h3 {
        font-size: 18px;
        line-height: 27px;
        padding: 12px 0;
    }

    .contact-info__wrap {
        display: none;
    }

    .contact-info__main {
        width: 100%;
    }

    .contact-info__content {
        border: none;
        padding: 0;
        margin: 0 -15px;
    }

    .account__form-group .form-group label {
        width: 100%;
    }

    .form-textarea textarea {
        width: 100%;
    }

    .account__form-group .form-group .account_upload-file {
        margin-right: 0;
    }

    .account__form-group .form-group .account_upload-file #myDropZone {
        width: 100%;
    }

    .contact-info__submit.account__action {
        text-align: center;
    }

    .account__action .form-group .button {
        width: auto;
    }

    #modalOk .modal-dialog, #modalConfirm .modal-dialog {
        width: auto;
    }

    .popup-body__item {
        flex-wrap: wrap;
    }

    .popup-footer {
        text-align: right;
    }

    .popup-footer .btn {
        margin: 8px;
        margin-left: 8px !important;
    }
}
