.topic-container {
    display: flex;
    padding-top: 16px;
}

.topic-container-content {
    flex: 0 0 50%;
}

.topic-container-content__left {
    padding-right: 32px;
    height: calc(100vh - 225px);
    overflow-y: auto;
}

.topic-content__media {
    border-radius: 12px;
    min-height: 300px;
}

.topic-content__media img {
    border-radius: 12px;
    width: 100%;
    height: 100%;
}

.topic-content__media video {
    border-radius: 12px;
    width: 100%;
}

.topic-content__title {
    width: 100%;
    margin: 7px 0 24px;
}

.topic-content__title span {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-weight: 400;
    font-size: 24px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    color: #000000;
}

.topic-content__sub-title {
    display: flex;
    margin-bottom: 32px;
    height: auto;
    flex-flow: row wrap;
}

.topic-content__sub-title span {
    background: #FFFFFF;
    border: 1px solid #A7A8A9;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 1px 10px;
    margin: 0 12px 10px 0;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    font-weight: 400;
}

.topic-content__sub-title span:last-child {
    margin-right: 0px;
}

.topic-content__description {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    /* min-height: 100px; */
    margin-bottom: 32px;
    font-weight: 400;
    word-break: break-word;
    white-space: pre-line;
}

.topic-content__action-dowload {
    width: 100%;
    margin-bottom: 64px;
}

.topic-content__hashtag {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-end;
}
.topic-content__hashtag span {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #A7A8A9;
    margin-left: 24px;
    margin-bottom: 8px;
}

.topic-container-content__right {
    padding-left: 32px;
    height: calc(100vh - 225px);
    overflow-y: auto;
}

.section-content__title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 16px;
    line-height: 150%;
    letter-spacing: -0.245455px;
    color: #000000;
    margin-bottom: 4px;
    font-weight: 400;
}

.section-content__sub-title {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 400;
    font-size: 11px;
    color: #000000;
    margin-bottom: 24px;
    word-break: break-word;
    white-space: pre-line;
    line-height: 150%;
    letter-spacing: 0.168824px;
}

.section-content__list-media {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding-bottom: 2px;
    margin-bottom: 8px;
}

.section-content_sub-meida {
    width: 80px;
    height: 80px;
    margin-right: 16px;
    background: #C4C4C4;
    flex: 0 0 80px;
    background-size: cover;
    border: 2px solid #FFFFFF;
    border-radius: 4px;
    box-shadow: inset 0 0 1px 1px white;
}

.section-content_sub-meida.list-circle__sub-component {
    width: 26px;
    height: 26px;
    background: transparent;
    border-radius: 50%;
    background-size: cover;
    cursor: pointer;
    border: 1px solid #FFFFFF;
    box-shadow: none;
    margin: 5px;
    flex: 0 0 26px;
}

.section-content_sub-meida.last-selected {
    border: 2px solid #009ace !important;
}

.section-content__title-artist {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    letter-spacing: 0.144706px;
    color: #000000;
    margin-bottom: 5px;
    margin-right: 16px;
    margin-left: 5px;
    max-width: 80px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.section-content__title-artist div {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.section-container .input-radio input:checked ~ .check-mark, .input-radio-input input:checked ~ .check-mark,
.bodytext--13 .input-radio input:checked ~ .check-mark, .input-radio-input input:checked ~ .check-mark{
    border: 1px solid var(--blue-color);
}

.section-container .input-radio .check-mark, .input-radio-input input:checked ~ .check-mark,
.bodytext--13 .input-radio .check-mark, .input-radio-input input:checked ~ .check-mark{
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.section-container .input-radio .check-mark::after,
.bodytext--13 .input-radio .check-mark::after{
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}

.input-radio input:checked ~ .check-mark, .input-radio-input input:checked ~ .check-mark{
    border: 1px solid var(--blue-color);
}

.input-radio .check-mark, .input-radio-input input:checked ~ .check-mark {
    width: 18px;
    height: 18px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.input-radio {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}

.section-container {
    margin-bottom: 64px;
}

.topic-action {
    width: 100%;
    padding: 48px 0px;
    text-align: right;
    border-top: 1px solid #F0F0F0;
    background: #fcfcfc;
    position: relative;
}

.topic-action:before {
    content: " ";
    width: 1000px;
    position: absolute;
    height: 141px;
    left: -1000px;
    background: #fcfcfc;
    top: -1px;
    border-top: 1px solid #f0f0f0;
}

.topic-action:after {
    content: " ";
    width: 1000px;
    position: absolute;
    height: 141px;
    right: -1000px;
    background: #fcfcfc;
    top: -1px;
    border-top: 1px solid #f0f0f0;
}

@media (max-width: 992px) {
    .topic-container {
        display: block;
    }

    .topic-container-content__left {
        padding: 24px;
        border: 1px solid #F0F0F0;
        border-radius: 16px;
        margin-bottom: 32px;
        max-height: none;
        height: auto;
    }

    .topic-container-content__right {
        max-height: none;
        padding: 0 0 124px 0;
        height: auto;
    }

    .section-content_sub-meida {
        width: 48px;
        height: 48px;
        margin-right: 16px;
        background: #C4C4C4;
        flex: 0 0 48px;
    }

    .topic-action {
        position: fixed;
        bottom: 0;
        left: 0;
        padding: 48px 24px;
        background: #fcfcfc;
    }
}

/* Topic form */
.topic-container__topic-form-1 {
    padding: 48px 0px;
    transition: 0.3s linear;
    /* display: none; */
}

.form-container {
    padding: 0px 43px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 12px;
}

.topic-container__topic-form-1 input, .topic-container__topic-form-1 textarea,
.topic-container__topic-form-2 input, .topic-container__topic-form-2 textarea {
    font-weight: 400;
}

#inputbox-toggle {
    height: 46px;
    padding: 10px 16px !important;
}

.topic-form-1__title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 24px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    color: #000000;
    font-weight: 400;
}

.fallback {
    cursor: pointer;
    background: #FFFFFF;
}

.account_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: none !important;
}

.account_upload-file .fallback:hover .dz-button {
    background: #A7A8A9 !important;
    color: #FFFFFF;
    transition: none !important;
}

.account_upload-file .fallback .dz-button {
    color: #A7A8A9;
}

.account_upload-file .fallback .dz-button i {
    font-size: 18px;
}

.account_upload-file .fallback .dz-button p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
}

.account_upload-file .dz-button {
   background: #FFFFFF;
}

.mattach-preview-container {
    margin-bottom: 5px;
}

.mattach-preview-container .determinate {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
}

.mcommment-file__delete {
    color: #000000;
}

.mattach-preview-container .mcommment-file {
    display: inline-flex;
    align-items: center;
    background-color: #F0F0F0 !important;
    border-radius: 4px;
    color: #000000;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    position: relative;
    max-width: 250px;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #000000;
    max-width: 150px;
    font-weight: 400;
}

.tocpic-form-1__upload-video, .topic-form-1__upload-detailed-materials {
    max-width: 335px;
}

.topic-container__topic-form-1 .mattach-preview-container .mattach-previews {
    margin-left: 0px;
    overflow-y: hidden;
}

.topic__field-label {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}

.topic__jp-astarisk-op {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.144706px;
    color: #A7A8A9;
    margin-left: 4px;
}

.topic__jp-astarisk {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.144706px;
    color: #009ACE;
    margin-left: 4px;
}

.dropzone.dz-drag-hover, .dropzone.dz-drag-hover .dz-button {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.label-form {
    margin-top: 24px;
}

.label-form.label-upload-image {
    width: auto;
    max-width: 100%;
}

.topic__field-label-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    letter-spacing: 0.144706px;
    color: #000000;
    font-weight: 400;
    margin-bottom: 7px;
}

.topic-upload__thumbnail {
    width: 160px;
    height: 90px;
    background: #C4C4C4;
    border-radius: 12px;
    margin-bottom: 7px;
    background-size: cover;
}

.topic-upload__image {
    width: 68px;
    height: 96px;
    background: #f0f0f0;
    border-radius: 12px;
    margin-bottom: 7px;
    background-size: cover;
    border-radius: 12px;
}

.topic-upload__thumbnail img{
    border-radius: 12px;
    width: 100%;
}

.btn-upload-thumbnail {
    width: 75px;
    height: 36px;
    padding: 8px 24px !important;
}

.input-form-container {
    max-width: 480px;
}

textarea[name='topic_detail'], .topic-container__topic-form-1 .form-textarea, .topic-container__topic-form-2 .form-textarea {
    padding: 12px 12px 12px 16px;
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 4px;
    height: auto;
    color: #000000;
}

textarea[name='topic_detail']:focus, .form-textarea:focus {
    border: 1px solid #D3D3D3 !important;
    outline: none !important;
}

textarea[name='topic_detail']::placeholder, .form-textarea::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 500;
    letter-spacing: 0.168824px;
    color: #D3D3D3;
    opacity: 1; /* Firefox */
}

textarea[name='topic_detail']:-ms-input-placeholder, .form-textarea:-ms-input-placeholder { /* Internet Explorer 10-11 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 500;
    letter-spacing: 0.168824px;
    color: #D3D3D3;
}

textarea[name='topic_detail']::-ms-input-placeholder, .form-textarea::-ms-input-placeholder { /* Microsoft Edge */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 500;
    letter-spacing: 0.168824px;
    color: #D3D3D3;
}

.topic-form-1__choices-container {
    border-top: 1px solid #F0F0F0;
    padding: 32px 0px;
    margin-top: 32px;
}

.choice-component {
    display: flex;
    max-width: 511px;
    margin-bottom: 10px;
}

.choice-content {
    padding: 24px;
    border: 2px solid #F0F0F0;
    width: 100%;
    border-radius: 12px;
    background: #FFFFFF;
    cursor: pointer;
}

.choice-content .data-container {
    display: none;
}

.choice-title {
font-family: 'A+mfCv-AXISラウンド 50 R StdN';
font-size: 13px;
line-height: 150%;
letter-spacing: 0.168824px;
margin-bottom: 4px;
color: #000000;
font-weight: 400;
}

.choice-description {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    letter-spacing: 0.144706px;
    color: #000000;
    margin-bottom: 8px;
    font-weight: 400;
    word-break: break-word;
    white-space: pre-line;
}

.choice-list-media {
    display: flex;
    flex-flow: row wrap;
}

.choice-list-media__component {
    width: 24px;
    height: 24px;
    background: #C4C4C4;
    border-radius: 50%;
    background-size: cover;
    margin-bottom: 2px;
    margin-right: 4px;
    border: 1px solid #C4C4C4;
}

.choice-action__button-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 12px;
    opacity: 0;
    pointer-events: none;
}

.choice-action {
    width: 31px;
}

.choice-component:hover .choice-action__button-container {
    opacity: 1;
    pointer-events: all;
}

.choice-action__button-container span {
    padding: 2px 0px;
    font-size: 19px;
    color: #A7A8A9;
}

.choice-action__button-container span:hover {
    color: #009ace;
    cursor: pointer;
}

.topic-form-1__footer-container, .topic-form-2__footer-container, .topic-form-3__footer-container {
    padding: 32px 0px;
    border-top: 1px solid #F0F0F0;
    display: flex;
    justify-content: flex-start;
}

.topic-form-2__footer-container {
    margin-top: 32px;
    display: flex;
}

.list-choices__btn-add-choice {
    border: 1px solid #F0F0F0;
    border-radius: 12px;
}

.list-choices__btn-add-choice {
    max-width: 480px;
    margin-top: 10px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    text-align: center;
    color: #A7A8A9;
}

.list-choices__btn-add-choice i {
    font-size: 20px;
    margin-bottom: 4px;
}

.list-choices__btn-add-choice p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 400;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
}

.list-choices__btn-add-choice:hover {
    cursor: pointer;
    background: #A7A8A9;
    color: #FFFFFF;
}

.topic-container__topic-form-2 {
    padding: 24px 0px 48px 0px;
    display: none;
    transition: 0.3s linear;
}

.topic-container__topic-form-3 {
    padding: 24px 0px 48px 0px;
    display: none;
    transition: 0.3s linear;
}

.exhibition-works__component-action-container {
    display: flex;
    justify-content: space-between;
    opacity: 0;
    pointer-events: none;
}

.exhibition-works__component-container {
    width: 75px;
    flex: 0 0 75px;
    margin-right: 16px;
}

.exhibition-works__component-container:hover .exhibition-works__component-action-container {
    opacity: 1;
    pointer-events: all;
}

.exhibition-works__component-content {
    width: 75px;
    height: 75px !important;
    background: #C4C4C4;
    border-radius: 4px;
    margin: 0px !important;
    background-size: cover;
}

.exhibition-works-list__container {
    display: flex;
    max-width: 480px;
    overflow-x: auto;
}

.exhibition-works__component-action-container span {
    padding: 2px 0px;
    font-size: 18px;
    color: #A7A8A9;
}

.exhibition-works__component-action-container span:hover {
    color: #009ace;
    cursor: pointer;
}

.exhibition-works__button-add {
    width: 75px;
    min-width: 75px;
    height: 75px;
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    text-align: center;
    justify-content: center;
    background: #FFFFFF;
}

.exhibition-works__button-add i {
    font-size: 19px;
    color: #A7A8A9;
}

.exhibition-works__button-add p {
    color: #A7A8A9;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    margin-bottom: 0;
    font-weight: 400;
}

.exhibition-works__button-add:hover {
    background: #A7A8A9;
    cursor: pointer;
}

.exhibition-works__button-add:hover p{
    color: #FFFFFF;
}

.exhibition-works__button-add:hover i{
    color: #FFFFFF;
}

.radio-component {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.radio-action, .toggle-input-action {
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.radio-component:hover .radio-action-button-container, .toggle-input-container:hover .toggle-input-button-container {
    opacity: 1;
    pointer-events: all;
}

.radio-action-button-container, .toggle-input-button-container {
    opacity: 0;
    pointer-events: none;
    width: 18px;
}

.radio-action-button-container span, .toggle-input-button-container span {
    color: #A7A8A9;
    font-size: 18px;
}

.radio-action-button-container span:hover, .toggle-input-button-container span:hover {
    color: #009ace;
    cursor: pointer;
}

.list-radio__add-action, .list-toggle__add-action {
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 4px;
    max-width: 452px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    align-content: center;
    padding: 10px 0px;
    color: #A7A8A9;
    background: #FFFFFF;
}

.list-radio__add-action:hover, .list-toggle__add-action:hover {
    color: #FFFFFF;
    background: #A7A8A9;
    cursor: pointer;
}

.list-radio__add-action p, .list-toggle__add-action p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    margin-bottom: 0;
    font-size: 13px;
    line-height: 150%;
    font-weight: 400;
}

.list-radio__add-action i, .list-toggle__add-action i {
    font-size: 19px;
}

.toggle-input-container {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
}

#modal-detail-topic.modal {
    margin-top: 65px;
    background-color: #FFFFFF !important;
}

#modal-detail-topic .popup-body {
    min-height: calc(100% - 70px);
}

#modal-detail-topic .modal-dialog {
    width: 100%;
    height: 100%;
    margin: 0;
    border: none;
    border-radius: 0;
}

#modal-detail-topic .popup-content {
    margin: auto;
    height: 100%;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

@media (max-width: 576px) {
    #modal-detail-topic.modal {
        top: 0;
    }
}


.custom-switch {
    display: flex;
    align-items: center;
    margin-right: 5px;
}

.topic-form-3__upload-file-work {
    max-width: 335px;
}

.toggle-text-topic {
    font-size: 13px;
    line-height: 100%;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    color: #000000;
    font-weight: 400;
}

.check-to-add-into-list {
    display: none;
    opacity: 0;
}
/* End topic form */


.disabledbutton {
    pointer-events: none;
}


/* Set color topic form 2 */
.exhibition-works__component-action-setcolor {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 2px;
}

.avatar-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 4px;
    border: 1px solid #C4C4C4;
    background-color: #C4C4C4;
}

.value-color {
    cursor: pointer;
}

.choice-list-color__value, .choice-list-color__style {
    display: none;
}

.icon--sicon-close {
    cursor: pointer;
}

#modal-confirm-step:before,
#modal-contact-artist:before,
#modal-create-success:before,
#modal-contact-artist-profile:before {
    content:"";
    display:inline-block !important;
    width:0;
    height:100% !important;
    vertical-align:middle !important;
}

@media (max-width: 576px) {
    #modal-confirm-step:before,
    #modal-contact-artist:before,
    #modal-create-success:before,
    #modal-contact-artist-profile:before {
        height:0 !important;
    }
}

#modal-confirm-step,
#modal-contact-artist,
#modal-create-success,
#modal-contact-artist-profile {
    vertical-align: middle !important;
}

#modal-confirm-step .modal-dialog.popup-dialog,
#modal-contact-artist .modal-dialog.popup-dialog,
#modal-create-success .modal-dialog.popup-dialog,
#modal-contact-artist-profile .modal-dialog.popup-dialog {
    vertical-align: middle;
    top: 0;
}

@media (max-width: 576px) {
    #modal-confirm-step.modal,
    #modal-contact-artist.modal,
    #modal-create-success.modal,
    #modal-contact-artist-profile.modal {
        top: 0;
    }
    #modal-confirm-step .modal-dialog.popup-dialog,
    #modal-contact-artist .modal-dialog.popup-dialog,
    #modal-create-success .modal-dialog.popup-dialog,
    #modal-contact-artist-profile .modal-dialog.popup-dialog {
        vertical-align: middle;
        top: 50% !important;
        width: auto !important;
        -webkit-transform: translateY(-54%) !important;
        -ms-transform: translateY(-54%) !important;
        transform: translateY(-54%) !important;
    }
}

#modal-confirm-step .popup-body .popup-body__wrap,
#modal-confirm-step .popup-body .popup-body__wrap .popup-body__item {
    margin-bottom: 12px;
}

.order-step__submit.order-step__action {
    text-align: right !important;
    margin: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-top: 48px;
    background: #fcfcfc;
    width: 100%;
    padding-bottom: 48px;
    border-top: 1px solid #F0F0F0;
    z-index: 100;
}

.topic-action .btn.btn--tertiary,
.topic-action .btn.btn--tertiary:focus,
.order-step__submit.order-step__action .btn.btn--tertiary,
.order-step__submit.order-step__action .btn.btn--tertiary:focus {
    background-color: transparent !important;
}
