.smodal .modal-dialog {
    width: 558px;
    max-width: 100%;
    /* margin-top: 75px; */
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}
.modal-header{
    border:none !important;
}

.smodal .modal-header {
    padding: 24px;
}

.smodal .modal-content {
    border: none;
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
}

.smodal .modal-body {
    padding: 24px;
    max-height: calc(100vh - 50px);
    padding-top: 12px;
}
/*
.smodal--large .modal-dialog {
    width: 752px;
}
*/

.smodal--large .smodal-close {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    color: #53565a;
    text-align: center;
}

.smodal--large .smodal-close .icon {
    margin-left: -3px;
}

.smodal .smodal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    color: #a7a8a9;
    z-index: 9;
    font-size: 20px;
}

.smodal .smodal-close:hover {
    cursor: pointer;
    color: #009ace;
}

.smodal .smodal-close--prev {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    color: #53565a;
    text-align: center;
}

.smodal .smodal-close--prev .icon {
    margin-left: -3px;
}

/*.smodal--video .modal-content {*/
/*  background: transparent;*/
/*  box-shadow: none; }*/

.smodal--video .modal-dialog {
    background-color: transparent;
}

@media (max-width: 992px) {
    .smodal {
        margin: 0 10px;
    }
}

.smodal--video .smodal-close {
    /* stylelint-disable-line */
    color: #fff;
    top: 5px;
    right: 10px;
}

@media (max-width: 992px) {
    .smodal--video .smodal-close {
        right: 5px;
    }
}

.smodal--video .smodal-close:hover {
    color: #009ace;
}

/*.smodal--video .modal-body {*/
/*  padding: 0;*/
/*  overflow: hidden; }*/

.smodal--image .modal-dialog,
.smodal--video .modal-dialog {
    width: auto;
}

.smodal--image .modal-content,
.smodal--video .modal-content {
    background: transparent;
    box-shadow: none;
    margin: 60px 0 0;
    height: 80vh;
    width: auto;
    max-width: 80vw;
}

.video-popup video {
    height: 80vh;
    width: auto;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.smodal--image .modal-content .image-popup__content {
    height: 80vh;
    width: 100vw;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: #000;
}

.smodal--image .modal-content .image-popup__content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

@media screen and (max-aspect-ratio: 12/10) {
    .smodal--image .modal-dialog,
    .smodal--video .modal-dialog {
        width: 100%;
    }

    .smodal--image .modal-content,
    .smodal--video .modal-content {
        height: 80vh;
        max-height: none;
        width: 100%;
        max-width: none;
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: #000;
    }

    .video-popup video {
        height: auto;
        width: calc(100vw - 16px);
    }
}

.smodal--image .smodal-close {
    /* stylelint-disable-line */
    color: #fff;
    top: 5px;
    right: 0;
}

.member-ip__text{
    margin-bottom: 20px !important;
    color: #53565A;
    margin-top: 10px;
}

.member-ip__action .member-ip__send{
    margin: auto;
    width: 190px;
    /* display: block; */
}

.member-item__action .btn-cancel_invitation{
    margin-right: 10px;
    border-radius:5px;
}

.member-item__action .btn-cancel_invitation button {
    text-decoration: none !important;
    color: #009ACE !important;
    border: 1px solid #009ACE !important;
    font-size: 13px !important;
}

.member-item__action .btn-cancel_invitation button:hover {
    text-decoration: none !important;
    color: #007096 !important;
    border: 1px solid #007096 !important;

}

.member-item__action .btn-resend-invitation button{
    background-color: #009ACE !important;
    border: 0 solid #007096 !important;
    font-size: 13px;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: unset !important;
}

.btn-primary.active, .btn-primary:active, .open>.dropdown-toggle.btn-primary {
    border: none !important;
}

.member-item__action .btn-resend-invitation button:hover{
    background-color: #007096 !important;
}

.member-manage__list .member-item-left{
    display: flex;
    flex-grow: 1;
}

.member-manage__list .member-item-left .member-item__name {
    width: 150px;
    font-style: normal;
    font-weight: normal;
    font-size: 13px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

.sheading--invite {
    color: #53565A;
    font-weight: unset;
    font-size: 9px;
}

.member-manage__heading.sheading.sheading--16 {
    font-size: 15px;
    margin-bottom: 4px;
}

.member__ip-delete {
    position: absolute;
    margin-top: 4px;
    left: 200px;
}

.hide-delete {
    display: none;
}

.active-delete {
    display: unset !important;
}

.member-manage__list .member-item-left .member-item__role {
    color: #53565A;
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    position: static;
    left: 0;
    top: 25px;
    margin-right: 8px;
    overflow: hidden;
    display: block;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    text-overflow: ellipsis;
}

.member-manage__list .member-item-left .member-item__company {
    color: #53565A;
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    position: static;
    left: 0px;
    top: 47px;
    margin-right: 8px;
    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.member-manage__list .member-item-right{
    position:relative;
    margin-right: 24px;
    width: 150px;
}
.member-manage__list .member-item-right .ip-edit-delete{
    display: flex;
    position: absolute;
    bottom: 0;
    height: 18px;
}

.member-manage__list .member-item-right .icon{
    color: #A7A8A9;
    font-size: 18px;
}

.member-manage__list .member-item-right .switch-label{
    font-size: 13px;
    color: #000;
}

.member-manage__list .member-item-right .icon--sicon-pencil{
    margin-right: 10px;
}

.member-manage__list .member-item-right .icon--sicon-pencil:hover{
    color: #0f9ca9;
}

.member-manage__list .member-item-right .list-ip{
    color: #53565A;
    font-weight: 300;
    width: 120px;
    margin-left: 10px;
}

.member-manage__list .member-item-right .list-ip .detail-list-ip {
    width: 90%;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
    cursor: pointer;
    padding: 5px;
}

.member-manage__list .member-item-right .list-ip .detail-list-ip .detail-text {
    color: #A7A8A9;
}

.member-manage__list .member-item-right .custom-switch{
    width: 115px;
    margin: auto;
    margin-top: 8px;
}


@media (max-width: 992px) {
    .smodal--image .smodal-close {
        right: 16px;
    }
    .member-item__name {
        width: 155px;
        font-weight: 400;
        font-size: 13px;
        word-break: break-word;
    }
    .member-item__action {
        margin-top: 5px;
    }

    .member-item__action button {
        padding: 3px 2px;
        font-size: 11px;
        line-height: 24px;
        max-height: 30px;
    }
    .avatar--round{
        width: 32px;
        height: 32px;
    }
        .member-manage__list .member-item-left{
        width: 200px;
    }
    .member-manage__list .member-item-right .ip-edit-delete{
        height: 5px;
    }
}

@media (max-width: 739px) {
    .member-manage__list .member-item-left .member-item__name {
        width: 120px;
    }

    .member__ip-delete {
        left: 173px;
    }
}

@media (min-width: 740px) and (max-width: 1023px) {
    .member-manage__list .member-item-left .member-item__name {
        width: 150px;
    }

    .member__ip-delete {
        left: 200px;
    }
}

.smodal--image .smodal-close:hover {
    color: #009ace;
}

.smodal--image .modal-body {
    padding: 0;
    overflow: hidden;
}

@media (max-width: 992px) {
    .smodal--document .modal-dialog {
        padding: 0 15px;
    }
}

.smodal--document .modal-content {
    background: transparent;
    overflow: hidden;
    margin: 60px 0 0;
}

.smodal--document .smodal-download, .smodal--image .smodal-download, .smodal--video .smodal-download {
    position: absolute;
    right: 0 !important;
    top: calc(100% + 30px) !important;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    /* bottom: -64px; */
    display: flex;
    justify-content: center;
    align-items: center;
}

.smodal--image .image-popup__title, .smodal--video .video-popup__title, .smodal--document .document-popup__title {
    position: absolute;
    right: calc(50% - 150px);
    top: calc(100% + 30px);
    width: 300px;
    color: #fff;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    padding: 6px 16px;
    text-align: center;
    /* background-color: rgba(255, 255, 255, 0.1); */
    /* height: 40px; */
    /* border-radius: 50%; */
    /* line-height: 40px; */
    /* bottom: -64px; */
}

.smodal--image .smodal-close,
.smodal--video .smodal-close,
#modal-document-popup .smodal-close,
#modal-confirm-upload .smodal-close,
#modal-pdf-approve-popup .smodal-close {
    top: 0;
    left: 545px;
}

#modal-document-popup .modal-dialog, #modal-document-popup .modal-content, #modal-document-popup .document-popup__content,
#modal-pdf-approve-popup .modal-dialog, #modal-pdf-approve-popup .modal-content, #modal-pdf-approve-popup .document-popup__content, 
#modal-ACR-check-popup .modal-content
{
    max-height: 75vh;
    width: 50vw;
}

#modal-confirm-upload .modal-dialog, #modal-confirm-upload .modal-content, #modal-confirm-upload .document-popup__content {
    max-height: 85vh;
    width: 50vw;
}

#modal-document-popup,
#modal-confirm-upload,
#modal-ACR-check-popup,
#modal-pdf-approve-popup {
    background-color: unset !important;
}

#modal-document-popup .modal-dialog,
#modal-confirm-upload .modal-dialog,
#modal-ACR-check-popup .modal-dialog,
#modal-pdf-approve-popup .modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: unset !important;
}

#modal-document-popup .modal-content,
#modal-confirm-upload .modal-content,
#modal-ACR-check-popup .modal-content,
#modal-pdf-approve-popup .modal-content {
    height: 100%;
    margin: 0px !important;
    border-radius: 12px;
    position: relative;
}

#modal-ACR-check-popup  .document-popup__content {
    padding: 60px 12px 32px;
    max-height: 75vh;
}

#modal-document-popup .document-popup__content,
#modal-pdf-approve-popup .document-popup__content {
    padding: 56px 8px 62px 8px;
}

#modal-confirm-upload .document-popup__content {
    padding: 80px 12px 153px;
}

#modal-document-popup iframe,
#modal-pdf-approve-popup iframe {
    max-width: 100%;
    min-height: calc(75vh - 62px - 56px) !important;
    border-radius: 8px;
}

#modal-confirm-upload iframe {
    max-width: 100%;
    min-height: calc(85vh - 153px - 80px) !important;
    border-radius: 8px;
}

#modal-confirm-upload.no-button iframe {
    max-width: 100%;
    min-height: calc(85vh - 32px - 80px) !important;
    border-radius: 8px;
}

#modal-confirm-upload.no-button .document-popup__content {
    padding: 80px 12px 32px;
}

#modal-confirm-upload .smodal-close, #modal-document-popup .smodal-close, #modal-image-popup .smodal-close, #modal-video-popup .smodal-close,
#modal-pdf-approve-popup .smodal-close, #modal-pdf-approve-popup .smodal-close, #modal-pdf-approve-popup .smodal-close, #modal-ACR-check-popup .smodal-close {
    position: absolute;
    left: 12px;
    top: 24px !important;
    color: #A7A8A9;
    width: 24px;
    height: 24px;
}

#modal-confirm-upload .smodal-close {
    top: 32px !important;
}

#modal-confirm-upload .smodal-close:hover svg path, #modal-document-popup .smodal-close:hover svg path, #modal-image-popup .smodal-close:hover svg path, #modal-video-popup .smodal-close:hover svg path,
#modal-pdf-approve-popup .smodal-close:hover svg path, #modal-pdf-approve-popup .smodal-close:hover svg path, #modal-pdf-approve-popup .smodal-close:hover svg path,
#modal-ACR-check-popup .smodal-close:hover svg path {
    fill: #009ace;
}

#modal-ACR-check-popup .smodal-close {
    margin-left: 12px;
}

@media (max-width: 992px) {
    #modal-document-popup .modal-dialog, #modal-document-popup .modal-content, #modal-document-popup .document-popup__content,
    #modal-pdf-approve-popup .modal-dialog, #modal-pdf-approve-popup .modal-content, #modal-pdf-approve-popup .document-popup__content,
    #modal-ACR-check-popup .modal-dialog {
        max-height: 75vh;
        width: 100% !important;
    }

    #modal-confirm-upload .modal-dialog, #modal-confirm-upload .modal-content, #modal-confirm-upload .document-popup__content {
        max-height: 85vh;
        width: 100% !important;
    }

    #modal-document-popup, #modal-image-popup, #modal-video-popup, #modal-pdf-approve-popup, #modal-confirm-upload, #modal-ACR-check-popup {
        margin: 0;
    }

    #modal-document-popup .smodal-close,
    #modal-confirm-upload .smodal-close,
    #modal-pdf-approve-popup .smodal-close,
    #modal-ACR-check-popup .smodal-close {
        left: 8px;
    }

    #modal-ACR-check-popup .modal-content {
        width: 100%;
    }

    #modal-image-popup .modal-dialog, #modal-video-popup .modal-dialog {
        width: auto !important;
        max-width: calc(100vw - 32px) !important;
    }

    #modal-image-popup img, #modal-video-popup video {
        width: auto !important;
        max-width: calc(100vw - 50px) !important;
        height: auto;
        max-height: calc(75vh - 56px - 62px);
    }
}

#modal-image-popup .modal-dialog, #modal-video-popup .modal-dialog{
    background: transparent;
    box-shadow: none;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    position: fixed;
    width: unset;
    max-width: 50vw;
    max-height: 75vh;
}

#modal-image-popup .modal-content, #modal-video-popup .modal-content {
    max-width: 100% !important;
    max-height: 75vh;
    background: #FFFFFF;
    padding: 56px 8px 62px 8px;
    border-radius: 12px;
    margin: 0;
    height: unset;
}

#modal-image-popup .image-popup, #modal-image-popup .image-popup__content, #modal-image-popup img,
#modal-video-popup .video-popup, #modal-video-popup .video-popup__content, #modal-video-popup video {
    max-width: 100%;
    max-height: calc(75vh - 56px - 62px);
    border-radius: 8px;
    width: auto;
}

#modal-confirm-upload .modal-content, #modal-document-popup .modal-content, #modal-image-popup .modal-content, #modal-video-popup .modal-content, #modal-pdf-approve-popup .modal-content,
#modal-ACR-check-popup .modal-content {
    filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.05));
    box-sizing: border-box;
    border: 1px solid #F0F0F0;
}

.smodal[id="modal-image-popup"], .smodal[id="modal-video-popup"], .smodal[id="modal-document-popup"] {
    background-color: transparent !important;
}

#modal-video-popup .video-popup, #modal-video-popup .video-popup__content, #modal-video-popup video{
    transform: none;
    height: unset;    
    position: static;
}

#modal-image-popup .image-popup__content, #modal-video-popup .video-popup__content {
    height: unset;
    width: unset;
    position: unset;
    left: unset;
    top: unset;
    transform: unset;
    display: unset;
    flex-direction: unset;
    justify-content: unset;
    background: unset;
}

#modal-image-popup:before, #modal-video-popup:before, #modal-confirm-upload:before, #modal-document-popup:before, #modal-pdf-approve-popup:before {
    height: 0px;
}

.smodal--document .document-popup__title {
    padding: 3px 16px !important;
    height: unset !important;
    line-height: unset;
}

.document-popup__title .file-name_modal {
    word-break: break-all;
    width: 100%;
}

.smodal--image .image-popup__title, .smodal--video .video-popup__title {
    margin-bottom: 0px;
}

.smodal--document, .smodal--image, .smodal--video {
    top: 0 !important;
}

@media (max-width: 992px) {
    .smodal--document .smodal-download, .smodal--image .smodal-download, .smodal--video .smodal-download {
        left: auto;
        top: auto;
        width: 30px;
        height: 30px;
        line-height: 30px;
        bottom: -54px;
    }
}

.smodal--document .smodal-close {
    /* stylelint-disable-line */
    color: #fff;
    top: 5px;
    right: 10px;
}

@media (max-width: 992px) {
    .smodal--document .smodal-close {
        right: 5px;
    }
}

.smodal--document .smodal-close:hover {
    color: #009ace;
}

.smodal--document .modal-body {
    padding: 0;
}

.smodal--document embed {
    min-height: 66vh;
}

.smodal--document iframe {
    min-height: 80vh;
    width: 100%;
}

@media (max-width: 992px) {
    .smodal--document iframe {
        min-height: 70vh;
    }
}


#modal-invite-member .modal-header {
  border-bottom: none;
  padding-bottom: 8px; }


.member-manage__list .icon--sicon-trash {
    color: #A7A8A9;
    font-size: 18px;
}

.member-manage__list .icon--sicon-trash:hover {
    color: #0f9ca9;
}

.account_upload-file {
    margin-right: -25px;
}

.mattach {
    position: relative;
    z-index: 99;
}

 #create-offer-upload-form {
    width: 100%;
    padding: 24px;
    cursor: pointer;
    border: 1px dashed #D3D3D3;
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 0px;
    margin-bottom: 0px;
}

.dropzone .dz-default.dz-message {
    margin: 0;
}

.dropzone .dz-message .dz-button {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
}

.account_upload-file .dz-button .icon {
    font-size: 20px;
    color: var(--black2-color);
}

.mattach-preview-container .mcommment-file .determinate {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropzone.dz-drag-hover {
    background-color: #009ACE !important;
    border: 1px dashed #009ACE !important;
    color: white !important;
}

.dropzone.dz-drag-hover .icon, .dropzone.dz-drag-hover p {
    color: white !important;
}

.account_upload-file .fallback:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
}

.account_upload-file .fallback:hover .icon, .account_upload-file .fallback:hover p{
    color: white !important;
}

.mattach-previews {
    margin-left: 0 !important;
}

.mcomment-attached:not(:empty) {
    padding-left: 0 !important;
}

.mattach-preview-container .mcommment-file__name {
    color: white;
}

.modal-content .mattach-preview-container .mattach-previews {
    overflow-y: hidden !important;
}

.mattach-preview-container .mcommment-file {
    background-color: #F0F0F0 !important;
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
}

.mattach-preview-container .mcommment-file .mcommment-file__name p{
    display: inline !important;
}


.mcommment-file__delete .icon, .mcommment-file__name .icon {
    color: #53565a;
}

.file-name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    display: inline;
}

.mattach-info-file .mcommment-file {
    display: inline-flex;
    align-items: center;
    background-color: #e4e4e4;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    position: relative;
}

.mattach-info-file .mcommment-file .mcommment-file__name {
    font-size: 11px;
    line-height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
    max-width: 100px;
    margin-right: 10px;

}

@media (max-width: 992px) {
    #create-offer-upload-form {
        width: 100%;
    }
}

@media (max-width: 768px) {
     #create-offer-upload-form {
        width: 100%;
    }
}

/* Modal invite member */
.invite-member__form .custom-switch .switch-label {
    color: #000;
}
/* End modal invite member */

.album_title_in_modal-title {
    max-width: calc(100% - 100px);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 13px;
    color: #000;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    line-height: 20px;
}

.album_title_in_modal-hint {
    margin-left: 8px;
    font-size: 8px;
    color: #000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    max-width: 100px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.album_title_in_modal-subtitle {
    font-size: 13px;
    color: #000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.album_title_in_modal-title-hint {
    font-size: 11px;
    color: #000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.album_title_in_modal {
    display: flex;
    align-items: end;
    margin-top: 8px;
}

.album_desc_in_modal {
    display: flex;
    align-items: start;
    flex-direction: column;
    margin-top: 6px;
}

.album_desc_in_modal .album_title_in_modal-subtitle,
.album_desc_in_modal .album_title_in_modal-title-hint {
    margin-bottom: 6px;
    line-break: anywhere;
    white-space: pre-line;
}

.contact__field-label {
    margin-bottom: 4px;
}

/* Modal ACR check */
.ACR-check-result-container {
    display: flex;
    justify-content: start;
    align-items: flex-start;
    flex-direction: column;
    background: #009ACE;
    border-radius: 4px;
    padding: 12px 8px;
    width: 100%;
    margin-bottom: 16px;
}

.ACR-check-result-container:last-child {
    margin-bottom: 0;
}

.ACR-check-detail-content-top {
    font-weight: 400;
    font-size: 13px;
    line-height: 200%;
    margin-bottom: 8px;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
}

.master-match {
    background-color: #009ACE;
}

.cover-match {
    background-color: #A7A8A9;
}

.custom-match {
    background-color: #FCFCFC;   
}

.master-match .ACR-check-detail-content-top, .cover-match .ACR-check-detail-content-top {
   color: #FFFFFF;
}

.custom-match .ACR-check-detail-content-top {
   color: #000000 
}

.ACR-check-detail-content-item {
    display: flex;
    justify-content: start;
    align-items: flex-start;
   
}

.ACR-check-detail-content-bottom:not(:last-child) {
    margin-bottom: 8px;
}

.ACR-check-detail-content-bottom {
    width: 100%;
    padding: 8px;
    background-color: #FFFFFF;
    border-radius: 4px;
}

.ACR-item-left {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    min-width: 48px;
}

.ACR-match-num {
    width: 24px;
    height: 24px;
    border-radius: 10px;
    border: 1px solid #FFFFFF;
    background: #A7A8A9;
    font-weight: 400;
    font-size: 8px;
    line-height: 150%;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #FFFFFF;
}

.ACR-item-middle {
    display: flex;
    flex-direction: column;
    justify-content: start;
    width: 100%;
}

.ACR-item-title {
    font-weight: 400;
    font-size: 13px;
    line-height: 200%;
    color: #000000;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
}

.ACR-item-artist-name {
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    color: #000000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.ACR-item-artist-match-time {
    display: flex;
    justify-content: start;
    align-items: flex-start;
}

.ACR-item-artist-list-match {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-right: 32px;
}

.ACR-item-artist-list-match-item {
    font-weight: 300;
    font-size: 11px;
    line-height: 200%;
    color: #000000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.ACR-item-artist-total-match {
    font-weight: 300;
    font-size: 11px;
    line-height: 200%;
    color: #000000;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.ACR-item-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin: auto;
}

.ACR-item-right:hover {
    cursor: pointer;
}

.ACR-item-right:hover .icon {
    color: #009ace;
}

.ACR-item-right .icon {
    font-size: 17px;
    color: #A7A8A9;
}

.full-match {
    background-color: #009ACE;
}

.ACR-check-popup__content-list-match {
    max-height: calc(75vh - 92px);
}

.ACR-image-album-thumb {
    display: flex;
    flex-direction: column;
}

.ACR-image-album-thumb img {
    width: 48px;
    height: 48px;
    border-radius: 3px;
    background-color: #f0f0f0;
}

.ACR-matching-percent {
    margin-top: 4px;
}

.ACR-image-album-thumb .ACR-percent-bar-border {
    width: 100%;
    height: 4px;
    background-color: #F0F0F0;
    border-radius: 4px;
    position: relative;
    margin-bottom: 2px;
}

.ACR-image-album-thumb .ACR-percent-bar-border .ACR-matching-percent-child {
    height: 4px;
    background-color: #009ACE;
    border-radius: 4px;
    position: absolute;
    left: 0;
    top: 0;
}

.ACR-image-album-thumb .ACR-percent-num {
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
    color: #009ACE;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}
/* End modal ACR check */
