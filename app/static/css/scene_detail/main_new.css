/* Color, font-size */
:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-size-40: 40px;
    --line-height-60: 60px;
    --font-size-32: 32px;
    --line-height-48: 48px;
    --font-size-24: 24px;
    --line-height-36: 36px;
    --font-size-18: 18px;
    --line-height-27: 27px;
    --font-size-16: 16px;
    --line-height-24: 24px;
    --font-size-13: 13px;
    --line-height-20: 20px;
    --font-size-11: 11px;
    --line-height-17: 17px;
    --font-size-8: 8px;
    --line-height-12: 12px;
    --font-weight-300: 300;
    --font-weight-400: 400;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}
/* End color, font-size  */

/* Common */
.container-wrap {
    position: relative;
    margin-top: 65px;
    background-color: var(--background-color);
}

.text--center {
    text-align: center !important;
}
/* End common */

/* Sidebar */
.user-info__wrap {
    float: left;
    position: fixed;
    top: 155px;
    background-color: var(--background-color);
    z-index: 2;
    width: 200px;
}

.user-info__main.account__info {
    margin-top: 0;
    /* padding-top: 0; */
    float: right;
    width: calc(100% - 200px);
}

.user-info__tabs-list {
    width: 70%;
}

.nav .nav-item a.nav-link {
    font-family: var(--font-family-R);
    font-size: 13px;
     
    line-height: var(--line-height-20);
    color: var(--grey1-color);
    background-color: var(--background-color);
    padding: 8px 16px;
}

.nav .nav-item a.nav-link.active, .nav .nav-item a.nav-link.active:hover,
.nav .nav-item a.nav-link:hover, .nav .nav-item a.nav-link.active:focus {
    color: var(--black1-color);
    background-color: var(--background-color);
    font-weight: var(--font-weight-400);
}
/* End sidebar */

/* Text link */
a {
     
    line-height: var(--line-height-20);
    color: var(--blue-color);
    text-decoration: none;
}

a:hover {
    color: var(--soremo-deep-blue);
}
/* End text link */

/* Input, select, textarea, button */
.form-group input.form-control, .form-group select.form-control {
    padding: 11px 15px;
    color: var(--black1-color);
    border: 1px solid var(--soremo-border);
    background-color: var(--white-color);
    height: auto;
    font-size: 13px;
    border-radius: 4px !important;
    line-height: var(--line-height-20);
}

.form-group select.form-control {
    cursor: pointer;
}

.form-textarea textarea {
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    padding: 11px 15px;
    font-size: 13px;
    line-height: var(--line-height-20);
    color: var(--black1-color);
    resize: none;
}

input, select, textarea {
    box-shadow: none !important;
    -webkit-appearance: none !important;
}

.form-group input.form-control:focus, .form-textarea textarea:focus, select:focus-visible {
    border: 1px solid var(--soremo-placeholder);
    outline: none !important;
}

.form-group input.form-control::placeholder, .form-textarea textarea::placeholder {
    color: var(--soremo-placeholder) !important;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

.btn.btn--primary, .btn.btn--secondary, .btn.btn--tertiary {
    font-family: var(--font-family-R) !important;
    font-size: 13px !important;
    font-weight: var(--font-weight-400);
    line-height: var(--line-height-20);
    background-image: none;
    border-radius: 4px;
    border: none !important;
    padding: 12px 24px !important;
    cursor: pointer;
}

.btn.btn--primary.btn--disabled, .btn.btn--primary:focus.btn--disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #F0F0F0 !important;
}

.btn.btn--primary, .btn.btn--primary:focus {
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
    min-width: 160px;
}

.btn.btn--primary:hover {
    color: var(--white-color) !important;
    background-color: var(--soremo-deep-blue) !important;
}

.btn.btn--secondary, .btn.btn--secondary:focus {
    color: var(--white-color) !important;
    background-color: var(--grey1-color) !important;
}

.btn.btn--secondary:hover {
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
}

.btn.btn--tertiary.close-modal-offer, .btn.btn--tertiary.close-modal-offer:focus, .btn.btn--tertiary.close-modal-offer:hover {
    background-color: transparent !important;
    margin-right: 0;
}

.btn.btn--tertiary, .btn.btn--tertiary:focus {
    color: var(--black2-color) !important;
    background-color: var(--white-color) !important;
}

.btn.btn--tertiary:hover {
    color: var(--blue-color) !important;
    background-color: var(--white-color) !important;
}

.btn.btn--primary.disable, .btn.btn--primary.disabled,
.form-group input.form-control.disabled, .form-group select.form-control.disabled {
    pointer-events: none;
    background-color: var(--soremo-border) !important;
    color: var(--grey1-color) !important;
    background-image: none;
}

input.mcalendar {
    cursor: pointer;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    margin-left: 8px;
}

input[type=number] {
    -moz-appearance:textfield;
}
/* End input, select, textarea, button */

/* Heading, Text */
.heading--40 {
    font-family: var(--font-family-R);
    font-size: var(--font-size-40);
    line-height: var(--line-height-60);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.heading--32 {
    font-family: var(--font-family-R);;
    font-size: var(--font-size-32);
    line-height: var(--line-height-48);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.heading--24, .heading--wrap {
    font-family: var(--font-family-R);;
    font-size: var(--font-size-24);
    line-height: var(--line-height-36);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.heading--18, .account__form-heading {
    font-family: var(--font-family-R);;
    font-size: var(--font-size-18);
    line-height: var(--line-height-27);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.heading--16, .account__form-heading {
    font-family: var(--font-family-R);;
    font-size: var(--font-size-16);
    line-height: var(--line-height-24);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.heading--13, .account__field-label {
    font-family: var(--font-family-R);;
    font-size: 13px;
    line-height: var(--line-height-20);
    color: var(--black1-color);
    font-weight: var(--font-weight-400);
}

.bodytext--13 {
   
    font-size: 13px;
    line-height: var(--line-height-20);
    color: var(--black1-color);
     
}

.caption--11, .account__field-hint {
   
    font-size: 11px;
    line-height: var(--line-height-17);
    color: var(--black1-color) !important;
     
}

.label--8 {
   
    font-size: 8px;
     
    l 
    color: var(--black1-color) !important;
}

.account__jp-astarisk, .blue-label--8 {
   
    font-size: 8px;
     
    l 
    color: var(--blue-color);
}

.account__jp-astarisk-op, .grey-label--8 {
   
    font-size: 8px;
     
    l 
    color: var(--grey1-color);
}

.label--8 {
   
    font-size: 8px;
     
    l 
    color: var(--black1-color);
}

.text--blue {
    color: var(--blue-color);
}
/* End heading, text */

/* Checkbox */
.account__field-checkbox-text {
    display: flex;
    align-items: center;
}

input.account__field-checkbox {
    margin-right: 12px;
    margin-top: 0;
    width: 16px;
    height: 16px;
}

.account__field-checkbox-text {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .account__field-checkbox-text input {
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: 3px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: rgba(216, 216, 216, 0.0001);
    border: 1px solid var(--grey1-color);
    box-sizing: border-box;
    border-radius: 3px;
  }

  .account__field-checkbox-text input:checked ~ .checkmark {
    background-color: var(--blue-color);
    border: none;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  .account__field-checkbox-text input:checked ~ .checkmark:after {
    display: block;
  }

  .account__field-checkbox-text .checkmark:after {
    left: 5px;
    top: 2px;
    width: 6px;
    height: 9px;
    border: solid var(--background-color);
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
/* End checkbox */

/* Modal */
.modal {
    background-color: rgba(0, 0, 0, 0.7) !important;
}

.modal:before {
    height: 0 !important;
    display: block !important;
}

.popup-content {
    padding: 24px;
}

.popup-content .popup-header {
    position: relative;
}

.popup-title, .modal-title {
    font-size: var(--font-size-16);
    font-weight: var(--font-weight-400);
    line-height: var(--line-height-24);
    color: var(--black1-color);
    margin: 0;
}

.popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0;
    background-color: var(--white-color);
}

.popup-close .icon--sicon-close {
    font-size: var(--font-size-18);
    color: var(--grey1-color);
}

.form-group label.control-label {
    font-size: 11px;
    color: var(--grey1-color);
    line-height: var(--line-height-17);
    padding-bottom: 8px;
}

.modal hr {
    margin: 24px 0;
    border: 1px solid var(--soremo-border);
}

.popup-footer {
    text-align: right !important;
}
/* End modal */

/* Input radio */
.account__sub-group {
}

.account__form-group .account__form-multi {
    display: block;
    margin-bottom: 9px;
}

.account__form-group .account__form-multi:last-child {
    margin-bottom: 0;
}

.account__form-group .account__form-multi.account__form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.account__form-group .account__form-multi .input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 300;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
}

.input-radio {
    font-weight: 300 !important;
}

.account__form-group .input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.account__form-group .input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.account__form-group .input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}
/* End input radio */

/* Label */
.account__status {
    font-size: 8px;
    l 
    padding: 4px 10px;
    border-radius: 4px;
}

.account__status-confirmed {
    color: var(--blue-color);
    border: 1px solid var(--blue-color);
}

.account__status-normal {
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
}
/* End label */

/* Tag */
.tag {
    padding: 4px 8px;
    border-radius: 4px;
}
.tag--selectable {
    color: var(--grey1-color);
    background-color: var(--white-color);
}

.tag--hover {
    color: var(--white-color);
    background-color: var(--grey1-color);
}

.tag--selected {
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
}
/* End tag */

/* Flex */
.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
}
.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}
.flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
}
.flex-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
}
/* End flex */

.input-radio {
    width: fit-content;
}

/* Usesage setting */
.hide-action-delete {
    display: none;
}

.active-action-delete {
    display: block !important;
}

/* .hover-zoom-netflix:hover {
    transition: height width 1s, top left transform 3s;
    position: fixed;
    height: 80vh;
    max-width: 80vw;
    width: auto;
    top: 50% !important;
    z-index: 1000;
    border-radius: 5px;
    left: 50% !important;
    transform: translateX(-50%) translateY(-50%);
} */
.hover-zoom-netflix-prepare {
    transition: 2.4s ease;
}

.hover-zoom-out-netflix-prepare {
    transition: 0.2s width ease, 0.2s height ease, 0.2s top ease, 0.2s left ease, 0.2s transfrom ease, 0.4s ease;
    height: 80vh !important;
    max-height: none !important;
    max-width: 80vw !important;
    width: auto !important;
    top: 49% !important;
    left: 49% !important;
    transform: translateX(-49%) translateY(-49%);
    z-index: 1050;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-radius: 8px !important;
    cursor: pointer;
    background: #000 !important;
}

.hover-zoom-out-netflix-prepare-2 {
    transition: 0.2s ease;
    height: 65vh !important;
    max-height: none !important;
    max-width: 65vw !important;
    width: auto !important;
    z-index: 1050;
    cursor: pointer;
}

.list-topics__content-top .hover-zoom-out-netflix-prepare-3 {
    height: 225px !important;
    max-height: none !important;
    max-width: 400px !important;
    width: auto !important;
}

.topic-content__media .hover-zoom-out-netflix-prepare-3 {
    height: 300px !important;
    max-height: none !important;
    max-width: 534px !important;
    width: auto !important;
}

.cvideo__thumb  .hover-zoom-out-netflix-prepare-3 {
    height: 100px !important;
    max-height: none !important;
    max-width: 184px !important;
    width: auto !important;
}

.hover-zoom-out-netflix-prepare-3 {
    transition: 0.6s ease;
    height: 144px !important;
    max-height: none !important;
    max-width: 256px !important;
    width: auto !important;
    z-index: 1050;
    cursor: pointer;
    position: relative;
    top: auto;
    left: auto;
}

.hover-zoom-netflix {
    height: 80vh !important;
    max-height: none !important;
    max-width: 80vw !important;
    width: auto !important;
    top: 49% !important;
    left: 49% !important;
    transform: translateX(-49%) translateY(-49%);
    z-index: 1050;
    transition: 0.8s ease;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-radius: 8px !important;
    cursor: pointer;
    background: #000 !important;
}

.hover-zoom-netflix-center {
    top: 50% !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-50%) !important;
    transition: 0.4s ease;
}

.list-topics__content-top video.hover-zoom-netflix {
    border-radius: 8px !important;
    width: 100%;
    max-height: 100% !important;
}

.video-netflix-overlay {
    width: 100vw;
    height: 100vh;
    z-index: 1049;
    position: fixed;
    top: 0;
    left: 0;
}

.list-new-works__media.hover-zoom-netflix video,
.sample-audio-thumbnail.hover-zoom-netflix video {
    width: auto !important;
    height: 70% !important;
    border-radius: 8px 8px 0 0;
    margin: auto;
}

.list-new-works__media.hover-zoom-netflix .list-new-works__content_hover,
.sample-audio-thumbnail.hover-zoom-netflix .list-new-works__content_hover {
    background: white;
    height: calc(30% + 2px) !important;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    overflow-y: scroll;
}

.list-new-works__media.hover-zoom-netflix.no-detail video,
.sample-audio-thumbnail.hover-zoom-netflix.no-detail video {
    width: auto !important;
    height: 100% !important;
}

.list-new-works__media.hover-zoom-netflix.no-detail .list-new-works__content_hover,
.sample-audio-thumbnail.hover-zoom-netflix.no-detail .list-new-works__content_hover {
    height: 0 !important;
}

.list-new-works__content_hover .list-new-works__title {
    max-width: 500px;
}

.list-new-works__content_hover .list-new-works__title__created-year {
    max-width: 100px;
    padding-bottom: 4px;
    margin-left: 8px;
}

.list-new-works__content_hover .list-new-works__heading {
    display: flex;
    align-items: end;
    margin-bottom: 16px;
}

.list-new-works__content_hover .list-new-works__artist {
    margin-bottom: 8px;
}

.list-new-works__content_hover .list-new-works__credit {
    margin-bottom: 16px;
    line-height: 22px;
    font-weight: 400;
    line-break: anywhere;
    white-space: pre-line;
}

.list-new-works__content_hover .list-new-works__desc {
    line-break: anywhere;
    white-space: pre-line
}

.list-new-works__content_hover::-webkit-scrollbar {
    background-color: #fff;
    width: 16px;
}

/* background of the scrollbar except button or resizer */
.list-new-works__content_hover::-webkit-scrollbar-track {
    background-color: #fff;
    border-radius: 8px;
}

/* scrollbar itself */
.list-new-works__content_hover::-webkit-scrollbar-thumb {
    background-color: #f0f0f0;
    border-radius: 16px;
    border: 4px solid #fff;
}

/* set button(top and bottom of the scrollbar) */
.list-new-works__content_hover::-webkit-scrollbar-button {
    display: none;
}

/* TODO: responsive */
@media screen and (max-aspect-ratio: 12/10) {
    .hover-zoom-netflix {
        height: auto !important;
        width: 95% !important;
        top: 20% !important;
        left: 2% !important;
        transition: 0.8s ease;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        border-radius: 8px;
    }

    .list-new-works__media.hover-zoom-netflix video,
    .sample-audio-thumbnail.hover-zoom-netflix video {
        width: 100% !important;
        height: auto !important;
    }

    .list-new-works__media.hover-zoom-netflix .list-new-works__content_hover,
    .sample-audio-thumbnail.hover-zoom-netflix .list-new-works__content_hover {
        max-height: 15vh;
        padding: 12px;
    }

    .hover-zoom-netflix-center {
        top: 50% !important;
        left: 50% !important;
        transform: translateX(-50%) translateY(-50%) !important;
        transition: 0.4s ease;
    }
}

.video-placeholder {
    min-width: 300px;
}

.video-newwork-placeholder {
    padding-top: 144px;
    min-width: 256px;
}
