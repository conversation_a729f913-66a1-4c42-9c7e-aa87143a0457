@charset "UTF-8";
/* CSS Document */


/*
------- accordion　-------*/


summary {
    cursor: pointer;
}

details {
    transition: .5s;
}

.acd-check {
    display: none;
}

.acd-label {
    background: #FCFCFC;
    color: #333333;
    display: block;
    padding: 15px 15px;
    margin: 10px 0;
    border: thin solid #f0f0f0;
}

.acd-content {
    padding: 15px;
}

.acd-content a {
    font-size: 1rem;
}

.acd-content a:link, .acd-content a:visited, .acd-content a:hover, .acd-content a:active {
    color: #333333;
}

.acd-content img {
    width: clamp(325px, 68.2%, 375px);
}


/* ---------
    Service
--------- */

.service {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: center;
    width: 100%;
    height: 618px;
    margin: 0px auto;
}

.service-text {
    width: 100%;
    text-align: center;
}

.service-text h2 {

    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-size: 3rem;
    font-weight: normal;
    letter-spacing: 0.8175rem;
    color: #f0f0f0;
    margin: 196px auto 0px;
}

.service-text h4 {
    margin: 0px auto 30px;
    letter-spacing: 0.8125rem;
}

.service-text p {
    padding-bottom: 10px;
}


.service-image {
    width: 100%;
    height: 500px;
    margin: 30px auto;
    background-color: #fcfcfc;
    background-image: url("../images/<EMAIL>");
    background-size: cover;
    background-position: center center;
}


/* SP Version
------------------------------
------------------------------ */

@media (max-width: 560px) {


    .service-whats-soremo {
        font-size: 1rem;
        letter-spacing: 3px;
        padding: 30px 15px;
        width: 124px;
        height: 124px;
    }

    .service-text {
        text-align: center;
    }

    .service-text h2 {
        font-size: 1.75rem;
        text-align: right;
        font-family: 'A+mfCv-AXISラウンド 50 R StdN';
        letter-spacing: 2px;
        padding-bottom: 20px;
        font-weight: normal;
        color: #009ace;
    }

    .service-text h3 {
        font-size: 1.25rem;
        text-align: right;
        font-family: 'A+mfCv-AXISラウンド 50 L StdN';
        letter-spacing: 2px;
        padding-bottom: 30px;
        font-weight: normal;
        color: #009ace;
    }

    .service-text p {
        font-size: 0.8125rem;
        font-family: 'A+mfCv-AXISラウンド 50 L StdN';
        text-align: right;
        color: #333333;
        margin: 5px 0px 15px;
    }

    .service-text a {
        font-size: 0.8125rem;
    }
}
