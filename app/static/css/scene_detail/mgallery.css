.mgallery {
    padding: 24px;
    border-radius: 12px;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    width: 496px;
    max-width: 100%;
    margin: 0 auto;
}

@media (max-width: 992px) {
    .mgallery {
        padding: 0;
        border: none;
    }
}

.mgallery__list {
    display: flex;
    flex-wrap: wrap;
    margin: -6px;
}

@media (max-width: 992px) {
    .mgallery__list {
        margin: -4px;
    }
}

.mgallery__image {
    padding-bottom: 100%;
    background-size: cover;
    background-position: center center;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
}

.mgallery__image:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: all .3s;
}

.mgallery__icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    color: #fff;
    opacity: 0;
    visibility: hidden;
    transition: all .3s;
}

.mgallery__icon:hover {
    color: #fff;
    transition: all .3s;
}

.mgallery__item {
    flex: 0 0 20%;
    position: relative;
    padding: 6px;
}

@media (max-width: 992px) {
    .mgallery__item {
        padding: 4px;
    }
}

.mgallery__item:hover .mgallery__image:before {
    opacity: 1;
    visibility: visible;
    transition: all .3s;
}

.mgallery__item:hover .mgallery__icon {
    opacity: 1;
    visibility: visible;
}
