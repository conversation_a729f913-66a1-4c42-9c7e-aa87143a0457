/* Messenger create offer - Search */
.psearch-heading h3 {
    padding-top: 16px;
    margin: 0;
}

.psearch-heading .caption--11 {
    padding: 8px 0 22px;
    margin: 0;
}

.psearch-section {
    border-radius: 32px;
}

.psearch-title {
    padding: 40px 32px 0;
    border-bottom: none;
}

.psearch-title h3, .psearch-title p {
    margin: 0;
}

/* Filter Skills */
.psearch-filter {
    margin-bottom: 40px;
}

.tabs-skill {
    display: flex;
    flex-wrap: wrap;
}

.tabs-skill .nav-item {
    position: relative;
}

.skill-selected {
    position: absolute;
    top: 0;
    right: 0;
}

.nav.tabs-skill .nav-item a.nav-link {
    color: #000000;
    border-bottom: 2px solid transparent;
    cursor: pointer;
}

.nav.tabs-skill .nav-item.active a.nav-link, .nav.tabs-skill .nav-item.active a.nav-link:hover, .nav.tabs-skill .nav-item a.nav-link:hover, .nav.tabs-skill .nav-item.active a.nav-link:focus {
    color: #009ACE;
    border-bottom: 2px solid #009ACE;
}

.skills-list-selected {
    display: flex;
    flex-wrap: wrap;
}

.skills-item {
    font-size: 11px;
    line-height: 17px;
    padding: 4px 16px;
    color: #000000;
    background-color: #F0F0F0;
    border-radius: 4px;
    margin: 11px 8px 0 0;
    cursor: pointer;
    border: 1px solid #F0F0F0;
}

.skills-item:hover, .skills-item.selected:hover  {
    color: #FFF;
    background-color: #009ACE;
    border: 1px solid #009ACE;
}

.skills-item.selected {
    color: #53565A;
    background-color: #FFF;
    border: 1px solid #53565A;
}
/* End Filter Skills */

.mcreator__header {
    display: flex;
    align-items: center;
}

.mcreator__header-info {
    width: 100%;
}

.mcreator__header-head, .mcreator__header-body {
    display: flex;
    justify-content: space-between;
}

.mcreator__avatar {
    position: relative;
    height: 40px;
}

.number-tasks {
    position: absolute;
    right: -7px;
    bottom: 0;
    border: 1px solid #FFF;
}

.mcreator__header-tag {
    display: flex;
    height: 14px;
}

.mcreator__header-tag span {
    padding: 0 8px;
    border: 1px solid #A7A8A9;
    border-radius: 4px;
}

.mcreator__header-tag span:not(:first-child) {
    margin-left: 8px;
}

.mcreator__header-tradeoff {
    width: 50%;
}

.account__tradeoff {
    width: 40%;
    padding-top: 5px;
    margin: 16px 0 0;
}

.account__trade-item.active:first-child:after,
.account__trade-item:first-child:after,
.account__trade-item.active:last-child:after,
.account__trade-item:last-child:after {
    left: calc(50% - 1.5px) !important;
}

.account__trade-slider {
    display: flex;
    margin: 0 -10%;
    padding: 10px 0;
}
.account__trade-item {
    width: 20%;
    position: relative;
}

.account__trade-item:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px !important;
    background-color: var(--soremo-border) !important;
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
}

.account__trade-item:after {
    content: "";
    position: absolute;
    left: 50%;
    width: 8px !important;
    height: 8px !important;
    border-radius: 50%;
    border: 1px solid var(--soremo-placeholder) !important;
    background-color: var(--white-color);
    transform: translate(-50%, -50%);
}

.account__trade-item:first-child:after {
    left: calc(50% - 7px);
}

.account__trade-item:last-child:after {
    left: calc(50% + 7px);
}

.account__trade-item:last-child:before {
    display: none;
}

.account__trade-item.active:after {
    width: 8px !important;
    height: 8px !important;
    background-repeat: no-repeat;
    background-size: 8px;
    background-position: center;
    background-color: var(--blue-color);
}

.account__trade-item.active:first-child:after {
    left: calc(50% - 1.5px);
}
.account__trade-item.active:last-child:after {
    left: calc(50% + 1.5px);
}

.mcreator__body-schedule .mcalendar .table-condensed {
    width: 100%;
}

.mcalendar .datepicker-inline .day.day__off:after,
.mcalendar .datepicker-inline .day.active.day__off:after,
.mcalendar .datepicker-inline .day.day__off.active:after,
.mcalendar.mcalendar--small .datepicker-inline .day.active.day__off:after {
    content: '';
    position: absolute;
    top: 0;
    left: 60%;
    background-image: url(../images/img_day_off.png);
    background-repeat: no-repeat;
    background-size: 99%;
    background-position: center;
    height: 16px;
    width: 16px;
}

.mcalendar .datepicker-inline .day.day__maybe:after,
.mcalendar.mcalendar--small .datepicker-inline .day.day__maybe.active:after,
.mcalendar .datepicker-inline .day.active.day__maybe:after,
.mcalendar.mcalendar--small .datepicker-inline .day.active.day__maybe:after {
    content: '';
    position: absolute;
    top: 0;
    left: 60%;
    background-image: url(../images/img_day_maybe.png);
    background-repeat: no-repeat;
    background-size: 99%;
    background-position: center;
    height: 16px;
    width: 16px;
}

.mcalendar .datepicker-inline .day.today:before {
    background-color: #A7A8A9 !important;
}

.psearch-content {
    padding: 32px;
}

.mcreator__body-wrap {
    padding: 10px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    border-radius: 4px;
    margin-top: 16px;
}

.mcreator__body-content {
    height: 50px;
    overflow-y: auto;
    word-break: break-word;
    white-space: pre-line !important;
}

.mcreator, .mcreator__body-schedule tr td{
    cursor: default!important;
}

.mcreator__body-schedule {
    margin-top: 8px;
    height: 76px;
    overflow-y: auto;
}

.mcreator__footer {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
}

@media (max-width: 992px) {
    .psearch-content {
        padding: 0;
    }
    .psearch-title {
        padding: 0 0 18px;
        border-bottom: none;
    }
}

@media (max-width: 739px) {
    .psearch-artist-list .mcreator {
        flex: 0 0 285px;
    }
    .mcalendar--small .datepicker .table-condensed > tbody > tr > td {
        padding: 8px;
    }
}
/* End messenger create offer - Search */
