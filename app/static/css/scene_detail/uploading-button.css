.upload-button-wrapper {
    z-index: 100;
    display: none;
    width: 100%;
    height: 100%;
    align-items: center;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 15% 20px;
    justify-content: center;
}

.upload-button-wrapper.fullscreen {
    position: fixed;
    width: 100vw;
    height: 100vh;
}

.upload-button-wrapper:not(success) p {
    display: block;
}

.upload-button-wrapper.success p,
.upload-button-wrapper.success .fill {
    display: none;
}

.upload-button-wrapper .fill {
    background: #a5a6a7;
    width: 60%;
    height: 12px;
    position: relative;
    border: 1px solid #009ace;
}

.upload-button-wrapper .fill .process {
    background: #009ace;
    width: 0;
    height: 10px;
    position: absolute;
    top: 0;
    left: 0;
    transition: .5s all ease .5s;
}

.upload-button-wrapper .fa {
    font-size: 25px;
    color: white;
    line-height: 50px;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
}

.upload-button-wrapper.clicked .fill .process {
    width: 0;
}

.upload-button-wrapper.success .fa {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    background: #009ace;
    padding: .5em 1em;
    border-radius: 50%;
    -webkit-transition: .3s all ease .3s;
    transition: .3s all ease .3s;
}

.upload-final-product-file {
    width:100vw;
    position:fixed;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
    background: rgb(255,255,255,0.7);
}
