.valid_mail ul li {
    list-style: none;
}

.valid_mail ul {
    padding: 0;
}

.checkbox input[type=checkbox] {
    margin-left: 0;
}

.banner {
    margin-bottom: 0 !important;
}

.auth {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 0;
}

.auth__main {
    position: relative;
    width: 30vw;
    min-width: 400px;
    display: flex;
    justify-content: center;
}

.auth__form {
    background-color: rgba(255, 255, 255, 1) !important;
    border-radius: 12px;
    padding: 40px 20px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-width: 364px;
}

.join-form__social-list {
    margin: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: none !important;
}

.auth__form-forgotpass {
    color: #A7A8A9 !important;
    font-weight: 300 !important;
}

.auth__form-register, .auth__form-note {
    font-style: normal;
    font-weight: 300 !important;
    font-size: 13px !important;
    line-height: 150%;
    color: #000000 !important;
}

.auth__form-forgotpass a, .auth__form-register a {
    color: #009ace !important;
}

.auth__form-forgotpass a:hover, .auth__form-register a:hover {
    color: #0076a5 !important;
}
.form-group .input-box {
    border: 1px solid #F0F0F0 !important;
    background-color: white !important;
    padding: 10px 25px !important;
    border-radius: 4px !important;
    box-shadow: none;
    -webkit-appearance: none;
}
.form-group .checkbox input[type='checkbox']:before {
    border: 1px solid #A7A8A9 !important;
    border-radius: 3px;
    box-sizing: border-box;
    background: white !important;
}

.form-group .checkbox input[type='checkbox']:checked:after {
    border: 1px solid #009ace;
    border-radius: 3px;
    border-width: 0 3px 3px 0;
}

.auth__form-button {
    width: 100% !important;
    background-color: #009ace !important;
    color: #ffffff !important;
    padding: 12px 25px !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 4px !important;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN' !important;
    font-size: 18px !important;
    font-weight: normal !important;
}

.auth__form-button:hover {
    background-color: #0076a5!important;
}

.form-group {
    margin-bottom: 20px !important;
}

.form-bottom {
    margin-bottom: 0 !important;
}

.form-group label {
    font-weight: 700 !important;
}

.btn--disabled {
    background: #F0F0F0 !important;
    color: #A7A8A9 !important;
    box-shadow: none;
}

.error-messager, .errorlist {
    font-family: AXIS Round 50 StdN;
    font-style: normal;
    font-weight: 300;
    font-size: 10px;
    line-height: 150%;
    color: #2CC84D !important;
    margin-bottom: 0;
    word-break: break-word;
    display: flex;
}

.checkbox label {
    color: #000000;
    font-style: normal;
    font-weight: normal !important;
    font-size: 13px;
    line-height: 150%;
}

.join-form__social-title {
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    color: #000000;
    text-align: center;
}

.signin-title {
    font-size: 24px;
    line-height: 150%;
    color: #000000;
}

.auth__form-title {
    font-style: normal;
    font-weight: 400 !important;
    margin: 0px 0px 24px 20px !important;
    border-bottom: none !important;
    font-size: 24px;
    line-height: 150%;
    color: #000000;
}


.account__field-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';;
    font-size: 11px;
    line-height: 17px;
    color: #000000;
    font-weight: 300;
    display: block;
    margin-bottom: 8px;
}

