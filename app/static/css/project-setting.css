/* ==========================================================================
   Foundation
   ========================================================================== */
@charset "utf-8";
@import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css");

/* ローカルのフォントを読み込む */
@font-face {
    font-family: "A+mfCv-AXISラウンド 50 L StdN";
    src: url('../fonts/AxisRound50StdN-L.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 R StdN";
    src: url('../fonts/AxisRound50StdN-R.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 M StdN";
    src: url('../fonts/AxisRound50StdN-M.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 B StdN";
    src: url('../fonts/AxisRound50StdN-B.otf') format('opentype');
}

@font-face {
  font-family: "soremoicons";
  /* src: url("../fonts/soremoicons.ttf?fd41f2") format("truetype"), url("../fonts/soremoicons.woff?fd41f2") format("woff"); */
  src: url('../fonts/soremoicons.eot');
  src: url('../fonts/soremoicons.eot?#iefix') format('embedded-opentype'),url('../fonts/soremoicons.svg#icomoon') format('svg'), url('../fonts/soremoicons.woff') format('woff'), url('../fonts/soremoicons.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: block; }

@font-face {
  font-family: "Material Symbols Rounded";
  font-style: normal;
  font-weight: 100 700;
  src: url(https://fonts.gstatic.com/s/materialsymbolsrounded/v7/sykg-zNym6YjUruM-QrEh7-nyTnjDwKNJ_190Fjzag.woff2)
    format("woff2");
  font-display: block;
}
* {
    touch-action: manipulation;
}
/* material icon */
/*               */
.material-symbols-rounded {
    font-variation-settings:
        'FILL' 1,
        'wght' 600,
        'GRAD' 0,
        'opsz' 24;
    color: var(--soremo-light-gray);
    font-family: "Material Symbols Rounded";
    -webkit-font-smoothing: antialiased;
}

/* Basic Font Setting */
/*                    */

/* Heading 18 Spacing */
.heading-32-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: 32px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-18-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: 18px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-13-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: 13px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.heading-spacing {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 100%;
    letter-spacing: 2.5px;
}

/* heading */
label,
button,
.heading {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;

    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
}

/* bodytext */
p,
a,
span,
li,
dt,
dd,
.bodytext {
    /* BodyText 13 - 16 */
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    /* 13px - 16px viewport 320px - 640px */
    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 200%;
}

input,
textarea,
.bodytext-16 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 16px;
    line-height: 200%;
}

.bodytext-13 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 13px;
    line-height: 200%;
}

.bodytext-11 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 200%;
}

.label-8 {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN',
        'M PLUS 1p',
        sans-serif;
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
}

/* <hr> */
hr {
    /* 既存のボーダーを削除 */
    border: none;
    /* 線の高さ（太さ） */
    height: 1px;
    /* 線の色 */
    background-color: var(--soremo-border);
    /* 上下のマージン */
    margin: 8px 0;
}


/* Reset
   ----------------------------------------------------------------- */

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;

    /* Google Chrome のスワイプによる戻りを無効に */
    overscroll-behavior-x: none ! important;
}

a,
a:visited,
a:hover,
a:active {
    text-decoration: none;
    /* 親要素から色を継承します */
    color: inherit;
}

a:focus {
    outline: none;
}

ul,
ol,
li {
    list-style: none;
}

/* スクロールバーの調整 */
/* スクロールバー全体のスタイル設定 */
::-webkit-scrollbar {
    /* スクロールバーの幅 */
    width: 4px;
    height: 6px;
}

/* スクロールバーのトラック（背景部分）のスタイル設定 */
::-webkit-scrollbar-track {
    /* トラックの背景色 */
    background: var(--soremo-background);
}

/* スクロールバーのつまみのスタイル設定 */
::-webkit-scrollbar-thumb {
    /* つまみの背景色 */
    background: var(--soremo-border);
    /* つまみの丸み */
    border-radius: 6px;
}

/* スクロールバーのつまみにホバーした時のスタイル設定 */
::-webkit-scrollbar-thumb:hover {
    /* ホバー時の背景色 */
    background: var(--soremo-light-gray);
}

.material-symbols-rounded {
  font-variation-settings:
  'FILL' 0,
  'wght' 400,
  'GRAD' 0,
  'opsz' 24
}

/* モーダルの背景  */
dialog::backdrop {
    background: rgba(0, 0, 0, 0.05);
}

dialog {
    display: none;
}

dialog[open]#creation-notice {
    position: fixed;
    width: clamp(320px, 80vw, 756px);
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 32px 16px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    /* 画面の中央に垂直方向で配置 */
    top: 50%;
    /* 画面の中央に水平方向で配置 */
    left: 50%;
    /* モーダルの左上の角を中央から左上に移動 */
    transform: translate(-50%, -50%);

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;

    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}
/* PCでのリサイズハンドルの設定（p-left-column） */
.resize-handle {
    width: 4px;
    height: 100%;

    position: absolute;
    top: 0;
    right: 0;

    background: var(--soremo-border);
}


/* Base
   ----------------------------------------------------------------- */
:root {
    --soremo-blue: #009ace;
    --soremo-deep-blue: #0076A5;
    --soremo-light-gray: #a7a8a9;
    --soremo-gray: #53565A;
    --soremo-border: #f0f0f0;
    --soremo-background: #fcfcfc;
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-size-40: 40px;
    --line-height-60: 60px;
    --font-size-32: 32px;
    --line-height-48: 48px;
    --font-size-24: 24px;
    --line-height-36: 36px;
    --font-size-18: 18px;
    --line-height-27: 27px;
    --font-size-16: 16px;
    --line-height-24: 24px;
    --font-size-13: 13px;
    --line-height-20: 20px;
    --font-size-11: 11px;
    --line-height-17: 17px;
    --font-size-8: 8px;
    --line-height-12: 12px;
    --font-weight-300: 300;
    --font-weight-400: 400;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
}


/* ==========================================================================
   Layout
   ========================================================================== */
#bg {
    background: linear-gradient(rgba(253, 253, 253, 0.5), rgba(247, 247, 246, 0.5), rgba(242, 242, 239, 0.5), rgba(236, 236, 232, 0.5), rgba(231, 231, 225, 0.5));
    z-index: -999;
}

body {
    overflow-x: hidden;
}

/* Header
   ----------------------------------------------------------------- */

/* グローバルヘッダー */
.d-block-header {
    width: 100%;
    position: sticky;
    top: -100px;
    transition: top 0.2s;

    background-color: var(--soremo-background);
    border-bottom: 1px solid var(--soremo-border);

    z-index: 5;
}

#grobal-header ul {
    padding: 16px 16px;

    /* flex親設定 */
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    gap: 48px;

    color: var(--soremo-light-gray);
}

/* プロジェクトバナー */
#project-banner {
    width: 100%;
    height: 75px;
    padding: 0px 16px 0px 16px;

    position: sticky;
    top: 0px;
    transition: top 0.2s;

    z-index: 4;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;

}

#top-app-bar {
    width: 100%;
    height: 40px;

    position: sticky;
    top: 0px;
    transition: top 0.2s;

    z-index: 3;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 8px;
}








/* DMの設定 */

.message-item {
    padding: 0px 0px 8px 0px;

    flex-grow: 1;

    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 4px;

}

.message-left {
    padding: 8px 8px;
    margin-right: clamp(64px, 61.8vw * 0.191 - 36px, 1140px * 0.618 * 0.191 - 36px);

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px;

}

.message-right {
    padding: 8px 8px;
    margin-left: clamp(64px, 61.8vw * 0.191, 1140px * 0.618 * 0.191);

    background-color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 6px;

    color: #fff;
}

/* アクションパネルの設定  */
#action-panel {
    padding: 0px 0px 0px;

    position: fixed;
    bottom: 80px;
    transition: bottom 0.2s;

}

#action-panel .p-right-column {
    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);
}

.approval-actions {
    width: 100%;
    padding: 8px 8px 8px 8px;
}

.comment-field {
    width: 100%;
    padding: 8px 8px 8px 8px;

    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    position: relative;
}

.comment-field textarea {
    padding-left: 40px;
    padding-right: 40px;
}

#attach-icon {
    color: var(--soremo-light-gray);

    position: absolute;
    left: 16px;
    bottom: 24px;
}

#attach-icon:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}


#send-icon {
    color: var(--soremo-border);

    font-size: 32px;
    position: absolute;
    right: 20px;
    bottom: 20px;
}

#send-icon:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}


/* Main
   ----------------------------------------------------------------- */
main {
    width: min(100% - 32px);
    margin-inline: auto;

    padding-bottom: 88px;
    transition: all 0.2s;
}

#project-setting {
    min-height: calc(100dvh - 75px - 40px);
}

#dm {
    min-height: calc(100dvh - 75px - 40px);
}


/* Footer
   ----------------------------------------------------------------- */
footer {
    width: 100%;
    margin-inline: auto;
}



/* bottom-app-bar内の設定 */
/*  */
#bottom-app-bar {
    height: 80px;
    padding: 16px 16px 0px;

    position: fixed;
    bottom: 0;
    transition: bottom 0.2s;

    /* サイドバーより上に設定 */
    z-index: 4;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    display: flex;
    align-items: flex-start;
    justify-content: center;
}





/* ==========================================================================
   Object
   ========================================================================== */

/* Component
   ----------------------------------------------------------------- */
input[type="text"],
input[type="tel"],
input[type="email"],
input[type="date"],
input[type="number"],
textarea {
    /* テキストボックスの幅 */
    width: 100%;
    /* 内側の余白 */
    padding: 12px 4px 12px 12px;
    /* 外側の余白 */
    margin: 0;
    /* ボーダーのスタイル */
    border: 1px solid var(--soremo-border);
    /* 角の丸み */
    border-radius: 4px;
}

input[type="tel"] {
    width: 144px;
}

input[type="date"] {
    /* 元のカレンダーアイコンを非表示にする */
    -webkit-appearance: none;
    appearance: none;
    /* パディングを追加してアイコンとテキストが重ならないようにする */
    padding-right: 24px;
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
}

input[type="number"] {
    /* 数値のみ入力可能 */
    -moz-appearance: textfield;
    appearance: textfield;
    /* 右寄せにする */
    text-align: right;
}

/* スピンボタンの非表示 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
textarea:focus {
    /* フォーカス時のボーダーカラー */
    border-color: var(--soremo-blue);
    /* デフォルトのアウトラインを削除 */
    outline: none;
    /* ボックスシャドウ */
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
textarea:focus {
    /* フォーカス時のボーダーカラー */
    border-color: var(--soremo-blue);
    /* デフォルトのアウトラインを削除 */
    outline: none;
    /* ボックスシャドウ */
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

input[type="text"].error,
input[type="tel"].error,
input[type="email"].error,
input[type="date"].error,
input[type="number"].error,
textarea.error {
    border: 1px solid var(--error-color) !important;
    border-radius: 4px;
    color: var(--error-color) !important;
}


/* オートフィルされたフォームの背景色を変更  */
input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px var(--soremo-background) inset !important;
}

textarea {
    resize: none;
}

textarea::-webkit-scrollbar {
    display: none;
}

/* プレイスホルダーの色 */
input::placeholder,
textarea::placeholder {
    color: var(--soremo-border);
    /* ここに希望の色を指定 */
}

/* フォームの設定 */
form {
    width: 100%;
}

/* プログレスバー */
/* 全体のスタイル */
progress {
    /* プログレスバーの幅 */
    width: 100%;
    /* プログレスバーの高さ */
    height: 6px;

    -webkit-appearance: none;
    appearance: none;
}

/* プログレスバーの未完了部分のスタイル */
progress::-webkit-progress-bar {
    /* 背景色 */
    background-color: var(--soremo-border);
    /* 角の丸み */
    border-radius: 6px;
    /* 内側の影 */
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25) inset; */
}

/* プログレスバーの完了部分のスタイル */
progress::-webkit-progress-value {
    /* バーの色 */
    background-color: var(--soremo-blue);
    /* 角の丸み */
    border-radius: 6px;
    /* 影 */
    box-shadow: 2px 5px 8px 0px rgba(0, 154, 206, 0.10);
}

.progressbar {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    width: min(100% - 16px, 640px);
}

.progressbar span {
    font-size: 11px;
    color: var(--soremo-light-gray);
    width: 42px;
    text-align: right;
}
.block-navigation-bar {
    display: flex;
    height: 80px;
    padding: 16px 16px 0px;
    position: fixed;
    bottom: 0;
    transition: bottom 0.2s ease;
    z-index: 400;
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13px);
    -webkit-backdrop-filter: blur(13px);
    margin-bottom: 0;
    width: 100%;
}
.navigation-bar {
    margin-inline: auto;
    display: flex;
    justify-content: space-between;
    /* justify-content: space-around; */
    width: clamp(320px, 100%, 640px);
    list-style-type: none;
    padding: 0;
}
.material-symbols-rounded, .label-nav-bar-item {
    color: #a7a8a9;
    line-height: 100%;
}

.label-nav-bar-item {
    font-size: 8px;
}
.nav-bar-item {
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 96px;
    gap: 2px;
    color: #a7a8a9;
    transition: 0.2s;
}
.link-nav-item:hover span {
    cursor: pointer;
    color: #009ace;
}

.link-nav-item.active span {
    color: #009ace;
}
.icon-with-badge {
    display: flex;
    position: relative;
}
.number-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #009ace;
    text-align: center;
    border-radius: 50px;
    line-height: 4px;
    width: auto;
    min-width: 15px;
    height: 15px;
    padding: 5px 2px;
    font-size: .7em;
    color: #fff !important;
    justify-content: center;
    align-content: center;
    border: 1px solid #FFFFFF;
}
.number-notification[value='0'] {
    display: none;
}
.material-symbols-rounded, .label-nav-bar-item {
    color: #a7a8a9;
    line-height: 100%;
}

.label-nav-bar-item {
    font-size: 8px;
}
.disabled {
    cursor: not-allowed;
}
.tabBottom {
    padding: 0 !important;
    display: block !important;
}
/* モーダル表示 */
/*            */


/* ボタンの設定 */
/*            */
.c-btn-primary {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;
}

.c-btn-primary:hover,
.c-btn-primary:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
    cursor: pointer;
}

.c-btn-primary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}

.c-btn-secondary {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);
    border-radius: 4px;
}

.c-btn-secondary:hover,
.c-btn-secondary:focus {
    color: #fff;
    background: var(--soremo-deep-blue);
    border: 1px solid var(--soremo-deep-blue);
    outline: 1px solid var(--soremo-deep-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
    cursor: pointer;
}

.c-btn-secondary:disabled {
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    outline: 1px solid var(--soremo-border);
    cursor: not-allowed;
}


.c-btn-tertiary {
    min-width: 128px;
    padding: 8px 24px;
    color: var(--soremo-light-gray);
    background: var(--soremo-border);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    transition: 0.2s;
}

.c-btn-tertiary:hover {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

.c-btn-tertiary:focus {
    color: #fff;
    background: var(--soremo-light-gray);
    border: 1px solid var(--soremo-light-gray);
    outline: 1px solid var(--soremo-light-gray);
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}

/* セグメントコントロールのスタイル設定 */
.c-segment-control {
    width: clamp(320px - 24px, 100% - 80px, 756px);
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    color: var(--soremo-light-gray)
}

.c-segment-control li,
.c-segment-control label {
    width: 50%;
    text-align: center;
    border-radius: 6px;
    padding: 4px 0;
    margin: 1px 1px;

    line-height: 100%;
}

.c-segment-control li:hover,
.c-segment-control label:hover {
    cursor: pointer;
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-segment-control label:has(input:checked) {
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--soremo-background);
    border: 1px solid var(--soremo-border);
    border-radius: 6px 6px 0px 0px;


    list-style: none;
    font-size: 13px;
    color: var(--soremo-light-gray)
}

.c-tabs li,
.c-tabs label {
    width: 50%;
    text-align: center;
    border-radius: 6px 6px 0px 0px;
    padding: 1px 0;
    margin: 1px 1px;
}

.c-tabs li:hover,
.c-tabs label:hover {
    cursor: pointer;
    background-color: var(--soremo-light-gray);
    color: #fff;
}

.c-tabs input:checked+label {
    background-color: var(--soremo-light-gray);
    color: #fff;
}

/* add-blockの設定 */
.c-add-block {
    width: 100%;
    height: 64px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0px;

    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    background-color: #FFF;

    color: var(--soremo-light-gray);

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    span {
        font-size: 32px;
    }

    a {
        font-size: 8px;
    }
}

.c-add-block:hover {
    cursor: pointer;
    color: #fff;
    background-color: var(--soremo-blue);
    border: 1px solid var(--soremo-blue);

    .material-symbols-rounded {
        color: #fff;
    }
}


/* グループ設定 */

.c-group {
    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    background: rgba(255, 255, 255, 0.5);
    padding: 16px 8px 16px 8px;
}





/* キーワード */
.c-keyword {
    margin: 4px;
    padding: 4px 4px;
    /* border: 1px solid #fcfcfc; */
    border-radius: 4px;

    color: #a7a8a9;
    user-select: none;
}

.c-keyword:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-light-gray);
    color: #FFF;
    background-color: var(--soremo-light-gray);
}

/* 画像をアップロード */
.c-upload-image {
    width: 100%;
    aspect-ratio: 5/1;
    margin-inline: auto;
    background-size: cover;
    background-position: left center;
    mix-blend-mode: screen;
    display: flex;
    align-items: center;
    justify-content: center;
}

.c-upload-image .material-symbols-rounded {
    font-size: 128px;
    color: var(--soremo-light-gray);
    mix-blend-mode: screen;
}

.c-upload-image:hover {
    cursor: pointer;
    filter: brightness(80%);
}

.c-navigate-before {
    width: 100%;
    height: 100%;

    color: var(--soremo-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: flex-start;

    @media (width > 640px) {
        display: none;
    }
}

.c-navigate-before:hover {
    cursor: pointer;

    color: var(--soremo-deep-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

.c-status-label {
    width: 4px;
    height: 100%;

    position: absolute;
    left: 0px;
    top: 0px;
}

.c-budge {
    width: 16px;
    height: 16px;

    background-color: var(--soremo-blue);
    color: #FFF;
    border-radius: 50%;

    font-family: 'A+mfCv-AXISラウンド 50 R StdN',
        'M PLUS 1p',
        sans-serif;
    font-style: normal;

    font-size: 8px;
    line-height: 100%;
}

/* カレンダー表示。まだ未作成 */
.c-date-pickers {
    width: 336px;
    height: 290px;

    padding: 48px 16px;
    border-radius: 4px;
    background-color: #FFF;
}

.project-avatars {
    border-radius: 11px;
    border: 1px solid var(--soremo-border);
    padding: 4px calc(4px + 4px) 4px 4px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.project-avatars:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-blue);
    outline: 1px solid var(--soremo-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

[class^="c-avatar"] {
    border: 1px solid #fff;

    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

.c-avatar48 {
    width: 48px;
    height: 48px;
    border-radius: 18px;
    /*overflow: hidden;*/
    position: relative;
}

.c-avatar48 img {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 18px;
}

.c-avatar40 {
    width: 40px;
    height: 40px;

    border-radius: 16px;
}

.c-avatar32 {
    width: 32px;
    height: 32px;

    border-radius: 13px;
}

.c-avatar24 {
    width: 24px;
    height: 24px;

    border-radius: 10px;
}

.c-avatar16 {
    width: 16px;
    height: 16px;
    margin-right: -4px;

    border-radius: 6px;
}


.c-avatar-producer {
    background-image: url('images/avatar-producer.png');
    background-size: cover;
}

.c-avatar-director1 {
    background-image: url('images/avatar-director1.png');
    background-size: cover;
}

.c-avatar{
    background-size: cover;
}

.c-avatar-lyricist {
    background-image: url('images/avatar-musician-lyricist.png');
    background-size: cover;
}

.c-avatar-vocalist {
    background-image: url('images/avatar-musician-vocalist.png');
    background-size: cover;
}

.c-avatar-guitarist {
    background-image: url('images/avatar-musician-guitar.png');
    background-size: cover;
}


.c-vertical-line {
    width: 3px;
    height: 100%;
    background-color: #fff;
    margin: 0 4px;
}

.c-icon-btn {
    width: 128px;
    color: var(--soremo-light-gray);

    .material-symbols-rounded {
        color: var(--soremo-light-gray);
    }
}

.c-icon-btn:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

.c-icon-date-range,
.c-icon-event {
    color: var(--soremo-light-gray);
    position: absolute;
    right: 8px;
    top: 16px;

    transition: 0.2s;
}

.c-icon-date-range:hover,
.c-icon-event:hover {
    cursor: pointer;
    color: var(--soremo-blue);

    scale: 1.3;
}

/*.icon--sicon-clip:before {*/
/*  content: ""; }*/

/*.icon--sicon-close:before {*/
/*  content: ""; }*/
/**
 * Media
 */
/* Project
   ----------------------------------------------------------------- */

.p-left-column {
    width: 100%;
    position: relative;

    @media (width > 640px) {
        width: clamp(320px, 38.2vw, 1140px * 0.382);
        flex: 0 0 auto;
    }
}

.p-right-column {
    margin-inline: auto;
}

/* プロジェクトメンバーモーダル */
/* 　　　　　　　　　　　　　　 */
.p-project-members {
    width: clamp(375px - 64px, 100vw - 64px, 1140px * 0.382);
    max-height: calc(100vh - 80px);

    position: absolute;
    top: -24px;
    right: 28px;
    transition: top 0.2s;

    border-radius: 0 0 12px 12px;
    background-color: var(--soremo-background);
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    z-index: 5;
    padding: 64px 8px 32px;
}

.p-project-members-mask,
.p-schedule-mask {
    width: 100vw;
    height: 100dvh;

    position: fixed;
    inset: 0;

    /* background: rgba(0, 0, 0, 0.05); */
}

/* スケジュールモーダル */
/* 　　　　　　　　　　 */
.p-schedule {
    width: min(336px + 16px, 100vw);
    max-height: calc(100dvh - 64px);

    position: absolute;
    top: -24px;
    right: -8px;
    transition: top 0.2s;

    border-radius: 0 0 12px 12px;
    background-color: var(--soremo-background);
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    z-index: 5;
    padding: 64px 8px 32px;

    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 16px;

    color: #000;
}

#p-schedule-open {
    color: #FFF;
}

#p-schedule-open:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}

#p-schedule-close {
    color: var(--soremo-light-gray);
}

#p-schedule-close:hover {
    cursor: pointer;
    color: var(--soremo-blue);
}


/* PL画面 */
/* 　     */
.financial-summary-header-container {
    width: 100%;

    display: flex;
    flex-direction: column;
    gap: 8px;
}

.financial-summary-header {
    width: 100%;
    padding: 12px 24px;

    border-radius: 4px;
    background-color: var(--soremo-blue);
    color: #fff;

    p {
        font-size: 13px;
        line-height: 200%;
    }

    h2 {
        font-size: 32px;
    }

    h2>span {
        font-size: 11px;
    }
}

.transaction-list-container {
    width: 100%;

}

.transaction-list-header {
    width: 100%;
    padding-left: 36px;
    padding-right: 32px;

    display: flex;
    align-items: center;
    justify-content: space-between;

    font-size: 11px;
    color: var(--soremo-light-gray);
}

.transaction-list {
    width: 100%;
    padding: 8px 0;

    display: flex;
    align-items: center;
    justify-content: space-between;
}

.transaction-list .artist-name {
    display: flex;
    align-items: center;
    gap: 8px;
}

.transaction-list li .financial-ratio {
    padding-left: 16px;
    text-align: right;
}


/* プロジェクト設定画面 */

.p-lists-headline {
    width: 100%;
    border-bottom: 1px solid var(--soremo-border);
}

.p-lists-headline label {
    width: 100%;
    height: 100%;

    padding: 8px 0px 8px 8px;

    /* flex親設定 */
    display: flex;
    align-items: center;
    justify-content: space-between;
}


[class^="p-lists"] input[type="radio"] {
    display: none;
}

[class^="p-lists"] .material-symbols-rounded {
    visibility: hidden;
    color: var(--soremo-light-gray);
}

[class^="p-lists"]:hover {
    cursor: pointer;
    background-color: var(--soremo-background);

    .material-symbols-rounded {
        visibility: visible;
        color: var(--soremo-border);
    }
}

[class^="p-lists"]:has(input[type="radio"]:checked+label) {
    background-color: var(--soremo-border);

    .material-symbols-rounded {
        visibility: visible;
        color: var(--soremo-light-gray);
    }
}


.p-right-column img {
    display: block;
    margin-inline: auto;
}

.p-right-column .indicator-icon {
    color: var(--soremo-light-gray);
    text-align: center;

    .material-symbols-rounded {
        font-size: 128px;
    }
}


/* バナーの設定 */

.c-tab1 {
    width: 100%;
    padding: 16px 8px 24px;
    border-left: 1px solid var(--soremo-border);
    border-right: 1px solid var(--soremo-border);
    border-bottom: 1px solid var(--soremo-border);
    border-radius: 0px 0px 6px 6px;
}

.c-tab2 {
    width: 100%;
    border-left: 1px solid var(--soremo-border);
    border-right: 1px solid var(--soremo-border);
    border-bottom: 1px solid var(--soremo-border);
    border-radius: 0px 0px 6px 6px;
    overflow: hidden;
}


.p-option-font {
    width: 100%;
    height: 75px;

    padding: 8px 16px;
    margin-bottom: 8px;

    border-radius: 4px;
    border: 1px solid var(--soremo-border);
    transition: 0.2s;
}

.p-option-font:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-blue);
}

/* 選択された li 要素を強調表示 */
.p-option-font:has(input[type="radio"]:checked) {
    border: 2px solid var(--soremo-blue);
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

[class^="code-font"] {
    width: 100%;
    line-height: 100%;

    /* テキストを改行させません */
    white-space: nowrap;
    /* はみ出したテキストを...で表現します */
    text-overflow: ellipsis;
}

/*.code-font1 {*/
/*    font-family: "corporate-logo-ver2", sans-serif;*/
/*    font-weight: 700;*/
/*    font-style: normal;*/

/*    !* font-size: 30px; *!*/
/*    !* font-size: 38px; *!*/
/*    font-size: clamp(1.875rem, 1.375rem + 2.5vw, 2.375rem);*/

/*    white-space: nowrap;*/
/*}*/

/*.code-font2 {*/

/*    font-family: "dnp-shuei-mincho-pr6n", sans-serif;*/
/*    font-weight: 400;*/
/*    font-style: normal;*/

/*    !* font-size: 24px; *!*/
/*    !* font-size: 36px; *!*/
/*    font-size: clamp(1.5rem, 0.75rem + 3.75vw, 2.25rem);*/

/*    white-space: nowrap;*/
/*}*/

/*.code-font3 {*/
/*    font-family: "din-2014", sans-serif;*/
/*    font-weight: 800;*/
/*    font-style: normal;*/

/*    !* font-size: 40px; *!*/
/*    !* font-size: 44px; *!*/

/*    font-size: clamp(2.5rem, 2.25rem + 1.25vw, 2.75rem);*/
/*    text-transform: uppercase;*/
/*}*/

/*.code-font4 {*/
/*    font-family: "futura-pt", sans-serif;*/
/*    font-weight: 300;*/
/*    font-style: italic;*/

/*    !* font-size: 38px; *!*/
/*    !* font-size: 42px; *!*/
/*    font-size: clamp(2.375rem, 2.125rem + 1.25vw, 2.625rem);*/
/*}*/

/*.code-font5 {*/
/*    font-family: "trajan-pro-3", serif;*/
/*    font-weight: 300;*/
/*    font-style: normal;*/

/*    !* font-size: 32px; *!*/
/*    !* font-size: 34px; *!*/
/*    font-size: clamp(2rem, 1.875rem + 0.63vw, 2.125rem);*/

/*    text-transform: uppercase;*/
/*}*/

/*.code-font6 {*/
/*    font-family: "lindsey-signature", sans-serif;*/
/*    font-weight: 400;*/
/*    font-style: normal;*/
/*    font-size: 36px;*/
/*}*/



.color-container {
    width: 100%;
    padding: 16px 16px;

    overflow-y: auto;
    scroll-snap-align: start;

}

.color-container [class^="banner-color"] {
    width: 32px;
    height: 32px;
    border-radius: 50%;

    flex-shrink: 0;

    filter: drop-shadow(2px 5px 8px rgba(0, 154, 206, 0.10));

    transition: transform 0.2s ease-in-out;

}

.color-container [class^="banner-color"]:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-blue);

    transform: scale(1.3);
}

/* 選択された li 要素を強調表示 */
[class^="banner-color"]:has(input[type="radio"]:checked) {
    border: 1px solid var(--soremo-blue);
}

/*.banner-color1 {*/
/*    background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/

/*}*/

/*.banner-color2 {*/
/*    background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color3 {*/
/*    background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color4 {*/
/*    background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color5 {*/
/*    background-color: var(--soremo-background);*/

/*    >div:first-child {*/
/*        color: #000;*/
/*    }*/
/*}*/

/*.banner-color6 {*/
/*    background: var(--soremo-gray);*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color7 {*/
/*    background: #C68ED5;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color8 {*/
/*    background: #EA6586;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color9 {*/
/*    background: #FFE3E8;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color10 {*/
/*    background: #FFA254;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color11 {*/
/*    background: #FADD94;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color12 {*/
/*    background: #6EB370;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color13 {*/
/*    background: #6FA8ED;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color14 {*/
/*    background: #2A5ECB;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color15 {*/
/*    background: #0091C0;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/
/*}*/

/*.banner-color16 {*/
/*    background: #3B5375;*/

/*    >div:first-child {*/
/*        color: white;*/
/*    }*/

/*}*/



.th-milestone-date {
    padding: 0px 8px;
    width: 154px;
}

#milestone-date {
    width: 184px;
}

.indicator-image {
    height: 160px;
    aspect-ratio: 16/9;

    display: block;
    margin-inline: auto;

    border-radius: 12px;
}

.hover-menu {
    width: 32px;
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: 0.2s;
    visibility: hidden;
}

.hover-menu:hover {
    cursor: pointer;
    scale: 1.3;


    .material-symbols-rounded {
        color: var(--soremo-blue);
    }
}

/* 注文ページ（未使用） */

.p-plan {
    width: 100%;
    min-height: 288px;

    /* ボーダー設定 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    /* 後ろをぼかす */
    backdrop-filter: bler(4px) brightness(0.5);
    transition: 0.1s;

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 16px 12px;

    position: relative;

    @media (width < 756px) {
        width: 100%;
    }
}


#plan1+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));
}

#plan2+.p-plan {
    /* 背景色 */
    background: linear-gradient(rgb(212, 227, 232), rgb(157, 168, 172), rgb(106, 114, 116), rgb(59, 63, 65), rgb(16, 18, 19));
    color: #fff;

    .material-symbols-rounded {
        color: #fff;
    }
}

.p-plan:hover {
    cursor: pointer;
    /* アウトライン */
    outline: 1px solid #fff;

    /* 影の設定 */
    box-shadow: 4px 4px 8px 4px rgba(0, 0, 0, 0.1);
}

input[type="radio"]:checked+.p-plan {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}



.p-option {
    width: 100%;
    height: 160px;

    padding: 0px 24px 0px 172px;
    margin-bottom: 12px;

    background-color: #fff;
    border: 1px solid var(--soremo-border);
    border-radius: 12px;

    /* flex親設定（縦） */
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;

    transition: ocacity 0.5s;

    position: relative;

    h4 {
        font-family: "A+mfCv-AXISラウンド 50 R StdN";
        font-weight: 400;
        font-size: 18px;
        letter-spacing: 2.5px;
    }


    a {
        font-size: 11px;
        color: var(--soremo-blue);
    }

    @media (width < 756px) {
        height: 128px;
        padding: 0px 0px 0px 136px;

        h4 {
            font-size: 16px;
        }

        p {
            font-size: 11px;
            line-height: 187.5%;
            font-feature-settings: 'palt' on;
        }
    }

}

.p-option-image1 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option1.png') no-repeat center / cover;

}

.p-option-image2 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option2.png') no-repeat center / cover;
}

.p-option-image3 {

    height: 100%;
    aspect-ratio: 1/1;
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;

    border-radius: 12px 0px 0px 12px;

    background: url('images/mgk_option3.png') no-repeat center / cover;
}

.p-option:hover {
    border: 1px solid var(--soremo-border);
    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

input[type="checkbox"]:checked+.p-option {
    outline: 2px solid var(--soremo-blue);
    /* 要素の左端が切れる問題を回避 */
    outline-offset: -2px;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}


/* DM */
/* 　　*/

/* スレッドリスト */
.p-lists-recieved-offer {
    width: 100%;
    padding: 8px 0px 8px 12px;
    border-bottom: 1px solid var(--soremo-border);

    position: relative;
}

.p-sender {
    padding: 0px 0px 0px 26px;

}

.p-lists-sent-offer {
    width: 100%;
    padding: 8px 0px 8px 12px;
    border-bottom: 1px solid var(--soremo-border);

    position: relative;
}



/**
 * Articles
 */



/* Utility
   ----------------------------------------------------------------- */
/* align
*/
.u-block-wrapper {
    width: 100%;
    padding: 4px 8px;

    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    border-radius: 4px;
    border: 1px solid var(--soremo-background);
    background: var(--soremo-background);
}

.u-block-wrapper:hover {
    cursor: pointer;
    border: 1px solid var(--soremo-border);
    background: var(--soremo-border);
}



.u-block-label {
    width: 100%;
    padding: 8px 8px 2px;

    display: flex;
    align-items: center;
    justify-content: flex-start;

    color: var(--soremo-light-gray);
}


.u-block-label>li:nth-child(1) {
    flex: 1;
    /* 最初のアイテムは残りのスペースの2倍の幅を持つ */
}

.u-block-label>li:nth-child(2) {
    flex: 1.382;
    /* 2番目のアイテムは残りのスペースの1倍の幅を持つ */
}


/* 2列構成。SPとPCで表示を切り替えるパターン */

.u-col-to-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    @media (width > 640px) {
        flex-direction: row;
        align-items: stretch;
        justify-content: flex-start;
    }
}

.u-wrapper {
    width: min(100% - 32px, 1140px);
}

.u-wrapper-reading {
    width: clamp(320px, 100%, 740px);
}

.u-wrapper-btn {
    width: min(100% - 32px, 1140px);
    margin-inline: auto;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    @media (width > 756px) {
        flex-direction: row;
        justify-content: space-between;
    }
}

.u-wrapper-btn:has(>:only-child) {
    justify-content: flex-end;
}

.u-col {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.u-col-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.u-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.u-row-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.u-row-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.u-row-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.u-row-btn {
    /* SPは中央寄せ */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    /* PCは右寄せ */
    @media (width > 640px) {
        justify-content: flex-end;
        gap: 16px;
    }
}


.u-items-start {
    align-items: flex-start;
}

.u-items-stretch {
    align-items: stretch;
}

.u-wrap {
    flex-wrap: wrap;
}

.u-white-space {
    white-space: nowrap;
}


.u-gap2 {
    gap: 2px;
}

.u-gap4 {
    gap: 4px;
}

.u-gap6 {
    gap: 6px;
}

.u-gap8 {
    gap: 8px;
}

.u-gap16 {
    gap: 16px;
}

.u-fill {
    flex: 1;
}

.u-w100 {
    width: 100%;
}

.u-border-top {
    border-top: 1px solid var(--soremo-border);
}

.u-border-right {
    border-right: 1px solid var(--soremo-border);
}

.u-border-bottom {
    border-bottom: 1px solid var(--soremo-border);
}

.u-relative {
    position: relative;
}


/* パディング */
.u-ptb4 {
    padding-top: 4px;
    padding-bottom: 4px;
}

.u-ptb8 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.u-pr8 {
    padding-right: 8px;
}

.u-pb4 {
    padding-bottom: 4px;
}

.u-pl8 {
    padding-left: 8px;
}



/* マージン */
.u-mt8 {
    margin-top: 8px;
}

.u-mt16 {
    margin-top: 16px;
}

.u-mt24 {
    margin-top: 24px;
}

.u-mr-4 {
    margin-right: -4px;
}

.u-mb8 {
    margin-bottom: 8px;
}

.u-mb16 {
    margin-bottom: 16px;
}

.u-mb24 {
    margin-bottom: 24px;
}


.u-text-center {
    text-align: center;
}


/* color設定 */
.u-text-white {
    color: #fff;
}

.u-text-blue {
    color: var(--soremo-blue);
}

.u-text-light-gray {
    color: var(--soremo-light-gray);
}

.u-text-black {
    color: #000;
}



.u-fontsize-32 {
    font-size: 32px !important;
}

.u-fontsize-48 {
    font-size: 48px;
}

.u-line-height-100 {
    line-height: 100%;
}

.u-line-height-150 {
    line-height: 150%;
}

.u-line-height-200 {
    line-height: 200%;
}



.u-bg-border {
    background-color: var(--soremo-border);
}

.u-bg-light-gray {
    background-color: var(--soremo-light-gray);
}

.u-bg-gray {
    background-color: var(--soremo-gray);
}

.u-bg-blue {
    background-color: var(--soremo-blue);
}





/**
 * Clearfix
 */

/**
 * Display
 */

/* step毎のセクションを隠すためのクラス */
.is-hidden {
    display: none;
}

.is-active {
    display: block;
}

.is-active-flex {
    display: flex;
}

.is-invisible {
    visibility: hidden;
}

.is-visible {
    visibility: visible;
}

.is-icon-inactive {
    color: var(--soremo-border);
}

.is-icon-inactive {
    color: var(--soremo-border);
}

.is-icon-active {
    cursor: pointer;
    color: var(--soremo-blue);
}

/* アニメーションの設定 */
/*                   */
@keyframes fadeIn {

    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes blink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}



.banner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    list-style: none;

    gap: clamp(8px, 1.5vw, 24px);
    margin: 64px 0 128px;
}

.p-fixed-banner {
    width: 100%;
    aspect-ratio: 5 / 1;
    padding: 0px 8px 0px 16px;
    border-radius: 6px;

    display: flex;
    /* 水平方向の中央揃え */
    justify-content: flex-start;
    /* 垂直方向の中央揃え */
    align-items: center;

    @media (width < 720px) {
        height: calc(720px / 5);
        background-size: auto 100%;
        aspect-ratio: auto;
    }
}

.p-fixed-banner .lindsey {
    font-family: "lindsey-signature", sans-serif;
    font-weight: 400;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(64px, 5vw, 96px);
    color: #fff;
}

.p-fixed-banner .din-2014 {
    font-family: "din-2014", sans-serif;
    font-weight: 800;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(32px, 5vw, 64px);
    color: #fff;
    text-transform: uppercase;
}

.p-fixed-banner .trajan-pro-3 {
    font-family: "trajan-pro-3", serif;
    font-weight: 400;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(32px, 5vw, 64px);
    color: #fff;
    text-transform: uppercase;
}


.p-fixed-banner .corporate-logo-ver2 {
    font-family: "corporate-logo-ver2",
        sans-serif;
    font-weight: 700;
    font-style: normal;

    mix-blend-mode: overlay;
    font-size: clamp(24px, 5vw, 48px);
    color: #fff;
}

.p-fixed-banner[data-color="color1"] {
    /* color1のスタイル */
    /* background-color: black; */
    background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));
}

.p-fixed-banner[data-color="color2"] {
    /* color2のスタイル */
    background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));
}

.p-fixed-banner[data-color="color3"] {
    /* color3のスタイル */
    background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));
}

.p-fixed-banner[data-color="color4"] {
    /* color4のスタイル */
    background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));
}

.p-fixed-banner[data-color="color5"] {
    /* color5のスタイル */
    background: url('images/studio.jpg') no-repeat top left / cover;

}

.p-fixed-banner:hover {
    cursor: pointer;

    transform: scale(1.02);
    transform-origin: center;

    transition: transform 0.3s ease-in-out;
    outline: 1px solid var(--soremo-border);

    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
}

.floating-action-button {
    width: 80px;
    height: 80px;
    position: fixed;
    bottom: 32px;
    right: 8px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    text-align: center;

    color: var(--soremo-light-gray);
}

.floating-action-button span {
    font-size: 64px;
}

.floating-action-button a {
    font-size: 8px;
}

.floating-action-button:hover {
    cursor: pointer;
    transform: scale(1.05);
    transform-origin: center;
    color: var(--soremo-blue);

    transition: 0.3s ease-in-out;

    span {
        /* FAB shadow */
        box-shadow: 2px 4px 10px 0px rgba(0, 154, 206, 0.15);
    }
}

/* Modal*/
.popup-dialog {
    height: auto;
    width: 100%;
    /* min-height: 184px; */
}
#modal-search-block-list {
    margin: 0 auto;
}
#modal-search-block-list .modal-header {
    padding: 14px 24px 6px 24px;
}

#modal-search-block-list .smodal-close--prev {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    color: #53565a;
    text-align: center;
}

#modal-search-block-list .smodal-close--prev:hover {
    color: #0076A5;
}

#modal-search-block-list .smodal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    /* color: #a7a8a9; */
    z-index: 9;
    font-size: 20px;
}
#modal-search-block-list .artist-infor__name,
#modal-search-block-list .blocked-company__company-name,
#modal-search-block-list .blocked-company__reason-text
{
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 190px;
    display: block;
}

@media (min-width: 768px) {
    #modal-search-block-list {
        width: 600px;
        margin: 30px auto;
    }

    #modal-crop-banner {
        width: 600px;
        margin: 30px auto;
    }
}

.block-list__results{
    width: 100%;
    margin-top: 20px;
}

/*.block-list__results-search {*/
/*    overflow-x: scroll;*/
/*    flex-wrap: nowrap;*/
/*    white-space: nowrap;*/
/*    display: flex;*/
/*    padding-bottom: 24px;*/
/*}*/

/*.block-list__results-search-detail {*/
/*    flex: 0 0 350px;*/
/*    margin-right: 24px;*/
/*    max-width: 350px;*/
/*    padding: 24px;*/
/*    border: 1px solid #F0F0F0;*/
/*}*/

/*.block-list__results-search-detail .project-setting__block-list {*/
/*    border: none;*/
/*    padding: 0;*/
/*}*/

/*.block-list__results-search-detail:last-child {*/
/*    margin-right: 0px;*/
/*}*/

/*.project-setting__block-list {*/
/*    border: 1px solid #F0F0F0;*/
/*    border-radius: 6px;*/
/*    display: flex;*/
/*    justify-content: space-between;*/
/*    padding: 10px;*/
/*    align-items: center;*/
/*    margin-bottom: 20px;*/
/*}*/

.form-group input.form-control, .form-group select.form-control {
    padding: 11px 15px;
    color: #000000;
    border: 1px solid #F0F0F0;
    background-color: #FFFFFF;
    height: auto;
    font-size: 13px;
    border-radius: 4px !important;
    line-height: 20px;
    max-width: 260px;
    padding-left: 42px;
}

.label-form {
    position: relative;
    display: flex;
}

.icon-search {
    position: absolute;
    right: auto;
    padding-left: 16px;
    color: #C4C4C4;
    transform: translateY(50%)
}

.icon--attach {
    color: #53565a;
    transform: rotate(45deg);
    font-size: 14px;
}

.icon--attach .material-symbols-rounded {
    font-size: 14px;
    color: #53565a;
    font-weight: lighter;
}

.icon--close .material-symbols-rounded {
    color: #53565a;
    font-size: 14px;
}

.smodal-close--prev {
    display: flex;
    align-items: center;
    justify-content: center;
}


dialog[open]#modal-search-block-list {
    position: fixed;
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 24px 24px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;

    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}

dialog[open]#modal-crop-banner {
    position: fixed;
    background: rgba(255, 255, 255, 1.0);
    border-radius: 12px;
    border: 1px solid var(--soremo-border);
    padding: 24px 24px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    /* flex親設定 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
    animation-name: fadeIn;
    animation-fill-mode: forwards;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
}


/*.popup-content {*/
/*    margin-bottom: 24px;*/
/*}*/

.popup-body__part-content {
    margin-top: 0px !important;
    margin-bottom: 32px;
}


.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

.btn.btn--primary, .btn.btn--secondary, .btn.btn--tertiary {
    font-family: var(--font-family-R) !important;
    font-size: 13px !important;
    font-weight: var(--font-weight-400);
    line-height: var(--line-height-20);
    background-image: none;
    border-radius: 4px;
    border: none !important;
    padding: 12px 24px !important;
    cursor: pointer;
    text-decoration: none;
}

.btn.btn--primary.btn--disabled, .btn.btn--primary:focus.btn--disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #F0F0F0 !important;
}

.btn.btn--primary, .btn.btn--primary:focus {
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
    min-width: 160px;
}

.btn.btn--primary:hover {
    color: var(--white-color) !important;
    background-color: var(--soremo-deep-blue) !important;
}

.btn.btn--secondary, .btn.btn--secondary:focus {
    color: var(--white-color) !important;
    background-color: var(--grey1-color) !important;
}

.btn.btn--secondary:hover {
    color: var(--white-color) !important;
    background-color: var(--blue-color) !important;
}

.btn.btn--tertiary.close-modal-offer, .btn.btn--tertiary.close-modal-offer:focus, .btn.btn--tertiary.close-modal-offer:hover {
    background-color: transparent !important;
    margin-right: 0;
}

.btn.btn--tertiary, .btn.btn--tertiary:focus {
    color: var(--black2-color) !important;
    background-color: var(--white-color) !important;
}

.btn.btn--tertiary:hover {
    color: var(--blue-color) !important;
    background-color: var(--white-color) !important;
}

.btn.btn--primary.disable, .btn.btn--primary.disabled,
.form-group input.form-control.disabled, .form-group select.form-control.disabled {
    pointer-events: none;
    background-color: var(--soremo-border) !important;
    color: var(--grey1-color) !important;
    background-image: none;
}

.material-symbols-rounded#thumnail-add-icon {
    pointer-events: none;
}

.mcommment-file__name {
    display: inline-flex;
}

.mcommment-file__delete {
    display: flex;
}

button.close {
    float: right;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: 0.2;
    background: 0 0;
    border: 0;
    padding: 0;
    /* つまみの丸み */
    border-radius: 0px;

}

button.close span {
    font-size: 28px !important;
    font-weight: 700 !important;;
    line-height: 1 !important;

}
button.close:focus-visible {
    outline: unset;
}
.modal-header {
    padding-bottom: 20px;
}

button.close:hover {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    opacity: 0.5;
    line-height: 100%;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.modal-footer .btn {
    border: 1px solid transparent;
    font-size: 14px !important;
    font-weight: 400 !important;
    padding: 8.5px 15px;
    min-width: 80px;
    line-height: 19px;
    border-radius: 4px;
}
.modal-footer .btn span{
    font-size: 18px !important;
}

.btn-primary {
    color: #fff;
    background-color: #337ab7;
}
.pull-left {
    float: left!important;
}
.btn-group, .btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

.image-banner {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1
}

.image-banner.hover {
    filter: brightness(80%);
}

.uploaded-file-container {
    padding: 4px 14px;
}

.image-banner[src=""] {
    display: none;
}

.hide {
    display: none;
}

.error-input {
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 200%;
    color: var(--error-color);
}

.scene-input {
    display: flex;
    flex-direction: column;
    align-items: center;
}


.code-font1 {
    font-family: "corporate-logo-ver2", sans-serif;
    font-weight: 700;
    font-style: normal;

    /* font-size: 30px; */
    /* font-size: 38px; */
    font-size: clamp(1.875rem, 1.375rem + 2.5vw, 2.375rem);

    white-space: nowrap;
}

.code-font2 {
    font-family: "dnp-shuei-mincho-pr6n", sans-serif;
    font-weight: 400;
    font-style: normal;

    /* font-size: 24px; */
    /* font-size: 36px; */
    font-size: clamp(1.5rem, 0.75rem + 3.75vw, 2.25rem);

    white-space: nowrap;
}

.code-font4 {
    font-family: "futura-pt", sans-serif;
    font-weight: 300;
    font-style: italic;

    /* font-size: 38px; */
    /* font-size: 42px; */
    font-size: clamp(2.375rem, 2.125rem + 1.25vw, 2.625rem);
}

.code-font5 {
    font-family: "trajan-pro-3", serif;
    font-weight: 300;
    font-style: normal;

    /* font-size: 32px; */
    /* font-size: 34px; */
    font-size: clamp(2rem, 1.875rem + 0.63vw, 2.125rem);

    text-transform: uppercase;
}

.code-font6 {
    font-family: "lindsey-signature", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 36px;
}


.banner-color1 {
    background: #53565A;

    > div:first-child {
        color: white;
    }
}

.banner-color2 {
    background: #3B5375;

    > div:first-child {
        color: white;
    }
}

.banner-color3 {
    background: linear-gradient(to right, #3b5375, #33578b, #2a5aa1, #255db6, #2a5ecb);

    > div:first-child {
        color: white;
    }
}

.banner-color4 {
    background: linear-gradient(to left, #6fa8ed, #5a96e6, #4783de, #3771d5, #2a5ecb);

    > div:first-child {
        color: white;
    }
}

.banner-color5 {
    background: #6EB370;
    background: linear-gradient(135deg, #6eb370, #26b89b, #00b8c5, #00b2e3, #6fa8ed);

    > div:first-child {
        color: white;
    }
}

.banner-color6 {
    background-color: #fcfcfc;
    background: linear-gradient(to right, #6eb370, #94be72, #b8c979, #dad384, #fadd94);;

    > div:first-child {
        color: white;
    }
}

.banner-color7 {
    background: linear-gradient(to right, #ffa254, #ffac83, #ffbbac, #ffcecf, #ffe3e8);

    > div:first-child {
        color: white;
    }
}

.banner-color8 {
    background: linear-gradient(135deg, #ea6586, #f2879e, #f8a6b6, #fdc5cf, #ffe3e8);

    > div:first-child {
        color: white;
    }
}

.banner-color9 {
    background: linear-gradient(135deg, #c68ed5, #d483c6, #df77b3, #e66d9e, #ea6586);

    > div:first-child {
        color: white;
    }
}

.banner-color10 {
    background: linear-gradient(135deg, rgb(168, 186, 181), rgb(178, 196, 191), rgb(188, 207, 202), rgb(198, 217, 212), rgb(208, 228, 223));

    > div:first-child {
        color: white;
    }
}

.banner-color11 {
    background: linear-gradient(135deg, rgb(255, 151, 143), rgb(254, 175, 159), rgb(251, 199, 175), rgb(247, 221, 191), rgb(240, 244, 208));

    > div:first-child {
        color: white;
    }
}

.banner-color12 {
    background: linear-gradient(135deg, rgb(76, 224, 221), rgb(131, 229, 220), rgb(171, 233, 218), rgb(204, 237, 217), rgb(235, 241, 215));

    > div:first-child {
        color: white;
    }
}

.banner-color13 {
    background: linear-gradient(135deg, rgb(11, 207, 228), rgb(102, 213, 229), rgb(145, 219, 229), rgb(180, 225, 230), rgb(211, 231, 230));

    > div:first-child {
        color: white;
    }
}


.banner-color14 {
    background: linear-gradient(135deg, rgb(247, 246, 247), rgb(232, 239, 239), rgb(217, 232, 231), rgb(201, 224, 224), rgb(186, 217, 216));

    > div:first-child {
        color: black;
    }
}

.banner-color15 {
    background: linear-gradient(135deg, rgb(254, 254, 254), rgb(246, 248, 243), rgb(238, 241, 232), rgb(231, 235, 222), rgb(223, 229, 211));

    > div:first-child {
        color: black;
    }
}

.banner-color16 {
    background: linear-gradient(135deg, rgb(251, 251, 251), rgb(249, 246, 238), rgba(247, 241, 225, 0.5), rgba(245, 236, 213, 0.6), rgba(242, 231, 200, 0.7));

    > div:first-child {
        color: black;
    }
}

.banner-color17 {
    background: linear-gradient(135deg, rgb(253, 253, 253), rgb(247, 247, 246), rgb(242, 242, 239), rgb(236, 236, 232), rgb(231, 231, 225));

    > div:first-child {
        color: black;
    }
}

.banner-color18 {
    background: linear-gradient(315deg, #f0f0f0, #f3f3f3, #f6f6f6, #f9f9f9, #fcfcfc);

    > div:first-child {
        color: black;
    }
}

.d-block-header {
    display: block;
}

.sheader {
    background-color: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    left: 0;
    width: 100%;
    top: -64px;
}

.sheader-pc {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    padding: 24px;
}

@media (max-width: 992px) {
    .sheader-pc__left {
        display: none;
    }
}

.sheader-pc__right {
    display: flex;
    align-items: center;
}

@media (max-width: 992px) {
    .sheader-pc__right {
        flex: 1;
        justify-content: flex-end;
    }
}

@media (max-width: 1140px) {
    .sheader-pc {
        padding: 0 16px;
        background-color: #fff;
    }
}

.smenu-pc .mcontainer {
    padding: 0;
}

.sheader-links {
    display: flex;
    align-items: center;
}

/* .sheader-links .sheader-link.text-link {
    color: #A7A8A9;
    font-size: 16px;
    font-weight: 300 !important;
    line-height: 12px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif' !important;
} */

.sheader-account-link {
    color: #a7a8a9;
}

header .avatar--round {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.avatar {
    display: inline-flex;
    align-items: center;
    overflow: hidden;
}

.avatar--32 .avatar-icon {
    font-size: 32px;
}

.avatar .avatar-icon img {
    border-radius: 50%;
    vertical-align: middle;
    border: 0;
    overflow-clip-margin: content-box;
    overflow: clip;
}


.sheader-link {
    color: #000;
    display: inline-block;
    margin: 0 24px;
    font-size: 13px;
    text-transform: uppercase;
}

.bdropdown {
    position: relative;
}

[role=button] {
    cursor: pointer;
}

.bdropdown-toggle .icon {
    color: #a7a8a9;
    font-size: 16px;
}

[class^='icon--'],
[class*=' icon--'] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: "soremoicons" !important;
    /* stylelint-disable-line */
    /* speak: never; */
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.bdropdown-menu.dropdown-menu {
    z-index: 200;
}

.bdropdown-menu {
    margin-left: -20vh;
    right: 0 !important;
    left: auto !important;
    display: none;
}
.open>.dropdown-menu {
    display: block !important;
}


.dropdown-menu {
    position: absolute;
    float: left;
    min-width: 160px;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
}

.bdropdown-item:hover {
    background-color: #f0f0f0;
    color: #000;
}

.bdropdown-menu {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    box-shadow: none;
    width: 180px;
    padding: 16px 0px 16px;
    margin-top: 7.5px;
    margin-left: -24px;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
    top: 100% !important;
}

.bdropdown-item {
    font-size: 13px !important;
    color: #000;
    display: block;
    line-height: 150%;
    padding: 16px 16px 16px;
    font-size: 13px;
}

#modal_mileage_popup.modal, body.no-backdrop .modal {
    background-color: transparent !important;
    position: absolute;
    top: 50px !important;
    right: 0;
    left: auto;
    bottom: auto;
    overflow: hidden;
}

.icon--sicon-dropdown:before {
  content: ""; }

.icon-bookmark-navbar:after {
    opacity: 0;
    content: '';
    width: 2px;
    height: 2px;
    background: var(--bg-album);
    border-radius: 4px;
    bottom: -80px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: 0s;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.icon--sicon-bookmark:before {
    content: '\e949';
}

.sheader-link .fa-bookmark {
    font-size: 20px;
}

.sheader-link .fa-bookmark {
    color: #A7A8A9;
    font-size: 20px;
}

.icon-bookmark-navbar {
    position: relative;
}

.sheader-link span:hover, .sheader-link.current span {
    color: #009ace;
}

.sheader-info {
    display: flex;
    align-items: center;
    position: relative;
}

@media (max-width: 992px) {
    .sheader-info {
        min-width: 35px;
    }

    .sheader-dropdown.bdropdown {
        padding-left: 4px;
    }
}

@media (max-width: 600px) {

    .nav-master-admin .sheader-link {
        margin: 0 6px;
        font-size: 7px;
    }
}

.datePicker.flatpickr-input.flatpickr-mobile {
    background-color: rgb(255, 255, 255) !important;
    color: rgb(0, 0, 0) !important;
}

@media (max-width: 695px) {

    #project-setting #modal-search-block-list .smodal-close {
        top: 15px;
        right: 0;
    }

    .indicator-icon .material-symbols-rounded {
        font-size: 128px;
    }
    .block-navigation-bar {
        padding: 0;
    }
    .block-navigation-bar .navigation-bar {
        padding: 16px 16px 0px;
        margin: 0;
        height: 100%;
    }
    .block-navigation-bar .navigation-bar a {
        max-width: unset;
        padding: 0;
        align-items: baseline;
        width: 100%;
        justify-content: center;
    }
    .block-navigation-bar .navigation-bar a .nav-bar-item {
         max-width: 128px;
         width: 100%;
     }
}