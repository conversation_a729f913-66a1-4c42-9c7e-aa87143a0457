/* Color, font-size */
/* :root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
    --font-family-R: 'A+mfCv-AXISラウンド 50 R StdN';
    --font-family-L: 'A+mfCv-AXISラウンド 50 L StdN';
} */
/* End color, font-size  */

/* Upload contract */
/* .popup-content {
    padding: 32px !important;
} */

.modal.popup-container .modal-dialog.popup-dialog {
    /* position: relative; */
    /* top: 0; */
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
}

#modal-upload-contract-plan .popup-content,
#modal-upload-contract .popup-content,
#modal-upload-plan .popup-content {
    padding: 0 !important;
    max-height: 100vh;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 100vw;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 0;
    border: none;
}

#modal-upload-contract-plan .button-footer-component,
#modal-upload-contract .button-footer-component,
#modal-upload-plan .button-footer-component {
    max-width: 1140px;
    width: 100%;
}

#modal-upload-contract-plan .popup-body,
#modal-upload-contract .popup-body,
#modal-upload-plan .popup-body {
    max-height: calc(100vh - 104px - 152px);
    height: calc(100vh - 104px - 152px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 24px;
    max-width: 100vw;
    width: 100%;
    display: flex;
    justify-content: center;
}

#modal-upload-contract-plan .sub-container-body, #modal-confirm-upload .sub-container-body,
#modal-upload-contract .sub-container-body,
#modal-upload-plan .sub-container-body {
    max-width: 887px;
    width: 100%;
    height: 100%;
}

#modal-upload-contract-plan .popup-header, #modal-confirm-upload .popup-header,
#modal-upload-contract .popup-header,
#modal-upload-plan .popup-header {
    height: 152px;
    padding: 56px 0 0;
    max-width: 1140px;
    width: 100%;
}

#modal-upload-contract-plan .popup-header hr, #modal-confirm-upload .popup-header hr,
#modal-upload-contract .popup-header hr, #modal-upload-plan .popup-header hr {
    margin: 0;
    border-bottom: none;
}

#modal-upload-contract-plan .popup-footer,
#modal-upload-contract .popup-footer,
#modal-upload-plan .popup-footer  {
    height: 104px;
    margin: 0;
    padding: 16px 24px 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    background-color: #FCFCFC;
}

#modal-upload-contract-plan .row-data-contract,
#modal-upload-contract .row-data-contract,
#modal-upload-plan .row-data-contract  {
    background-color: #FCFCFC;
    width: auto; border: 1px solid #F0F0F0;
    border-radius: 4px;
    padding: 8px 8px 0 8px;
    margin-bottom: 24px;
    /* width: 100%; */
}

#modal-upload-contract-plan .switch-checkbox-public,
#modal-upload-contract .switch-checkbox-public,
#modal-upload-plan .switch-checkbox-public {
    background: #F0F0F0;
    border: 2px solid var(--soremo-border);
    box-shadow:none;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-left: 9px;
}

#modal-upload-contract-plan .switch-checkbox-public span,
#modal-upload-contract .switch-checkbox-public span,
#modal-upload-plan .switch-checkbox-public span {
    color: var(--grey1-color);
}

#modal-upload-contract-plan .switch-checkbox-public.checked span,
#modal-upload-plan .switch-checkbox-public.checked span,
#modal-upload-contract .switch-checkbox-public.checked span {
    color: var(--black1-color);
}

#modal-upload-contract-plan .switch-checkbox-public.checked,
#modal-upload-contract .switch-checkbox-public.checked,
#modal-upload-plan .switch-checkbox-public.checked {
    background: var(--white-color);
    border: 2px solid var(--blue-color);
    box-shadow:none;
    border-radius: 4px;
}

#modal-upload-contract-plan .component-tab-container,
#modal-upload-contract .component-tab-container,
#modal-upload-plan .component-tab-container {
    margin-bottom: 24px;
}

#modal-upload-contract-plan .component-tab-container.tab-note-form-contract,
#modal-upload-contract .component-tab-container.tab-note-form-contract,
#modal-upload-plan .component-tab-container.tab-note-form-contract {
    margin-bottom: 0;
}

#modal-upload-contract-plan .component-datetime-container.calendar-time,
#modal-upload-contract .component-datetime-container.calendar-time,
#modal-upload-plan .component-datetime-container.calendar-time {
    width: 100%;
}

#modal-upload-contract-plan .SumoSelect,
#modal-upload-contract .SumoSelect,
#modal-upload-plan .SumoSelect {
    width: 100%;
    /* border: 1px solid transparent; */
}

.custom-input-component .sselect-wrapper .SumoSelect > .CaptionCont > label {
    margin-right: 8px !important;
}

#modal-upload-contract-plan .form-textarea-form-contract-container,
#modal-upload-contract .form-textarea-form-contract-container,
#modal-upload-plan .form-textarea-form-contract-container {
    background: var(--background-color);
    padding: 8px;
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
}

#modal-upload-contract-plan .form-textarea-form-contract-container hr,
#modal-upload-contract .form-textarea-form-contract-container hr,
#modal-upload-plan .form-textarea-form-contract-container hr {
    margin: 8px 0;
    width: auto;
    color: var(--soremo-border);
}

#modal-upload-contract-plan .button-scroll-top,
#modal-upload-contract .button-scroll-top,
#modal-upload-plan .button-scroll-top {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 44px;
    padding-right: 8px;
    border-top: 1px solid #F0F0F0;
}

#modal-upload-contract-plan .button-scroll-top-container:hover svg path,
#modal-upload-contract .button-scroll-top-container:hover svg path,
#modal-upload-plan .button-scroll-top-container:hover svg path {
    fill: var(--soremo-deep-blue);
}

#modal-upload-contract-plan .button-scroll-top svg:hover,
#modal-upload-contract .button-scroll-top svg:hover,
#modal-upload-plan .button-scroll-top svg:hover {
    cursor: pointer;
}

#modal-upload-contract-plan .popup-footer .btn-popup-close,
#modal-upload-contract .popup-footer .btn-popup-close,
#modal-upload-plan .popup-footer .btn-popup-close {
    width: 96px;
    min-width: 96px;
    margin-right: 12px;
    background: #fcfcfc !important;
}

#modal-confirm-upload .popup-footer .btn--tertiary {
    width: 96px;
    min-width: 96px;
}

#modal-upload-contract-plan .tab-option-form-contract .tab-content,
#modal-upload-contract .tab-option-form-contract .tab-content,
#modal-upload-plan .tab-option-form-contract .tab-content {
    padding: 12px 8px 0 8px;
}

#modal-upload-contract .tab-option-form-contract .tab-content {
    padding: 8px 8px 0 8px !important;
}

#modal-upload-contract-plan .form-group,
#modal-upload-contract .form-group,
#modal-upload-plan .form-group {
    margin: 0 0 16px;
}

.col-sm-12.form-group {
    width: 100%;
}

#modal-upload-contract-plan .popup-header h3, #modal-confirm-upload .popup-header h3,
#modal-upload-contract .popup-header h3, #modal-upload-plan .popup-header h3 {
    line-height: 100%;
    margin: 23px 0 32px;
}

#modal-upload-contract-plan .popup-header p,
#modal-upload-contract .popup-header p,
#modal-upload-plan .popup-header p {
    margin: 0;
}

#modal-upload-contract-plan hr, .modal-confirm-upload hr,
#modal-upload-contract hr, #modal-upload-plan hr {
    margin: 32px 0;
}

.form-row.row {
    margin: 0 -15px;
    justify-content: left;
}

.contract__form-wrap h4 {
    margin-bottom: 16px;
}

.contract__form-label {
    margin-bottom: 4px;
}

.contract__form-multi {
    display: block;
    padding: 6px 0;
}

.contract__form-multi .input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 300;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
    margin: 12px 0;
}

.contract__form-multi .input-radio p {
    margin: 8px 0 0;
}

.contract__form-multi .input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.contract__form-multi .input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

#modal-upload-plan .form-control:not(:placeholder-shown) {
    border: 1px solid var(--grey1-color);
}

.contract__form-multi .input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}

.form-row .form-group label {
    margin: 0;
}

.input-time {
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    padding: 12px 16px;
    color: var(--black1-color);
    width: 100%;
    cursor: pointer;
}

.sform-group__input-group {
    padding: 0;
}

.sform-group__input-group .mcalendar, .sform-group__input-group .sform-group__append {
    cursor: pointer;
}

.form-check.custom-switch .switch-label {
    font-size: 13px;
    margin-left: 8px;
}

.popup-body__item .custom-switch .form-check-group input[disabled] ~ .switch-slider,
.popup-body__item .custom-switch .form-check-label {
    cursor: default;
}

#modal-confirm-upload .popup-footer {
    padding-top: 6px;
    text-align: right;
    background-color: #FCFCFC;
}

#modal-confirm-upload .modal-dialog {
    margin: 0;
}

#modal-confirm-upload .popup-content {
    padding: 0 !important;
    max-height: 100vh;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 100vw;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 0;
    border: none;
}

#modal-confirm-upload .popup-content .popup-header {
    max-width: 1140px;
    width: 100%;
}

#modal-confirm-upload .popup-content .popup-footer {
    max-width: 100vw;
    width: 100%;
    height: 104px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 24px 32px;
    margin: 0;
}

#modal-confirm-upload .popup-content .popup-body {
    max-width: 800px;
    width: 100%;
    max-height: calc(100vh - 104px - 152px);
    height: calc(100vh - 104px - 152px);
    padding: 24px;
    max-width: 100vw;
    width: 100%;
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    justify-content: center;
}

#modal-confirm-upload .button-container {
    width: 100%;
    max-width: 1140px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.form-textarea textarea {
    width: 100%;
    word-break: break-word;
    white-space: pre-line;
}

#modal-upload-contract .form-textarea textarea:focus {
    border: 1px solid var(--soremo-deep-blue) !important;
}

#modal-upload-contract-plan .component-input-select,
#modal-upload-contract .component-input-select,
#modal-upload-plan .component-input-select {
    margin: 0;
}

#modal-upload-contract-plan .sform-group__select:after,
#modal-upload-contract .sform-group__select:after,
#modal-upload-plan .sform-group__select:after {
    bottom: 50%;
    transform: translate(0, 50%);
}

.contract__form-content-item, .contract__form-content-label {
    display: flex;
    align-items: center;
    z-index: 9999;
}

.contract__form-content-label {
    margin-bottom: 4px;
    min-height: 26px;
    padding: 0 8px;
}

.contract__form-content-item {
    background-color: var(--soremo-border);
    margin-bottom: 8px;
    padding: 3px 8px;
    border-radius: 4px;
}

.contract__form-content-item.ui-sortable-helper .contract__form-action-content{
    display: none !important;
}

.contract__form-input {
    margin-right: 4px;
}

.contract__form-input .bodytext--13 {
    color: var(--grey1-color);
}

.input_item, .input_item .input-select-container {
    width: 160px;
    min-width: 160px;
}

.input_price {
    width: 128px;
    min-width: 128px;
    margin-right: 0;
}

.input_quantity {
    width: 64px;
    min-width: 64px;
}

.input_unit, .input_unit .input-select-container {
    width: 96px;
    min-width: 96px;
    margin-right: 0;
}

.input_total {
    width: 120px;
    min-width: 120px;
}

.input_note {
    width: auto;
    min-width: 145px;
    margin-right: 0;
}

.contract__form-select-item, .contract__form-price, .contract__form-quantity, .contract__form-unit, .contract__form-total, .contract__form-content_note {
    width: 100%;
}

.contract__form-price .bodytext--13 , .contract__form-total .bodytext--13 {
    color: var(--black1-color);
}

.contract__form-quantity {
    text-align: right;
}

.contract__form-total {
    line-height: 54px;
}

.contract__form-price {
    display: flex;
    align-items: center;
}

.contract__form-price input {
    text-align: right;
}

.contract__form-price span {
    margin-left: 4px;
}

.multiplication svg, .equal svg {
    margin: 0 4px;
}

#contract__add-button.form-group {
    padding: 0;
    margin: 0 0 16px 0;
    z-index: 1
}

.sform-group__select {
    position: relative;
}

.sform-group__select:after {
    content: '\e920';
    font-family: 'soremoicons';
    color: var(--grey1-color);
    right: 14px;
    bottom: 9px;
    position: absolute;
    pointer-events: none;
}

.contract__add-row {
    background-color: var(--white-color);
    color: var(--grey1-color);
    border: 1px solid var(--soremo-border);
    border-radius: 4px;
    padding: 12px;
    text-align: center;
    display: block;
    cursor: pointer;
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.05);
    height: 64px;
}

.contract__add-row:hover {
    background-color: var(--blue-color);
    color: var(--background-color) !important;
}

.contract__add-row:hover .contract__add-icon {
    background-color:var(--background-color);
}

.contract__add-wrap .contract__add-icon {
    margin-bottom: 2.67px;
    height: 100%;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: var(--soremo-border);
    display: flex;
    justify-content: center;
    align-items: center;
}

.contract__add-wrap.contract__add-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.contract__add-wrap .contract__add-icon .icon {
    color: var(--white-color);
    font-size: 16px;
    border-radius: 50%;
}

.contract__add-row:hover .contract__add-wrap .contract__add-icon .icon {
    background-color: var(--white-color);
    color: var(--blue-color);
}

.contract__add-row:hover .contract__add-wrap p {
    color: var(--white-color);
}

.contract__add-wrap p {
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
    color: var(--black1-color);
}

.contract__form-item {
    display: flex;
    justify-content: space-between;
    line-height: 200%;
    margin-bottom: 8px;
    color: var(--black1-color);
}

.contract__form-item-total {
    margin-left: 30px;
}

.contract__form-total-wrap {
    padding-top: 8px;
    border-top: 1px solid var(--soremo-border);
}

.contract__form-total-list {
    float: left;
    width: 292px;
}

.popup-body__item {
    padding: 8px 0;
}


.popup-body__export-list {
    display: flex;
}

.popup-body__export-item {
    position: relative;
    padding: 24px;
    border: 2px solid var(--soremo-border);
    border-radius: 8px;
    text-align: center;
}

.popup-body__export-item.checked, .popup-body__export-item:hover {
    border: 2px solid var(--blue-color);
    cursor: pointer;
}

.popup-body__export-item:not(:first-child) {
    margin-left: 20px;
}

.popup-body__export-list .download_document {
    margin: 0;
    font-size: 32px;
    color: var(--black2-color);
    background-color: var(--soremo-border);
    width: 68px;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.icon--checked::before {
    font-size: 18px;
    content: "";
    position: absolute;
    top: 8px;
    right: 8px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 1px solid var(--black2-color);
    background-color: var(--white-color);
    cursor: pointer;
}

.popup-body__export-item.checked .icon--checked::before {
    background-image: url(../images/checked_button.png);
    border: none;
}

.text-title {
    margin-bottom: 4px;
}

.text-content {
    word-break: break-word;
    white-space: pre-line;
}

#modal-confirm-upload .table tr th, #modal-confirm-upload .table tr td {
    border: none;
    color: var(--black1-color);
}

#modal-confirm-upload .table tr th {
    font-family: var(--font-family-R);;
    font-size: 13px;
    line-height: 20px;
    color: var(--black1-color);
    font-weight: 400;
    width: 50%;
}

#modal-confirm-upload .table tr td {
   
    font-size: 13px;
    line-height: 20px;
    color: var(--black1-color);
    font-weight: 300;
    padding: 5px;
    word-break: break-word;
    white-space: pre-line;
}

.table tr td:first-child, .table tr th:first-child  {
    padding: 5px 5px 5px 0 !important;
}

#modal-confirm-upload .custom-switch .form-check-group input:checked[disabled] ~ .switch-slider:before {
    background-color: #009ace;
    transform: translateX(13px);
}

#modal-confirm-upload .custom-switch .form-check-group input:checked[disabled] + .switch-slider {
    background-color: #e9f9ff;
}

#modal-confirm-upload .custom-switch .form-check-group input[disabled] ~ .switch-slider:before {
    background-color: #A7A8A9;
}

.form-group-action {
    cursor: pointer;
    width: 20px;
}

.form-group-action .delete-row {
    font-size: 18px;
    color: var(--grey1-color);
}

.form-group-action .delete-row:hover {
    color: var(--blue-color);
}

#id_business_content_value table {
    width: auto !important;
}

#id_business_content_value table tbody tr td:nth-child(1) {
    min-width: 100px;
    width: auto;
}

#id_business_content_value table tbody tr td:nth-child(2) {
    text-align: right;
    min-width: 50px;
    width: auto;
}

#id_business_content_value table tbody tr td:nth-child(3) {
    min-width: 50px;
    width: auto;
}

#id_business_content_value table tbody tr td:nth-child(4) {
    width: auto;
    min-width: 300px;
}

.header-table {
    padding-bottom: 8px !important;
}

.disable-click {
    pointer-events:none;
}

.contract__form-action-content {
    display: grid;
    cursor: pointer;
    width: auto;
    max-width: 40px;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    right: 15px;
    height: 55px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-left: 9px;
    padding-right: 9px;
}

.show-action {
    opacity: 1;
    pointer-events: all;
}

.contract__form-action-content.disabled {
    pointer-events: none;
    color: var(--grey1-color);
    background-image: none;
}

.contract__form-action-content .icon {
    font-size: 18px;
    color: var(--grey1-color);
}

.contract__form-action-content .icon:hover {
    color: var(--blue-color);
}

.custom-switch .form-check-group .switch-slider {
    left: 0 !important;
}


#modal-upload-contract label > div, #modal-upload-plan label > div {
    line-height: 200%;
    margin: 0;
}

#modal-upload-contract label, #modal-upload-plan label {
    margin: 0;
}

@media (max-width: 992px) {
    #modal-upload-contract-plan .popup-header h3, #modal-confirm-upload .popup-header h3,
    #modal-upload-contract .popup-header h3, #modal-upload-plan .popup-header h3 {
        font-size: 24px !important;
        margin: 20px 0 32px;
    }

    #modal-upload-contract-plan .popup-content,
    #modal-upload-contract .popup-content,
    #modal-upload-plan .popup-content {
        padding: 15px;
    }

    #id_budget {
        width: calc(100% - 80px);
    }

    .contract__form-content-list {
        width: fit-content;
    }

    #modal-upload-contract-plan .popup-body, #modal-confirm-upload .popup-body,
    #modal-upload-contract .popup-body, #modal-upload-plan .popup-body {
        padding: 16px;
    }
    
    #modal-upload-contract-plan .popup-header, #modal-confirm-upload .popup-header,
    #modal-upload-contract .popup-header, #modal-upload-plan .popup-header {
        padding-left: 16px;
        padding-right: 16px;
        height: 132px;
    }

    #modal-upload-contract-plan .row-data-scroll-container,
    #modal-upload-contract .row-data-scroll-container,
    #modal-upload-plan .row-data-scroll-container {
        width: calc(100% + 15px);
    }

    #modal-upload-contract-plan .component-datetime-container.calendar-time,
    #modal-upload-contract .component-datetime-container.calendar-time,
    #modal-upload-plan .component-datetime-container.calendar-time {
        width: 100%;
    }

    #modal-upload-contract-plan .popup-content,
    #modal-upload-contract .popup-content,
    #modal-upload-plan .popup-content {
        max-height: 100vh;
    }
    
    #modal-upload-contract-plan .popup-body, #modal-confirm-upload .popup-body,
    #modal-upload-contract .popup-body, #modal-upload-plan .popup-body {
        max-height: calc(100vh - 120px - 132px) !important;
        height: calc(100vh - 120px - 132px) !important;
    }

    #modal-upload-contract-plan .popup-footer, #modal-confirm-upload .popup-footer,
    #modal-upload-contract .popup-footer, #modal-upload-plan .popup-footer {
        height: 120px !important;
        padding: 32px 16px !important;
    }
}

@media (max-width: 739px) {
    .form-group {
        width: 100%;
    }

    .popup-body__export-item {
        padding: 16px;
    }
}

.blank_item i.icon--sicon-equal {
    pointer-events: none;
}

.form-upload-contract-tab > .nav.component-tab {
    margin-bottom: 24px;
}

.custom-checkbox .form-check-label:after {
    content: '';
    border: 1px solid transparent;
    transform: translateY(-50%);
    transition: all .15s ease;
    z-index: 1;
    height: 24px;
    width: 24px;
    border-radius: 3px;
    position: absolute;
    top: 50%;
    left: 0;
    right: auto;
    bottom: auto;
    background-image: url(../images/icon_checkbox_uncheck.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.custom-checkbox .form-check-label:hover:after {
    background-image: url(../images/icon_checkbox_uncheck_hover.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.custom-checkbox .form-check-input:checked ~ .form-check-label:before {
    visibility: hidden; 
}

.custom-checkbox .form-check-input:checked ~ .form-check-label:after {
    height: 24px;
    width: 24px;
    background-image: url(../images/icon_checkbox_checked.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-color: transparent;
    background-color: #ffffff;
}

.checkbox-and-button-container {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
}

.card-select-container {
    display: flex;
    color: #fff;
    min-height: 100%;
    align-items: center;
    justify-content: center;
}

.card-select-container .InputGroup {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.card-select-container input[type="radio"] {
    visibility: hidden; /* 1 */
    height: 0; /* 2 */
    width: 0; /* 2 */
}

.card-select-container label {
    display: flex;
    flex: auto;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #a7a8a9;
    color: white;
    border-radius: 6px;
    transition: color .1s ease-out,
    background-color .1s ease-in,
    box-shadow .4s ease-in-out;
    user-select: none;
    padding: 15px;
    flex-direction: column;
}

.card-select-container label:last-of-type {
    margin-right: 0;
}

.card-select-container input[type="radio"]:checked + label {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    background-color: #0076a5;
    color: #fff;
}

.card-select-container input[type="radio"]:hover:not(:checked) + label {
    background-color: #009ace;
    color: #fff;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

#modal-upload-contract .account_upload-file #uploadFile,
#modal-drag-file .account_upload-file #uploadFile {
    padding: 10px;
    cursor: pointer;
    background: #fcfcfc;
    border: 1px dashed #D3D3D3;
    border-radius: 6px;
    text-align: center;
    min-height: 96px !important;
    margin-top: 8px;
}

#modal-upload-contract .account_upload-file #uploadFile p,
#modal-drag-file .account_upload-file #uploadFile p {
    color: #000;
    margin-top: -8px;
    font-weight: 300;
    font-size: 8px;
    line-height: 100%;
}

#modal-upload-contract .account_upload-file .fallback:hover .icon_upload_svg path,
#modal-drag-file .account_upload-file .fallback:hover .icon_upload_svg path {
    fill: #fff;
}

#modal-drag-file.modal.popup-container .modal-dialog.popup-dialog {
    position: absolute;
    top: 50% !important;
    left: 50%!important;
    transform: translate(-50%,-50%) !important;
    -webkit-transform: translate(-50%,-50%) !important;
}

#modal-upload-plan .form-group input.form-control, #modal-upload-plan .form-group select.form-control,
#modal-upload-contract .form-group input.form-control, #modal-upload-contract .form-group select.form-control {
    min-height: 50px !important;
    margin: 0;
}
/* End Upload contract */
