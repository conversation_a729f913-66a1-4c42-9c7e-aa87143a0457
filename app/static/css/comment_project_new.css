.prdt .container .new-video-menu .main-talk-room {
   width: 100%;
} 

.prdt .mmessage-list .mmessage.clicked .dropdown-comment-new .more-action-hoz {
    width: 20px;
    height: 10px;
}

.prdt .mmessage-list .mmessage.clicked .mmessage-info .message-info-container .mmessage-status .mmessage-time {
    margin-bottom: 2px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif';
    font-size: 8px;
    line-height: 100%;
}

.prdt .mmessage-list .mmessage.clicked .mmessage-info .mmessage-user .mmessage-user-seen .avatar-image {
    width: 12px;
    height: 12px;
}

span.material-symbols-rounded.img-resolve-comment {
    color: #F0F0F0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-info .dropdown.dropdown-comment-new.dropdown-comment {
    justify-content: start;
    display: flex;
    align-items: end;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received {
    justify-content: end;
    display: flex;
    align-items: end;
}

.prdt .dropdown-menu-comment {
    border-radius: 6px;
    background-color: #FFF;
    min-width: 200px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
    border-color: #FFF;
    padding: 0 8px;
}
.prdt .top-modal {
    top: -100px
}

.prdt .dropdown-menu-comment li .dropdown-item {
    display: flex;
    justify-content: space-between;
    padding: 0;
    margin: 0;
}

.prdt .dropdown-menu-comment li .dropdown-item:hover, .dropdown-menu-comment li .dropdown-item:focus {
    background-color: #FFF;
}

.prdt .dropdown-menu-comment li .dropdown-item .txt-item-comment {
    font-size: 13px;
    color: #000;
    line-height: 200%;
}

.prdt .dropdown-menu-comment li {
    padding: 8px 0;
}

.prdt .dropdown-menu-comment li:not(.last-action-comment) {
    border-bottom: 1px solid #F0F0F0;
}

/* .prdt .dropdown-item .txt-green {
    color: #2CC84D !important;
} */

.prdt .last-action-comment .mmessage-reply.active, .mmessage--sent .dropdown-menu-comment li .dropdown-item.active {
    background-color: #FFF;
}

.prdt .dropdown-item.mmessage-resolve .img-resolve-comment {
    width: 24px;
    height: 24px;
}

.prdt .dropdown-item.mmessage-resolved .txt-item-comment {
    color: #009ACE !important;
}

.prdt .dropdown-item.mmessage-resolve .img-resolve-comment {
    background: url("../../images/scene-detail/icon-resolve.svg");
}

.prdt .dropdown-item.mmessage-resolved .img-resolve-comment {
    background: url("../../images/scene-detail/icon-resolve-active.svg") !important;
}

.prdt .dropdown-item.mmessage-edit .img-edit-comment, .prdt .dropdown-item.mmessage-reply .img-reply-comment {
    width: 24px;
    height: 24px;
}

.prdt .dropdown-item.mmessage-edit .img-edit-comment {
    background: url("../../images/scene-detail/icon-edit.svg");
}

.prdt .mmessage.clicked.editing .dropdown-item.mmessage-edit .img-edit-comment {
    background: url("../../images/scene-detail/icon-edit-active.svg") !important;
}


.prdt .dropdown-item.mmessage-reply .img-reply-comment {
    background: url("../../images/scene-detail/icon-reply.svg");
}

.prdt .mmessage.clicked.reply .dropdown-item.mmessage-reply .img-reply-comment {
    background: url("../../images/scene-detail/icon-reply-active.svg") !important;
}

.dropdown-comment-new {
    place-self: center;
    cursor: pointer;
}

.prdt .mmessage-list .mmessage.mmessage--sent.clicked .mmessage-info .dropdown.dropdown-comment-new .dropdown-toggle.show-more-comment {
    display: none;
    padding-left: 1px;
}

.prdt .mmessage-list .mmessage.clicked .messenger-image-preview-content {
    padding: 8px;
    background-color: #FFF;
}

.prdt .mmessage-list .mmessage.clicked.reply .mmessage-main .mmessage-content {
    background-color: #009ace !important;
}

.prdt .mmessage-list .mmessage.clicked .messenger-image-preview-content .image-preview-comment {
    margin-bottom: 6px;
}

.prdt .mmessage-list .mmessage.clicked .mmessage-content {
    padding: 0;
    border-radius: 6px;
    border: none;
    margin: 0 0 0 4px;
}

.prdt .mmessage-list .mmessage.mmessage--sent.clicked .mmessage-content {
    padding: 8px;
}

.prdt .mmessage-list .mmessage.mmessage--received.clicked .mmessage-content:has(.mmessenger--audio-wave.mmessenger--gray) {
    border: 1px solid #F0F0F0;
    background-color: #FCFCFC;
    padding: 8px
}

.prdt .mmessage-list .mmessage.mmessage--sent.clicked .mmessage-content {
    /* background-color: #009ace; */
    background: var(--soremo-bg-blue);
    border: 1px solid rgba(0, 154, 206, 0.1);
}

.prdt .mmessage.clicked .messenger-content .s-file.s-file--file .comment-file-content {
    /*margin-top: 6px;*/
    margin-bottom: 0;
    justify-content: space-between;
}

.prdt .mmessage.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--file.mmessenger--black:not(:last-child) {
    margin-bottom: 4px;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--black {
    margin-bottom: 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--black .s-text.s-text--black:not(.not-file) {
    padding: 0 0 0 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--black .messenger-content .messenger-content .s-text.s-text--black.not-file {
    padding: 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--black .messenger-content {
    justify-content: flex-start;
}

/* .prdt .mmessage.mmessage--sent.clicked .mmessage-main .audio-message .s-audio-control {
    font-size: 32px
} */

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .audio-message {
    border-bottom: 1px solid #F0F0F0;
    border-radius: initial;
    background-color: #FFF;
    padding: 0 0 4px 0;
    margin-bottom: 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .audio-message .messenger-content .s-audio.s-audio--audio-wave {
    width: 100%;
    background-color: #FFF;
    border: none;
}

/* .prdt .mmessage.clicked .mmessage-main .info-audio-message {
    margin-top: 6px;
} */

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio {
    display: flex;
    place-content: space-between;
    /* padding-bottom: 6px; */
    /* align-items: center; */

}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio .file-name-message.file-name-cmt {
    margin-bottom: 0;
    line-height: 150%;
    font-size: 11px;
    color: #a7a8a9;
}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio .acr-result-icon.btn-finger-print {
    /* min-width: 20px; */
    /* min-height: 20px; */
    /* padding-right: 16px; */
    position: initial;
    transform: initial;
    margin-right: 0;
}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio .acr-result-icon.btn-finger-print img {
    max-width: 24px;
    max-height: 24px;
}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio .acr-result-icon.btn-finger-print:not(.active) {
    cursor: initial;
}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .block-name-action-audio .block-btn-action-audio-msg {
    display: flex;
}

.prdt .mmessage.clicked .mmessage-main .info-audio-message .audio-type-info-msg .file-info-message {
    margin-bottom: 0;
    line-height: 100%;
    font-weight: 300;
    font-style: normal;
    font-size: 8px;
    color: #A7A8A9;
}

.prdt .mmessage.clicked .mmessage-main .messenger-content .messenger-image-preview-content .info-message-image {
    display: flex;
    place-content: space-between;
    width: 100%;
    align-items: center;
    padding-top: 8px;
}

.prdt .mmessage.clicked .mmessage-main .messenger-content .messenger-image-preview-content .info-message-image .size-file-message {
    margin-bottom: 0;
    font-size: 8px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%;
    color: #A7A8A9;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .audio-message:not(:last-child) {
    margin-bottom: 4px;
}

.prdt .mmessage.clicked .messenger-content .message-video-content {
    width: 100%;
    justify-content: space-between;
    border: 1px solid #F0F0F0;
    background-color: #FFF;
    padding: 8px;
    display: block;
    max-width: 300px;
}

.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content {
    place-content: space-between;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    width: 100%;
}

.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content .action-right-video .acr-result-icon.btn-finger-print {
    min-width: 20px;
    min-height: 20px;
    padding-right: 16px;
    position: initial;
    transform: initial;
    margin-right: 0;
}

.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content .action-right-video .acr-result-icon.btn-finger-print img {
    max-width: 20px;
    max-height: 20px;
}


.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content .action-right-video .acr-result-icon.btn-finger-print:not(.active) {
    cursor: initial;
}

.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content .action-right-video {
    display: flex;
    z-index: 1
}

.prdt .mmessage.clicked .messenger-content .message-video-content .comment-file-content .file-name {
    color: #a7a8a9;
    font-size: 11px;
    font-style: normal;
    line-height: 200%;
}

.prdt .mmessage.clicked .messenger-content .message-video-content .info-message-video .size-file-message {
    margin-bottom: 0;
    font-size: 8px;
    line-height: 100%;
    color: #A7A8A9;
    padding-right: 6px;
}

.prdt .mmessage.clicked .messenger-content .message-document-content {
    display: block;
    padding: 8px;
    width: 100%;
}

.prdt .mmessage.mmessage--received.clicked .messenger-content .message-document-content {
    background-color: #FFF;
}

.prdt .mmessage.clicked .messenger-content .message-document-content .block-pdf-image {
    height: 300px;
}

.prdt .mmessage.clicked .messenger-content .message-document-content .block-pdf-image .pdf-image {
    height: 100%;
}

.prdt .mmessage.clicked .messenger-content .message-document-content .file-name {
    overflow: hidden;
    color: #000;
    text-overflow: ellipsis;
    font-size: 13px;
    line-height: 150%;
}

.prdt .mmessage.clicked .messenger-content .message-document-content .size-file-message {
    margin-bottom: 0;
    font-size: 8px;
    line-height: 100%;
    color: #A7A8A9;
    display: flex;
    justify-content: space-between;
}

.prdt .mmessage.mmessage.clicked .messenger-content .s-file.message-document-content .comment-file-content {
    margin: 6px 0;
}

.prdt .mmessage.clicked .messenger-content .other-file {
    padding: 8px;
    display: block;
    background-color: #FFF;
}

.prdt .mmessage.mmessage--received.clicked .messenger-content .other-file {
    display: block;
    background-color: #FFF;
}

.prdt .mmessage.clicked .messenger-content .other-file .comment-file-content {
    margin-top: 0;
}

.prdt .mmessage.clicked .messenger-content .mmessage-main .mmessenger.mmessenger--text .messenger-content .not-file {
    padding-bottom: 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .s-filedisable-wrap .s-filetext {
    /* background-color: #009ace; */
    border: none;
    padding: 0;
    margin-left: initial;
    font-size: 13px;
    line-height: 150%;
    margin-top: 4px;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .s-filedisable.s-filedisable--filedisable.s-filedisable--black {
    padding: 8px;
    border-radius: 4px;
    background: radial-gradient(50% 50% at 50% 50%, rgba(252, 252, 252, 0.05) 0%, rgba(0, 154, 206, 0.05) 100%);
    margin-left: initial;
    font-size: 11px;
    line-height: 150%;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
    line-height: 150%;
}

.prdt .mmessage.clicked .messenger-content .other-file .size-file-message {
    margin-bottom: 0;
    font-size: 8px;
    line-height: 100%;
    color: #A7A8A9;
    margin-top: 6px;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .s-filedisable-wrap {
    padding: 8px;
    border: 1px solid #F0F0F0;
    margin: 0 4px;
    border-radius: 6px;
    max-width: initial;
    background-color: #FCFCFC;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .s-filedisable-wrap .s-filetext {
    font-size: 13px;
    line-height: 150%;
    padding: 0;
    margin-top: 4px;
    background-color: #FCFCFC;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .s-filedisable-wrap .s-filedisable.s-filedisable--filedisable.s-filedisable--gray {
    font-size: 11px;
    line-height: 150%;
    color: #000;
    padding: 8px;
    background-color: #F0F0F0;
    border-radius: 4px;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger--audio-wave.mmessenger--gray .messenger-content .s-audio.s-audio--audio-wave.s-audio--gray .s-audio-control {
    font-size: 32px;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger--audio-wave.mmessenger--gray .messenger-content .s-audio.s-audio--audio-wave.s-audio--gray {
    border: none;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger--audio-wave.mmessenger--gray .messenger-content {
    padding: 8px;
    margin-bottom: 4px;
    display: block;
    border-radius: 6px;

}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger--audio-wave.mmessenger--gray .messenger-content .info-item-audio-comment .block-name-action-audio {
    display: flex;
    justify-content: space-between;
    padding-bottom: 6px;
    align-items: center;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger--audio-wave.mmessenger--gray .messenger-content .info-item-audio-comment .block-name-action-audio .file-name-message.file-name-cmt {
    margin-bottom: 0;
    line-height: 150%;
    font-size: 13px;
    color: #000;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content .comment-audio-content .mmessenger.mmessenger--audio-wave.mmessenger--gray {
    margin-bottom: 4px;
    border-bottom: 1px solid #F0F0F0;
}

.comment-audio-content {
    padding: 8px;
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    background-color: #FFF;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content:has(.comment-audio-content) .mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
    padding: 0 0 4px 0;
    font-size: 13px;
    line-height: 150%;
    border: none;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content:has(.comment-audio-content) .mmessenger--text.mmessenger--gray .messenger-content {
    display: flex;
    justify-content: flex-start;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content .comment-audio-content:not(:last-child) {
    margin-bottom: 4px;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .audio-message .comment-audio-content .messenger-content .s-audio.s-audio--audio-wave.s-audio--black {
    margin-bottom: 4px;
    border: none;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .audio-message .comment-audio-content .messenger-content {
    border-bottom: 1px solid #F0F0F0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .audio-message .comment-audio-content {
    background-color: #FFF;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .message-folder-content {
    padding: 8px 4px;
    background-color: #FFF;
    border: none;
}

.prdt .mmessage.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .message-folder-content .left-folder-message {
    display: flex;
    max-height: 24px;
}

.prdt .mmessage.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .message-folder-content .left-folder-message .file-name-cmt {
    padding-left: 4px;
    max-width: 90%;
    color: #000;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 200%;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .txt-total-file {
    color: #FFFFFF;
    font-size: 8px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%;
    margin-bottom: 0;
    padding-top: 6px;
}

.prdt .mmessage.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content {
    display: block;
}

.prdt .mmessage.clicked .mmessage-main .mmessage-content .has_user_downloaded .sview-user-seen .avatar.avatar--image.avatar--14.avatar--square {
    flex: initial;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .message-folder-content {
    padding: 8px 4px;
    background-color: #FCFCFC;
    border: 1px solid #F0F0F0;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content:has(.messager-folder) {
    background-color: #FFF;
    padding: 8px;
    border: 1px solid #F0F0F0;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .messager-folder .messenger-content .txt-total-file {
    color: #A7A8A9;
    font-size: 8px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%;
    margin-bottom: 0;
    padding-top: 6px;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content:has(.mmessenger.mmessenger--file.mmessenger--gray) {
    border: 1px solid #F0F0F0;
    background-color: #FCFCFC;
    padding: 8px;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content:has(.mmessenger.mmessenger--file.mmessenger--gray) .mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
    padding: 0 0 4px 0;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 150%;
    border: none;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .mmessage-content .info-message-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.prdt .mmessage.mmessage.clicked .mmessage-main .messenger-content:has(.messenger-image-preview-content) {
    justify-content: center;
}

.dp-none {
    display: none !important
}

.dm-block-message .messenger-detail .mcontent .mmessage-list.mscrollbar {
    padding-bottom: 130px;
}

.prdt .mattach-preview-container .progress {
    border: none;
}

.prdt .mmessage.mmessage--received.reply.clicked .messenger-content .s-file.s-file--file .comment-file-content span {
    overflow: hidden;
    color: #000;
    text-overflow: ellipsis;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 150%;
}

.prdt .mmessage.mmessage--sent.editing.clicked .messenger-content .s-file.s-file--file .comment-file-content span {
    overflow: hidden;
    /* color: #000; */
    text-overflow: ellipsis;
    /* font-size: 13px; */
    font-style: normal;
    font-weight: 300;
    line-height: 150%;
}

.prdt .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--text.mmessenger--gray .messenger-content .s-text.s-text--gray {
    color: #000000;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content .mmessenger.mmessenger--gray .messenger-content .single-text {
    padding: 0;
    border: none;
}

.prdt .mmessage.mmessage--received.clicked .mmessage-main .mmessage-content:has(.mmessenger.mmessenger--gray .messenger-content .single-text) {
    padding: 8px;
    border: 1px solid #f0f0f0;
    background: var(--soremo-bg-white);
}

.prdt .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content:has(.mmessenger.mmessenger--gray .messenger-content .single-text) {
    background-color: #009ace;
}

.prdt .mmessage.mmessage--received.clicked.reply .mmessage-main .mmessage-content .mmessenger.mmessenger--gray .messenger-content .single-text {
    padding: 8px;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .block-video-cmt {
    display: none;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .info-message-video.info-message-file {
    display: none;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .action-right-video {
    display: none;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content {
    margin-bottom: 0;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .file-name {
    margin-bottom: 0;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-bottom .mcomment-action {
    padding-left: 4px;
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .image-preview-comment.active-view {
    display:none
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .info-message-image.info-message-file {
    display:none
}

.prdt .mmessage-component .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .btn-download-file-cmt {
    display:none
}

.prdt .mmessage.mmessage--sent.clicked.editing .mmessage-main .mmessage-content .mmessenger--file.mmessenger--black.block-download-file .messenger-content .info-message-file {
    background-color: #FFF;
}

.prdt .mmessage.mmessage--sent.clicked.editing .mmessage-main .audio-message .messenger-content .s-audio.s-audio--audio-wave, .prdt .mmessage.mmessage--sent.clicked.reply .mmessage-main .audio-message .messenger-content .s-audio.s-audio--audio-wave  {
    background-color: #009ace;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .block-video-cmt {
    display: none;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .info-message-video.info-message-file {
    display: none;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .action-right-video {
    display: none;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content {
    margin-bottom: 0;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .file-name {
    margin-bottom: 0;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-bottom .mcomment-action {
    padding-left: 4px;
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .image-preview-comment.active-view {
    display:none
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .info-message-image.info-message-file {
    display:none
}

.prdt .dm-block-message .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content .btn-download-file-cmt {
    display:none
}

.prdt .mmessage.clicked.editing .dropdown-item.mmessage-edit .txt-item-comment.txt-edit-comment, .prdt .mmessage.clicked.reply .dropdown-item.mmessage-reply .txt-item-comment.txt-reply-comment {
    color: #009ACE !important
}

.prdt .dm-block-message .mmessage.mmessage--sent.clicked .dropdown-comment-new .dropdown-menu.dropdown-menu-comment .li-resolve-message {
    display: none
}

.prdt .dm-block-message .mmessage.mmessage--sent.clicked .dropdown-comment-new .dropdown-menu.dropdown-menu-comment .li-reply-message {
    display: none;
}

.prdt .dm-block-message .mcommment {
    bottom: 0;
}

.prdt .mmessage.mmessage--sent.clicked .mmessage-main .mmessage-content .comments-audio-block .audio-message .messenger-content .s-audio.s-audio--audio-wave.s-audio--black.active {
    background-color: #009ACE !important
}

.prdt .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .info-message-file {
    display: none;
}

.prdt .maction .mcommment.border-editing .mcomment-message .mcomment-top .mattach-previews.collection .collection-item.item-template .mattach-info .mcommment-file .mcommment-file__name .comment-file-content span {
    text-overflow: ellipsis;
    overflow: hidden;
}

.show-more-action-message {
    display: none
}

.show-more-action-message.show-action{
    display: flex !important;
}

.mmessage--sent.clicked .mmessage-info .message-info-container .mmessage-status .mmessage-user .notification.notification--outline-gray.notification--round  {
  margin-left: 2px
}

.prdt .height-init {
    height: inherit !important;
}

/* .prdt .mmessage.mmessage--sent.clicked .s-text.s-text--black.not-file a  {
    color: #FFFFFF;
}

.prdt .mmessage.mmessage--sent.clicked .s-text.s-text--black.not-file a:hover {
    color: #efeded;
    text-decoration: underline;
} */

.prdt .mmessage.mmessage--received.clicked .s-text.s-text--gray a:hover {
    text-decoration: underline;
}

/* .prdt .dropdown-menu.dropdown-menu-comment.message-action-right {
    left: -100% !important;
} */
