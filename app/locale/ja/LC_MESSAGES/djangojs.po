# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-27 15:49+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: app/static/js/action_banner.js:238 app/static/js/soremo.js:841
#: app/static/js/top_page_member.js:3
msgid "Do you really want to delete this?"
msgstr "本当に削除しますか？"

#: app/static/js/action_banner.js:367
msgid "Would you like to leave the project?"
msgstr "プロジェクトから退出させますか？"

#: app/static/js/action_banner.js:601 app/static/js/action_banner.js:863
#: app/static/js/messenger_artist.js:293 app/static/js/messenger_artist.js:312
#: app/static/js/messenger_artist.js:401 app/static/js/messenger_owner.js:184
#: app/static/js/messenger_owner.js:218 app/static/js/messenger_owner.js:673
#: app/static/js/messenger_owner.js:704 app/static/js/project_detail.js:2837
#: app/static/js/search_creator.js:172 app/static/js/search_creator.js:191
#: app/static/js/search_creator.js:491 app/static/js/search_creator.js:494
#: app/static/js/upload_contract.js:1292 app/static/js/upload_contract.js:1321
#: app/static/js/upload_contract.js:1351
msgid "Something went wrong!"
msgstr "エラーが発生しました"

#: app/static/js/action_banner.js:1379
msgid "Are you sure you want to unlock this artist?"
msgstr "このアーティストを解除してもよろしいですか？"

#: app/static/js/contact_info.js:193
msgid "I'm not a robot."
msgstr "私はロボットではありません。"

#: app/static/js/messenger_artist.js:326 app/static/js/messenger_artist.js:344
#: app/static/js/messenger_owner.js:169 app/static/js/messenger_owner.js:206
#: app/static/js/search_creator.js:511 app/static/js/search_creator.js:527
msgid "I made an offer."
msgstr "オファーしました。"

#: app/static/js/messenger_owner.js:734
msgid "Please enter the correct value. (100 yen ↔ 99,999,999 yen)"
msgstr "正しいバリューを入力してください。(100円 ↔ 99,999,999円)"

#: app/static/js/password_reset_confirm.js:23
msgid "Set password confirm title"
msgstr "パスワード設定"

#: app/static/js/search_creator.js:393
msgid "Uploading ..."
msgstr "アップロード中…"

#: app/static/js/validate_contract.js:59 app/static/js/validate_contract.js:108
msgid "Please enter a valid email address."
msgstr "有効なメールアドレスを入力してください。"

#: app/static/js/validate_contract.js:70
msgid "The URL format is incorrect. "
msgstr "URLのフォーマットが正しくありません。"

#: app/static/js/validate_contract.js:127
msgid "Please enter a valid phone number."
msgstr "有効な電話番号を入力して下さい。"

#~ msgid "The password must be at least 8-20 characters."
#~ msgstr "パスワードは8～20文字で設定してください。"

#~ msgid "You cannot use a password with only numbers."
#~ msgstr "パスワードには、数字だけではなく文字も含めてください。"

#~ msgid "The new password and the confirmation password are not the same."
#~ msgstr "入力されたパスワードが一致していません。"

#~ msgid "Only during production"
#~ msgstr "制作中のみ"

#~ msgid "E-mail address is incorrect."
#~ msgstr "メールアドレスが正しくありません。"

#~ msgid "Do you really want to remove members from this project?"
#~ msgstr "本当にこのプロジェクトからメンバーを削除しますか？"

#~ msgid "Please enter the detailed information of the project."
#~ msgstr "プロジェクトの詳細情報を入力してください。"
