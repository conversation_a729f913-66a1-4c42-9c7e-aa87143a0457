# 01EG89H6SS2141VNGDDEHBMV4Q
import json
import logging
import re
from datetime import datetime, timedelta, date
from django.contrib.auth.decorators import user_passes_test, REDIRECT_FIELD_NAME
import requests
import voice.settings as settings
from voice.constants import CONST_REGEX_FILE
import urllib.parse
from pydub import AudioSegment
from PIL import Image
from urllib.parse import unquote
import os
from io import BytesIO
import imageio
import base64
from pdf2image import convert_from_bytes
import soundfile as sf
import pyloudnorm
from io import BytesIO
from PyPDF2 import PdfReader
from pdf2image import convert_from_path
from django.conf import settings
import boto3
from botocore.exceptions import NoCredentialsError
import logging
from soundfile import SoundFile


def login_staff_required(function=None, redirect_field_name=REDIRECT_FIELD_NAME, login_url=None):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated and u.role in "master_admin, admin",
        login_url=login_url,
        redirect_field_name=redirect_field_name
    )
    if function:
        return actual_decorator(function)
    return actual_decorator


def login_messenger_required(function=None, redirect_field_name=REDIRECT_FIELD_NAME, login_url=None):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated and u.role in "admin, master_client, master_admin",
        login_url=login_url,
        redirect_field_name=redirect_field_name
    )
    if function:
        return actual_decorator(function)
    return actual_decorator


def login_master_admin_required(function=None, redirect_field_name=REDIRECT_FIELD_NAME, login_url=None):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated and u.role == "master_admin",
        login_url=login_url,
        redirect_field_name=redirect_field_name
    )
    if function:
        return actual_decorator(function)
    return actual_decorator


def login_curator_required(function=None, redirect_field_name=REDIRECT_FIELD_NAME, login_url=None):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated and u.role == "curator",
        login_url=login_url,
        redirect_field_name=redirect_field_name
    )
    if function:
        return actual_decorator(function)
    return actual_decorator


def login_manager_account_required(function=None, redirect_field_name=REDIRECT_FIELD_NAME, login_url=None):
    """
    Decorator for views that checks that the user is logged in, redirecting
    to the log-in page if necessary.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated and u.role in "master_admin, curator",
        login_url=login_url,
        redirect_field_name=redirect_field_name
    )
    if function:
        return actual_decorator(function)
    return actual_decorator


def parser_date(str):
    return datetime.strptime(str, '%d/%m/%Y')


def to_hour(timestamp):
    return timestamp.strftime("%H:%M")


def get_end_date_of_month(dt):
    end_date = get_start_date_of_next_month(dt) - timedelta(days=1)
    return end_date.replace(hour=23, minute=59, second=59)


def get_start_date_of_month(dt):
    return dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)


def get_start_date_of_next_month(dt):
    start_date = datetime(
        dt.year + int(dt.month / 12),
        dt.month % 12 + 1,
        1
    )
    return start_date.replace(hour=0, minute=0, second=0, microsecond=0)


def get_end_date_of_next_month(dt):
    start_date = get_start_date_of_next_month(dt)
    return get_end_date_of_month(start_date)


def get_start_date_of_week(dt):
    today = datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)
    return today - timedelta(days=datetime.today().isoweekday() % 7)


def get_end_date_of_four_week(dt):
    return get_start_date_of_week(dt) + timedelta(days=28)


def get_range_date_of_four_week(dt):
    for n in range(28):
        yield get_start_date_of_week(dt) + timedelta(n)


def get_range_date_of_this_week(dt):
    for n in range(7):
        yield get_start_date_of_week(dt) + timedelta(n)


def get_range_date_of_next_week(dt):
    for n in range(7):
        yield get_start_date_of_week(dt) + timedelta(7) + timedelta(n)


def get_range_date_of_three_week(dt):
    for n in range(7):
        yield get_start_date_of_week(dt) + timedelta(14) + timedelta(n)


def strf_today():
    return strf_date(date.today())


def strf_date(input_date):
    return input_date.strftime('%Y/%m/%d')


def get_start_time_today(dt):
    return dt.replace(hour=0, minute=0, second=0)


def diff_time_second(end_time, start_time):
    end_time_second = get_sec(end_time)
    start_time_second = get_sec(start_time)
    return end_time_second - start_time_second


def get_sec(time_str):
    total_time = 0
    for index, value in enumerate(reversed(time_str.split(':'))):
        total_time += 60**index * int(value)
    return total_time


def convert_seconds_to_time_format(time_seconds):
    if time_seconds <= 0:
        return '00:00'
    minutes = time_seconds // 60
    seconds = time_seconds % 60
    return '%02d:%02d' % (minutes, seconds)


def get_total_diff_time(acr_play_offsets):
    total_diff_time = 0
    for match in acr_play_offsets.split(','):
        total_diff_time += diff_time_second(match.split('-')[1], match.split('-')[0])
    return convert_seconds_to_time_format(total_diff_time)


def get_type_file(file_name):
    file_extension = re.search(CONST_REGEX_FILE, file_name)
    if file_extension:
        file_extension = file_extension.group()
        file_extension = file_extension.lower()
        if file_extension in ['.mp3', '.wav']:
            return 'audio'
        elif file_extension in ['.mp4', '.x-m4v', '.avi', '.webm', '.mov']:
            return 'video'
        elif file_extension in ['.png', '.jpeg', '.bmp', '.gif', '.ppm', '.jpg', '.svg']:
            return 'image'
        elif file_extension in ['.pdf']:
            return 'document'
    return 'other'


def get_download_link(object_name, filename):
    try:
        s3 = boto3.client('s3',
                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Use a raw string for the regex pattern
        ext_match = re.search(r'(?P<ext>\.[0-9a-zA-Z]{2,}$)', filename)
        
        if ext_match:
            ext = ext_match.group('ext')
            # Remove the extension from filename to get base name
            base_filename = filename[:ext_match.start()]
        else:
            # If no extension in filename, try to get it from object_name
            ext_match = re.search(r'(?P<ext>\.[0-9a-zA-Z]{2,}$)', object_name)
            ext = ext_match.group('ext') if ext_match else ''
            base_filename = filename

        # URL-encode the base filename (without extension)
        encoded_base_filename = urllib.parse.quote(base_filename, safe='')
        
        # Generate the presigned URL
        url = s3.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                'Key': object_name,
                'ResponseContentDisposition': f"attachment; filename*=UTF-8''{encoded_base_filename}{ext}",
            }
        )
        return url

    except Exception as e:
        # Log the exception or handle it as needed
        # print(f"An error occurred: {e}")
        pass
    return ''


UNIT_MAPPING = {
    '1': '人月',
    '2': '曲',
    '3': '演出',
    '4': '名',
    '5': '時間',
    '6': '式',
    '7': '人日',
}

WORK_TYPE_MAPPING = {
    '1': '進行管理',
    '2': '開発',
    '3': 'ディレクター',
    '4': 'QA',
    '5': '楽曲',
    '6': '効果音',
    '7': '実演',
    '8': 'スタジオ',
    '9': 'デザイン',
    '10': '撮影',
    '11': '動画編集',
    '12': '作編曲',
    '13': '編曲',
    '14': '作詞',
    '15': '歌唱',
    '16': '演奏',
    '17': 'エンジニア',
}


def calculator_budget(form_contract_and_plan):

    contract_work_content = form_contract_and_plan.get_work_content()
    total_without_tax = 0
    for item in contract_work_content:
        item['amount_money'] = item['price'] * item['quantity']
        item['work_type_dsp'] = WORK_TYPE_MAPPING[item['work_type']] if item['work_type'] in WORK_TYPE_MAPPING.keys() else item['work_type_dsp']
        item['unit_dsp'] = UNIT_MAPPING[item['unit']] if item['unit'] in UNIT_MAPPING.keys() else item['unit_dsp']
        total_without_tax += item['amount_money']
    return int(total_without_tax  * 1.1 )


def get_info_file(file_name, real_name):
    result = {}
    path_file_downloaded = ''
    try:
        file_name = urllib.parse.quote(file_name)
        url = settings.URL_FILE_S3 % (settings.AWS_STORAGE_BUCKET_NAME, file_name)
        file_extension = re.search(CONST_REGEX_FILE, file_name)
        path_file_downloaded = download_file_comment(url, file_name)
        if path_file_downloaded is None:
            url = get_download_link(file_name, real_name)
            path_file_downloaded = download_file_comment(url, file_name)
        if path_file_downloaded is None:
            raise Exception('download file comment failed')
        if file_extension:
            file_extension = file_extension.group()
            file_extension = file_extension.lower()
            if file_extension in ['.mp3', '.wav']:
                result_info = get_info_deep_audio(path_file_downloaded)
                if result_info is not None:
                    result = {
                        "type_file": "audio",
                        "file_info": result_info
                    }
            elif file_extension in ['.mp4', '.x-m4v', '.avi', '.webm', '.mov']:
                result_info = get_video_info(path_file_downloaded)
                if result_info is not None:
                    result = {
                        "type_file": "video",
                        "file_info": result_info
                    }
            elif file_extension in ['.png', '.jpeg', '.bmp', '.gif', '.ppm', '.jpg', '.svg']:
                result_info = get_size_image(path_file_downloaded)
                if result_info is not None:
                    result = {
                        "type_file": "image",
                        "file_info": result_info
                    }
            elif file_extension in ['.pdf']:
                result_info = get_size_pdf(path_file_downloaded)
                if result_info is not None:
                    result = {
                        "type_file": "document",
                        "file_info": result_info,
                    }
            else:
                result_info = get_file_size_url(path_file_downloaded)
                if result_info is not None:
                    result = {
                        "type_file": "other",
                        "file_info": result_info
                    }
    except Exception as e:
        result = None
        object_log = {
            'file_name': file_name,
            'real_name': real_name,
            'path_file_downloaded': path_file_downloaded
        }
        save_logging_update_info_file('get info file failed', e, object_log)
    remove_file_upload(path_file_downloaded)
    return result


def create_info_file(file_path, file_name, file_size):
    result = {}
    try:
        logging.info('go to create_info_file')
        if not file_size:
            raise 'file size not found.'
        file_name = urllib.parse.quote(file_name)
        file_extension = re.search(CONST_REGEX_FILE, file_name)
        if file_extension:
            file_extension = file_extension.group()
            file_extension = file_extension.lower()
            if file_extension in ['.mp3', '.wav']:
                result_info = get_info_deep_audio(file_path, file_size)
                if result_info is not None:
                    result = {
                        "type_file": "audio",
                        "file_info": result_info
                    }
            elif file_extension in ['.mp4', '.x-m4v', '.avi', '.webm', '.mov']:
                result_info = get_video_info(file_path)
                if result_info is not None:
                    result = {
                        "type_file": "video",
                        "file_info": result_info
                    }
            elif file_extension in ['.png', '.jpeg', '.bmp', '.gif', '.ppm', '.jpg', '.svg']:
                result_info = get_size_image(file_path)
                if result_info is not None:
                    result = {
                        "type_file": "image",
                        "file_info": result_info
                    }
            elif file_extension in ['.pdf']:
                result_info = get_size_pdf(file_path)
                if result_info is not None:
                    result = {
                        "type_file": "document",
                        "file_info": result_info,
                    }
            else:
                result_info = get_file_size_url(file_path)
                if result_info is not None:
                    result = {
                        "type_file": "other",
                        "file_info": result_info
                    }
    except Exception as e:
        result = None
        object_log = {
            'file_name': file_name,
            'path_file_downloaded': file_path
        }
        save_logging_update_info_file('get info file failed', e, object_log)
    remove_file_upload(file_path)
    return result


def get_info_deep_audio(path_file_downloaded, file_size=None):
    max_size_audio = 50  # mb
    max_seconds = 600  # 10 min
    min_seconds = 75  # 75 sec
    try:
        if not file_size:
            file_size = os.path.getsize(path_file_downloaded)
        file = SoundFile(path_file_downloaded)
        sample_rate = file.samplerate
        pattern = re.compile(r'(\w+)\s*:\s*([^\n]+)')
        matches = pattern.findall(file.extra_info)
        file.close()
        file_size_converted = round(file_size * 0.000001)  # convert bytes to MB
        if file_size_converted > max_size_audio:
            loudness_result = get_loudness_large_size_audio(file, sample_rate, path_file_downloaded, max_seconds)
            if not loudness_result:
                while max_seconds > min_seconds:
                    max_seconds = max_seconds / 2
                    loudness_result = get_loudness_large_size_audio(file, sample_rate, path_file_downloaded,
                                                                    max_seconds)
                    if loudness_result:
                        break
                    if max_seconds <= min_seconds:
                        raise 'cannot calculate loudness result'
        else:
            data, sample_rate = sf.read(path_file_downloaded)
            meter = pyloudnorm.Meter(sample_rate, block_size=0.100)
            loudness_result = meter.integrated_loudness(data)
        extra_info_dict = dict(matches)
        if extra_info_dict:
            result = {
                'sample_rate': sample_rate,
                'bit_depth': extra_info_dict['Width'] if 'Width' in extra_info_dict else '',
                'channel_type': file.channels if file.channels else '',
                'loudness': str(round(loudness_result, 1))

            }
            return json.dumps(result)
        return None
    except Exception as e:
        print(e)
        save_logging_update_info_file('get info deep audio failed', e, {'path_file_downloaded': path_file_downloaded})
        return None


def get_loudness_large_size_audio(file, sample_rate, path_file_downloaded, max_seconds):
    last_start_frame = 0
    chunk_lufs = 0.0
    chunk_size_s = 240  # 240 second chunks
    frames = file.frames
    duration = int(frames / sample_rate)
    max_time = sample_rate * max_seconds  # frames * seconds
    max_duration = sample_rate * max_seconds  # frames * seconds
    child_files = []
    chunk_size_samp = int(chunk_size_s * sample_rate)

    num_files = int(frames / max_time)  # number file split
    num_div = frames % max_time  # div number plus to last file
    if num_files > 0 and duration >= max_seconds:
        for i in range(num_files):
            if num_files == 1:
                child_file = {
                    'start_frame': last_start_frame,
                    'stop_frame': max_duration + num_div
                }
                child_files.append(child_file)
                last_start_frame = max_duration
                continue
            else:
                if i == 0:
                    child_file = {
                        'start_frame': last_start_frame,
                        'stop_frame': max_duration
                    }
                    child_files.append(child_file)
                    last_start_frame = max_duration
                    continue
                elif i == range(num_files)[-1] and num_div:
                    child_file = {
                        'start_frame': last_start_frame,
                        'stop_frame': last_start_frame + num_div
                    }
                    child_files.append(child_file)
                    continue

            child_file = {
                'start_frame': last_start_frame,
                'stop_frame': last_start_frame + max_duration
            }
            child_files.append(child_file)
        for item in child_files:
            data_reader = sf.read(path_file_downloaded, start=int(item['start_frame']), stop=int(item['stop_frame']))
            data = data_reader[0]
            meter = pyloudnorm.Meter(sample_rate)
            ns = int(data.shape[0] / chunk_size_samp) + 1
            for n in range(ns):
                start_idx = n * chunk_size_samp
                stop_idx = start_idx + chunk_size_samp
                chunk = data[start_idx:stop_idx, :]
                loudness = meter.integrated_loudness(chunk)  # measure loudness
                chunk_lufs += loudness * (chunk.shape[0] / data.shape[0])
        return chunk_lufs / len(child_files)
    return None


def download_file_comment(url, file_name):
    file_downloaded = ''
    try:
        folder_name = 'medias'
        path_folder = create_folder_media(folder_name)
        local_filename = file_name.split('/')[-1]
        filename = unquote(local_filename)
        full_path = os.path.join(path_folder, filename)
        # counter_num = 1
        # while os.path.exists(full_path):
        #     filename = f'{filename}_{counter_num}.png'
        #     full_path = os.path.join(path_folder, filename)
        #     counter_num += 1
        # NOTE the stream=True parameter below
        with requests.get(url, stream=True) as r:
            r.raise_for_status()
            with open(full_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    # If you have chunk encoded response uncomment if
                    # and set chunk_size parameter to None.
                    f.write(chunk)
        return full_path
    except Exception as e:
        remove_file_upload(file_downloaded)
        object_log = {
            'url': url,
            'file_name': file_name,
            'file_downloaded': file_downloaded
        }
        save_logging_update_info_file('download file comment failed', e, object_log)
        return None


def create_folder_media(folder_name):
    current_date = datetime.now()
    # Create a folder structure based on the current date
    folder_path = os.path.join(folder_name, str(current_date.year), str(current_date.month), str(current_date.day))
    # Create the folder if it doesn't exist
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
    return folder_path


def get_size_image(path_file_downloaded):
    try:
        image = imageio.imread(path_file_downloaded)
        data_image = image.shape
        if data_image:
            result = {
                "height": data_image[0],
                "width": data_image[1]
            }
            return json.dumps(result)
        return None
    except Exception as e:
        save_logging_update_info_file('get size image by path failed', e, {'path_file_downloaded': path_file_downloaded})
        return None


def get_file_size_url(path_file_downloaded):
    try:
        file_size = os.path.getsize(path_file_downloaded)
        if file_size:
            result = {
                'size': file_size,  # bytes
                'size_converted': sizeof_fmt(file_size),
            }
            return json.dumps(result)
        return None
    except Exception as e:
        save_logging_update_info_file('get file size by path failed', e, {'path_file_downloaded': path_file_downloaded})
        return None


def sizeof_fmt(num, suffix="b"):
    for unit in ("", "k", "m", "g", "t", "p", "e", "z"):
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Yi{suffix}"


def get_total_file_in_folder(folder):
    if folder is None:
        return 0
    total_files = folder.children.all().count()
    for child_folder in folder.child_folders.all():
        total_files += get_total_file_in_folder(child_folder)
    return total_files


def get_video_info(path_file_downloaded):
    try:
        reader = imageio.get_reader(path_file_downloaded)
        width, height = reader.get_meta_data()['size']
        fps = round(reader.get_meta_data()['fps'])
        return json.dumps({
            "width": width,
            "height": height,
            "fps": fps
        })
    except Exception as e:
        save_logging_update_info_file('Cannot get info of video', e, {'path_file_downloaded': path_file_downloaded})
        return None


def get_size_pdf(path_file_downloaded):
    output_path_image = ''
    try:
        directory_path = os.path.dirname(path_file_downloaded)
        full_path_upload = check_folder_upload_pdf(directory_path)
        filename_without_extension = os.path.splitext(os.path.basename(path_file_downloaded))[0]
        pdf_reader = PdfReader(path_file_downloaded)
        first_page = pdf_reader.pages[0]
        page_width = first_page.mediabox[2]
        page_height = first_page.mediabox[3]
        images = convert_from_path(path_file_downloaded, first_page=1, last_page=1)
        if images:
            # Save the image to the output folder
            filename_without_extension = f'{filename_without_extension}.png'
            counter = 1
            while os.path.exists(output_path_image):
                filename_without_extension = f'{filename_without_extension}_{counter}.png'
                counter += 1
            output_path_image = os.path.join(full_path_upload, filename_without_extension)
            images[0].save(output_path_image, format="PNG")
            result_uploaded = upload_image_to_s3(output_path_image, filename_without_extension)
            if not result_uploaded:
                raise "upload to s3 failed"
            # save file image to s3 and get url
            # url_image = get_download_link(result_uploaded, filename_without_extension)
            # khi hien thi ra client thi lay goi ham get_download_link xu ly
            result = {
                "width": int(page_width),
                "height": int(page_height),
                "url_image": result_uploaded
            }
            remove_file_upload(output_path_image)
            return json.dumps(result)
        else:
            raise "Page not found."

    except Exception as e:
        remove_file_upload(output_path_image)
        save_logging_update_info_file('Cannot get info of pdf', e, {'path_file_downloaded': path_file_downloaded})
        return None


def check_folder_upload_pdf(base_path):
    folder_name = "image_pdf"
    full_path = os.path.join(base_path, folder_name)
    if not os.path.exists(full_path):
        # If the folder doesn't exist, create it
        os.makedirs(full_path)
    return full_path


def get_image_pdf_file(url_pdf):
    try:
        response = requests.get(url_pdf)
        pdf_data = response.content
        images = convert_from_bytes(pdf_data, first_page=1, last_page=1)
        if images:
            buffered = BytesIO()
            images[0].save(buffered, format="PNG")
            image_base64 = base64.b64encode(buffered.getvalue()).decode()
            image_url = f"data:image/png;base64,{image_base64}"
            return image_url
        else:
            save_logging_update_info_file('images pdf first page not found', None, {'url_pdf': url_pdf})
            return None
    except Exception as e:
        save_logging_update_info_file('cannot get pdf file', e, {'url_pdf': url_pdf})
        return None


def upload_image_to_s3(file_path, file_name):
    try:
        prefix = 'storage/'
        full_path_s3 = prefix + file_name
        s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        s3.upload_file(file_path, settings.AWS_STORAGE_BUCKET_NAME, full_path_s3)
        return full_path_s3
    except FileNotFoundError as e:
        msg = 'File upload not found'
        exception = e
    except NoCredentialsError as e:
        msg = 'Credentials invalid'
        exception = e
    except Exception as e:
        msg = 'Error upload to s3'
        exception = e
    save_logging_update_info_file(msg, exception, {'file_path': file_path})
    return False


def remove_file_upload(file_path):
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except Exception as e:
        msg = 'remove file upload failed'
        save_logging_update_info_file(msg, e, {'file_path': file_path})
        return False


def save_logging_update_info_file(message, e, object_log):
    result_error = {
        'type_log': 'run command update_info_comment_file',
        'message': message if message is not None else '',
        'object_result': str(object_log) if object_log is not None else '',
        'error': str(e) if e is not None else ''
    }
    logging.info(result_error)
    return False

import subprocess
def capture_thumbnail_ffmpeg(video_path):
    cmd = [
        'ffmpeg',
        '-i', video_path,
        '-ss', '0',  # get second
        '-vframes', '1',  # get frame
        '-f', 'image2pipe',
        '-c', 'png',
        '-'
    ]

    try:
        # run command ffmpeg and start reading output from process
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            logging.info('Error when call ffmpeg:', stderr)
            return None
        base64_string = base64.b64encode(stdout).decode('utf-8')
        base64_url = f'data:image/png;base64,{base64_string}'
        return base64_url
    except Exception as e:
        print("Error get thumbnail from video:", e)
        return None