import ast
import json

import dateutil.parser
import datetime
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import EmptyResultSet
from django.core.validators import FileExtensionValidator
from django.db import transaction
from accounts.models import ProductUser, AuthUser, Creator
import re

from landingPage.models import ContactInfo
from . import models
from common.custom_acr import customrecognize
from accounts.tasks import auto_use_last_scene, update_product_user_order
from .models import OfferProduct, SelectionGallery, TopicGallery
from .tasks import add_remove_member_into_offer_product


class SceneForm(forms.ModelForm):
    title_val = forms.CharField(label='タイトル', max_length=64, required=False)
    variation_val = forms.CharField(label='バリアント', max_length=128, required=False)
    title_id = forms.CharField(max_length=300, widget=forms.HiddenInput, required=False)
    thumbnail = forms.ImageField(widget=forms.FileInput(attrs={'accept': 'image/*'}), required=False, label='サムネイル')
    movie = forms.FileField(label='動画ファイル', widget=forms.FileInput(
                                attrs={'accept': '.mp4, .x-m4v, .webm, video/*',
                                       'oninvalid': "this.setCustomValidity('このフィールドは必須項目です。')",
                                       'oninput': "setCustomValidity('')",
                                       'allow_multiple_selected': True
                                       }),
                            validators=[
                                FileExtensionValidator(['mp4', 'webm', 'ogg'],
                                                       'ファイルのフォーマットが正しくありません。MP4, webmファイルをアップロードしてください。',
                                                       402)], )
    list_width = forms.CharField(widget=forms.HiddenInput, )
    list_height = forms.CharField(widget=forms.HiddenInput, )

    class Meta:
        model = models.Scene
        exclude = ('title', 'modified', 'created', 'fps_movie', 'video_height', 'video_width', 'acr_result', 'ok', 'ok_by', 'variation')
        widgets = {
            'owner_id': forms.HiddenInput(),
            'product': forms.HiddenInput(),
            'product_scene': forms.HiddenInput(),
            'order': forms.HiddenInput(),
            'version': forms.HiddenInput(),
            'tag': forms.HiddenInput(),
            'flag_tag': forms.HiddenInput(),
            'rating': forms.HiddenInput(),
            'audio_pin': forms.HiddenInput(),
        }

    def clean(self):
        cleaned_data = super(SceneForm, self).clean()
        title_val = cleaned_data.get("title_val")
        title_id = cleaned_data.get("title_id")
        # タイトルが入力されている
        if title_val:
            return cleaned_data
        if models.SceneTitle.objects.get(pk=title_id):
            return cleaned_data
        raise forms.ValidationError("タイトルが入力されていません。")


class SceneTagChangeForm(forms.ModelForm):
    class Meta:
        model = models.SceneTitle
        fields = ('tag',)
        widgets = {
            'tag': forms.HiddenInput(),
        }

    def clean(self):
        cleaned_data = super(SceneTagChangeForm, self).clean()
        tag = cleaned_data.get("tag", "")
        if tag != '3':
            raise forms.ValidationError('値が不正です')
        return cleaned_data


class SceneChangeForm(forms.ModelForm):
    thumbnail = forms.ImageField(widget=forms.FileInput(attrs={'accept': 'image/*'}), required=False, label='サムネイル')
    movie = forms.FileField(label='動画ファイル', widget=forms.FileInput(attrs={'accept': '.mp4, .x-m4v, .webm, video/*',
                                                                          'oninput': "setCustomValidity('')", }),
                            required=False,
                            validators=[FileExtensionValidator(['mp4', 'webm', 'ogg'],
                                                               'ファイルのフォーマットが正しくありません。MP4, webmファイルをアップロードしてください。',
                                                               402)], )

    class Meta:
        model = models.Scene
        exclude = (
            'owner_id', 'modified', 'created', 'fps_movie', 'order', 'tag', 'owner_id', 'product', 'product_scene',
            'order', 'title', 'version', 'flag_tag', 'rating', 'audio_pin', 'version')
        widgets = {
            'video_width': forms.HiddenInput(),
            'video_height': forms.HiddenInput(),
        }


class SceneCommentForm(forms.ModelForm):
    class Meta:
        model = models.SceneComment
        exclude = ('modified', 'created',)
        widgets = {
            'scene': forms.HiddenInput(),
            'owner_id': forms.HiddenInput(),
            'user': forms.HiddenInput(),
            'pin_video': forms.HiddenInput()
        }

    def clean_pin_time(self):
        pin = self.cleaned_data["pin_time"]
        if pin and not re.compile(r'^(\d?\d:)?([0-5]?\d):([0-5]?\d)(.([0-9]?\d))?(\n|$)').search(pin):
            raise forms.ValidationError('ピンのバリューが正しくありません。')
        return pin

    def clean_comment(self):
        comment = self.cleaned_data["comment"]
        if comment and re.compile(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>').search(comment):
            raise forms.ValidationError('コメント内でスクリプトを使用するのは禁止します。')
        return comment

    def clean(self):
        self.cleaned_data = super(SceneCommentForm, self).clean()
        owner_id = self.cleaned_data["owner_id"]
        self.cleaned_data['user'] = AuthUser.objects.get(pk=owner_id)
        file = self.cleaned_data["file"]
        pin = self.cleaned_data["pin_time"]
        if self.cleaned_data['pin_video']:
            scene = models.Scene.objects.get(pk=self.cleaned_data['pin_video'])
            if scene and pin or pin and file and re.search("[.](mp3|wav|ogg|MP3|WAV|OGG)", file.name):
                frames = pin.split('.')
                max_fps = scene.fps_movie if scene.fps_movie else 0
                if len(frames) > 1 and max_fps < int(frames[-1]):
                    if re.match(self.cleaned_data['pin_time'], self.cleaned_data['comment']):
                        self.cleaned_data['comment'] = self.cleaned_data['comment'].replace(
                            self.cleaned_data['pin_time'], f'{frames[0]}.{max_fps}')
                    self.cleaned_data['pin_time'] = f'{frames[0]}.{max_fps}'

        return self.cleaned_data


class ProductOrderForm(forms.ModelForm):
    class Meta:
        model = models.ProductOrder
        exclude = ('modified', 'created',)
        widgets = {
            'product': forms.HiddenInput(),
            'owner_id': forms.HiddenInput(),
            'user': forms.HiddenInput(),
        }

    def clean_comment(self):
        comment = self.cleaned_data["comment"]
        return comment

    def clean(self):
        self.cleaned_data = super(ProductOrderForm, self).clean()
        owner_id = self.cleaned_data["owner_id"]
        self.cleaned_data['user'] = AuthUser.objects.get(pk=owner_id)
        return self.cleaned_data


def link_in_comment(comment) -> str:
    head_link = comment.find('http', 0, len(comment))
    if head_link >= 0:
        end_link = comment.find(' ', head_link, len(comment))
        if end_link < 0:
            end_link = len(comment)
        link = comment[slice(head_link, end_link)]
        left_comment = len(comment) - len(link)
        if comment.find('http', left_comment, len(comment)):
            link_in_comment(comment[slice(left_comment, len(comment))])

        html_link = '<a href ="' + link + '" target="_blank">' + link + '</a>'
        comment = comment.replace(link, html_link)
    return comment


class ProductOrderUploadForm(forms.ModelForm):
    file = forms.FileField(required=True)

    class Meta:
        model = models.ProductOrderUpload
        exclude = ('modified', 'created',)
        widgets = {
            'owner_id': forms.HiddenInput(),
            'product': forms.HiddenInput(),
            'type': forms.HiddenInput(),
            'tag': forms.HiddenInput(),
        }


class SceneTitleDeleteForm(forms.Form):
    product_id = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)
    product_scene_id = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)
    tag = forms.CharField(max_length=10, required=True, widget=forms.HiddenInput)
    scene_title_ids = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)

    def save(self):
        scene_title_ids = self.cleaned_data['scene_title_ids'].split(',')
        for scene_title_id in scene_title_ids:
            try:
                scene_title = models.SceneTitle.objects.get(title_id=scene_title_id)
            except:
                continue
            scene_title.delete()


class SceneTitleMoveForm(forms.Form):
    product_id = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)
    product_scene_id = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)
    tag = forms.CharField(max_length=10, required=True, widget=forms.HiddenInput)
    move_scene_title_ids = forms.CharField(max_length=200, required=True, widget=forms.HiddenInput)
    move_product_scene_id = forms.ChoiceField(label='シーン', required=False)

    def save(self):
        scene_title_ids = self.cleaned_data['move_scene_title_ids'].split(',')
        for scene_title_id in scene_title_ids:
            try:
                scene_title = models.SceneTitle.objects.get(title_id=scene_title_id)
            except:
                continue
            scene_title.move_product_scene(self.cleaned_data['move_product_scene_id'])
            scene_title.save()


class ProductForm(forms.ModelForm):
    scene_count = forms.IntegerField(widget=forms.HiddenInput, required=False, initial=0)
    name = forms.CharField(max_length=256, required=True, widget=forms.TextInput(
        attrs={'oninvalid': "this.setCustomValidity('プロジェクト名は必須項目です。')",
               'oninput': "this.setCustomValidity('')"}), label='プロジェクト名')
    x = forms.FloatField(required=False, widget=forms.HiddenInput())
    y = forms.FloatField(required=False, widget=forms.HiddenInput())
    width = forms.FloatField(required=False, widget=forms.HiddenInput())
    height = forms.FloatField(required=False, widget=forms.HiddenInput())
    image = forms.FileField(required=False)

    class Meta:
        model = models.Product
        fields = (
            'name', 'image', 'max_scene', 'x', 'y',
            'width', 'height', 'is_active', 'acr_host', 'acr_access_key', 'acr_access_secret', 'code_name',
            'start_time', 'end_time', 'genres', 'description', 'client_name', 'allow_url_share',
            'auto_use_last', 'share_link')
        widgets = {
            'is_active': forms.HiddenInput(),
            'current_scene': forms.HiddenInput(),
            'current_heart': forms.HiddenInput(),
            'progress': forms.HiddenInput(),
            'total_budget': forms.NumberInput(attrs={'min': 0}),
            'acr_host': forms.TextInput(attrs={'class': 'form-control'}),
            'acr_access_key': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'required': False}),
            'acr_access_secret': forms.TextInput(attrs={'class': 'form-control'}),
            'client_name': forms.TextInput(attrs={'list': 'client_name', 'class': 'form-control'})
        }

    def clean(self):
        super(ProductForm, self).clean()
        acr_host = self.cleaned_data.get("acr_host", None)
        acr_access_key = self.cleaned_data.get("acr_access_key", None)
        acr_access_secret = self.cleaned_data.get("acr_access_secret", None)
        start_time = self.cleaned_data.get("start_time", None)
        end_time = self.cleaned_data.get("end_time", None)

        if start_time and end_time and end_time < start_time:
            self.add_error('end_time', "End time more than start time.")

        if acr_host and acr_access_key and acr_access_secret:
            re_config = customrecognize.config
            re_config["host"] = acr_host
            re_config["access_key"] = acr_access_key
            re_config["access_secret"] = acr_access_secret
            is_valid = customrecognize.check_config(re_config, customrecognize.get_test_buffer())
            if not is_valid:
                self.errors["acr_host"] = [customrecognize.ERROR_CONFIG_MESSAGE]
        elif not acr_host and not acr_access_key and not acr_access_secret:
            pass
        else:
            self.errors["acr_host"] = [customrecognize.ERROR_CONFIG_MESSAGE]

    def save(self, *args, **kwargs):
        instance = super(ProductForm, self).save(*args, **kwargs)
        # if instance.total_budget < instance.master_admin_get_spent_budget():
        #     instance.total_budget = instance.master_admin_get_spent_budget()
        #     instance.save()
        master_admins = AuthUser.objects.filter(role='master_admin')
        from accounts.services import update_product_user_new_video
        products = list()
        products.append(instance)
        update_product_user_new_video(products, master_admins)
        if instance.auto_use_last:
            scene_titles = models.SceneTitle.objects.filter(product_scene__product_scene=instance, status='5')
            for scene_title in scene_titles:
                if scene_title.scene_title.exists():
                    auto_use_last_scene(scene_title)


class CreateProductForm(forms.ModelForm):
    scene_count = forms.IntegerField(widget=forms.HiddenInput, required=False, initial=0)
    name = forms.CharField(max_length=256, required=True, widget=forms.TextInput(
        attrs={'oninvalid': "this.setCustomValidity('プロジェクト名は必須項目です。')",
               'oninput': "this.setCustomValidity('')"}), label='プロジェクト名')
    x = forms.FloatField(required=False, widget=forms.HiddenInput(attrs={'value': 0}))
    y = forms.FloatField(required=False, widget=forms.HiddenInput(attrs={'value': 0}))
    width = forms.FloatField(required=False, widget=forms.HiddenInput(attrs={'value': 0}))
    height = forms.FloatField(required=False, widget=forms.HiddenInput(attrs={'value': 0}))
    owner = forms.ModelChoiceField(required=False, queryset=AuthUser.objects.filter(role='master_client'), label='オーナー')
    image = forms.FileField(required=False)

    class Meta:
        model = models.Product

        fields = (
            'name', 'image', 'max_scene', 'x', 'y',
            'width', 'height', 'is_active', 'acr_host', 'acr_access_key', 'acr_access_secret', 'code_name',
            'total_budget', 'start_time', 'end_time', 'genres', 'description', 'owner', 'client_name',
            'allow_url_share', 'auto_use_last', 'share_link')

        widgets = {
            'is_active': forms.HiddenInput(),
            'current_scene': forms.HiddenInput(),
            'current_heart': forms.HiddenInput(),
            'progress': forms.HiddenInput(),
            'total_budget': forms.NumberInput(attrs={'min': 0}),
            'acr_host': forms.TextInput(attrs={'class': 'form-control'}),
            'acr_access_key': forms.TextInput(attrs={'class': 'form-control'}),
            'acr_access_secret': forms.TextInput(attrs={'class': 'form-control'}),
            'max_scene': forms.NumberInput(attrs={'min': 1, 'required': True}),
            'client_name': forms.TextInput(attrs={'list': 'client_name', 'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'required': False}),
        }

    def clean(self):
        super().clean()
        acr_host = self.cleaned_data.get("acr_host", None)
        acr_access_key = self.cleaned_data.get("acr_access_key", None)
        acr_access_secret = self.cleaned_data.get("acr_access_secret", None)
        start_time = self.cleaned_data.get("start_time", None)
        end_time = self.cleaned_data.get("end_time", None)

        if start_time and end_time and end_time < start_time:
            self.add_error('end_time', "End time more than start time.")

        if acr_host and acr_access_key and acr_access_secret:
            re_config = customrecognize.config
            re_config["host"] = acr_host
            re_config["access_key"] = acr_access_key
            re_config["access_secret"] = acr_access_secret
            is_valid = customrecognize.check_config(re_config, customrecognize.get_test_buffer())
            if not is_valid:
                self.errors["acr_host"] = [customrecognize.ERROR_CONFIG_MESSAGE]
        elif not acr_host and not acr_access_key and not acr_access_secret:
            pass
        else:
            self.errors["acr_host"] = [customrecognize.ERROR_CONFIG_MESSAGE]

    def save(self, *args, **kwargs):
        with transaction.atomic():
            instance = super(CreateProductForm, self).save(*args, **kwargs)
            master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN)
            instance.created_by = master_admins.first()
            instance.save()
            if master_admins:
                for master_admin in master_admins:
                    user_max = update_product_user_order(master_admin)
                    product_user, created = ProductUser.objects.get_or_create(product=instance, user=master_admin)
                    if created:
                        product_user.order = user_max
                    product_user.position = ProductUser.MASTERADMIN
                    product_user.save()

            owner = self.cleaned_data.get("owner", None)
            total_budget = self.cleaned_data.get("total_budget", 0)
            if owner and owner.role == AuthUser.MASTERCLIENT:
                user_max = update_product_user_order(owner)
                product_user, created = ProductUser.objects.get_or_create(product=instance, user=owner)
                if created:
                    product_user.order = user_max
                product_user.position = ProductUser.OWNER
                product_user.save()
            offer = models.OfferProduct.objects.create(master_admin=master_admins.first(),
                                               amount=total_budget, project=instance,
                                               budget=total_budget, start_time=instance.start_time,
                                               end_time=instance.end_time, deadline=instance.end_time)
            add_remove_member_into_offer_product(offer)

            return instance


class ProductSceneAddForm(forms.Form):
    product_id = forms.CharField(max_length=100, widget=forms.HiddenInput)
    name = forms.CharField(max_length=64, label='シーン名')

    def clean_product_id(self):
        product_id = self.cleaned_data['product_id']
        try:
            models.Product.objects.get(pk=product_id)
        except:
            raise ValueError('不正な値です')
        return product_id

    def save(self):
        product_id = self.cleaned_data['product_id']
        name = self.cleaned_data['name']
        product = models.Product.objects.get(pk=product_id)
        result = product.add_product_scene(name)
        product.save()
        return result


class CreateAudioForm(forms.ModelForm):
    class Meta:
        model = models.Audio
        fields = ['name', 'type', 'user', 'audio']
        widgets = {
            'user': forms.HiddenInput(),
        }

    def clean(self):
        self.cleaned_data = super(CreateAudioForm, self).clean()
        self.cleaned_data['user'] = Creator.objects.filter(pk=str(self.data['user_id']))[0]
        return self.cleaned_data


class ContentSaleForm(forms.ModelForm):
    x = forms.FloatField(widget=forms.HiddenInput(), required=False)
    y = forms.FloatField(widget=forms.HiddenInput(), required=False)
    width = forms.FloatField(widget=forms.HiddenInput(), required=False)
    height = forms.FloatField(widget=forms.HiddenInput(), required=False)
    image = forms.FileField()
    file = forms.FileField(label='オーディオデータ', widget=forms.FileInput(attrs={'accept': 'audio/*',
                                                                           'oninput': "setCustomValidity('')",
                                                                           'allow_multiple_selected': True}),
                           validators=[FileExtensionValidator(['mp3', 'wav', 'ogg'],
                                                              'ファイルのフォーマットが正しくありません。MP3, WAV ファイルをアップロードしてください。',
                                                              402)], )

    class Meta:
        model = models.ContentSale
        fields = ['title', 'desc', 'owner', 'price']
        widgets = {
            'owner': forms.HiddenInput(),
            'price': forms.TextInput(attrs={'class': 'form-control', 'autocomplete': 'off', 'pattern': '[0-9]+',
                                            'title': 'Enter Numbers Only ', 'maxlength': '9'}),
        }

    def is_valid(self):
        valid = super(ContentSaleForm, self).is_valid()
        if not valid:
            return valid

        if not self.cleaned_data['file'] and self.instance:
            if self.instance.audio_sale.all().count() == self.data['list_clear_audio'].split(',')[:-1].__len__():
                self._errors['file'] = 'このフィールドは必須です。'
                return False

        return True


class ProductSetting(forms.ModelForm):
    class Meta:
        model = models.ProductSetting
        exclude = ('modified', 'created')
        widgets = {}

    def save(self, *args, **kwargs):
        instance = super(ProductSetting, self).save(*args, **kwargs)
        owner_id = self.cleaned_data['owner_id']

        master_admins = AuthUser.objects.filter(role='master_admin')
        from accounts.services import update_product_user_new_video
        products = list()
        products.append(instance)
        if master_admins:
            for master_admin in master_admins:
                product_user, created = ProductUser.objects.get_or_create(product=instance, user=master_admin)
                product_user.position = ProductUser.MASTERADMIN
                product_user.save()

        if owner_id:
            try:
                user = get_user_model().objects.get(id=owner_id)
            except:
                return
            users = list()
            users.append(user)
            pu, created = ProductUser.objects.get_or_create(product=instance, user=user)
            pu.position = ProductUser.OWNER
            pu.save()
            update_product_user_new_video(products, users)


class SearchCreatorForm(forms.Form):
    keyword = forms.CharField(max_length=120, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'type': 'search', 'placeholder': 'アーティスト名/サンプル名', 'autocomplete': 'off'}))
    from_date = forms.CharField(required=True, widget=forms.TextInput(attrs={'type': 'text', 'class':'form-control form-control', 'placeholder': 'yyyy/mm/dd', 'autocomplete': 'off'}))
    to_date = forms.CharField(required=True, widget=forms.TextInput(attrs={'type': 'text', 'class':'form-control form-control', 'placeholder': 'yyyy/mm/dd', 'autocomplete': 'off'}))
    role = forms.ChoiceField(choices=Creator.ROLE_CREATOR, widget=forms.Select(attrs={'class': 'select__value-list'}))

    def clean(self):
        super(SearchCreatorForm, self).clean()
        try:
            from_date = dateutil.parser.parse(self.data["from_date"])
            self.cleaned_data['from_date'] = from_date
            to_date = dateutil.parser.parse(self.data["to_date"])
            self.cleaned_data['to_date'] = to_date
        except:
            raise forms.ValidationError("Your Input date invalid!")

    def is_valid(self):
        super().is_valid()
        if self.cleaned_data["to_date"] < self.cleaned_data["from_date"]:
            self.add_error("to_date", "ToDate must larger than FromDate!")
            return False
        return True


class OfferCreatorForm(forms.ModelForm):
    class Meta:
        model = models.OfferCreator
        exclude = ("status", "admin", "review")

    project = forms.CharField(widget=forms.HiddenInput(), required=True)
    creator = forms.CharField(widget=forms.HiddenInput(), required=True, initial="0")
    contract = forms.ChoiceField(choices=models.OfferCreator.TAG_CONTRACT, widget=forms.Select(attrs={"class": "select__value-list"}))
    message = forms.CharField(widget=forms.Textarea(attrs={"class": "form-control"}), required=True)
    reward = forms.IntegerField(widget=forms.NumberInput(attrs={"class": "form-control"}), required=True)
    scenes = forms.CharField(widget=forms.TextInput(attrs={"class": "form-control"}), required=True)
    type_contract = forms.CharField(
        widget=forms.TextInput(attrs={"class": "form-control", 'readonly': 'readonly', 'value': 'コンポーザー'}))
    file = forms.FileField(required=False, widget=forms.FileInput(attrs={"class": "upload__file-input"}))
    start_time = forms.CharField(widget=forms.HiddenInput(), required=True)
    deadline = forms.CharField(widget=forms.HiddenInput(), required=True)
    quantity = forms.CharField(widget=forms.TextInput(attrs={"class": "form-control"}), required=False)
    data_format = forms.CharField(widget=forms.TextInput(attrs={"class": "form-control"}), required=False)

    def clean_creator(self):
        try:
            return AuthUser.objects.get(pk=self.cleaned_data["creator"])
        except:
            raise forms.ValidationError("Invalid Creator ID")

    def clean_project(self):
        try:
            return models.Product.objects.get(pk=self.cleaned_data["project"])
        except:
            raise forms.ValidationError("Invalid Product ID")

    def clean_start_time(self):
        try:
            return datetime.datetime.strptime(self.cleaned_data["start_time"], '%Y/%m/%d')
        except:
            raise forms.ValidationError("Invalid Start Time")

    def clean_deadline(self):
        try:
            return datetime.datetime.strptime(self.cleaned_data["deadline"], '%Y/%m/%d %H:%M')
        except:
            raise forms.ValidationError("Invalid Deadline")


class AcrcloudTestForm(forms.Form):
    host = forms.CharField(required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    access_key = forms.CharField(required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    access_secret = forms.CharField(required=True, widget=forms.TextInput(attrs={'class': 'form-control', 'type': 'password'}))
    duration = forms.CharField(widget=forms.HiddenInput())
    file = forms.FileField(widget=forms.FileInput(attrs={"accept": "audio/*,video/*", "allow_multiple_selected": True}), required=True)

    def clean_duration(self):
        duration = self.cleaned_data.get('duration', None)
        return int(duration)

    def is_valid(self):
        super(AcrcloudTestForm, self).is_valid()
        if(self.cleaned_data is None):
            return False
        else:
            return True


class SceneTitleUploadForm(forms.ModelForm):
    class Meta:
        model = models.SceneTitle
        fields = 'production_file',


class OfferProductForm(forms.ModelForm):
    start_time = forms.DateTimeField(required=False, widget=forms.HiddenInput(), input_formats=['%Y/%m/%d'],)
    end_time = forms.DateTimeField(required=False, widget=forms.HiddenInput(), input_formats=['%Y/%m/%d'],)
    deadline = forms.DateTimeField(required=False, input_formats=['%Y/%m/%d %H:%M'],
                                   widget=forms.TextInput(attrs={'placeholder': 'yyyy/mm/dd'}))

    class Meta:
        model = OfferProduct
        fields = ('description', 'budget', 'start_time',
                  'end_time', 'deadline', 'contract_type', 'ownership_type', 'disclosure_rule')
        widgets = {
            'contract_type': forms.RadioSelect(),
            'ownership_type': forms.RadioSelect(),
            'disclosure_rule': forms.RadioSelect(),
            # 'bgm': forms.RadioSelect(),
            # 'se': forms.RadioSelect(),
            # 'voice': forms.RadioSelect(),
            'description': forms.Textarea(
                attrs={
                    'class': 'form-textarea',
                    'cols': '40',
                    'rows': '10',
                    'placeholder': '添付プロジェクトのサウンド制作をお願いしたいです。資料を確認の上、お見積もりプランをお願いします。折り返しをお待ちしておリます。'
                }
            ),
            'budget': forms.TextInput(
                attrs={
                    'class': 'form-control order-step__input-text',
                    'placeholder': '1,000,000',
                    'maxlength': '10',

                }
            ),
        }

    def save(self, *args, **kwargs):
        instance = super(OfferProductForm, self).save(*args, **kwargs)
        return instance


class ProductUpdateModalForm(forms.ModelForm):
    text_note = forms.CharField(max_length=255, required=False, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': 'プロジェクト名'}))
    x = forms.FloatField(required=False, widget=forms.HiddenInput())
    y = forms.FloatField(required=False, widget=forms.HiddenInput())
    width = forms.FloatField(required=False, widget=forms.HiddenInput())
    height = forms.FloatField(required=False, widget=forms.HiddenInput())
    x_logo = forms.FloatField(required=False, widget=forms.HiddenInput())
    y_logo = forms.FloatField(required=False, widget=forms.HiddenInput())
    width_logo = forms.FloatField(required=False, widget=forms.HiddenInput())
    height_logo = forms.FloatField(required=False, widget=forms.HiddenInput())
    image = forms.FileField(required=False)
    logo = forms.FileField(required=False)

    class Meta:
        model = models.Product
        fields = (
            'text_note', 'image', 'x', 'y', 'width', 'height',
            'logo', 'x_logo', 'y_logo', 'width_logo', 'height_logo',
            'acr_host', 'acr_access_key', 'acr_access_secret')
        widgets = {
            'acr_host': forms.TextInput(attrs={'class': 'form-control account__input-text', 'placeholder': 'identify-ap-southeast-1.acrcloud.com'}),
            'acr_access_key': forms.TextInput(attrs={'class': 'form-control account__input-text', 'placeholder': '9250e67d17f10c8592ab95ad12ada80c'}),
            'acr_access_secret': forms.TextInput(attrs={'class': 'form-control account__input-text', 'placeholder': 'tTRY8oZfuk2FoVO7qkblSqJPN7D3NA7bvnd88KyT'}),
        }

    def clean(self):
        super(ProductUpdateModalForm, self).clean()
        acr_host = self.cleaned_data.get('acr_host', None)
        acr_access_key = self.cleaned_data.get('acr_access_key', None)
        acr_access_secret = self.cleaned_data.get('acr_access_secret', None)
        if acr_host and acr_access_key and acr_access_secret:
            re_config = customrecognize.config
            re_config['host'] = acr_host
            re_config['access_key'] = acr_access_key
            re_config['access_secret'] = acr_access_secret
            is_valid = customrecognize.check_config(re_config, customrecognize.get_test_buffer())
            if not is_valid:
                self.errors['acr_host'] = [customrecognize.ERROR_CONFIG_MESSAGE]
        elif not acr_host and not acr_access_key and not acr_access_secret:
            pass
        else:
            self.errors['acr_host'] = [customrecognize.ERROR_CONFIG_MESSAGE]


class OfferProductWithProducerForm(forms.ModelForm):
    start_time = forms.DateTimeField(widget=forms.HiddenInput(), input_formats=['%Y/%m/%d'], )
    end_time = forms.DateTimeField(widget=forms.HiddenInput(), input_formats=['%Y/%m/%d'], )
    deadline = forms.DateTimeField(required=False, input_formats=['%Y/%m/%d %H:%M'],
                                   widget=forms.TextInput(attrs={'placeholder': 'yyyy/mm/dd'}))

    class Meta:
        model = OfferProduct
        fields = ('description', 'budget', 'start_time', 'end_time', 'deadline', 'contract_type', 'ownership_type',
                  'disclosure_rule')
        widgets = {
            'contract_type': forms.RadioSelect(),
            'ownership_type': forms.RadioSelect(),
            'disclosure_rule': forms.RadioSelect(),
            'description': forms.Textarea(
                attrs={
                    'class': 'form-textarea',
                    'cols': '40',
                    'rows': '10',
                    'placeholder': '添付プロジェクトのサウンド制作をお願いしたいです。資料を確認の上、お見積もりプランをお願いします。折り返しをお待ちしておリます。'
                }
            ),
            'budget': forms.TextInput(
                attrs={
                    'class': 'form-control order-step__input-text',
                    'placeholder': '1,000,000',
                    'maxlength': '10',
                }
            ),
        }

    def save(self, *args, **kwargs):
        instance = super(OfferProductWithProducerForm, self).save(*args, **kwargs)
        return instance


class TopicGalleryForm(forms.ModelForm):
    # thumbnail = forms.ImageField(widget=forms.FileInput(attrs={'accept': 'image/*'}), required=True)
    # x = forms.FloatField(widget=forms.HiddenInput())
    # y = forms.FloatField(widget=forms.HiddenInput())
    # width = forms.FloatField(widget=forms.HiddenInput())
    # height = forms.FloatField(widget=forms.HiddenInput())
    title = forms.CharField(max_length=255, required=True, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': 'トピックタイトル'}))
    overview = forms.CharField(max_length=255, required=False, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': '展示会の空間サウンドデザイン、オーディオガイドを制作します。'}))
    description = forms.CharField(max_length=1000, required=False, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': '展示会やプロジェクションマッピング、インスタレーショ\nン作品のサウンド演出にもっと向き合ってみませんか。イ\nマーシブで再生環境に寄り添い、かつ、タッチや各種センサーに反応する動的なサウンドデザインを提供。テクノロ\nジーを駆使した演出にしっかり応えます。\nまた、展示作品の解説に必要なオーディオガイド、QR\nコードで入場者限定で配信できる作品解説番組も制作でき\nます。'}))

    class Meta:
        model = TopicGallery
        fields = ('video', 'thumbnail', 'title', 'overview', 'description', 'image')

    # def save(self, commit=True, **kwargs):
    #     for key, value in kwargs.items():
    #         setattr(self.instance, key, value)
    #     super().save(commit)


class SelectionGalleryForm(forms.ModelForm):
    title = forms.CharField(max_length=255, required=False, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': '選択項目タイトル'}))
    description = forms.CharField(max_length=1000, required=False, widget=forms.TextInput(
        attrs={'class': 'form-control account__input-text',
               'placeholder': 'ここに選択項目の説明がはいります。\nここに選択項目の説明がはいります。'}))

    class Meta:
        model = SelectionGallery
        fields = ('title', 'description', 'selection_content', 'toggle_content', 'selection_content', 'toggle_content')

    def clean_selection_content(self):
        return json.dumps(ast.literal_eval(self.cleaned_data['selection_content']))

    def clean_toggle_content(self):
        return json.dumps(ast.literal_eval(self.cleaned_data['toggle_content']))

    def save(self, commit=True, **kwargs):
        for key, value in kwargs.items():
            setattr(self.instance, key, value)
        super().save(commit)


class ArtistCreateProductChargeForm(forms.ModelForm):
    class Meta:
        model = models.UserProductCharge
        fields = ('code_name', 'name', 'total_budget', 'amount', 'service_fee')
        widgets = {
            'total_budget': forms.NumberInput(attrs={'min': 0})
        }


class MgkContactForm(forms.ModelForm):
    fullname = forms.CharField(max_length=60, required=True,
                               widget=forms.TextInput(
                                   attrs={'placeholder': '岡部 健一', 'class': 'form-control account__input-text'}))
    email = forms.CharField(max_length=50, required=True, label='メールアドレス',
                            widget=forms.TextInput(
                                attrs={'placeholder': '<EMAIL>', 'class': 'form-control account__input-text'}))
    email_confirm = forms.CharField(max_length=50, required=True,
                                    widget=forms.TextInput(attrs={'placeholder': '<EMAIL>',
                                                                  'class': 'form-control account__input-text'}))
    job_type = forms.CharField(max_length=60, required=False,
                               widget=forms.TextInput(
                                   attrs={'placeholder': 'コンプライアンスマネージャー',
                                          'class': 'form-control account__input-text'}))
    enterprise = forms.CharField(max_length=60, required=False,
                                 widget=forms.TextInput(
                                     attrs={'placeholder': 'ネオストリームエンターテインメント株式会社',
                                            'class': 'form-control account__input-text'}))
    phone = forms.CharField(max_length=30, required=False,
                            widget=forms.TextInput(
                                attrs={'placeholder': '03-6457-1780', 'class': 'form-control account__input-text'}))

    message = forms.CharField(max_length=1000, required=False,
                              widget=forms.Textarea(attrs={'class': 'form-textarea',
                                                           'placeholder': 'こちらのサービスを活用して、著作権侵害のリスクを低減したいと考えています。詳しい説明資料をいただけますか？'}))

    class Meta:
        model = ContactInfo
        fields = ('plan', 'option', 'fullname', 'email', 'email_confirm', 'job_type', 'enterprise', 'phone',
                  'contact_channel', 'message')
        widgets = {
            'plan': forms.HiddenInput(),
            'option': forms.HiddenInput(),
            'contact_channel': forms.HiddenInput()
        }
