{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load widget_tweaks %}
{% load static %}
{% load util %}
{% load i18n %}
{% load compress %}

{% block extrahead %}
    {% compress js inline %}
    <!-- <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script> -->
        <!-- <script src="{% static 'js/cropper.min.js' %}"></script> -->
    <script src="{% static 'js/flatpickr_soremo.js' %}"></script>
    {% endcompress %}
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" /> -->
    {% compress css %}
        <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
        <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
        <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
        <!-- <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/> -->
        <link href="{% static 'css/account_info.css' %}" rel="stylesheet">
        <link rel="stylesheet" type="text/css" href="{% static 'css/calendar.css' %}"/>
        <link rel="stylesheet" href="{% static 'css/flatpickr_soremo.css' %}">

        <style>
          .modal-backdrop {
              z-index: -1 !important;
          }
      </style>
    {% endcompress %}


    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script> -->


{% endblock %}

{% block content %}
  <div class="user-info">
    <div class="container">
      {% include 'messenger/_modal_open_file.html' %}
      <div class="user-info__wrap">
        <ul class="nav flex-column user-info__tabs-list">
          <li class="nav-item">
            <a class="nav-link" href="#tab_1">アカウント情報</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#tab_2">個人情報</a>
          </li>
          {% if page == 'account_artist' %}
            <li class="nav-item">
              <a class="nav-link" href="#tab_3">現住所</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#tab_4">入金口座</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#tab_5">API Settings</a>
            </li>
          {% else %}
            <li class="nav-item">
              <a class="nav-link" href="#tab_3">請求先住所</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#tab_4">API Settings</a>
            </li>
          {% endif %}
        </ul>
      </div>

      <div class="user-info__main account__info" id="tab_1">
        <div class="user-info__heading">
          <h3 class="heading-spacing-2140">アカウント情報</h3>
        </div>

        <!-- API Settings Quick Actions - Separate from form -->
        <div class="api-quick-actions" id="{% if page == 'account_artist' %}tab_5{% else %}tab_4{% endif %}" style="margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
          <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
            <div>
              <h4 style="margin: 0 0 5px 0; color: #24292e; font-size: 16px; font-weight: 600;">🔑 MCP API Quick Access</h4>
              <small style="color: #586069;">API Keyを素早く作成してMCP URLをコピーできます</small>
            </div>
            <div style="display: flex; gap: 10px; align-items: center;">
              <button type="button" id="quickCreateMcpBtn" class="btn btn--secondary" 
                      style="background-color: #28a745; border-color: #28a745; color: white; padding: 8px 16px;">
                🚀 MCP URL作成&コピー
              </button>
              <a href="{% url 'accounts:api_key_list' %}" class="btn btn--primary" style="padding: 8px 16px;">API Key管理画面へ</a>
            </div>
          </div>
          <div id="mcpQuickCreateStatus" style="margin-top: 15px; display: none;">
            <div class="alert alert-success" id="mcpSuccessMessage"></div>
            <div class="alert alert-danger" id="mcpErrorMessage"></div>
          </div>
        </div>

        <div class="user-info__content">
          <form class="user-info__form row" id="acc-info__form" method="post" data-user="{{ user_form.pk }}"
                action="{% if page == 'account_artist' %}{% url 'accounts:creator_info' user_form.pk %}{{ redirect_url }}{% else %}{% url 'accounts:accounts_update_info' user_form.pk %}{% if redirect_url%}?next_url={{ redirect_url }}{% endif %}{% endif %}" enctype="multipart/form-data">
            {% csrf_token %}
            <!-- <div class="account__title">アカウント情報</div> -->
            <div class="form-group col-sm-12 text-center user-info__images-wrap">
              <div class="user-info__images">
                <img src="{{ user_form|get_avatar:'medium' }}" class="user-info__images-img" alt="" width="300" height="300"/>
                <div class="user-info__upload half-circle">
                  <label class="user-info__upload-icon" for="id_avatar">
                    <i class="icon icon--sicon-camera"></i>
                  </label>
                   <input type="file" name="avatar" class="user-info__upload-file" id="id_avatar">
                </div>
              </div>
            </div>
            <div class="form-group col-sm-6 col-xs-6 hide">
              <label for="id_last-name">姓*</label>
              {{ form.last_name|add_class:"form-control"|attr:"placeholder:姓" }}
            </div>
            <div class="form-group col-sm-6 col-xs-6 hide">
              <label for="id_first-name">名*</label>
              {{ form.first_name|add_class:"form-control"|attr:"placeholder:名" }}
            </div>
            <div class="account__form-group form-group col-sm-12">
              {% if page == 'account_artist' %}
                <div class="col-sm-12 form-group">
                  <label class="col-sm-12" for="id_stage_name">
                    <span class="heading-13">{% trans "Artist name" %}<span class="account__jp-astarisk-op">[任意]</span></span>
                    <span class="account__field-hint">{% trans "Let's set the stage name and nickname to be written on the work credit." %}</span>
                    <div class="col-sm-12" style="padding: 0 25px 0 0; margin: -15px;">
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.stage_name|add_class:"form-control account__input-text" }}
                        {{ form.stage_name.errors }}
                      </div>
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.stage_name_en|add_class:"form-control account__input-text" }}
                        {{ form.stage_name_en.errors }}
                      </div>
                    </div>
                  </label>
                </div>
                <div class="col-sm-12 form-group">
                  <label class="col-sm-12" for="role">
                    <span class="heading-13">{% trans "Title account" %}<span class="account__jp-astarisk-op">[任意]</span></span>
                    <span class="account__field-hint">{% trans "Explain your role in SOREMO." %}</span>
                    <div class="col-sm-12" style="padding: 0 25px 0 0; margin: -15px;">
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.position|add_class:"form-control account__input-text" }}
                        {{ form.position.errors }}
                      </div>
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.type|add_class:"form-control account__input-text" }}
                        {{ form.type.errors }}
                      </div>
                    </div>
                  </label>
                </div>
                <div class="col-sm-12 form-group">
                  <label class="col-sm-5" for="id_enterprise">
                    <span class="heading-13">{% trans "Organization name" %}<span class="account__jp-astarisk-op">[任意]</span></span>
                    {{ form.enterprise|add_class:"form-control account__input-text" }}
                    {{ form.enterprise.errors }}
                  </label>
                </div>
              {% else %}
                <div class="col-sm-12 form-group">
                  <label class="col-sm-5" for="id_display_name">
                    <span class="heading-13">{% trans "Showing name" %}<span class="account__jp-astarisk-op">[任意]</span></span>
                    <span class="account__field-hint">{% trans "You can freely set stage names and nicknames." %}</span>
                    {{ form.display_name|add_class:"form-control account__input-text" }}
                    {{ form.display_name.errors }}
                  </label>
                </div>
                <div class="col-sm-12 form-group">
                  <label class="col-sm-5" for="role">
                    <span class="heading-13">{% trans "Job title / charge" %}<span class="account__jp-astarisk-op">[任意]</span></span>
{#                    <span class="account__field-hint">{% trans "Explain your role in SOREMO." %}</span>#}
                    {{ form.position|add_class:"form-control account__input-text" }}
                    {{ form.position.errors }}
                  </label>
                </div>

                <div class="col-sm-12 form-group">
                  <label class="col-sm-5" for="id_enterprise">
                    <span class="heading-13">{% trans "Company name / URL" %}<span class="account__jp-astarisk-op">[任意]</span></span>
                    {{ form.enterprise|add_class:"form-control account__input-text" }}
                    {{ form.enterprise.errors }}
                  </label>

                  <label class="col-sm-5" for="id_company_url">
                    <span class="heading-13"><span class="account__jp-astarisk-op"></span></span>
                    {{ form.company_url|add_class:"form-control account__input-text" }}
                    {{ form.company_url.errors }}
                  </label>
                </div>
              {% endif %}
            </div>

            <div class="account__form-group form-group col-sm-12" id="tab_2">
              <h3 class="account__form-heading">個人情報</h3>
              <div class="col-sm-12 form-group">
                <label class="col-sm-5" for="email">
                  <span class="heading-13">メールアドレス<span class="account__jp-astarisk">[必須]</span></span>
                  {{ form.email|add_class:"form-control account__input-text" }}
                  {{ form.email.errors }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-2" for="acc_password">
                  <span class="heading-13">パスワード<span class="account__jp-astarisk">[必須]</span></span>
                  <input type="button" class="btn btn--secondary" data-target="#resetPassword" data-toggle="modal"
                         id="acc_password" value="パスワードを変更"/>
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-5" for="id_fullname">
                  <span class="heading-13">氏名<span class="account__jp-astarisk">[必須]</span></span>
                  {{ form.fullname|add_class:"form-control account__input-text" }}
                  {{ form.fullname.errors }}
                  {% if page == 'account_artist' %}
                  <p class="account__field-description">※アーティスト名を設定すると、氏名は公開されません。</p>
                  {% else %}
                  <p class="account__field-description">※表示名を設定すると、氏名は公開されません。</p>
                  {% endif %}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-3 mcalendar-wrap" for="id_dob">
                  <span class="heading-13">生年月日<span class="account__jp-astarisk-op">[任意]</span></span>
                  {{ form.dob|add_class:"mcalendar mcalendar--small form-control account__input-text" }}
                  {{ form.dob.errors }}
                  <i class="icon icon--sicon-calendar"></i>
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-3" for="id_phone">
                  <span class="heading-13">電話番号<span class="account__jp-astarisk-op">[任意]</span></span>
                  {{ form.phone|add_class:"form-control account__input-text" }}
                  {{ form.phone.errors }}
                </label>
              </div>

             <div class="col-sm-12 form-group">
                <label class="col-sm-3" for="id_invoice_register_number">
                  <span class="heading-13">適格請求書発行事業者登録番号<span class="account__jp-astarisk-op">[任意]</span></span>
                  {{ form.invoice_register_number|add_class:"form-control account__input-text" }}
                  {{ form.invoice_register_number.errors }}
                </label>
              </div>

              {% if page == 'account_artist' %}
                {% with user_form.user_creator.first as creator %}
                  <div class="col-sm-12 form-group">
                    <label class="col-sm-12 label-status" for="">
                      <span class="heading-13">本人確認書類
                      <span class="account__jp-astarisk-op">[任意]</span>
                        {% if creator.creator_file_status == '1' %}
                          <span class="account__status account__status-normal">審査中</span>
                        {% elif creator.creator_file_status == '2' %}
                          <span class="account__status account__status-confirmed">承認済</span>
                        {% elif creator.creator_file_status == '3' %}
                          <span class="account__status account__status-normal">拒否済</span>
                        {% endif %}
                    </span>
                      <span class="account__field-hint">提出して、オファーが届くプロジェクトを増やしましょう。</span>
                      {% if creator.idetity_files.exists %}
                        {% for file in creator.idetity_files.all %}
                          <div class="account__file account__current-file" data-file-id="{{ file.pk }}">
                            <i class="icon icon--sicon-clip"></i>
                            <a href="{{ file.file.url }}" target="_blank">
                              <div class="account__file-name">{{ file.real_name }}</div>
                            </a>
                            <i class="icon icon--sicon-close"></i>
                          </div>
                        {% endfor %}
                        {% if creator.creator_file_status == '1' %}
                          <span class="account__field-hint text--file">書類を確認しています。今しばらくお待ちください。</span>
                        {% endif %}
                      {% endif %}

                      <div class="account_upload-file mattach mattach-form">
                        <div class="mcomment-attached">
                          <div class="mattach-preview-container mattach-preview-container-form">
                            <div class="mattach-previews mattach-previews-form collection">
                              <div class="mattach-template mattach-template-form collection-item item-template">
                                <div class="mattach-info" data-dz-thumbnail="">
                                  <div class="mcommment-file">
                                    <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                                    <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                            class="icon icon--sicon-close"></i>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div id="myId" class="fallback dropzone">
                        </div>
                      </div>

                    </label>
                  </div>

                  <div class="col-sm-12 form-group">
                    <label class="col-sm-5" for="">
                      {% if creator.curator_file %}
                        <span class="heading-13">守秘義務契約{% if creator.curator_file %}
                          <span class="account__status account__status-confirmed">{% trans "Concluded" %}</span>{% endif %}</span>
                        {% with  creator.get_type_file_curator as type_file %}
                          <input type="button" class="btn btn--secondary btn-nondisclosure-agreement" value="内容を確認"
                                 data-type="{{ type_file }}"
                                 data-source="{{ creator.curator_file|get_link_to_preview_file:type_file }}"
                                 data-name="{{ creator.curator_file_name }}"/>
                        {% endwith %}
                      {% else %}
                        <span class="heading-13">守秘義務契約</span>
                          <input type="button" class="btn btn--secondary" value="内容を確認" disabled/>
                      {% endif %}
                    </label>
                  </div>
                {% endwith %}
              {% endif %}
            </div>

            <div class="account__form-group form-group col-sm-12" id="tab_3">
              {% if page == 'account_artist' %}
              <h3 class="account__form-heading">現住所</h3>
              {% else %}
              <h3 class="account__form-heading">請求先住所</h3>
              {% endif %}
              <div class="col-sm-12 form-group">
                <label class="col-sm-5 postalcode">
                  <span class="heading-13">郵便番号<span class="account__jp-astarisk-op">[任意]</span></span>
                  <div class="col-sm-12" style="padding: 0;margin: -15px;">
                    <div class="col-sm-7" style="padding: 15px;">
                      {{ form.post_number|add_class:"form-control account__input-text" }}
                      {{ form.post_number.errors }}
                    </div>
                    <div class="col-sm-5" style="padding: 15px;">
                      <input type="button" class="btn btn--secondary" value="郵便番号検索" id="get_zip_code"/>
                    </div>
                  </div>
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-5 prefectures">
                  <span class="heading-13">都道府県<span class="account__jp-astarisk-op">[任意]</span></span>
                  <div class="col-sm-12" style="padding: 0;margin: -15px;">
                    <div class="col-sm-7" style="padding: 15px;">
                      {{ form.province|add_class:"form-control account__input-text" }}
                      {{ form.province.errors }}
                    </div>
                  </div>

                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-5 adress1">
                  <span class="heading-13">市町村<span class="account__jp-astarisk-op">[任意]</span></span>
                  {{ form.city|add_class:"form-control account__input-text" }}
                  {{ form.city.errors }}
                </label>
              </div>

              <div class="col-sm-12 form-group">
                <label class="col-sm-5 adress2">
                  <span class="heading-13">番地・部屋番号<span class="account__jp-astarisk-op">[任意]</span></span>
                  {{ form.mansion|add_class:"form-control account__input-text" }}
                  {{ form.mansion.errors }}
                </label>
              </div>

              <div class="col-sm-12 form-group address-wrap">
                <h3 class="account__form-heading" style="padding: 0 0 8px; border: none;">送付先住所</h3>
                <div class="col-sm-12 form-group">
                  <label class="col-sm-5" for="team-adress">
                    <span class="account__field-hint">必要書類や開発機材の送付が必要な場合に利用致します。</span>
                    <label class="account__field-checkbox-text">
                      {{ form.only_address|add_class:"account__field-checkbox" }}
                       <span class="checkmark"></span>
                       {% if page == 'account_artist' %}
                       <span class="account__field_input_text">現住所と同じ</span>
                      {% else %}
                      <span class="account__field_input_text">請求先住所と同じ</span>
                      {% endif %}
                    </label>
                  </label>
                </div>
                <div class="address2">
                  <div class="col-sm-12 form-group">
                    <label class="col-sm-5 postalcode">
                      <span class="heading-13">郵便番号<span class="account__jp-astarisk-op">[任意]</span></span>
                      <div class="col-sm-12" style="padding: 0;margin: -15px;">
                        <div class="col-sm-7" style="padding: 15px;">
                          {{ form.post_number2|add_class:"form-control account__input-text" }}
                          {{ form.post_number.errors }}
                        </div>
                        <div class="col-sm-5" style="padding: 15px;">
                          <input type="button" class="btn btn--secondary" value="郵便番号検索" id="get_zip_code2"/>
                        </div>
                      </div>
                    </label>
                  </div>

                  <div class="col-sm-12 form-group">
                    <label class="col-sm-5 prefectures">
                      <span class="heading-13">都道府県<span class="account__jp-astarisk-op">[任意]</span></span>
                      <div class="col-sm-12" style="padding: 0;margin: -15px;">
                        <div class="col-sm-7" style="padding: 15px;">
                          {{ form.province2|add_class:"form-control account__input-text" }}
                          {{ form.province2.errors }}
                        </div>
                      </div>
                    </label>
                  </div>

                  <div class="col-sm-12 form-group">
                    <label class="col-sm-5 adress1">
                      <span class="heading-13">市町村<span class="account__jp-astarisk-op">[任意]</span></span>
                      {{ form.city2|add_class:"form-control account__input-text" }}
                      {{ form.city2.errors }}
                    </label>
                  </div>

                  <div class="col-sm-12 form-group">
                    <label class="col-sm-5 adress2">
                      <span class="heading-13">番地・部屋番号<span class="account__jp-astarisk-op">[任意]</span></span>
                      {{ form.mansion2|add_class:"form-control account__input-text" }}
                      {{ form.mansion2.errors }}
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {% if page == 'account_artist' %}
              <div class="account__form-group form-group col-sm-12" id="tab_4">
                <h3 class="account__form-heading">入金口座</h3>
                <div class="col-sm-12 form-group">
                  <label class="col-sm-5 bankname" for="id_bank">
                    <span class="heading-13">銀行名<span class="account__jp-astarisk-op">[任意]</span></span>
                    {{ form.bank|add_class:"form-control account__input-text" }}
                  </label>
                </div>

                <div class="col-sm-12 form-group">
                  <label class="col-sm-6 branchname">
                    <span class="heading-13">支店名・番号<span class="account__jp-astarisk-op">[任意]</span></span>
                    <div class="col-sm-12" style="padding: 0; margin: -15px;">
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.bank_branch|add_class:"form-control account__input-text" }}
                      </div>
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.bank_branch_number|add_class:"form-control account__input-text" }}
                      </div>
                    </div>
                  </label>
                </div>

                <div class="col-sm-12 form-group">
                  <label class="col-sm-6">
                    <span class="heading-13">口座種類・番号<span class="account__jp-astarisk-op">[任意]</span></span>
                    <div class="col-sm-12" style="padding: 0; margin: -15px;">
                      <div class="col-sm-5 account__select-option" style="padding: 15px;">
                        {{ form.account_type|add_class:"form-control account__input-text account__select" }}
                      </div>
                      <div class="col-sm-5" style="padding: 15px;">
                        {{ form.account_number|add_class:"form-control account__input-text" }}
                      </div>
                    </div>
                  </label>
                </div>

                <div class="col-sm-12 form-group">
                  <label class="col-sm-5 transfername" for="id_transfer">
                    <span class="heading-13">振込名義<span class="account__jp-astarisk-op">[任意]</span></span>
                    {{ form.account_name|add_class:"form-control account__input-text" }}
                  </label>
                </div>
              </div>
            {% endif %}

            {{ form.x }}
            {{ form.y }}
            {{ form.width }}
            {{ form.height }}
            <div class="user-info__submit col-sm-12 acc_action">
              {% buttons %}
                <input type="submit" value="OK" id="btn__ok" class="btn btn--primary"/>
              {% endbuttons %}
            </div>
          </form>
        </div>
        <div class="user-info__submit col-sm-12 acc_delete">
          {% with user_form|deleteable as deleteable %}
            {% if deleteable %}
            <input type="submit" id="delete-account" data-target="#deleteAccount" data-toggle="modal"
                  class="btn btn--secondary" value="SOREMOのアカウントを完全に削除">
            {% endif %}
          {% endwith %}
        </div>
      </div>
    </div>

    <div class="modal" id="modalCrop">
      <div class="modal-dialog" style="transform: translate(0, 10%);">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
            <h3 class="modal-title">{% trans "Register image" %}</h3>
          </div>
          <div class="modal-body">
            <img src="" id="image" style="max-width: 100%;"/>
          </div>
          <div class="modal-footer">
            <div class="btn-group pull-left" role="group">
              <button type="button" class="btn btn-default js-zoom-in">
                <span class="glyphicon glyphicon-zoom-in"></span>
              </button>
              <button type="button" class="btn btn-default js-zoom-out">
                <span class="glyphicon glyphicon-zoom-out"></span>
              </button>
            </div>
            <button type="button" class="btn btn-primary js-crop-and-upload">{% trans "To register" %}</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal reset pass -->
    <div class="modal popup account__popup-container" id="resetPassword" tabindex="-1" role="dialog"
         aria-labelledby="addInfoTitle" aria-hidden="true">
      <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
          <div class="popup-header">
          </div>
          <div class="popup-body">
            <form method="post" action="{% url 'accounts:update_password' %}" id="acc_info"
                  enctype="multipart/form-data">
              {% csrf_token %}
              <div class="row col-md-12 popup-pass">
                <div class="form-group">
                  <label class="control-label" for="new_pass">新しいパスワード</label>
                  <div class="form-group__input-group">
                    <input type="password" class="form-control account_input-pass" maxlength="20" minlength="8"
                           name="new_password1"
                           id="new_pass" placeholder="" autocomplete="off"/>
                  </div>
                </div>

                <div class="form-group">
                  <label class="control-label" for="confirm_pass">新しいパスワード確認</label>
                  <div class="form-group__input-group">
                    <input type="password" class="form-control account_input-pass" maxlength="20" minlength="8"
                           name="new_password2"
                           id="confirm_pass" placeholder="" autocomplete="off"/>
                  </div>
                </div>

                <div class="form-group">
                  <label class="control-label" for="current_pass">現在のパスワード</label>
                  <div class="form-group__input-group">
                    <input type="password" class="form-control account_input-pass"
                           name="old_password"
                           id="current_pass" placeholder="" autocomplete="off"/>
                  </div>
                </div>
              </div>
              <div class="popup-footer">
                <button type="button" class="btn btn--tertiary" data-dismiss="modal" aria-label="Close">{% trans "cancel" %}</button>
                <button type="submit" class="btn btn--primary btn-popup-save">更新</button>
              </div>
            </form>
          </div>

        </div>
      </div>
    </div>
    <!-- End reset pass-->

    <!-- Modal delete -->
    <div class="modal popup account__popup-container" id="deleteAccount" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog popup-dialog" role="document">
        <div class="modal-content popup-content">
          <div class="popup-header">
          </div>
          <div class="popup-body">
            <p class="popup-text">この操作は元に戻せません。本当にアカウントを削除しますか？</p>
          </div>
          <div class="popup-footer">
            <button type="button" class="btn btn--primary btn-popup-close" data-dismiss="modal">いいえ</button>
            <button type="button" class="btn btn--tertiary btn-popup-delete">はい</button>
          </div>
        </div>
      </div>
    </div>
    <!-- End modal delete -->
  </div>

{% endblock content %}
{% block extra_script %}
  <!-- <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script> -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
  <script src="{% url 'javascript-catalog' %}"></script>
    {% compress js inline %}
  <script type="text/javascript" src="{% static 'js/common_variable.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/validate_contract.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/account_info.js' %}"></script>
    {% endcompress %} -->

    {% compress js inline %}
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script type="text/javascript" src="{% static 'js/image_cropping.js' %}"></script>
    <script src="{% static 'js/cropper.min.js' %}"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $('#id_avatar').attr({accept: 'image/*'});

            $('#id_avatar').on('change', function () {
                var image_dom = $('.user-info__images img');
                if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
                    let reader = new FileReader();
                    reader.onload = function (e) {
                        $('#image').attr('src', e.target.result);
                        $('#modalCrop').modal('show');
                    };
                    reader.readAsDataURL(this.files[0]);
                } else if (this.files.length == 0) {
                    return false;
                } else {
                    alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
                    $(this).val('').clone(true);
                }
            })
            $("#modalCrop").modal({
                show: false,
                backdrop: 'static'
            });

            var $image = $('#image');
            var cropBoxData;
            var canvasData;
            $('#modalCrop').on('shown.bs.modal', function () {
                $image.cropper({
                    viewMode: 1,
                    aspectRatio: 1 / 1,
                    minCropBoxWidth: 200,
                    minCropBoxHeight: 200,
                    minContainerHeight: 300,
                    ready: function () {
                        $image.cropper('setCanvasData', canvasData);
                        $image.cropper('setCropBoxData', cropBoxData);
                    }
                });
                $('#modalCrop').keypress(function (e) {
                    var code = e.which; // recommended to use e.which, it's normalized across browsers
                    if (code == 13) {
                        e.preventDefault();
                        $('.js-crop-and-upload').click();
                        return false;
                    }
                });
            }).on('hidden.bs.modal', function () {
                cropBoxData = $image.cropper('getCropBoxData');
                canvasData = $image.cropper('getCanvasData');
                if ($('.user-info__images img')[0].src.match('/default-avt.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-client.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-admin.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-master-admin.png')
                    || $('.user-info__images img')[0].src.match('/default-avatar-creator.png')) {
                    $('#id_avatar').val('').clone(true);
                }
                $image.cropper('destroy');
            });

            // Enable zoom in button
            $('.js-zoom-in').click(function () {
                $image.cropper('zoom', 0.1);
            });

            // Enable zoom out button
            $('.js-zoom-out').click(function () {
                $image.cropper('zoom', -0.1);
            });

            $('.js-crop-and-upload').click(function () {
                var cropData = $image.cropper("getData", {fillColor: '#fff'});
                var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");
                var image_dom = $('.user-info__images img');
                image_dom.attr('src', croppedImageDataURL);

                $('#id_x').val(cropData['x']);
                $('#id_y').val(cropData['y']);
                $('#id_height').val(cropData['height']);
                $('#id_width').val(cropData['width']);
                $image[0].height = cropData['height'];
                $image[0].width = cropData['width'];
                console.log($image[0].height, $image[0].width, $image[0].x, $image[0].y);
                $('#modalCrop').modal('hide');
            });

            // API Settings tab - smooth scroll to quick actions
            $(document).on('click', 'a[href="{% if page == "account_artist" %}#tab_5{% else %}#tab_4{% endif %}"]', function(e) {
                e.preventDefault();
                const target = document.getElementById('{% if page == "account_artist" %}tab_5{% else %}tab_4{% endif %}');
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });

            // Quick create MCP URL and copy functionality
            $('#quickCreateMcpBtn').click(function() {
                var $btn = $(this);
                var originalText = $btn.text();
                var $status = $('#mcpQuickCreateStatus');
                var $successMsg = $('#mcpSuccessMessage');
                var $errorMsg = $('#mcpErrorMessage');
                
                // Reset status messages
                $status.hide();
                $successMsg.hide().text('');
                $errorMsg.hide().text('');
                
                // Disable button and show loading
                $btn.prop('disabled', true).text('🔄 作成中...');
                
                $.ajax({
                    url: '{% url "accounts:api_key_create_ajax" %}',
                    type: 'POST',
                    data: {
                        'memo': 'MCP Quick Access - ' + new Date().toLocaleString('ja-JP'),
                        'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Copy MCP URL to clipboard
                            copyToClipboard(response.mcp_url);
                            
                            // Show success message
                            $successMsg.html(
                                '✅ API Keyが作成され、MCP URLがクリップボードにコピーされました！<br>' +
                                '<small style="font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px;">' + 
                                response.mcp_url + '</small>'
                            ).show();
                            $status.show();
                            
                            // Auto-hide success message after 8 seconds
                            setTimeout(function() {
                                $status.fadeOut();
                            }, 8000);
                        } else {
                            $errorMsg.text('❌ ' + response.message).show();
                            $status.show();
                        }
                    },
                    error: function(xhr) {
                        var errorMessage = '予期しないエラーが発生しました';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        $errorMsg.text('❌ ' + errorMessage).show();
                        $status.show();
                    },
                    complete: function() {
                        // Re-enable button
                        $btn.prop('disabled', false).text(originalText);
                    }
                });
            });
            
            // Helper function to copy text to clipboard
            function copyToClipboard(text) {
                if (navigator.clipboard && window.isSecureContext) {
                    // Modern browsers with clipboard API
                    navigator.clipboard.writeText(text);
                } else {
                    // Fallback for older browsers
                    var textarea = document.createElement('textarea');
                    textarea.value = text;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                }
            }

        });

        let checkActive = function (obj) {
            let $obj = $(obj);
            $.ajax({
                url: '/product_user/' + $obj.attr('value') + '/?active=' + $obj.prop('checked'),
                type: 'GET',
            })
                .done(function () {
                    console.log("success");
                })
                .fail(function () {
                    $obj.prop('checked', false)
                });

        }
    </script>
    {% endcompress %}
{% endblock %}
