from rest_framework import serializers
from .models import AuthUser, ProductUser

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)
    remember_me = serializers.BooleanField(default=False, required=False)

    def validate(self, attrs):
        from django.contrib.auth import authenticate
        user = authenticate(username=attrs['username'], password=attrs['password'])
        if user is None:
            raise serializers.ValidationError('Invalid username or password.')
        attrs['user'] = user
        return attrs


# 自分の情報を取得する用（機密情報も含む）
class MeAuthUserSerializer(serializers.ModelSerializer):

    class Meta:
        model = AuthUser
        fields = [
            'id', 
            'role',
            'avatar_original',
            'avatar_optimized',
            'display_name_jp',
            'display_name_en',
            'title_jp',
            'title_en',
            'affiliation_jp',
            'affiliation_en',
            'notification_setting',
            'notification_time',
            'email',
            'full_name',
            'date_of_birth',
            'phone_number',
            'invoice_registration_number',
            'total_miles',
            'available_balance',
            'pending_balance',
            'bank',
            'bank_branch',            
        ]
    avatar_original = serializers.ImageField(source='avatar', required=False)
    avatar_optimized = serializers.ImageField(source='medium_avatar', required=False)
    display_name_jp = serializers.CharField(source='stage_name', required=False, allow_blank=True)
    display_name_en = serializers.CharField(source='stage_name_en', required=False, allow_blank=True)
    title_jp = serializers.CharField(source='position', required=False, allow_blank=True)
    title_en = serializers.CharField(source='type', required=False, allow_blank=True)
    affiliation_jp = serializers.CharField(source='enterprise', required=False, allow_blank=True)
    notification_setting = serializers.CharField(source='setting_mail', required=False, allow_blank=True)
    notification_time = serializers.CharField(source='noti_hours', required=False, allow_blank=True)
    full_name = serializers.CharField(source='fullname', required=False, allow_blank=True)
    date_of_birth = serializers.DateTimeField(source='dob', required=False)
    phone_number = serializers.CharField(source='phone', required=False, allow_blank=True)
    invoice_registration_number = serializers.CharField(source='invoice_register_number', required=False, allow_blank=True)
    total_miles = serializers.FloatField(source='total_point', required=False)
    available_balance = serializers.DecimalField(source='balance_available', max_digits=30, decimal_places=2, required=False)
    pending_balance = serializers.DecimalField(source='balance_expected', max_digits=30, decimal_places=2, required=False)
    

# 後方互換性のため既存のものも残す（段階的に上記に移行推奨）

class AuthUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthUser
        fields = '__all__'

class ProductUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductUser
        fields = '__all__'
