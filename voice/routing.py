import os
import django
from pathlib import Path

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
from django.urls import path, re_path

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "voice.settings")
django.setup()
# This application object is used by any ASGI server configured to use this file.
django_application = get_asgi_application()

from app.consumers import MessengerConsumer
from accounts.consumers import UserConsumer
# MCP Integration
from django_mcp import mount_mcp_server

# Define the ASGI application
application = ProtocolTypeRouter({
    "http": mount_mcp_server(
        django_http_app=django_application,
        mcp_base_path='/mcp/<str:token>'
    ),  # For HTTP requests
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter([
            re_path(r"^ws/messenger/(?P<offer_id>[a-zA-Z0-9¥-]+)$", MessengerConsumer.as_asgi()),
            re_path(r"^ws/users_online$", UserConsumer.as_asgi()),
        ])
        )
    ),
})
