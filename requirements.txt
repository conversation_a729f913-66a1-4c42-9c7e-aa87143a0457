aioredis>=1.2.0
amqp>=2.5.2
appnope>=0.1.0
asgiref>=3.2.2
asn1crypto>=0.24.0
async-timeout>=3.0.1
attrs>=19.3.0
autobahn>=19.9.3
Automat>=0.7.0
backcall>=0.1.0
bcrypt>=3.1.5
billiard>=3.6.1
boto>=2.48.0
boto3>=1.4.8
botocore>=1.8.50
Brotli>=1.0.7
celery==4.4.0
certifi>=2018.11.29
cffi>=1.11.5
channels>=2.3.0
channels-redis>=2.4.0
chardet>=3.0.4
constantly>=15.1.0
cryptography>=2.8
daphne>=2.3.0
decorator>=4.4.0
defusedxml>=0.6.0
Django>=5.1.5
django-appconf>=1.0.5
django-bootstrap3>=9.0.0
django-braces>=1.13.0
django-celery-results==1.2.1
django-debug-toolbar>=2.0a1
django-environ>=0.4.4
django-extensions>=1.9.8
django-image-cropping>=1.2.0
django-model-utils>=3.0.0
django-mysql>=2.4.1
django-redis>=4.8.0
django-storages>=1.6.3
django-user-agents>=0.3.2
django-widget-tweaks>=1.4.3
docutils>=0.14
easy-thumbnails>=2.5
Fabric3>=1.13.1.post1
flake8>=3.8.4
flake8-django>=1.1.1
future>=0.18.2
h2>=3.2.0
hiredis>=1.0.0
hpack>=3.0.0
hyperframe>=5.2.0
hyperlink>=19.0.0
idna>=2.5
imageio>=2.5.0
imageio-ffmpeg>=0.2.0
importlib-metadata>=1.6.1
incremental>=17.5.0
ipython>=7.5.0
ipython-genutils>=0.2.0
jedi>=0.13.3
jmespath>=0.9.3
jsonfield
kombu>=4.6.7
mccabe>=0.6.1
moviepy>=1.0.3
msgpack>=0.6.1
msgpack-python>=0.5.6
mysql>=0.0.3
mysqlclient>=1.4.2.post1
newrelic>=*********
numpy>=1.17.3
oauthlib>=3.1.0
pafy>=0.5.4
paramiko>=2.4.2
parso>=0.4.0
pexpect>=4.7.0
pickleshare>=0.7.5
Pillow>=5.4.0
priority>=1.3.0
proglog>=0.1.9
prompt-toolkit>=2.0.9
psycopg2-binary>=2.8.5
ptyprocess>=0.6.0
pyacrcloud @ git+https://github.com/acrcloud/acrcloud_sdk_python@c0836f1682b769722592c6b46960d342058be0c4
pyasn1>=0.4.6
pyasn1-modules>=0.2.8
pycodestyle>=2.6.0
pycparser>=2.19
pydub>=0.23.1
pyflakes>=2.2.0
Pygments>=2.3.1
PyHamcrest>=1.9.0
PyJWT>=1.7.1
PyNaCl>=1.3.0
pyOpenSSL>=19.1.0
python-dateutil>=2.7.5
python-vlc
python3-openid>=3.1.0
pytz>=2017.3
PyYAML>=3.13
redis>=3.3.8
requests>=2.22.0
requests-oauthlib>=1.3.0
s3transfer>=0.1.13
service-identity>=18.1.0
six>=1.12.0
social-auth-app-django>=4.0.0
social-auth-core>=3.3.3
sqlparse>=0.3.1
stripe>=2.23.0
swapper>=1.1.0
tqdm>=4.32.1
traitlets>=4.3.2
Twisted>=20.3.0
txaio>=18.8.1
typing>=3.6.6
ua-parser>=0.8.0
unicodecsv>=0.14.1
urllib3>=1.22
user-agents>=1.1.0
uwsgitop>=0.11
vine>=1.3.0
wcwidth>=0.1.7
websockets>=8.0.2
whitenoise>=5.1.0
zipp>=3.1.0
zope.interface>=5.0.0
pdfkit>=1.0.0
wkhtmltopdf>=0.2
pyvirtualdisplay>=2.2
django-compressor>=4.4
pypdf2>=3.0.1
pdf2image>=1.16.3
soundfile>=0.12.1
pyloudnorm>=0.1.1
djangorestframework>=3.11
django-cors-headers==4.4.0
uvicorn[standard]==0.31.0
django-mcp>=0.1.0